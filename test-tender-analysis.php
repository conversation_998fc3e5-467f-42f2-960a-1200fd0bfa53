<?php

// Simple test script to check tender analysis endpoints
// Run this from the Laravel root directory: php test-tender-analysis.php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Test URLs
$baseUrl = 'http://localhost';
$endpoints = [
    '/api/tender-analysis/stats?period=30',
    '/api/tender-analysis/trends?months=6',
    '/api/tender-analysis/category?period=30',
    '/api/tender-analysis/method?period=30',
    '/api/tender-analysis/top-lpse?period=30',
];

echo "🧪 Testing Tender Analysis API Endpoints\n";
echo "==========================================\n\n";

foreach ($endpoints as $endpoint) {
    echo "Testing: {$endpoint}\n";
    
    try {
        // Create a request
        $request = Illuminate\Http\Request::create($endpoint, 'GET');
        
        // Process the request
        $response = $kernel->handle($request);
        
        echo "Status: " . $response->getStatusCode() . "\n";
        
        if ($response->getStatusCode() === 200) {
            $content = $response->getContent();
            $data = json_decode($content, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "✅ Success - Valid JSON response\n";
                echo "Data count: " . (is_array($data) ? count($data) : 'N/A') . "\n";
                
                // Show sample data
                if (is_array($data) && !empty($data)) {
                    echo "Sample data: " . json_encode(array_slice($data, 0, 2), JSON_PRETTY_PRINT) . "\n";
                }
            } else {
                echo "❌ Error - Invalid JSON response\n";
                echo "Response: " . substr($content, 0, 200) . "...\n";
            }
        } else {
            echo "❌ Error - HTTP " . $response->getStatusCode() . "\n";
            echo "Response: " . substr($response->getContent(), 0, 200) . "...\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Exception: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}

echo "🏁 Test completed!\n";
