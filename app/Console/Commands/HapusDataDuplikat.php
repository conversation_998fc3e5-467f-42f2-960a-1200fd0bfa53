<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PaketPengadaan;
use App\Models\TransaksiPaket;

class HapusDataDuplikat extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hapus:data-duplikat';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Hapus data duplikat di tabel paket pengadaan';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Kode yang saya berikan sebelumnya
        $duplikat = PaketPengadaan::groupBy('nomor_paket')
            ->havingRaw('COUNT(*) > 1')
            ->pluck('nomor_paket');

        // Hapus data duplikat
        foreach ($duplikat as $nomorPaket) {
            $paketPengadaan = PaketPengadaan::where('nomor_paket', $nomorPaket)
                ->orderBy('created_at', 'asc')
                ->first();

            // Hapus data transaksi paket yang terkait
            $transaksiPaket = TransaksiPaket::where('id_paket', $paketPengadaan->id_paket)->get();
            $transaksiPaket->each(function ($transaksi) {
                $this->info("Menghapus transaksi paket #{$transaksi->id} untuk paket #{$transaksi->id_paket}");
            });
            TransaksiPaket::where('id_paket', $paketPengadaan->id_paket)->delete();

            // Hapus data paket pengadaan duplikat
            $paketDuplikat = PaketPengadaan::where('nomor_paket', $nomorPaket)
                ->where('id_paket', '!=', $paketPengadaan->id_paket)
                ->get();
            foreach ($paketDuplikat as $paket) {
                // Hapus data transaksi paket yang terkait dengan paket duplikat
                $transaksiPaketDuplikat = TransaksiPaket::where('id_paket', $paket->id_paket)->get();
                $transaksiPaketDuplikat->each(function ($transaksi) {
                    $this->info("Menghapus transaksi paket #{$transaksi->id} untuk paket #{$transaksi->id_paket}");
                });
                TransaksiPaket::where('id_paket', $paket->id_paket)->delete();

                // Hapus data paket pengadaan duplikat
                $this->info("Menghapus paket pengadaan #{$paket->id} dengan nomor paket {$paket->nomor_paket}");
                $paket->delete();
            }
        }

        $this->info('Data duplikat telah dihapus');
    }
}