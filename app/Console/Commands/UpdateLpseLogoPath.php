<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Lpse;

class UpdateLpseLogoPath extends Command
{
    protected $signature = 'update:lpse-logo-path';
    protected $description = 'Update logo_path in lpse table to remove domain prefix and keep relative path';

    public function handle()
    {
        $domainPrefix = 'http://proyeklpse.test';
        $lpseRecords = Lpse::where('logo_path', 'like', $domainPrefix . '%')->get();

        $this->info('Found ' . $lpseRecords->count() . ' LPSE records with logo_path starting with domain prefix.');

        $updatedCount = 0;

        foreach ($lpseRecords as $lpse) {
            $originalPath = $lpse->logo_path;
            if (strpos($originalPath, $domainPrefix) === 0) {
                $newPath = substr($originalPath, strlen($domainPrefix));
                $lpse->logo_path = $newPath;
                $lpse->save();
                $this->info("Updated LPSE kd_lpse={$lpse->kd_lpse}: logo_path updated from '{$originalPath}' to '{$newPath}'");
                $updatedCount++;
            }
        }

        $this->info("Update completed. Total records updated: {$updatedCount}");

        return 0;
    }
}
