<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TelegramWebhook extends Command
{
    protected $signature = 'telegram:webhook';
    protected $description = 'Set Telegram webhook';

    public function handle()
    {
        $token = config('services.telegram-bot-api.token');
        $url = url('/api/telegram/webhook');

        $response = Http::get("https://api.telegram.org/bot{$token}/setWebhook", [
            'url' => $url,
        ]);

        $this->info($response->body());
    }
}