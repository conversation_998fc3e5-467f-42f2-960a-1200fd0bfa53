<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DataSirup;
use App\Crawler\CustomClientSquid;
use Symfony\Component\DomCrawler\Crawler;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Throwable;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class ScrapeDataSirup extends Command
{
    use CommandManagementTrait;

    protected $signature = 'sirup:scrape {kodeSirup?}
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}
                            {--lock-name= : Custom lock name for this command instance}';
    protected $description = 'Scrape data from Sirup website and update DataSirup records';

    protected $bulanMapping = [
        'Januari' => 'January',
        'Februari' => 'February',
        'Maret' => 'March',
        'April' => 'April',
        'Mei' => 'May',
        'Juni' => 'June',
        'Juli' => 'July',
        'Agustus' => 'August',
        'September' => 'September',
        'Oktober' => 'October',
        'November' => 'November',
        'Desember' => 'December',
    ];

    protected $client;

    protected $baseUrl = 'https://sirup.lkpp.go.id';

    public function __construct()
    {
        parent::__construct();

        // Daftar User-Agent yang realistis
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36 Edg/92.0.902.84'
        ];

        // Pilih User-Agent secara acak
        $randomUserAgent = $userAgents[array_rand($userAgents)];

        // Header tambahan untuk mengurangi risiko diblokir
        $headers = [
            'User-Agent' => $randomUserAgent,
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language' => 'id,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding' => 'gzip, deflate, br',
            'Connection' => 'keep-alive',
            'Upgrade-Insecure-Requests' => '1',
            'Cache-Control' => 'max-age=0',
            'TE' => 'Trailers'
        ];

        $this->client = new CustomClientSquid([
            'base_uri' => $this->baseUrl,
            'timeout' => 10.0, // Timeout 10 detik untuk request HTTP
            'headers' => $headers
        ]);
    }


    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');
        // Lock name is handled automatically by CommandManagementTrait

        if (!$this->initCommandManagement($timeout, $force)) {
            return 1; // FAILURE
        }

        try {
            $startTime = microtime(true);
            $kodeSirup = $this->argument('kodeSirup');

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                $kodeSirup ? "Memulai scraping untuk kode sirup: {$kodeSirup}" : "Memulai proses scraping data sirup",
                [
                    'kode_sirup' => $kodeSirup,
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            if ($kodeSirup) {
                $this->scrapeDataSingleSirup($kodeSirup);

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    100,
                    100,
                    "Selesai scraping untuk kode sirup: {$kodeSirup}",
                    [
                        'kode_sirup' => $kodeSirup,
                        'memory_usage' => $this->getMemoryUsage(),
                        'elapsed_time' => $this->getElapsedTimeFromStart($startTime)
                    ]
                );
            } else {
                $kodeSirups = DataSirup::whereNull('tahun_anggaran')
                    ->whereNull('tanggal_paket_diumumkan')
                    ->pluck('kode_rup');

                $totalCount = $kodeSirups->count();

                $this->info("Memulai proses scraping ulang total pagu bermasalah... ({$totalCount} data)");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    0,
                    $totalCount,
                    "Memulai proses scraping ulang total pagu bermasalah",
                    [
                        'total_data' => $totalCount,
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $totalProcessed = 0;
                $totalUpdated = 0;
                $totalFailed = 0;
                $bar = $this->output->createProgressBar($totalCount);
                $bar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %remaining:-6s%');
                $bar->setBarCharacter('<fg=green>=</>');
                $bar->setEmptyBarCharacter('<fg=red>-</>');
                $bar->setProgressCharacter('<fg=yellow>></>');
                $bar->setOverwrite(true);
                $bar->setRedrawFrequency(1);
                $bar->start();

                $kodeSirups = $kodeSirups->shuffle();

                foreach ($kodeSirups as $index => $kodeSirup) {
                    try {
                        // Tambahkan jeda waktu acak yang singkat (200-500ms)
                        $acak = random_int(125000, 200000);
                        usleep($acak);
                        $this->scrapeDataSingleSirup($kodeSirup);
                        $bar->advance();
                        $totalProcessed++;

                        // Update progress every 10 items or at the end
                        if ($totalProcessed % 10 === 0 || $totalProcessed === $totalCount) {
                            $progress = min(99, round(($totalProcessed / $totalCount) * 100));
                            CommandProgress::updateProgress(
                                $this->getName(),
                                $totalProcessed,
                                $totalCount,
                                "Memproses data sirup ({$totalProcessed}/{$totalCount})",
                                [
                                    'processed' => $totalProcessed,
                                    'total' => $totalCount,
                                    'percentage' => $progress,
                                    'memory_usage' => $this->getMemoryUsage(),
                                    'elapsed_time' => $this->getElapsedTimeFromStart($startTime)
                                ]
                            );
                        }

                        // Periksa apakah perlu berhenti karena batas waktu
                        if (!$this->checkTimeLimit("Berhenti karena batas waktu tercapai")) {
                            $this->warn("Berhenti karena batas waktu tercapai");
                            break;
                        }
                    } catch (\Exception $e) {
                        // Jika mendapatkan error 429, hentikan seluruh proses
                        if ($e->getCode() == 429) {
                            $this->error("Menghentikan seluruh proses scraping karena error 429 Too Many Requests");

                            // Update progress
                            CommandProgress::markAsFailed(
                                $this->getName(),
                                "Proses scraping dihentikan karena error 429 Too Many Requests",
                                [
                                    'processed' => $totalProcessed,
                                    'total' => $totalCount,
                                    'percentage' => round(($totalProcessed / $totalCount) * 100),
                                    'memory_usage' => $this->getMemoryUsage(),
                                    'elapsed_time' => $this->getElapsedTimeFromStart($startTime),
                                    'error_code' => 429
                                ]
                            );

                            // Keluar dari loop
                            break;
                        } else {
                            // Untuk error lain, log dan lanjutkan
                            $this->error("Error pada kode sirup {$kodeSirup}: " . $e->getMessage());
                            $totalFailed++;
                            continue;
                        }
                    }
                }
                $bar->finish();
                $this->info('Proses scraping selesai.');
                $this->info("Total data diproses: {$totalProcessed}");
                $this->info("Total data diperbarui: {$totalUpdated}");
                $this->info("Total data gagal: {$totalFailed}");

                // Mark command as completed
                CommandProgress::markAsCompleted(
                    $this->getName(),
                    "Proses scraping selesai. Total data diproses: {$totalProcessed}",
                    [
                        'total_processed' => $totalProcessed,
                        'total_updated' => $totalUpdated,
                        'total_failed' => $totalFailed,
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage(),
                        'elapsed_time' => $this->getElapsedTimeFromStart($startTime)
                    ]
                );
            }

            $this->info('Proses scraping selesai.');
            return 0; // SUCCESS
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return 1; // FAILURE
        }
    }

    /**
     * Get elapsed time in seconds from a specific start time
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTimeFromStart(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    /**
     * Get elapsed time in seconds from command start time
     * This method is required by CommandManagementTrait
     *
     * @return float
     */
    protected function getElapsedTime(): float
    {
        // Menggunakan commandStartTime dari CommandManagementTrait
        return round(microtime(true) - $this->commandStartTime, 2);
    }

    public function scrapeDataSingleSirup($kodeSirup)
    {
        Log::info("Memulai proses scraping untuk kode sirup: {$kodeSirup}");
        $this->newLine();
        $this->info("Memulai proses scraping untuk kode sirup: {$kodeSirup}");

        $url = "/sirup/rup/detailPaketPenyedia2020?idPaket={$kodeSirup}";
        $fullUrl = $this->baseUrl . $url;

        try {
            $cekAda = DataSirup::where('kode_rup', $kodeSirup)
                ->whereNotNull('tahun_anggaran')->first();
            if ($cekAda) {
                $this->info("========Data untuk kode sirup {$kodeSirup} sudah ada.==========");
                return;
            }

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                "Mengambil data untuk kode sirup: {$kodeSirup}",
                [
                    'kode_sirup' => $kodeSirup,
                    'status' => 'fetching',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Implementasi backoff eksponensial untuk mengurangi risiko diblokir
            $maxRetries = 2;
            $retryCount = 0;
            $backoffSeconds = 0.5; // Mulai dengan 0.5 detik

            while ($retryCount <= $maxRetries) {
                try {
                    // Tambahkan opsi timeout 10 detik untuk request ini
                    $response = $this->client->get($fullUrl, [
                        'timeout' => 10.0, // Timeout 10 detik untuk request HTTP
                    ]);

                    // Jika berhasil, keluar dari loop
                    break;
                } catch (\GuzzleHttp\Exception\ClientException $e) {
                    // Jika mendapat error 429, throw exception dengan kode 429
                    if ($e->getResponse() && $e->getResponse()->getStatusCode() == 429) {
                        $errorMessage = "Terjadi HTTP 429 Too Many Requests. Proses scraping dihentikan untuk menghindari pemblokiran.";
                        $this->error($errorMessage);
                        Log::error("HTTP 429 Too Many Requests diterima. Proses scraping dihentikan.");

                        // Log system error
                        $this->logSystemError(
                            new \Exception($errorMessage, 429),
                            "HTTP 429 Too Many Requests"
                        );

                        // Update progress
                        CommandProgress::markAsFailed(
                            $this->getName(),
                            $errorMessage,
                            [
                                'kode_sirup' => $kodeSirup,
                                'status_code' => 429,
                                'memory_usage' => $this->getMemoryUsage()
                            ]
                        );

                        throw new \Exception($errorMessage, 429); // Throw exception with HTTP 429 status code
                    }

                    // Untuk error lain, coba lagi dengan backoff
                    $retryCount++;
                    if ($retryCount > $maxRetries) {
                        throw $e; // Jika sudah melebihi batas retry, throw exception
                    }

                    // Tunggu dengan backoff eksponensial (dalam mikrodetik)
                    $sleepTime = $backoffSeconds * pow(2, $retryCount - 1);
                    $sleepTimeMicro = (int)($sleepTime * 1000000); // Konversi ke mikrodetik
                    $this->warn("Request gagal, mencoba lagi dalam {$sleepTime} detik (percobaan {$retryCount}/{$maxRetries})");
                    usleep($sleepTimeMicro);
                }
            }

            // Check if the response status is 429 Too Many Requests
            if (isset($response) && $response->getStatusCode() == 429) {
                $errorMessage = "Terjadi HTTP 429 Too Many Requests. Proses scraping dihentikan untuk menghindari pemblokiran.";
                $this->error($errorMessage);
                Log::error("HTTP 429 Too Many Requests diterima. Proses scraping dihentikan.");

                // Log system error
                $this->logSystemError(
                    new \Exception($errorMessage, 429),
                    "HTTP 429 Too Many Requests"
                );

                // Update progress
                CommandProgress::markAsFailed(
                    $this->getName(),
                    $errorMessage,
                    [
                        'kode_sirup' => $kodeSirup,
                        'status_code' => 429,
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                throw new \Exception($errorMessage, 429); // Throw exception with HTTP 429 status code
            }

            $html = (string) $response->getBody();

            // Check if the HTML contains the login page title
            if (strpos($html, '<title>RUP - Form Login</title>') !== false) {
                $warningMessage = "Halaman login terdeteksi untuk kode sirup {$kodeSirup}, menghapus data jika ada dan melewati proses.";
                $this->warn($warningMessage);
                Log::warning($warningMessage);

                // Delete the DataSirup record if exists
                DataSirup::where('kode_rup', $kodeSirup)->delete();

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    50,
                    100,
                    $warningMessage,
                    [
                        'kode_sirup' => $kodeSirup,
                        'status' => 'login_page_detected',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                return;
            }

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                50,
                100,
                "Parsing data untuk kode sirup: {$kodeSirup}",
                [
                    'kode_sirup' => $kodeSirup,
                    'status' => 'parsing',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $crawler = new Crawler($html);
            $dataScrape = $this->parseHtml($crawler);

            // Check if the record exists
            $dataSirup = DataSirup::where('kode_rup', $kodeSirup)->first();

            if ($dataSirup) {
                // Update existing record
                $dataSirup->update($dataScrape);
                $successMessage = "Data untuk kode sirup {$kodeSirup} berhasil diperbarui.";
                $this->info($successMessage);
                $this->newLine();

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    100,
                    100,
                    $successMessage,
                    [
                        'kode_sirup' => $kodeSirup,
                        'status' => 'updated',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );
            } else {
                // Create new record
                $dataSirup = new DataSirup();
                $dataSirup->kode_rup = $kodeSirup;
                $dataSirup->fill($dataScrape);
                $dataSirup->save();
                $successMessage = "Data untuk kode sirup {$kodeSirup} berhasil ditambahkan.";
                $this->info($successMessage);

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    100,
                    100,
                    $successMessage,
                    [
                        'kode_sirup' => $kodeSirup,
                        'status' => 'created',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );
            }
        } catch (Throwable $e) {
            $errorMessage = "Gagal memproses kode sirup: {$kodeSirup}. Error: " . $e->getMessage();
            $this->error($errorMessage);
            Log::error($errorMessage);

            // Log system error
            $this->logSystemError(
                $e,
                "scrapeDataSingleSirup for kode_sirup: {$kodeSirup}"
            );

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                100,
                100,
                $errorMessage,
                [
                    'kode_sirup' => $kodeSirup,
                    'status' => 'error',
                    'error' => $e->getMessage(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );
        }
    }

    protected function parseHtml(Crawler $crawler)
    {
        $data = [
            'nama_klpd' => null,
            'tahun_anggaran' => null,
            'satuan_kerja' => null,
            'lokasi_pekerjaan' => [],
            'volume_pekerjaan' => null,
            'uraian_pekerjaan' => null,
            'spesifikasi_pekerjaan' => null,
            'produk_dalam_negeri' => null,
            'umkm' => null,
            'total_pagu' => null,
            'pra_dipa_dpa' => null,
            'sumber_dana' => [],
            'jenis_pengadaan' => [],
            'pemanfaatan_barang_jasa' => [],
            'jadwal_pelaksanaan_kontrak' => [],
            'jadwal_pemilihan_penyedia' => [],
            'tanggal_paket_diumumkan' => null,
        ];

        $crawler->filter('table.table > tr')->each(function (Crawler $tr, $i) use (&$data) {
            $tds = $tr->filter('td');
            if ($tds->count() < 2) {
                return;
            }

            $label = trim($tds->eq(0)->text());
            $valueNode = $tds->eq(1);

            switch ($label) {
                case 'Nama Paket':
                    $data['nama_paket'] = trim($valueNode->text());
                    break;
                case 'Nama KLPD':
                    $data['nama_klpd'] = trim($valueNode->text());
                    break;
                case 'Satuan Kerja':
                    $data['satuan_kerja'] = trim($valueNode->text());
                    break;
                case 'Tahun Anggaran':
                    $data['tahun_anggaran'] = intval(trim($valueNode->text()));
                    break;
                case 'Lokasi Pekerjaan':
                    $lokasi = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $lokTr, $j) use (&$lokasi) {
                        if ($j == 0)
                            return;
                        $tdsLok = $lokTr->filter('td');
                        if ($tdsLok->count() >= 4) {
                            $lokasi[] = [
                                'no' => trim($tdsLok->eq(0)->text(), '.'),
                                'provinsi' => trim($tdsLok->eq(1)->text()),
                                'kabupaten_kota' => trim($tdsLok->eq(2)->text()),
                                'detail_lokasi' => trim($tdsLok->eq(3)->text()),
                            ];
                        }
                    });
                    $data['lokasi_pekerjaan'] = $lokasi;
                    break;
                case 'Volume Pekerjaan':
                    $data['volume_pekerjaan'] = trim($valueNode->text());
                    break;
                case 'Uraian Pekerjaan':
                    $data['uraian_pekerjaan'] = trim($valueNode->text());
                    break;
                case 'Spesifikasi Pekerjaan':
                    $data['spesifikasi_pekerjaan'] = trim($valueNode->text());
                    break;
                case 'Produk Dalam Negeri':
                    $data['produk_dalam_negeri'] = $this->parseBadge($valueNode);
                    break;
                case 'Usaha Kecil/Koperasi':
                    $data['umkm'] = $this->parseBadge($valueNode);
                    break;
                case 'Pra DIPA / DPA':
                    $data['pra_dipa_dpa'] = $this->parseBadge($valueNode);
                    break;
                case 'Sumber Dana':
                    $sumberDana = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $sdTr, $k) use (&$sumberDana) {
                        if ($k == 0)
                            return;
                        $tdsSd = $sdTr->filter('td');
                        if ($tdsSd->count() >= 6) {
                            $sumberDana[] = [
                                'no' => trim($tdsSd->eq(0)->text(), '.'),
                                'sumber_dana' => trim($tdsSd->eq(1)->text()),
                                'ta' => trim($tdsSd->eq(2)->text()),
                                'klpd' => trim($tdsSd->eq(3)->text()),
                                'mak' => trim($tdsSd->eq(4)->text()),
                                'pagu' => $this->parseRupiah($tdsSd->eq(5)->text()),
                            ];
                        }
                    });
                    $data['sumber_dana'] = $sumberDana;
                    break;
                case 'Jenis Pengadaan':
                    $jenisPengadaan = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $jpTr, $m) use (&$jenisPengadaan) {
                        if ($m == 0)
                            return;
                        $tdsJp = $jpTr->filter('td');
                        if ($tdsJp->count() >= 3) {
                            $jenisPengadaan[] = [
                                'no' => trim($tdsJp->eq(0)->text(), '.'),
                                'jenis_pengadaan' => trim($tdsJp->eq(1)->text()),
                                'pagu_jenis_pengadaan' => $this->parseRupiah($tdsJp->eq(2)->text()),
                            ];
                        }
                    });
                    $data['jenis_pengadaan'] = $jenisPengadaan;
                    break;
                case 'Total Pagu':
                    $data['total_pagu'] = $this->parseRupiah(trim($valueNode->text()));
                    break;
                case 'Metode Pemilihan':
                    $data['metode_pemilihan'] = trim($valueNode->text());
                    break;
                case 'Pemanfaatan Barang/Jasa':
                    $pemanfaatan = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $pbjTr, $n) use (&$pemanfaatan) {
                        if ($n == 0)
                            return;
                        $tdsPbj = $pbjTr->filter('td');
                        if ($tdsPbj->count() >= 2) {
                            $pemanfaatan[] = [
                                'mulai' => trim($tdsPbj->eq(0)->text()),
                                'akhir' => trim($tdsPbj->eq(1)->text()),
                            ];
                        }
                    });
                    $data['pemanfaatan_barang_jasa'] = $pemanfaatan;
                    break;
                case 'Jadwal Pelaksanaan Kontrak':
                    $jadwalKontrak = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $jpKontrakTr, $o) use (&$jadwalKontrak) {
                        if ($o == 0)
                            return;
                        $tdsJpKontrak = $jpKontrakTr->filter('td');
                        if ($tdsJpKontrak->count() >= 2) {
                            $jadwalKontrak[] = [
                                'mulai' => trim($tdsJpKontrak->eq(0)->text()),
                                'akhir' => trim($tdsJpKontrak->eq(1)->text()),
                            ];
                        }
                    });
                    $data['jadwal_pelaksanaan_kontrak'] = $jadwalKontrak;
                    break;
                case 'Jadwal Pemilihan Penyedia':
                    $jadwalPenyedia = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $jpPenyediaTr, $p) use (&$jadwalPenyedia) {
                        if ($p == 0)
                            return;
                        $tdsJpPenyedia = $jpPenyediaTr->filter('td');
                        if ($tdsJpPenyedia->count() >= 2) {
                            $jadwalPenyedia[] = [
                                'mulai' => trim($tdsJpPenyedia->eq(0)->text()),
                                'akhir' => trim($tdsJpPenyedia->eq(1)->text()),
                            ];
                        }
                    });
                    $data['jadwal_pemilihan_penyedia'] = $jadwalPenyedia;
                    break;
                case 'Tanggal Umumkan Paket':
                    $tanggal = trim($valueNode->text());
                    $tanggalTranslated = $this->translateMonth($tanggal);
                    try {
                        $data['tanggal_paket_diumumkan'] = \Carbon\Carbon::createFromFormat('d F Y H:i', $tanggalTranslated);
                    } catch (\Exception $e) {
                        $data['tanggal_paket_diumumkan'] = null;
                        Log::warning("Format tanggal tidak sesuai: {$tanggal}");
                    }
                    break;
                default:
                    break;
            }
        });

        return $data;
    }

    protected function translateMonth(string $dateString): string
    {
        foreach ($this->bulanMapping as $indonesian => $english) {
            if (stripos($dateString, $indonesian) !== false) {
                $dateString = str_ireplace($indonesian, $english, $dateString);
            }
        }

        return $dateString;
    }

    protected function parseBadge(Crawler $node)
    {
        if ($node->filter('.badge')->count() > 0) {
            $badgeText = strtolower(trim($node->filter('.badge')->text()));

            if ($badgeText === 'ya') {
                return true;
            } elseif ($badgeText === 'tidak') {
                return false;
            }
        }

        return null;
    }

    protected function parseRupiah($text)
    {
        $number = preg_replace('/[^\d]/', '', $text);

        return $number ? intval($number) : null;
    }
}
