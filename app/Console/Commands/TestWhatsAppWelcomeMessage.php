<?php

namespace App\Console\Commands;

use App\Helpers\WhatsAppHelper;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestWhatsAppWelcomeMessage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:test-welcome {user_id? : ID pengguna yang akan dikirim pesan} {--phone= : Nomor telepon untuk pengujian}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mengirim pesan WhatsApp selamat datang untuk pengujian';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $phone = $this->option('phone');

        if (!$userId && !$phone) {
            $this->error('Harap berikan ID pengguna atau nomor telepon untuk pengujian.');
            return 1;
        }

        if ($userId) {
            $user = User::find($userId);
            if (!$user) {
                $this->error("Pengguna dengan ID {$userId} tidak ditemukan.");
                return 1;
            }

            if (!$user->phone) {
                $this->error("Pengguna dengan ID {$userId} tidak memiliki nomor telepon.");
                return 1;
            }

            $phone = $user->phone;
            $userData = [
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $phone,
            ];

            $this->info("Mengirim pesan selamat datang ke pengguna: {$user->name} ({$phone})");
        } else {
            // Gunakan nomor telepon yang diberikan dengan data dummy
            $userData = [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'phone' => $phone,
            ];

            $this->info("Mengirim pesan selamat datang ke nomor: {$phone}");
        }

        $this->info("Data pengguna: " . json_encode($userData, JSON_PRETTY_PRINT));

        try {
            // Kirim pesan selamat datang
            $success = WhatsAppHelper::sendWelcomeMessage($phone, $userData);

            if ($success) {
                $this->info("Pesan berhasil dikirim!");

                Log::info('WhatsApp test message sent successfully', [
                    'phone' => $phone
                ]);

                return 0;
            } else {
                $this->error("Gagal mengirim pesan!");

                Log::error('Failed to send WhatsApp test message', [
                    'phone' => $phone
                ]);

                return 1;
            }
        } catch (\Exception $e) {
            $this->error("Terjadi kesalahan: " . $e->getMessage());

            Log::error('Exception when sending WhatsApp test message', [
                'phone' => $phone,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 1;
        }
    }
}
