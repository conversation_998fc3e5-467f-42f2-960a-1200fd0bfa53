<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DataSirup;
use GuzzleHttp\Client;
use Symfony\Component\DomCrawler\Crawler;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ScrapePaguDataSirup extends Command
{
    protected $signature = 'fixkan:pagu-datasirup 
                        {rup_id? : ID RUP spesifik untuk diproses} 
                        {--hapus : Menghapus data dengan total_pagu <= 0}';
    protected $description = 'Scrape ulang data total pagu bermasalah pada DataSirup';
    protected $client;
    protected $baseUrl = 'https://hostestapp.xyz/?url=https://sirup.lkpp.go.id';

    public function __construct()
    {
        parent::__construct();
        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => 240.0,
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (compatible; PaguDataSirupScraper/1.0)',
            ],
        ]);
    }

    public function handle()
    {
        $rupId = $this->argument('rup_id');
    	
    	    // Handle opsi --hapus
    	if ($this->option('hapus')) {
        	return $this->handleDelete($rupId);
    	}

        if ($rupId) {
            $this->info("Memproses data untuk RUP ID: {$rupId}");
            $query = DataSirup::where('kode_rup', $rupId);
        	$allCount = $query->count();
        } else {
            $this->info('Memulai proses scraping ulang total pagu bermasalah...');
            $query = DataSirup::where('total_pagu', '<=', 0)->inRandomOrder();
        	$allCount = $query->count();
        }

        $totalProcessed = 0;
        $totalUpdated = 0;
        $totalFailed = 0;
    	$bar = $this->output->createProgressBar($allCount);
        $bar->start();

        $query->chunkById(5000, function ($dataSirups) use ($rupId, &$totalProcessed, &$totalUpdated, &$totalFailed, $bar) {
        
            foreach ($dataSirups as $dataSirup) {
                $detailMessage = "Memproses kode RUP: {$dataSirup->kode_rup}";

                // Update progress bar and dynamic message
                $this->updateProgressBarWithDetail($bar, $detailMessage);

                DB::beginTransaction();

                try {
                    $recordQuery = DataSirup::where('id', $dataSirup->id)->lockForUpdate();

                    if (!$rupId) {
                        $recordQuery->where('total_pagu', '<=', 0);
                    }

                    $record = $recordQuery->first();

                    if (!$record) {
                        DB::rollBack();
                        $detailMessage = "Data untuk ID {$dataSirup->id} tidak ditemukan atau tidak memenuhi kriteria.";
                        $this->updateProgressBarWithDetail($bar, $detailMessage);
                        $totalFailed++;
                        continue;
                    }

                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error("Error locking record ID {$dataSirup->id}: " . $e->getMessage());
                    $detailMessage = "Gagal memproses ID {$dataSirup->id}: {$e->getMessage()}";
                    $this->updateProgressBarWithDetail($bar, $detailMessage);
                    $totalFailed++;
                    continue;
                }

                $url = "/sirup/rup/detailPaketPenyedia2020?idPaket={$record->kode_rup}";
                $fullUrl = $this->baseUrl . $url;

                try {
                    $response = $this->client->request('GET', $fullUrl);
                    $html = (string) $response->getBody();
                    $crawler = new Crawler($html);

                    $totalPagu = $this->extractTotalPagu($crawler);
                    $oldTotalPagu = $record->total_pagu;

                    if ($totalPagu != $oldTotalPagu && $totalPagu != 0) {
                        $record->update([
                            'total_pagu' => $totalPagu,
                            'is_processing' => 1,
                        ]);
                        $detailMessage = "Data total pagu untuk kode RUP {$record->kode_rup} berhasil diperbarui dari {$oldTotalPagu} menjadi {$totalPagu}.";
                        $totalUpdated++;
                    } else {
                        $record->update([
                            'is_processing' => 1,
                        ]);
                        $detailMessage = "Data total pagu untuk kode RUP {$record->kode_rup} TIDAK BERUBAH. Pagu Sebelumnya {$oldTotalPagu}; Hasil Pengecekan {$totalPagu}.";
                    }
                } catch (\Exception $e) {
                    Log::error("Error scraping kode RUP {$record->kode_rup}: " . $e->getMessage());
                    $detailMessage = "Gagal scraping kode RUP {$record->kode_rup}: {$e->getMessage()}";
                    $totalFailed++;
                    continue;
                }

                $totalProcessed++;
                $this->updateProgressBarWithDetail($bar, $detailMessage);
                usleep(150000); // Delay 0.1 detik
                $bar->advance();
            }

        });
    	$bar->finish();

        $this->info("\nProses scraping selesai.");
        $this->info("Rangkuman:");
        $this->info("Total data diproses: {$totalProcessed}");
        $this->info("Total data berhasil diperbarui: {$totalUpdated}");
        $this->info("Total data gagal diproses: {$totalFailed}");

        return 0;
    }

	protected function handleDelete($rupId)
	{
    	$this->info('Memulai proses penghapusan data...');

    	$query = DataSirup::where('total_pagu', '<=', 0);

    	if ($rupId) {
        	$query->where('kode_rup', $rupId);
        	$this->info("Memproses penghapusan untuk RUP ID: {$rupId}");
   	 	}

    	$count = $query->count();

    	if ($count === 0) {
        	$this->info("Tidak ada data yang memenuhi kriteria penghapusan.");
        	return 0;
    	}

    	if (!$this->confirm("Anda yakin ingin menghapus {$count} data?")) {
        	$this->info('Penghapusan dibatalkan.');
        	return 0;
    	}

    	$deleted = $query->delete();
    	$this->info("Berhasil menghapus {$deleted} data.");

    	return 0;
	}

    protected function extractTotalPagu(Crawler $crawler)
    {
        $totalPagu = null;

        $crawler->filter('table.table > tr')->each(function (Crawler $tr) use (&$totalPagu) {
            $tds = $tr->filter('td');
            if ($tds->count() < 2) {
                return;
            }

            $label = trim($tds->eq(0)->text());
            $value = trim($tds->eq(1)->text());

            if ($label === 'Total Pagu') {
                $totalPagu = $this->parseRupiah($value);
            }
        });

        return $totalPagu;
    }

    protected function parseRupiah($value)
    {
        $value = preg_replace('/[^\d-]/', '', $value);
        return (float) $value;
    }

    protected function updateProgressBarWithDetail($bar, $detailMessage)
    {
        $bar->clear();
        $this->line($detailMessage);
        $bar->display();
    }
}
