<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class UpdatePerusahaanStatistik extends Command
{
    use CommandManagementTrait;

    // Menambahkan opsi --namaPerusahaan untuk memfilter perusahaan berdasarkan nama
    protected $signature = 'update:perusahaan-statistik
                            {--namaPerusahaan= : Filter perusahaan berdasarkan nama}
                            {--lock-name= : Custom lock name for this command instance}
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}';
    protected $description = 'Mengupdate data statistik perusahaan ke tabel perusahaan_statistik';

    /**
     * Get elapsed time in seconds
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTime(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            $this->info('Memulai proses update statistik perusahaan...');

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                "Memulai proses update statistik perusahaan...",
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Ambil nilai opsi namaPerusahaan jika disediakan
            $namaPerusahaan = $this->option('namaPerusahaan');

            // Ambil data dari list_perusahaan_tender, dengan filter jika parameter diberikan
            if ($namaPerusahaan) {
                $this->info("Memfilter perusahaan dengan nama: {$namaPerusahaan}");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    5,
                    100,
                    "Memfilter perusahaan dengan nama: {$namaPerusahaan}",
                    [
                        'nama_perusahaan' => $namaPerusahaan,
                        'stage' => 'filtering',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $perusahaanList = DB::table('list_perusahaan_tender')
                    ->select('id')
                    ->where('nama_peserta', 'like', "%{$namaPerusahaan}%")
                    ->get();
            } else {
                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    5,
                    100,
                    "Mengambil semua data perusahaan",
                    [
                        'stage' => 'fetching_all',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $perusahaanList = DB::table('list_perusahaan_tender')
                    ->select('id')
                    ->get();
            }

            $totalPerusahaan = count($perusahaanList);
            $this->info("Total {$totalPerusahaan} perusahaan akan diproses");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                10,
                100,
                "Memulai pemrosesan {$totalPerusahaan} perusahaan",
                [
                    'total_perusahaan' => $totalPerusahaan,
                    'stage' => 'processing_start',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $counter = 0;

            foreach ($perusahaanList as $perusahaan) {
                $counter++;
                $progress = min(90, 10 + round(($counter / $totalPerusahaan) * 80));
                $idPerusahaan = $perusahaan->id;

                try {
                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        $progress,
                        100,
                        "Memproses perusahaan ID {$idPerusahaan} ({$counter}/{$totalPerusahaan})",
                        [
                            'id_perusahaan' => $idPerusahaan,
                            'current' => $counter,
                            'total' => $totalPerusahaan,
                            'stage' => 'processing_company',
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    // Hitung jumlah tender diikuti
                    $jumlahTenderDiikuti = DB::table('peserta_tender')
                        ->where('id_peserta', $idPerusahaan)
                        ->count();

                    // Hitung jumlah tender dimenangkan
                    $jumlahTenderDimenangkan = DB::table('pemenang_lpse')
                        ->where('id_peserta', $idPerusahaan)
                        ->count();

                    // Hitung nilai tender dimenangkan
                    $nilaiTenderDimenangkan = DB::table('pemenang_lpse')
                            ->where('id_peserta', $idPerusahaan)
                            ->sum(DB::raw('CASE WHEN harga_penawaran = 0 THEN harga_terkoreksi ELSE harga_penawaran END'));

                    // Hitung jumlah e-katalog 5 didapatkan
                    $jumlahEkatalog5Didapatkan = DB::table('ekatalog_paket_pengadaan')
                        ->where('id_pelaksana', $idPerusahaan)
                        ->count();

                    // Hitung nilai e-katalog 5 didapatkan
                    $nilaiEkatalog5Didapatkan = DB::table('ekatalog_transaksi_paket')
                        ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                        ->where('ekatalog_paket_pengadaan.id_pelaksana', $idPerusahaan)
                        ->sum('ekatalog_transaksi_paket.total_harga');

                    // Hitung jumlah e-katalog 6 didapatkan
                    $jumlahEkatalog6Didapatkan = DB::table('ekatalog6')
                        ->where('id_penyedia', $idPerusahaan)
                        ->count();

                    // Hitung nilai e-katalog 6 didapatkan
                    $nilaiEkatalog6Didapatkan = DB::table('ekatalog6')
                        ->where('id_penyedia', $idPerusahaan)
                        ->sum('total_pelaksanaan');

                    // Hitung total jumlah e-katalog (5 + 6)
                    $jumlahEkatalogDidapatkan = $jumlahEkatalog5Didapatkan + $jumlahEkatalog6Didapatkan;

                    // Hitung total nilai e-katalog (5 + 6)
                    $nilaiEkatalogDidapatkan = $nilaiEkatalog5Didapatkan + $nilaiEkatalog6Didapatkan;

                    // Hitung total tender dan e-katalog
                    $totalTenderEkatalog = $nilaiTenderDimenangkan + $nilaiEkatalogDidapatkan;

                    // Update atau insert data ke tabel perusahaan_statistik
                    DB::table('perusahaan_statistik')->updateOrInsert(
                        ['id_perusahaan' => $idPerusahaan],
                        [
                            'jumlah_tender_diikuti' => $jumlahTenderDiikuti,
                            'jumlah_tender_dimenangkan' => $jumlahTenderDimenangkan,
                            'nilai_tender_dimenangkan' => $nilaiTenderDimenangkan,
                            'jumlah_ekatalog_didapatkan' => $jumlahEkatalogDidapatkan,
                            'nilai_ekatalog_didapatkan' => $nilaiEkatalogDidapatkan,
                            'total_tender_ekatalog' => $totalTenderEkatalog,
                            'updated_at' => now(),
                        ]
                    );

                    $this->info("Statistik untuk perusahaan ID {$idPerusahaan} berhasil diupdate.");
                } catch (\Exception $e) {
                    $this->error("Error saat memproses perusahaan ID {$idPerusahaan}: " . $e->getMessage());

                    // Log system error
                    $this->logSystemError($e, "handle method - processing company ID: {$idPerusahaan}");
                }
            }

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            $message = "Proses update selesai dalam " . round($executionTime, 2) . " detik.";

            $this->info($message);

            // Mark command as completed
            CommandProgress::markAsCompleted(
                $this->getName(),
                $message,
                [
                    'total_perusahaan' => $totalPerusahaan,
                    'processed' => $counter,
                    'execution_time' => round($executionTime, 2),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }
}
