<?php
namespace App\Console\Commands;

use App\Models\Anggaran;
use App\Models\InstansiSatker;
use App\Models\Jadwal;
use App\Models\ListPerusahaanTender;
use App\Models\LokasiPaket;
use App\Models\PemenangLpse;
use App\Models\PesertaTender;
use App\Models\SyncLog;
use App\Models\TenderLpse;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SyncLpseData extends Command
{
    protected $signature = 'sync:lpse-data {kd_lpse? : The specific LPSE code to sync}';
    protected $description = 'Sync LPSE data from API';

    public function handle()
    {
        $kdLpseParam = $this->argument('kd_lpse');

        $query = DB::table('lpse')
            ->orderBy('kd_lpse', 'asc');

        if ($kdLpseParam) {
            $query->where('kd_lpse', $kdLpseParam);
        } else {
            $query->where('kd_lpse', '>', 0);
        }

        $kdLpses = $query->pluck('kd_lpse');

        if ($kdLpses->isEmpty()) {
            $this->warn("No LPSE codes found for syncing.");
            //Log::warning("No LPSE codes found for syncing.");
            return;
        }

        //Log::info("Starting LPSE data sync");
        foreach ($kdLpses as $kdLpse) {
            $this->info("Syncing data for LPSE code: {$kdLpse}");
            //  Log::info("Syncing data for LPSE code: {$kdLpse}");
            $this->syncLpseData($kdLpse);
        }

        $this->info("All LPSE data sync completed.");
        //Log::info("All LPSE data sync completed.");
    }

    private function syncLpseData($kdLpse)
    {
        $apiUrl     = "https://tenderid.mcmtestapp.com/api/tender/lpse/{$kdLpse}";
        $maxRetries = 3;
        $retryDelay = 5; // seconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                $response = Http::timeout(300)->get($apiUrl); // Increased timeout to 5 minutes

                if ($response->successful()) {
                    $data = $response->json();

                    foreach ($data as $tenderData) {
                        try {
                            DB::beginTransaction();
                            $this->processLpseData($tenderData);
                            DB::commit();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            $this->error("Error processing tender data for Tender code {$tenderData['Kode Tender']}: " . $e->getMessage());
                            // Log::error("Error processing tender data for Tender code {$tenderData['Kode Tender']}: " . $e->getMessage());
                        }
                    }

                    $this->info("Data sync completed for LPSE code: {$kdLpse}");
                            //Log::info("Data sync completed for LPSE code: {$kdLpse}");
                    return; // Exit the retry loop if successful
                } else {
                    throw new \Exception("API returned non-successful status: " . $response->status());
                }
            } catch (\Illuminate\Http\Client\ConnectionException $e) {
                $this->warn("Attempt {$attempt} failed for LPSE code {$kdLpse}: " . $e->getMessage());
                //Log::warning("Attempt {$attempt} failed for LPSE code {$kdLpse}: " . $e->getMessage());

                if ($attempt < $maxRetries) {
                    $this->info("Retrying in {$retryDelay} seconds...");
                    sleep($retryDelay);
                } else {
                    $this->error("All attempts failed for LPSE code {$kdLpse}. Skipping...");
                            //Log::error("All attempts failed for LPSE code {$kdLpse}. Skipping...");
                    return; // Exit the function if all retries failed
                }
            } catch (\Exception $e) {
                $this->error("Unexpected error for LPSE code {$kdLpse}: " . $e->getMessage());
                        //Log::error("Unexpected error for LPSE code {$kdLpse}: " . $e->getMessage());
                return; // Exit the function if unexpected error occurs
            }
        }
    }

    private function processLpseData($tenderData)
    {
        $kodeTender     = $tenderData['Kode Tender'];
        $existingTender = TenderLpse::find($kodeTender);

        if ($existingTender) {
            $this->updateTenderData($existingTender, $tenderData);
        } else {
            $this->createNewTenderData($tenderData);
        }
    }

    private function updateTenderData($existingTender, $newData)
    {
        $changes    = [];
        $mappedData = $this->mapTenderData($newData);

        foreach ($mappedData as $key => $value) {
            if ($existingTender->$key != $value) {
                $changes[$key] = [
                    'old' => $existingTender->$key,
                    'new' => $value,
                ];
                $existingTender->$key = $value;
            }
        }

        if (! empty($changes)) {
            $existingTender->save();
            $this->logChanges($existingTender->kode_tender, 'update', $changes);
            $this->info("Updated tender: {$existingTender->kode_tender}");
            //Log::info("Updated tender: {$existingTender->kode_tender}");
        }

        $this->updateRelatedData($existingTender, $newData);
    }

    private function createNewTenderData($data)
    {
        $tender     = new TenderLpse();
        $mappedData = $this->mapTenderData($data);

        foreach ($mappedData as $key => $value) {
            $tender->$key = $value;
        }
        $tender->save();

        $this->logChanges($tender->kode_tender, 'create', $mappedData);
        $this->info("Created new tender: {$tender->kode_tender}");
        //Log::info("Created new tender: {$tender->kode_tender}");

        $this->updateRelatedData($tender, $data);
    }

    private function updateRelatedData($tender, $data)
    {
        $this->updateAnggaran($tender, $data['anggaran'] ?? []);
        $this->updateInstansiSatker($tender, $data['Instansi dan Satker'] ?? []);
        $this->updateJadwal($tender, $data['jadwal'] ?? []);
        $this->updateLokasiPaket($tender, $data['lokasi_paket'] ?? []);
        $this->updatePesertaTender($tender, $data['Daftar Peserta'] ?? []);
        $this->updatePemenangLpse($tender, $data ?? null);
    }

    private function updateAnggaran($tender, $anggaranData)
    {
        Anggaran::where('kode_tender', $tender->kode_tender)->delete();
        foreach ($anggaranData as $data) {
            $anggaran = new Anggaran($data);
            $tender->anggaran()->save($anggaran);
        }
    }

    private function updateInstansiSatker($tender, $satkerData)
    {
        InstansiSatker::where('kode_tender', $tender->kode_tender)->delete();
        foreach ($satkerData as $data) {
            $satker = new InstansiSatker([
                'rup_id'         => $data['rup_id'],
                'nama_satker'    => $data['stk_nama'],
                'nama_instansi'  => $data['nama_instansi'],
                'jenis_instansi' => $data['jenis_instansi'],
            ]);
            $tender->instansiSatker()->save($satker);
        }
    }

    private function updateJadwal($tender, $jadwalData)
    {
        // Hapus semua jadwal yang terkait dengan kode_tender
        Jadwal::where('kode_tender', $tender->kode_tender)->delete();

        if (is_array($jadwalData)) {
            foreach ($jadwalData as $data) {
                try {
                    $jadwal = new Jadwal([
                        'step'            => $data['no'],
                        'tahap'           => $data['tahap'],
                        'tanggal_mulai'   => Carbon::parse($data['mulai'])->format('Y-m-d H:i:s'),
                        'tanggal_selesai' => Carbon::parse($data['sampai'])->format('Y-m-d H:i:s'),
                        'keterangan'      => $data['perubahan'],
                    ]);

                    // Simpan jadwal terkait tender
                    $tender->jadwal()->save($jadwal);
                } catch (\Illuminate\Database\QueryException $e) {
                    // Periksa apakah error adalah pelanggaran constraint unik
                    if ($e->getCode() === '23000') {
                        $this->warn("Duplicate entry for step: {$data['no']}, tahap: {$data['tahap']} - skipping.");
                    } else {
                        // Jika error lain, lempar ulang exception
                        throw $e;
                    }
                } catch (\Exception $e) {
                    $this->error("Error processing jadwal for step: {$data['no']}, tahap: {$data['tahap']} - " . $e->getMessage());
                    //Log::error("Error processing jadwal for step: {$data['no']}, tahap: {$data['tahap']} - " . $e->getMessage());
                }
            }
        }
    }

    private function updateLokasiPaket($tender, $lokasiData)
    {
        LokasiPaket::where('kode_tender', $tender->kode_tender)->delete();
        foreach ($lokasiData as $data) {
            $lokasi = new LokasiPaket($data['lokasi']);
            $tender->lokasiPaket()->save($lokasi);
        }
    }

    private function updatePesertaTender($tender, $pesertaData)
    {
        if (! empty($pesertaData)) {
            // Ambil nama peserta yang sudah ada untuk tender ini
            $existingPeserta = PesertaTender::where('kode_tender', $tender->kode_tender)
                ->join('list_perusahaan_tender', 'peserta_tender.id_peserta', '=', 'list_perusahaan_tender.id')
                ->pluck('list_perusahaan_tender.normalized_name') // Ambil normalized_name untuk pencocokan lebih akurat
                ->toArray();

            $existingPeserta = array_map('strtolower', $existingPeserta); // Ubah semua nama menjadi huruf kecil

            if (count($existingPeserta) > 0) {

            } else {
                $output = new \Symfony\Component\Console\Output\BufferedOutput();
                Artisan::call('sync:peserta-tender', ['kode_tender' => $tender->kode_tender], $output);
                $this->info($output->fetch()); // Menampilkan output dari command Artisan

                // foreach ($pesertaData as $data) {
                //     // Gunakan metode findOrCreatePerusahaan untuk pencocokan berbasis NPWP & skor nama
                //     $perusahaan = ListPerusahaanTender::findOrCreatePerusahaan(
                //         $data['Nama Perusahaan'],
                //         $data['NPWP'] ?? null,
                //         $data['Alamat'] ?? null
                //     );

                //     if ($perusahaan && ! in_array(strtolower($perusahaan->normalized_name), $existingPeserta)) {
                //         $peserta = PesertaTender::create([
                //             'kode_tender' => $tender->kode_tender,
                //             'id_peserta'  => $perusahaan->id,
                //             'status'      => 'Peserta',
                //         ]);

                //         // Log peserta baru yang ditambahkan
                //         $this->info("Created new peserta: {$perusahaan->nama_peserta} - {$perusahaan->npwp}");
                //         $this->logChanges($peserta->id, 'create', $peserta->toArray(), 'peserta_tender');
                //     }
                // }
            }
        }
    }

    private function updatePemenangLpse($tender, $data)
    {
        if (isset($data['pemenang'])) {
            // Gunakan findOrCreatePerusahaan dari model
            $perusahaan = ListPerusahaanTender::findOrCreatePerusahaan(
                $data['pemenang']['Nama Perusahaan'],
                $data['pemenang']['NPWP'] ?? null,
                $data['pemenang']['Alamat'] ?? null
            );

            if ($perusahaan) {
                // Cek apakah data pemenang sudah ada
                $existingPemenang = PemenangLpse::where('kode_tender', $tender->kode_tender)
                    ->where('id_peserta', $perusahaan->id)
                    ->first();

                if ($existingPemenang) {
                    // Cek apakah ada perubahan
                    if (
                        $existingPemenang->harga_penawaran != $data['harga_penawaran'] ||
                        $existingPemenang->harga_terkoreksi != $data['harga_terkoreksi']
                    ) {
                        // Update jika ada perubahan
                        $existingPemenang->update([
                            'harga_penawaran'  => $data['harga_penawaran'],
                            'harga_terkoreksi' => $data['harga_terkoreksi'],
                        ]);
                        $this->info("Updated Pemenang: {$perusahaan->nama_peserta} - {$perusahaan->npwp}");
                        $this->logChanges($tender->kode_tender, 'update', $existingPemenang->toArray(), 'pemenang_lpse');
                    }
                } else {

                    $exist = PemenangLpse::where('kode_tender', $tender->kode_tender)->first();
                    if ($exist) {

                    } else {
                        // Buat data baru jika belum ada
                        $pemenang = PemenangLpse::create([
                            'kode_tender'      => $tender->kode_tender,
                            'id_peserta'       => $perusahaan->id,
                            'harga_penawaran'  => $data['harga_penawaran'],
                            'harga_terkoreksi' => $data['harga_terkoreksi'],
                        ]);
                        $this->info("Created new Pemenang: {$perusahaan->nama_peserta} - {$perusahaan->npwp}");
                        $this->logChanges($tender->kode_tender, 'create', $pemenang->toArray(), 'pemenang_lpse');
                    }
                }
            } else {
                $this->error("Perusahaan tidak ditemukan atau gagal dibuat.");
            }
        } else {
            $this->warn("Data Pemenang untuk $tender->kode_tender tidak ditemukan.");
        }
    }

    private function getOrCreatePerusahaan($perusahaanData)
    {
        $perusahaan = ListPerusahaanTender::where('nama_peserta', $perusahaanData['Nama Perusahaan'])->first();

        if ($perusahaan) {
            if ($perusahaan->alamat === null && $perusahaanData['Alamat'] != null) {
                $perusahaan->update([
                    'nama_peserta' => $perusahaanData['Nama Perusahaan'],
                    'alamat'       => $perusahaanData['Alamat'],
                ]);

                $this->info("Updated perusahaan: {$perusahaan->nama_peserta}");
                Log::info("Updated perusahaan: {$perusahaan->nama_peserta}");
                $this->logChanges($perusahaan->id, 'update', $perusahaan->toArray(), 'list_perusahaan_tender');
            }
        } else {
            $perusahaan = new ListPerusahaanTender([
                'npwp'         => $perusahaanData['NPWP'],
                'nama_peserta' => $perusahaanData['Nama Perusahaan'],
                'alamat'       => $perusahaanData['Alamat'] ?? null,
            ]);
            $perusahaan->save();

            $this->info("Created new perusahaan: {$perusahaan->nama_peserta}");
            //Log::info("Created new perusahaan: {$perusahaan->nama_peserta}");
            $this->logChanges($perusahaan->id, 'create', $perusahaan->toArray(), 'list_perusahaan_tender');
        }

        return $perusahaan->id;
    }

    private function logChanges($id, $action, $changes, $table = 'tender_umum_publik')
    {
        $syncLog = new SyncLog([
            'table'     => $table,
            'record_id' => $id,
            'action'    => $action,
            'changes'   => json_encode($changes),
        ]);
        $syncLog->save();
    }

    private function mapTenderData(array $tenderData): array
    {
        return [
            'kode_tender'              => (string) $tenderData['Kode Tender'],
            'kd_lpse'                  => $tenderData['Kode LPSE'] ?? null,
            'status_tender'            => $tenderData['Status_Tender'] ?? null,
            'nama_paket'               => $tenderData['Nama Paket'] ?? null,
            'pagu'                     => $tenderData['Pagu'],
            'hps'                      => $this->formatDecimal($tenderData['HPS']),
            'tanggal_paket_dibuat'     => $tenderData['tanggal paket dibuat'] ?? null,
            'tanggal_paket_tayang'     => $tenderData['tanggal paket tayang'] ?? null,
            'kategori_pekerjaan'       => $tenderData['Kategori Pekerjaan'] ?? null,
            'metode_pemilihan'         => $tenderData['Metode Pemilihan'] ?? null,
            'metode_pengadaan'         => $tenderData['Metode Pengadaan'] ?? null,
            'metode_evaluasi'          => $tenderData['Metode Evaluasi'] ?? null,
            'cara_pembayaran'          => $tenderData['Cara Pembayaran'] ?? null,
            'jenis_penetapan_pemenang' => $tenderData['Jenis Penetapan Pemenang'] ?? null,
            'apakah_paket_konsolidasi' => $tenderData['Apakah paket konsolidasi'] ?? null,
            'jumlah_pendaftar'         => $tenderData['Jumlah Pendaftar'] ?? null,
            'jumlah_penawar'           => $tenderData['Jumlah Penawar'] ?? null,
            'jumlah_kirim_kualifikasi' => $tenderData['jumlah_kirim_kualifikasi'] ?? null,
            'durasi_tender'            => $tenderData['Durasi Tender'] ?? null,
            'versi_paket_spse'         => $tenderData['Versi_spse_paket'] ?? null,
            'kualifikasi_usaha'        => $tenderData['Kualifikasi Usaha'] ?? null,
            'tahap_tender'             => $tenderData['tahap_tender'] ?? null,
            'syarat_kualifikasi'       => $tenderData['syarat_kualifikasi'] ?? null,

        ];
    }

    private function formatDecimal($value)
    {
        return is_numeric($value) ? number_format($value, 1, '.', '') : $value;
    }
}
