<?php

namespace App\Console\Commands;

use App\Models\Jadwal;
use App\Models\TenderLpse;
use App\Models\Lpse;
use App\Models\CommandProgress;
use App\Models\SystemError;
use App\Crawler\CustomClient;
use App\Traits\CommandManagementTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Symfony\Component\DomCrawler\Crawler;
use GuzzleHttp\Psr7\Response;

class CheckMismatchedJadwalNew extends Command
{
    use CommandManagementTrait;

    protected $signature = 'check:mismatched-jadwal-new
                            {--scrape : Automatically scrape mismatched tenders}
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}
                            {--lock-name= : Custom lock name for this command instance}
                            {--max-retries=5 : Maximum number of retries for failed requests}
                            {--connection-timeout=30 : Connection timeout in seconds}
                            {--request-timeout=60 : Total request timeout in seconds}
                            {--delay=2 : Delay between requests in seconds}';

    protected $description = 'Check tenders with mismatched jadwal tahap and tahap_tender with integrated scraping';

    protected $client;
    protected $cacheEnabled = true;
    protected $cacheExpiry = 3600; // 1 hour

    public function __construct()
    {
        parent::__construct();
        $this->client = new CustomClient();
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return 1;
        }

        try {
            $startTime = microtime(true); // Start measuring time
            $now = Carbon::now();

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                "Starting check for mismatched jadwal",
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Get jadwal entries where current date is between tanggal_mulai and tanggal_selesai
            $jadwals = Jadwal::where('tanggal_mulai', '<=', $now)
                ->where('tanggal_selesai', '>=', $now)
                ->get()
                ->shuffle()
                ->groupBy('kode_tender');

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                10,
                100,
                "Found " . $jadwals->count() . " active jadwal entries",
                [
                    'jadwal_count' => $jadwals->count(),
                    'stage' => 'jadwal_fetched',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $mismatchedTenders = [];

            $kodeTenderList = $jadwals->keys()->shuffle()->unique()->values()->all();

            $total = count($kodeTenderList);
            $this->info("Total unique kode_tender to process: {$total}");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                20,
                100,
                "Processing {$total} unique tenders",
                [
                    'total_tenders' => $total,
                    'stage' => 'processing_tenders',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $processedCount = 0;
            foreach ($kodeTenderList as $index => $kodeTender) {
                $processedCount++;
                $progress = min(90, 20 + round(($processedCount / $total) * 70));

                //$this->info("Processing iteration " . ($index + 1) . " of {$total} for kode_tender: {$kodeTender}");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    $progress,
                    100,
                    "Processing tender {$processedCount} of {$total}",
                    [
                        'kode_tender' => $kodeTender,
                        'current' => $processedCount,
                        'total' => $total,
                        'stage' => 'processing_tender',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $jadwalGroup = $jadwals->get($kodeTender);
                if (!$jadwalGroup) {
                    continue;
                }

                // Pick the jadwal with the smallest step number
                $jadwal = $jadwalGroup->sortBy('step')->first();

                $tender = TenderLpse::where('kode_tender', $kodeTender)
                    ->whereIn('status_tender', ['Aktif', 'Tender Sedang Berjalan'])
                    ->first();

                if (!$tender) {
                    Log::warning("Tender not found or not active for kode_tender: {$kodeTender}");
                    continue;
                }

                $jadwalTahap = trim($jadwal->tahap);
                $tenderTahap = trim($tender->tahap_tender ?? '');

                if ($jadwalTahap !== $tenderTahap) {
                    $mismatchedTenders[$kodeTender] = [
                        'jadwal_tahap' => $jadwalTahap,
                        'tender_tahap' => $tenderTahap,
                        'nama_paket' => $tender->nama_paket,
                        'tender' => $tender,
                    ];
                }
            }

            // Update progress
            $mismatchedCount = count($mismatchedTenders);
            CommandProgress::updateProgress(
                $this->getName(),
                95,
                100,
                "Processing completed, found {$mismatchedCount} mismatched tenders",
                [
                    'mismatched_count' => $mismatchedCount,
                    'stage' => 'processing_completed',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            if (empty($mismatchedTenders)) {
                $this->info("No mismatched tenders found.");

                // Mark command as completed
                $endTime = microtime(true);
                $executionTime = $endTime - $startTime;
                CommandProgress::markAsCompleted(
                    $this->getName(),
                    "No mismatched tenders found",
                    [
                        'execution_time' => round($executionTime, 2),
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage()
                    ]
                );

                return 0;
            }

            $mismatchedCount = count($mismatchedTenders);
            $this->info("Mismatched tenders found: {$mismatchedCount}");
            // foreach ($mismatchedTenders as $kodeTender => $info) {
            //     $this->line("Kode Tender: {$kodeTender} | Nama: {$info['nama_paket']} | Jadwal Tahap: {$info['jadwal_tahap']} | Tender Tahap: {$info['tender_tahap']}");
            // }

            if ($this->option('scrape')) {
                $mismatchedCount = count($mismatchedTenders);
                $this->info("Starting scraping for {$mismatchedCount} mismatched tenders...");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    98,
                    100,
                    "Starting scraping for {$mismatchedCount} mismatched tenders",
                    [
                        'mismatched_count' => $mismatchedCount,
                        'stage' => 'scraping_started',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $scrapedCount = 0;
                foreach ($mismatchedTenders as $kodeTender => $info) {
                    $scrapedCount++;
                    $this->info("Scraping tender {$scrapedCount} of {$mismatchedCount}: {$kodeTender}");

                    try {
                        // Scrape tender details directly instead of calling another command
                        $this->scrapeTenderDetails($info['tender']);
                        $this->info("Scraping tender details completed.");

                        try {
                            $this->info("Scraping jadwal tender for kodeTender: {$kodeTender}");
                            $this->scrapeJadwalTender($info['tender']);
                            $this->info("Scraping jadwal tender completed.");
                        } catch (\Exception $e) {
                            // Jika error pada jadwal, log tapi jangan gagalkan seluruh proses
                            $this->warn("Error scraping jadwal for tender {$kodeTender}: " . $e->getMessage());
                            Log::warning("Error scraping jadwal for tender {$kodeTender}: " . $e->getMessage());

                            // Log system error tapi dengan level warning
                            $this->logSystemError($e, "Error scraping jadwal for tender {$kodeTender}");
                        }
                    } catch (\Exception $e) {
                        $this->error("Error scraping tender {$kodeTender}: " . $e->getMessage());

                        // Log system error
                        $this->logSystemError($e, "Error scraping tender {$kodeTender}");
                    }

                    $delay = (int)$this->option('delay');
                    $this->info("Sleeping for {$delay} seconds to avoid rate limiting...");
                    sleep($delay);
                    $this->newLine();
                }

                $this->info("Scraping completed.");
            } else {
                $this->info("Run this command with --scrape option to automatically scrape mismatched tenders.");
            }

            // Mark command as completed
            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            CommandProgress::markAsCompleted(
                $this->getName(),
                "Command completed successfully",
                [
                    'mismatched_count' => $mismatchedCount,
                    'scraped' => $this->option('scrape'),
                    'execution_time' => round($executionTime, 2),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return 0;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());
            Log::error("CheckMismatchedJadwalNew error: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return 1;
        }
    }

    /**
     * Fetch URL with exponential backoff retry
     *
     * @param string $url
     * @param int $maxRetries
     * @return array
     * @throws \Exception
     */
    protected function fetchUrlWithRetry($url, $maxRetries = null)
    {
        $maxRetries = $maxRetries ?? (int)$this->option('max-retries');
        $connectionTimeout = (int)$this->option('connection-timeout');
        $requestTimeout = (int)$this->option('request-timeout');

        $attempt = 0;
        $lastException = null;

        // Check cache first if enabled
        $cacheKey = 'tender_url_' . md5($url);
        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            $cachedData = Cache::get($cacheKey);
            $this->info("Using cached result for URL: {$url}");

            // Buat response dari data cache
            $response = new Response(
                $cachedData['status_code'],
                ['Content-Type' => 'text/html'],
                $cachedData['html']
            );

            // Validasi dan buat crawler dari HTML yang di-cache
            return $this->validateAndGetContent($url, $response, $cachedData['html']);
        }

        while ($attempt < $maxRetries) {
            try {
                $attempt++;

                // Exponential backoff: 1.5^attempt * 1000ms with some jitter
                if ($attempt > 1) {
                    $backoff = (1.5 ** ($attempt - 1)) * 1000 + rand(100, 1000);
                    $backoffSeconds = round($backoff / 1000, 2);
                    $this->warn("Retry attempt {$attempt}/{$maxRetries} after {$backoffSeconds}s backoff...");
                    usleep($backoff * 1000); // Convert to microseconds
                }

                // Use different request options for each retry
                $options = [
                    'timeout' => $requestTimeout,
                    'connect_timeout' => $connectionTimeout
                ];

                if ($attempt > 1) {
                    // Add different user agent for retries
                    $userAgents = [
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
                        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36'
                    ];
                    $options['headers'] = [
                        'User-Agent' => $userAgents[array_rand($userAgents)],
                        'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language' => 'en-US,en;q=0.5',
                        'Cache-Control' => 'no-cache',
                        'Pragma' => 'no-cache'
                    ];
                }

                $response = $this->client->get($url, $options);
                $html = $response->getBody()->getContents();
                $statusCode = $response->getStatusCode();

                // Check if redirected to login page
                if (strpos($html, 'login') !== false && strpos($html, 'password') !== false) {
                    if ($attempt < $maxRetries) {
                        $this->warn("Redirected to login page. Will retry...");
                        $lastException = new \Exception("Redirected to login page");
                        continue;
                    } else {
                        throw new \Exception("Redirected to login page after {$maxRetries} attempts");
                    }
                }

                // Check for empty or invalid response
                if (empty($html) || !preg_match('/<[^>]*>/', $html)) {
                    if ($attempt < $maxRetries) {
                        $this->warn("Empty or invalid HTML response. Will retry...");
                        $lastException = new \Exception("Empty or invalid HTML response");
                        continue;
                    } else {
                        throw new \Exception("Empty or invalid HTML response after {$maxRetries} attempts");
                    }
                }

                $result = $this->validateAndGetContent($url, $response, $html);

                // Cache the result if enabled
                // Kita tidak bisa langsung cache objek Crawler karena berisi DOMDocument yang tidak bisa diserialisasi
                // Jadi kita hanya cache HTML-nya saja
                if ($this->cacheEnabled) {
                    $cacheData = [
                        'html' => $html,
                        'url' => $url,
                        'status_code' => $statusCode
                    ];
                    Cache::put($cacheKey, $cacheData, $this->cacheExpiry);
                }

                return $result;

            } catch (\GuzzleHttp\Exception\ConnectException $e) {
                $lastException = $e;
                $this->warn("Connection error on attempt {$attempt}/{$maxRetries}: " . $e->getMessage());

                // If this is the last attempt, rethrow
                if ($attempt >= $maxRetries) {
                    throw new \Exception("Failed to connect after {$maxRetries} attempts: " . $e->getMessage(), 0, $e);
                }
            } catch (\Exception $e) {
                $lastException = $e;
                $this->warn("Error on attempt {$attempt}/{$maxRetries}: " . $e->getMessage());

                // If this is the last attempt, rethrow
                if ($attempt >= $maxRetries) {
                    throw new \Exception("Failed after {$maxRetries} attempts: " . $e->getMessage(), 0, $e);
                }
            }
        }

        // If we get here, all retries failed
        throw $lastException ?: new \Exception("Failed after {$maxRetries} attempts");
    }

    /**
     * Validate and get HTML content from response
     *
     * @param string $url
     * @param \Psr\Http\Message\ResponseInterface $response
     * @return array Returns array with 'html' and 'crawler'
     * @throws \Exception
     */
    protected function validateAndGetContent($url, $response, $htmlContent = null)
    {
        $statusCode = $response->getStatusCode();

        if ($statusCode !== 200) {
            throw new \Exception("HTTP Error: Received status code {$statusCode} for URL: {$url}");
        }

        // Jika HTML sudah disediakan, gunakan itu
        // Jika tidak, ambil dari response
        $html = $htmlContent ?? $response->getBody()->getContents();

        // Validate if the response is actually HTML
        if (!preg_match('/<[^>]*>/', $html)) {
            throw new \Exception("Invalid HTML response received from URL: {$url}");
        }

        $crawler = new Crawler($html);

        // Check if we got a Cloudflare or other security challenge
        if ($crawler->filter('title')->count() > 0) {
            $title = $crawler->filter('title')->text();
            if (stripos($title, 'security check') !== false ||
                stripos($title, 'cloudflare') !== false ||
                stripos($title, 'attention required') !== false) {
                throw new \Exception("Security challenge detected: {$title}");
            }
        }

        // Check if we got redirected to a login page
        if (stripos($html, 'login') !== false &&
            (stripos($html, 'password') !== false || stripos($html, 'kata sandi') !== false)) {
            throw new \Exception("Redirected to login page");
        }

        return [
            'html' => $html,
            'crawler' => $crawler
        ];
    }

    /**
     * Scrape tender details
     *
     * @param TenderLpse $tender
     * @return void
     */
    protected function scrapeTenderDetails(TenderLpse $tender)
    {
        $lpse = Lpse::where('kd_lpse', $tender->kd_lpse)->first();
        if (!$lpse) {
            Log::error("LPSE not found for kd_lpse: {$tender->kd_lpse}");
            $this->error("LPSE not found for kd_lpse: {$tender->kd_lpse}");
            return;
        }

        $url = $lpse->url . '/lelang/' . $tender->kode_tender . '/pengumumanlelang';
        Log::info("Fetching URL: {$url}");

        try {
            // Fetch URL with retry mechanism
            $result = $this->fetchUrlWithRetry($url);
            $crawler = $result['crawler'];

            // Untuk Kode Tender
            $kodeTender = '';
            if ($crawler->filter('th:contains("Kode Tender")')->count() > 0) {
                $kodeTender = $crawler->filter('th:contains("Kode Tender")')->siblings()->text();
            }

            // Untuk Alasan
            $alasan = '';
            if ($crawler->filter('th:contains("Alasan")')->count() > 0) {
                $alasan = $crawler->filter('th:contains("Alasan")')->siblings()->text();
            }

            // Untuk Syarat Kualifikasi
            $syaratKualifikasi = '';
            if ($crawler->filter('th:contains("Syarat Kualifikasi")')->count() > 0) {
                $syaratKualifikasi = $crawler->filter('th:contains("Syarat Kualifikasi")')->siblings()->html();
            }

            // Untuk Kualifikasi Usaha
            $kualifikasiUsaha = '';
            if ($crawler->filter('th:contains("Kualifikasi Usaha")')->count() > 0) {
                $kualifikasiUsaha = $crawler->filter('th:contains("Kualifikasi Usaha")')->siblings()->text();
            }

            // Untuk Tahap Tender - coba beberapa kemungkinan label
            $tahapTender = '';
            $tahapLabels = [
                'th:contains("Tahap Tender Saat Ini")',
                'th:contains("Tahap Tender Saat ini")',
                'th:contains("Tahap Tender")',
                'th:contains("Tahap Lelang")'
            ];

            foreach ($tahapLabels as $label) {
                if ($crawler->filter($label)->count() > 0) {
                    $tahapTender = $crawler->filter($label)->siblings()->text();
                    $this->info("Found tahap tender with selector: {$label}");
                    break;
                }
            }

            // Rapikan tahap tender
            if (!empty($tahapTender)) {
                $tahapTender = rapikan_str_tahap_tender(trim($tahapTender));
            }

            // Untuk Keterangan
            $keterangan = '';
            if ($crawler->filter('th:contains("Keterangan")')->count() > 0) {
                $keterangan = $crawler->filter('th:contains("Keterangan")')->siblings()->text();
            }

            // Validasi data yang diperoleh
            if (!empty($kodeTender)) {
                $kodeTender = trim($kodeTender);

                if (!empty($tahapTender)) {
                    // Periksa apakah tender batal
                    $batalStages = ['Tender Batal', 'Seleksi Batal'];

                    if (in_array($tahapTender, $batalStages)) {
                        $tender->status_tender = 'Ditutup';
                        $tender->save();
                        $this->info("Status tender {$tender->kode_tender} updated to Ditutup.");
                        Log::info("Status tender {$tender->kode_tender} updated to Ditutup.");
                    }

                    // Update syarat kualifikasi jika kosong
                    if ($tender->syarat_kualifikasi == null && $syaratKualifikasi != '') {
                        $tender->syarat_kualifikasi = $syaratKualifikasi;
                        $tender->save();
                        $this->info("Syarat Kualifikasi tender {$tender->kode_tender} updated with new data.");
                        Log::info("Syarat Kualifikasi tender {$tender->kode_tender} updated with new data.");
                    }

                    // Update kualifikasi usaha jika kosong
                    if ($tender->kualifikasi_usaha == null && $kualifikasiUsaha != '') {
                        $tender->kualifikasi_usaha = $kualifikasiUsaha;
                        $tender->save();
                        $this->info("Kualifikasi Usaha tender {$tender->kode_tender} updated with new data: {$kualifikasiUsaha}.");
                        Log::info("Kualifikasi Usaha tender {$tender->kode_tender} updated with new data: {$kualifikasiUsaha}.");
                    }

                    // Update keterangan jika kosong dan alasan tersedia
                    if ($tender->keterangan == null && $alasan != '') {
                        $tender->keterangan = $alasan;
                        $tender->save();
                        $this->info("Alasan tender di-xxx kan {$tender->kode_tender} with new data.");
                        Log::info("Alasan tender di-xxx kan {$tender->kode_tender} with new data.");
                    }

                    // Update tahap tender jika berbeda
                    if ($tender->tahap_tender != $tahapTender) {
                        $this->info("Tahap Tender tender {$tender->kode_tender} diubah dari {$tender->tahap_tender} menjadi {$tahapTender}.");
                        Log::info("Tahap Tender tender {$tender->kode_tender} diubah dari {$tender->tahap_tender} menjadi {$tahapTender}.");
                        $tender->tahap_tender = $tahapTender;
                        $tender->save();
                    }
                }
            }

            $this->info("Scraping tender details completed for {$tender->kode_tender}.");
            Log::info("Scraping tender details completed for {$tender->kode_tender}.");

        } catch (\Exception $e) {
            $this->error("Error scraping tender details for {$tender->kode_tender}: " . $e->getMessage());
            Log::error("Error scraping tender details for {$tender->kode_tender}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Scrape jadwal tender
     *
     * @param TenderLpse $tender
     * @return void
     */
    protected function scrapeJadwalTender(TenderLpse $tender)
    {
        $lpse = Lpse::where('kd_lpse', $tender->kd_lpse)->first();
        if (!$lpse) {
            Log::error("LPSE not found for kd_lpse: {$tender->kd_lpse}");
            $this->error("LPSE not found for kd_lpse: {$tender->kd_lpse}");
            return;
        }

        $url = $lpse->url . '/lelang/' . $tender->kode_tender . '/jadwal';
        Log::info("Fetching jadwal URL: {$url}");

        try {
            // Fetch URL with retry mechanism
            $result = $this->fetchUrlWithRetry($url);
            $crawler = $result['crawler'];

            $jadwalTender = [];

            // Coba beberapa selector tabel yang mungkin digunakan
            $tableSelectors = [
                'table.table-sm',
                'table.table-bordered',
                'table.table',
                'table'
            ];

            $tableFound = false;
            foreach ($tableSelectors as $selector) {
                if ($crawler->filter($selector)->count() > 0) {
                    $this->info("Using table selector: {$selector}");
                    $tableFound = true;

                    // Coba ekstrak data jadwal
                    $crawler->filter($selector . ' tr')->each(function (Crawler $row, $i) use (&$jadwalTender) {
                        if ($i > 0) { // Skip header tabel
                            try {
                                $cells = $row->filter('td');

                                // Periksa jumlah kolom
                                if ($cells->count() >= 3) {
                                    // Coba dapatkan data dari berbagai format tabel
                                    $tahapIndex = ($cells->count() >= 4) ? 1 : 0;
                                    $mulaiIndex = ($cells->count() >= 4) ? 2 : 1;
                                    $sampaiIndex = ($cells->count() >= 4) ? 3 : 2;

                                    $jadwalTender[] = [
                                        'no' => $i,
                                        'tahap' => trim($cells->eq($tahapIndex)->text()),
                                        'mulai' => $this->parseDateTime(trim($cells->eq($mulaiIndex)->text())),
                                        'sampai' => $this->parseDateTime(trim($cells->eq($sampaiIndex)->text())),
                                        'perubahan' => ($cells->count() > $sampaiIndex + 1) ? trim($cells->eq($sampaiIndex + 1)->text()) : null
                                    ];
                                }
                            } catch (\Exception $e) {
                                // Log error tapi lanjutkan dengan baris berikutnya
                                $this->warn("Error parsing jadwal row {$i}: " . $e->getMessage());
                            }
                        }
                    });

                    break;
                }
            }

            if (!$tableFound) {
                // Cek apakah ada pesan error atau informasi lain di halaman
                $pageText = $crawler->filter('body')->text();

                if (strpos($pageText, 'tidak ditemukan') !== false ||
                    strpos($pageText, 'tidak tersedia') !== false ||
                    strpos($pageText, 'tidak ada') !== false) {
                    $this->warn("Jadwal tidak tersedia: {$pageText}");
                    return; // Keluar dari method tanpa error
                }

                // Jika tidak ada informasi yang berguna, log HTML untuk debugging
                $html = $result['html'];
                $shortHtml = substr($html, 0, 500) . '... [truncated]';
                Log::error("Jadwal table not found. HTML preview: " . $shortHtml);

                throw new \Exception("Jadwal table not found");
            }

            $this->processJadwal($tender, $jadwalTender);

        } catch (\Exception $e) {
            $this->error("Error scraping jadwal: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process jadwal data and save to database
     *
     * @param TenderLpse $tender
     * @param array $jadwalData
     * @return void
     */
    private function processJadwal($tender, $jadwalData)
    {
        // Hapus jadwal yang ada
        $deletedCount = Jadwal::where('kode_tender', $tender->kode_tender)->delete();
        $this->info("Deleted {$deletedCount} existing jadwal entries for tender {$tender->kode_tender}.");

        // Jika tidak ada data jadwal, log dan return
        if (empty($jadwalData)) {
            $this->warn("No jadwal data found for tender {$tender->kode_tender}.");
            return;
        }

        $this->info("Processing {$tender->kode_tender} jadwal with " . count($jadwalData) . " entries.");

        $successCount = 0;
        $errorCount = 0;

        foreach ($jadwalData as $data) {
            try {
                // Validasi data
                if (empty($data['mulai']) || empty($data['sampai'])) {
                    $this->warn("Invalid date data for jadwal step {$data['no']}: mulai={$data['mulai']}, sampai={$data['sampai']}");
                    $errorCount++;
                    continue;
                }

                Jadwal::create([
                    'kode_tender' => $tender->kode_tender,
                    'step' => $data['no'],
                    'tahap' => $data['tahap'],
                    'tanggal_mulai' => Carbon::parse($data['mulai']),
                    'tanggal_selesai' => Carbon::parse($data['sampai']),
                    'keterangan' => $data['perubahan'] ?? null
                ]);

                $successCount++;
            } catch (\Exception $e) {
                $errorCount++;
                $this->error("Jadwal processing error for step {$data['no']}: " . $e->getMessage());
                Log::error("Jadwal processing error for tender {$tender->kode_tender}, step {$data['no']}: " . $e->getMessage());
            }
        }

        $this->info("Jadwal processing completed: {$successCount} entries added, {$errorCount} errors.");
    }

    /**
     * Parse date time string to Carbon object
     *
     * @param string $dateString
     * @return string|null
     */
    private function parseDateTime($dateString)
    {
        try {
            // Try parsing with Indonesian month names
            $months = [
                'Januari' => 'January',
                'Februari' => 'February',
                'Maret' => 'March',
                'April' => 'April',
                'Mei' => 'May',
                'Juni' => 'June',
                'Juli' => 'July',
                'Agustus' => 'August',
                'September' => 'September',
                'Oktober' => 'October',
                'November' => 'November',
                'Desember' => 'December',
            ];

            foreach ($months as $id => $en) {
                $dateString = str_replace($id, $en, $dateString);
            }

            return \Carbon\Carbon::createFromFormat('d F Y H:i', $dateString)->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            Log::error("Date parse error for '{$dateString}': " . $e->getMessage());
            return null;
        }
    }

    /**
     * Log system error
     *
     * @param \Exception $exception
     * @param string $context
     * @return void
     */
    protected function logSystemError(\Exception $exception, string $context = '')
    {
        try {
            SystemError::create([
                'command' => $this->getName(),
                'context' => $context,
                'error_message' => $exception->getMessage(),
                'error_trace' => $exception->getTraceAsString(),
                'created_at' => now(),
                'updated_at' => now()
            ]);
        } catch (\Exception $e) {
            // Jika gagal menyimpan error, log ke file
            Log::error("Failed to log system error: " . $e->getMessage());
            Log::error("Original error: " . $exception->getMessage());
        }
    }
}
