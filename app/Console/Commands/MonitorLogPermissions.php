<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class MonitorLogPermissions extends Command
{
    protected $signature = 'logs:monitor-permissions 
                           {--fix : Automatically fix permission issues}
                           {--check-only : Only check permissions without fixing}';

    protected $description = 'Monitor and fix log file permissions in Docker environment';

    public function handle()
    {
        $fix = $this->option('fix');
        $checkOnly = $this->option('check-only');
        
        $this->info('🔍 Monitoring log file permissions...');
        $this->newLine();

        $logDirectories = [
            storage_path('logs'),
            storage_path('logs/query'),
            storage_path('logs/telegram'),
            storage_path('logs/whatsapp'),
            storage_path('logs/sirup'),
            storage_path('logs/performance'),
        ];

        $issues = [];
        $totalFiles = 0;
        $fixedFiles = 0;

        foreach ($logDirectories as $directory) {
            if (!File::isDirectory($directory)) {
                $issues[] = "Directory missing: {$directory}";
                continue;
            }

            $files = File::glob($directory . '/*.log');
            foreach ($files as $file) {
                $totalFiles++;
                $issue = $this->checkFilePermissions($file);
                
                if ($issue) {
                    $issues[] = $issue;
                    
                    if ($fix && !$checkOnly) {
                        if ($this->fixFilePermissions($file)) {
                            $fixedFiles++;
                            $this->info("✅ Fixed: " . basename($file));
                        } else {
                            $this->warn("⚠️ Could not fix: " . basename($file));
                        }
                    }
                }
            }
        }

        $this->displayResults($totalFiles, count($issues), $fixedFiles, $issues);
        
        if (!empty($issues) && !$fix && !$checkOnly) {
            $this->newLine();
            $this->info('💡 Run with --fix to automatically resolve issues');
        }

        return empty($issues) ? 0 : 1;
    }

    private function checkFilePermissions(string $file): ?string
    {
        if (!File::exists($file)) {
            return null;
        }

        $permissions = substr(sprintf('%o', fileperms($file)), -3);
        $owner = fileowner($file);
        $currentUser = getmyuid();
        
        // Check if file is writable
        if (!is_writable($file)) {
            return "Not writable: " . basename($file) . " (permissions: {$permissions})";
        }
        
        // Check if owned by different user (potential issue)
        if ($owner !== $currentUser) {
            $ownerInfo = posix_getpwuid($owner);
            $currentUserInfo = posix_getpwuid($currentUser);
            
            return "Different owner: " . basename($file) . 
                   " (owned by: " . ($ownerInfo['name'] ?? $owner) . 
                   ", current: " . ($currentUserInfo['name'] ?? $currentUser) . ")";
        }
        
        // Check if permissions are too restrictive
        if ($permissions < '644') {
            return "Restrictive permissions: " . basename($file) . " ({$permissions})";
        }

        return null;
    }

    private function fixFilePermissions(string $file): bool
    {
        try {
            // Try to change ownership first
            $currentUser = get_current_user() ?: 'www-data';
            @exec("chown {$currentUser}:{$currentUser} " . escapeshellarg($file) . " 2>/dev/null");
            
            // Set proper permissions
            chmod($file, 0666);
            
            return is_writable($file);
        } catch (\Exception $e) {
            return false;
        }
    }

    private function displayResults(int $totalFiles, int $issues, int $fixed, array $issueList): void
    {
        $this->newLine();
        $this->info('📊 PERMISSION MONITORING RESULTS:');
        $this->newLine();
        
        $this->line("📁 Total log files checked: {$totalFiles}");
        $this->line("⚠️ Issues found: {$issues}");
        
        if ($fixed > 0) {
            $this->line("✅ Issues fixed: {$fixed}");
        }
        
        if (!empty($issueList)) {
            $this->newLine();
            $this->warn('🚨 ISSUES DETECTED:');
            foreach ($issueList as $issue) {
                $this->line("  • {$issue}");
            }
        } else {
            $this->newLine();
            $this->info('✅ All log files have proper permissions!');
        }
        
        $this->newLine();
        $this->info('💡 RECOMMENDATIONS:');
        $this->line('  • Run this command regularly: php artisan logs:monitor-permissions --fix');
        $this->line('  • Add to cron: 0 */6 * * * php artisan logs:monitor-permissions --fix');
        $this->line('  • Use ./create-log-files.sh for new log files');
    }
}
