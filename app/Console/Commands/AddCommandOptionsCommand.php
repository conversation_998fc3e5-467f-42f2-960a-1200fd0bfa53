<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class AddCommandOptionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:command-options
                            {cmd_name? : Specific command to add options to}
                            {--all : Add options to all commands}
                            {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add standard options (timeout, force) to commands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $commandName = $this->argument('cmd_name');
        $all = $this->option('all');
        $dryRun = $this->option('dry-run');

        if (!$commandName && !$all) {
            $this->error('Please specify a command name or use --all option.');
            return Command::FAILURE;
        }

        $commandsPath = app_path('Console/Commands');
        $commands = [];

        if ($all) {
            $files = File::files($commandsPath);
            foreach ($files as $file) {
                if ($file->getExtension() === 'php') {
                    $commands[] = $file->getPathname();
                }
            }

            $this->info('Found ' . count($commands) . ' command files.');
        } else {
            $commandFile = $commandsPath . '/' . $commandName . '.php';
            if (!File::exists($commandFile)) {
                $commandFile = $commandsPath . '/' . Str::studly($commandName) . 'Command.php';
                if (!File::exists($commandFile)) {
                    $this->error("Command file for '{$commandName}' not found.");
                    return Command::FAILURE;
                }
            }

            $commands[] = $commandFile;
            $this->info("Found command file: {$commandFile}");
        }

        $modifiedCount = 0;
        $skippedCount = 0;

        foreach ($commands as $commandFile) {
            $content = File::get($commandFile);
            $className = basename($commandFile, '.php');

            // Skip this command and other utility commands
            if (in_array($className, ['AddCommandOptionsCommand', 'ImplementCommandManagementCommand', 'MonitorCommandsCommand'])) {
                $this->info("Skipping utility command: {$className}");
                $skippedCount++;
                continue;
            }

            // Check if signature property exists
            if (!preg_match('/protected\s+\$signature\s*=\s*[\'"]([^\'"]+)[\'"]/s', $content, $matches)) {
                $this->warn("Could not find signature property in {$className}. Skipping.");
                $skippedCount++;
                continue;
            }

            $signature = $matches[1];

            // Check if options already exist
            $hasTimeoutOption = Str::contains($signature, '--timeout');
            $hasForceOption = Str::contains($signature, '--force');

            if ($hasTimeoutOption && $hasForceOption) {
                $this->info("Command {$className} already has both options. Skipping.");
                $skippedCount++;
                continue;
            }

            $this->info("Adding options to {$className}...");

            $newSignature = $signature;

            // Add timeout option if not exists
            if (!$hasTimeoutOption) {
                if (Str::endsWith($newSignature, '}\'') || Str::endsWith($newSignature, '}"')) {
                    // Signature ends with a parameter, add option before closing brace
                    $newSignature = preg_replace('/([\'"])\s*$/', ' {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}$1', $newSignature);
                } else {
                    // Signature doesn't end with a parameter, add option at the end
                    $newSignature = preg_replace('/([\'"])\s*$/', ' {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}$1', $newSignature);
                }
            }

            // Add force option if not exists
            if (!$hasForceOption) {
                if (Str::endsWith($newSignature, '}\'') || Str::endsWith($newSignature, '}"')) {
                    // Signature ends with a parameter, add option before closing brace
                    $newSignature = preg_replace('/([\'"])\s*$/', ' {--force : Force run even if command is already running}$1', $newSignature);
                } else {
                    // Signature doesn't end with a parameter, add option at the end
                    $newSignature = preg_replace('/([\'"])\s*$/', ' {--force : Force run even if command is already running}$1', $newSignature);
                }
            }

            // Replace signature in content
            $newContent = preg_replace(
                '/protected\s+\$signature\s*=\s*[\'"]' . preg_quote($signature, '/') . '[\'"];/s',
                'protected $signature = \'' . $newSignature . '\';',
                $content
            );

            if ($dryRun) {
                $this->info("Would modify {$className} (dry run)");
                $this->line("--- Original Signature ---");
                $this->line($signature);
                $this->line("--- New Signature ---");
                $this->line($newSignature);
            } else {
                File::put($commandFile, $newContent);
                $this->info("Options added to {$className}.");
            }

            $modifiedCount++;
        }

        $this->info("Options added to {$modifiedCount} commands, {$skippedCount} commands skipped.");

        if (!$dryRun && $modifiedCount > 0) {
            $this->warn("Please review the changes and make any necessary adjustments.");
        }

        return Command::SUCCESS;
    }
}
