<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\SystemError;

class MonitorCommandsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'commands:monitor
                            {--cleanup : Clean up stuck commands}
                            {--timeout=3600 : Timeout in seconds for stuck commands}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor and clean up stuck commands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Monitoring commands...');

        // Get all running commands from database
        $runningCommands = DB::table('commands_status')
            ->where('is_running', true)
            ->get();

        $this->info('Found ' . count($runningCommands) . ' running commands in database.');

        $stuckCommands = [];
        $timeout = $this->option('timeout');

        foreach ($runningCommands as $command) {
            $commandName = $command->command_name;
            $startedAt = Carbon::parse($command->started_at);
            $runningTime = $startedAt->diffInSeconds(now());

            // Check if command is stuck (running for more than timeout)
            if ($runningTime > $timeout) {
                $stuckCommands[] = $command;
                $this->warn("Command '{$commandName}' has been running for {$runningTime} seconds (started at {$startedAt}).");
            } else {
                $this->info("Command '{$commandName}' has been running for {$runningTime} seconds (started at {$startedAt}).");
            }

            // Check if process is still running
            $lockFile = storage_path("app/locks/{$commandName}.lock");
            if (file_exists($lockFile)) {
                $lockData = json_decode(file_get_contents($lockFile), true);

                if (isset($lockData['pid'])) {
                    $pid = $lockData['pid'];

                    if (!file_exists("/proc/{$pid}")) {
                        $this->warn("Command '{$commandName}' has a lock file but process {$pid} is not running.");

                        if ($this->option('cleanup')) {
                            $this->cleanupCommand($command, $lockFile);
                        }
                    } else {
                        $this->info("Command '{$commandName}' is running with PID {$pid}.");
                    }
                } else {
                    $this->warn("Command '{$commandName}' has a lock file but no PID.");

                    if ($this->option('cleanup')) {
                        $this->cleanupCommand($command, $lockFile);
                    }
                }
            } else {
                $this->warn("Command '{$commandName}' is marked as running but has no lock file.");

                if ($this->option('cleanup')) {
                    $this->cleanupCommand($command);
                }
            }
        }

        // Check for orphaned lock files
        $lockFiles = glob(storage_path('app/locks/*.lock'));
        foreach ($lockFiles as $lockFile) {
            $commandName = basename($lockFile, '.lock');

            // Check if command is in database
            $commandExists = DB::table('commands_status')
                ->where('command_name', $commandName)
                ->where('is_running', true)
                ->exists();

            if (!$commandExists) {
                $this->warn("Found orphaned lock file for command '{$commandName}'.");

                if ($this->option('cleanup')) {
                    $this->info("Removing orphaned lock file for command '{$commandName}'.");
                    unlink($lockFile);
                }
            }
        }

        // Summary
        if (count($stuckCommands) > 0) {
            $this->warn('Found ' . count($stuckCommands) . ' stuck commands.');

            if ($this->option('cleanup')) {
                $this->info('Cleaned up ' . count($stuckCommands) . ' stuck commands.');
            } else {
                $this->info('Run with --cleanup option to clean up stuck commands.');
            }
        } else {
            $this->info('No stuck commands found.');
        }

        return Command::SUCCESS;
    }

    /**
     * Clean up a stuck command
     *
     * @param object $command Command record from database
     * @param string|null $lockFile Path to lock file
     * @return void
     */
    protected function cleanupCommand(object $command, ?string $lockFile = null): void
    {
        $commandName = $command->command_name;

        // Update database
        DB::table('commands_status')
            ->where('command_name', $commandName)
            ->update(['is_running' => false, 'finished_at' => now()]);

        $this->info("Marked command '{$commandName}' as not running in database.");

        // Remove lock file
        if ($lockFile && file_exists($lockFile)) {
            unlink($lockFile);
            $this->info("Removed lock file for command '{$commandName}'.");
        }

        // Update command progress
        try {
            DB::table('command_progress')
                ->where('command', $commandName)
                ->where('status', 'running')
                ->update([
                    'status' => 'failed',
                    'message' => 'Command was forcibly terminated by monitor',
                    'data' => json_encode([
                        'terminated_at' => now()->toDateTimeString(),
                        'terminated_by' => 'commands:monitor'
                    ]),
                    'updated_at' => now()
                ]);

            $this->info("Updated command progress for '{$commandName}'.");

            // Log system error
            $this->logSystemError(
                new \Exception("Command '{$commandName}' was forcibly terminated by monitor."),
                "Command forcibly terminated",
                $commandName
            );
        } catch (\Exception $e) {
            $this->error("Failed to update command progress: " . $e->getMessage());

            // Log system error for the failure
            $this->logSystemError(
                $e,
                "Failed to update command progress",
                $commandName
            );
        }

        // Try to kill process if PID is known
        if ($lockFile && file_exists($lockFile)) {
            $lockData = json_decode(file_get_contents($lockFile), true);

            if (isset($lockData['pid'])) {
                $pid = $lockData['pid'];

                if (file_exists("/proc/{$pid}")) {
                    $this->warn("Attempting to kill process {$pid} for command '{$commandName}'.");

                    try {
                        // Use shell_exec to kill process
                        shell_exec("kill -15 {$pid}"); // SIGTERM
                        $this->info("Sent SIGTERM to process {$pid}.");

                        // Wait a bit and check if process is still running
                        sleep(5);

                        if (file_exists("/proc/{$pid}")) {
                            $this->warn("Process {$pid} is still running, sending SIGKILL.");
                            shell_exec("kill -9 {$pid}"); // SIGKILL
                            $this->info("Sent SIGKILL to process {$pid}.");
                        } else {
                            $this->info("Process {$pid} has been terminated.");
                        }
                    } catch (\Exception $e) {
                        $this->error("Failed to kill process: " . $e->getMessage());

                        // Log system error
                        $this->logSystemError(
                            $e,
                            "Failed to kill process",
                            $commandName
                        );
                    }
                }
            }
        }
    }

    /**
     * Log system error
     *
     * @param \Exception $exception Exception to log
     * @param string $context Error context
     * @param string $commandName Command name
     * @return void
     */
    protected function logSystemError(\Exception $exception, string $context = '', string $commandName = ''): void
    {
        try {
            $errorData = [
                'type' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTrace(),
                'request_method' => 'CLI',
                'request_url' => "artisan commands:monitor",
                'user_id' => null,
                'user_email' => null,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Artisan CLI',
                'request_input' => [
                    'command' => $commandName,
                    'context' => $context,
                    'options' => $this->options()
                ],
                'resolved' => false
            ];

            SystemError::create($errorData);
            $this->error("Error logged to system_errors table: " . $exception->getMessage());
        } catch (\Exception $e) {
            $this->error("Failed to log error to system_errors: " . $e->getMessage());
            Log::error("Failed to log error to system_errors: " . $e->getMessage());
        }
    }
}
