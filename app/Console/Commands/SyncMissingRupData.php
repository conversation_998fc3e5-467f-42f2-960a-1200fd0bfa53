<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\DomCrawler\Crawler;
use Carbon\Carbon;

use App\Models\Anggaran;
use App\Models\PaketPengadaan;
use App\Models\DataSirup;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use App\Crawler\CustomClientSquid;

class SyncMissingRupData extends Command
{
    use CommandManagementTrait;

    protected $signature = 'sirup:sync-missing-rup
        {--chunk=100 : Jumlah record per batch}
        {--max-retry=3 : Jumlah maksimal percobaan ulang}
        {--lock-name= : Custom lock name for this command instance}
        {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
        {--force : Force run even if command is already running}';

    protected $description = 'Sinkronisasi data RUP dari Anggaran dan PaketPengadaan ke DataSirup';
    protected $bulanMapping = [
        'Januari' => 'January',
        'Februari' => 'February',
        'Maret' => 'March',
        'April' => 'April',
        'Mei' => 'May',
        'Juni' => 'June',
        'Juli' => 'July',
        'Agustus' => 'August',
        'September' => 'September',
        'Oktober' => 'October',
        'November' => 'November',
        'Desember' => 'December',
    ];

    private $client;

    public function __construct()
    {
        parent::__construct();
        $this->client = new CustomClientSquid();
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return Command::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            $maxRetries = $this->option('max-retry');
            $chunkSize = $this->option('chunk');

            Log::info("Memulai proses sinkronisasi RUP...");
            $this->info("Memulai proses sinkronisasi RUP...");

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                "Memulai proses sinkronisasi RUP...",
                [
                    'max_retries' => $maxRetries,
                    'chunk_size' => $chunkSize,
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Hitung waktu pengambilan data RUP ID
            $startGetRup = microtime(true);

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                5,
                100,
                "Mengambil data RUP ID dari database...",
                [
                    'stage' => 'fetching_rup_ids',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $anggaranRupIds = Anggaran::whereNotIn('rup_id', function ($query) {
                $query->select('kode_rup')->from('data_sirup');
            })->pluck('rup_id');

            $paketPengadaanRupIds = PaketPengadaan::whereNotIn('rup_id', function ($query) {
                $query->select('kode_rup')->from('data_sirup');
            })->pluck('rup_id');
            $endGetRup = microtime(true);
            $elapsedGetRup = $endGetRup - $startGetRup;
            $this->info("Waktu pengambilan RUP ID dari database: {$elapsedGetRup} detik");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                10,
                100,
                "Data RUP ID berhasil diambil dari database",
                [
                    'stage' => 'rup_ids_fetched',
                    'anggaran_count' => $anggaranRupIds->count(),
                    'paket_pengadaan_count' => $paketPengadaanRupIds->count(),
                    'elapsed_time' => $elapsedGetRup,
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $uniqueRupIds = $anggaranRupIds->merge($paketPengadaanRupIds)->unique();
            $uniqueRupIds = $uniqueRupIds->shuffle();

            $totalRupIds = $uniqueRupIds->count();
            $this->info("Total RUP ID yang akan disinkronkan: $totalRupIds");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                15,
                100,
                "Memulai sinkronisasi $totalRupIds RUP ID",
                [
                    'stage' => 'start_processing',
                    'total_rup_ids' => $totalRupIds,
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Progress bar
            $progressBar = $this->output->createProgressBar($totalRupIds);
            $progressBar->start();

            // Scrape dan simpan data
            $processedCount = 0;
            $successCount = 0;
            $failedCount = 0;
            $skippedCount = 0;

            foreach ($uniqueRupIds as $index => $rupId) {
                try {
                    // Cek apakah RUP ID sudah ada di database
                    $existingRup = DB::table('data_sirup')->where('kode_rup', $rupId)->exists();

                    if ($existingRup) {
                        $this->info("RUP ID $rupId sudah ada, dilewatkan.");
                        $progressBar->advance();
                        $skippedCount++;

                        // Update progress every 10 items or at the end
                        if ($processedCount % 10 === 0 || $processedCount === $totalRupIds - 1) {
                            $progress = min(99, round((($processedCount + 1) / $totalRupIds) * 100));
                            CommandProgress::updateProgress(
                                $this->getName(),
                                15 + $progress * 0.8, // Scale to 15-95%
                                100,
                                "Memproses RUP ID ($processedCount/$totalRupIds)",
                                [
                                    'stage' => 'processing',
                                    'processed' => $processedCount,
                                    'success' => $successCount,
                                    'failed' => $failedCount,
                                    'skipped' => $skippedCount,
                                    'total' => $totalRupIds,
                                    'current_rup_id' => $rupId,
                                    'percentage' => $progress,
                                    'memory_usage' => $this->getMemoryUsage(),
                                    'elapsed_time' => $this->getElapsedTime($startTime)
                                ]
                            );
                        }

                        continue; // Skip ke iterasi berikutnya
                    }

                    // Update progress for current RUP ID
                    CommandProgress::updateProgress(
                        $this->getName(),
                        15 + round(($processedCount / $totalRupIds) * 80), // Scale to 15-95%
                        100,
                        "Memproses RUP ID: $rupId",
                        [
                            'stage' => 'processing_rup',
                            'current_rup_id' => $rupId,
                            'index' => $index,
                            'processed' => $processedCount,
                            'total' => $totalRupIds,
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    $this->info("Memproses RUP ID: $rupId");
                    $startProcessRup = microtime(true);
                    $result = $this->scrapeRupDetails($rupId, $maxRetries);
                    $endProcessRup = microtime(true);
                    $elapsedProcessRup = $endProcessRup - $startProcessRup;
                    $this->info("Waktu total proses RUP ID {$rupId}: {$elapsedProcessRup} detik");

                    if ($result) {
                        $successCount++;
                    } else {
                        $failedCount++;
                    }

                    sleep(1);
                } catch (\Exception $e) {
                    $this->error("Gagal memproses RUP ID $rupId: " . $e->getMessage());
                    $failedCount++;

                    // Log system error
                    $this->logSystemError($e, "handle method - processing RUP ID: $rupId");
                }

                $progressBar->advance();
                $processedCount++;

                // Update progress every 10 items or at the end
                if ($processedCount % 10 === 0 || $processedCount === $totalRupIds) {
                    $progress = min(99, round(($processedCount / $totalRupIds) * 100));
                    CommandProgress::updateProgress(
                        $this->getName(),
                        15 + $progress * 0.8, // Scale to 15-95%
                        100,
                        "Memproses RUP ID ($processedCount/$totalRupIds)",
                        [
                            'stage' => 'processing',
                            'processed' => $processedCount,
                            'success' => $successCount,
                            'failed' => $failedCount,
                            'skipped' => $skippedCount,
                            'total' => $totalRupIds,
                            'current_rup_id' => $rupId,
                            'percentage' => $progress,
                            'memory_usage' => $this->getMemoryUsage(),
                            'elapsed_time' => $this->getElapsedTime($startTime)
                        ]
                    );
                }
            }

            $progressBar->finish();
            $this->info("\nProses sinkronisasi RUP selesai.");
            Log::info("Proses sinkronisasi RUP selesai.");

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            $message = "Proses sinkronisasi RUP selesai dalam " . round($executionTime, 2) . " detik.";

            // Mark command as completed
            CommandProgress::markAsCompleted(
                $this->getName(),
                $message,
                [
                    'total_rup_ids' => $totalRupIds,
                    'processed' => $processedCount,
                    'success' => $successCount,
                    'failed' => $failedCount,
                    'skipped' => $skippedCount,
                    'execution_time' => round($executionTime, 2),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return Command::FAILURE;
        }
    }

    /**
     * Get elapsed time in seconds
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTime(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    protected function scrapeRupDetails($rupId, $maxRetries)
    {
       // Update progress
       CommandProgress::updateProgress(
           $this->getName(),
           0,
           100,
           "Memulai scraping RUP ID: $rupId",
           [
               'stage' => 'scraping_start',
               'rup_id' => $rupId,
               'max_retries' => $maxRetries,
               'memory_usage' => $this->getMemoryUsage()
           ]
       );

       $this->info("Menggunakan CustomClientSquid untuk mengakses data RUP");

       $retries = 0;
       while ($retries < $maxRetries) {
           try {
               // Update progress
               CommandProgress::updateProgress(
                   $this->getName(),
                   0,
                   100,
                   "Mencoba scraping RUP ID: $rupId (percobaan " . ($retries + 1) . " dari $maxRetries)",
                   [
                       'stage' => 'scraping_attempt',
                       'rup_id' => $rupId,
                       'attempt' => $retries + 1,
                       'max_retries' => $maxRetries,
                       'memory_usage' => $this->getMemoryUsage()
                   ]
               );

               // Menghitung waktu proses scraping web
               $startScrape = microtime(true);

               //$url = "https://hostestapp.xyz/?url=https://sirup.lkpp.go.id/sirup/rup/detailPaketPenyedia2020?idPaket={$rupId}";
               $url = "https://sirup.lkpp.go.id/sirup/rup/detailPaketPenyedia2020?idPaket={$rupId}";


               // Proses mengakses web
               $startWebAccess = microtime(true);
               $response = $this->client->get($url);
               $endWebAccess = microtime(true);
               $elapsedWebAccess = $endWebAccess - $startWebAccess;
               $this->info("Waktu akses web untuk RUP ID {$rupId}: {$elapsedWebAccess} detik");

               // Update progress
               CommandProgress::updateProgress(
                   $this->getName(),
                   30,
                   100,
                   "Berhasil mengakses web untuk RUP ID: $rupId",
                   [
                       'stage' => 'web_access_success',
                       'rup_id' => $rupId,
                       'elapsed_web_access' => $elapsedWebAccess,
                       'memory_usage' => $this->getMemoryUsage()
                   ]
               );

               $html = (string) $response->getBody();

               // Check if the response status is 429 Too Many Requests
               if ($response->getStatusCode() == 429) {
                   $errorMessage = "Terjadi HTTP 429 Too Many Requests. Proses scraping dihentikan untuk menghindari pemblokiran.";
                   $this->error($errorMessage);
                   Log::error("HTTP 429 Too Many Requests diterima. Proses scraping dihentikan.");

                   // Log system error
                   $this->logSystemError(
                       new \Exception($errorMessage),
                       "HTTP 429 Too Many Requests"
                   );

                   // Update progress
                   CommandProgress::updateProgress(
                       $this->getName(),
                       100,
                       100,
                       $errorMessage,
                       [
                           'rup_id' => $rupId,
                           'status_code' => 429,
                           'stage' => 'too_many_requests',
                           'memory_usage' => $this->getMemoryUsage()
                       ]
                   );

                   // Don't exit, throw exception instead to be caught by the retry loop
                   throw new \Exception($errorMessage);
               }

               // Check if the HTML contains the login page title
               if (strpos($html, '<title>RUP - Form Login</title>') !== false) {
                   $warningMessage = "Halaman login terdeteksi untuk RUP ID {$rupId}, melewati proses.";
                   $this->warn($warningMessage);
                   Log::warning($warningMessage);

                   // Update progress
                   CommandProgress::updateProgress(
                       $this->getName(),
                       100,
                       100,
                       $warningMessage,
                       [
                           'rup_id' => $rupId,
                           'stage' => 'login_page_detected',
                           'memory_usage' => $this->getMemoryUsage()
                       ]
                   );

                   return false;
               }

               // Check if the HTML contains error messages
               if (strpos($html, 'Mohon maaf, terjadi kesalahan pada sistem') !== false ||
                   strpos($html, 'Error 404') !== false ||
                   strpos($html, 'Page Not Found') !== false ||
                   strpos($html, 'Internal Server Error') !== false) {

                   $warningMessage = "Halaman error terdeteksi untuk RUP ID {$rupId}, melewati proses.";
                   $this->warn($warningMessage);
                   Log::warning($warningMessage);

                   // Update progress
                   CommandProgress::updateProgress(
                       $this->getName(),
                       100,
                       100,
                       $warningMessage,
                       [
                           'rup_id' => $rupId,
                           'stage' => 'error_page_detected',
                           'memory_usage' => $this->getMemoryUsage()
                       ]
                   );

                   return false;
               }

               // Check if the HTML contains Cloudflare challenge
               if (strpos($html, 'Cloudflare') !== false &&
                   (strpos($html, 'security check') !== false ||
                    strpos($html, 'challenge') !== false ||
                    strpos($html, 'attention required') !== false)) {

                   $warningMessage = "Cloudflare challenge terdeteksi untuk RUP ID {$rupId}, melewati proses.";
                   $this->warn($warningMessage);
                   Log::warning($warningMessage);

                   // Update progress
                   CommandProgress::updateProgress(
                       $this->getName(),
                       100,
                       100,
                       $warningMessage,
                       [
                           'rup_id' => $rupId,
                           'stage' => 'cloudflare_challenge_detected',
                           'memory_usage' => $this->getMemoryUsage()
                       ]
                   );

                   return false;
               }

               // Proses crawler/parsing HTML
               $startCrawler = microtime(true);
               $crawler = new Crawler($html);
               $dataScrape = $this->parseRupDetails($crawler);
               $endCrawler = microtime(true);
               $elapsedCrawler = $endCrawler - $startCrawler;
               $this->info("Waktu parsing/crawler untuk RUP ID {$rupId}: {$elapsedCrawler} detik");

               // Update progress
               CommandProgress::updateProgress(
                   $this->getName(),
                   60,
                   100,
                   "Berhasil parsing data untuk RUP ID: $rupId",
                   [
                       'stage' => 'parsing_success',
                       'rup_id' => $rupId,
                       'elapsed_parsing' => $elapsedCrawler,
                       'memory_usage' => $this->getMemoryUsage()
                   ]
               );

               // Jika data berhasil di-scrape, simpan ke database
               if ($dataScrape) {
                   $this->info("Successfully scraped RUP ID: {$rupId}");

                   // Proses penyimpanan data ke database
                   $startDBSave = microtime(true);
                   $simpan = $this->saveRupData($rupId, $dataScrape);
                   $endDBSave = microtime(true);
                   $elapsedDBSave = $endDBSave - $startDBSave;
                   $this->info("Waktu penyimpanan database untuk RUP ID {$rupId}: {$elapsedDBSave} detik");

                   // Update progress
                   CommandProgress::updateProgress(
                       $this->getName(),
                       90,
                       100,
                       $simpan ? "Berhasil menyimpan data RUP ID: $rupId" : "Gagal menyimpan data RUP ID: $rupId",
                       [
                           'stage' => $simpan ? 'save_success' : 'save_failed',
                           'rup_id' => $rupId,
                           'elapsed_db_save' => $elapsedDBSave,
                           'memory_usage' => $this->getMemoryUsage()
                       ]
                   );

                   if ($simpan) {
                       $this->info("RUP ID: {$rupId} data berhasil disimpan.");
                   } else {
                       $this->error("Gagal menyimpan data RUP ID: {$rupId}");
                   }

               } else {
                   $this->error("Data RUP ID: {$rupId} tidak ditemukan.");

                   // Update progress
                   CommandProgress::updateProgress(
                       $this->getName(),
                       90,
                       100,
                       "Data RUP ID: $rupId tidak ditemukan",
                       [
                           'stage' => 'data_not_found',
                           'rup_id' => $rupId,
                           'memory_usage' => $this->getMemoryUsage()
                       ]
                   );
               }

               $endScrape = microtime(true);
               $elapsedScrape = $endScrape - $startScrape;
               $this->info("Waktu total scraping untuk RUP ID {$rupId}: {$elapsedScrape} detik");

               // Update progress
               CommandProgress::updateProgress(
                   $this->getName(),
                   100,
                   100,
                   "Selesai memproses RUP ID: $rupId",
                   [
                       'stage' => 'scraping_complete',
                       'rup_id' => $rupId,
                       'elapsed_total' => $elapsedScrape,
                       'memory_usage' => $this->getMemoryUsage()
                   ]
               );

               return true;
           } catch (\Exception $e) {
               $retries++;
               $this->error("Gagal memproses RUP ID: {$rupId}. Error: " . $e->getMessage());
               Log::error("Gagal scrape RUP ID: {$rupId} setelah {$retries} kali percobaan.");

               // Log system error
               $this->logSystemError($e, "scrapeRupDetails attempt $retries for RUP ID: $rupId");

               // Update progress
               CommandProgress::updateProgress(
                   $this->getName(),
                   0,
                   100,
                   "Gagal memproses RUP ID: $rupId (percobaan $retries dari $maxRetries)",
                   [
                       'stage' => 'scraping_failed',
                       'rup_id' => $rupId,
                       'attempt' => $retries,
                       'max_retries' => $maxRetries,
                       'error' => $e->getMessage(),
                       'memory_usage' => $this->getMemoryUsage()
                   ]
               );

               sleep(5);
           }
       }

       $this->error("Gagal memproses RUP ID: {$rupId} setelah {$maxRetries} kali percobaan.");

       // Update progress
       CommandProgress::updateProgress(
           $this->getName(),
           100,
           100,
           "Gagal memproses RUP ID: $rupId setelah $maxRetries percobaan",
           [
               'stage' => 'scraping_max_retries',
               'rup_id' => $rupId,
               'max_retries' => $maxRetries,
               'memory_usage' => $this->getMemoryUsage()
           ]
       );

       return false;
   }

   protected function saveRupData($rupId, $scrapedData)
   {
       DB::beginTransaction();
       try {
           // Update progress
           CommandProgress::updateProgress(
               $this->getName(),
               0,
               100,
               "Menyimpan data RUP ID: $rupId ke database",
               [
                   'stage' => 'saving_to_db',
                   'rup_id' => $rupId,
                   'memory_usage' => $this->getMemoryUsage()
               ]
           );

           $dataSirup = new DataSirup();
           $dataSirup->kode_rup = $rupId;

           $dataSirup->nama_paket = $scrapedData['nama_paket'];
           $dataSirup->nama_klpd = $scrapedData['nama_klpd'];
           $dataSirup->satuan_kerja = $scrapedData['satuan_kerja'];
           $dataSirup->tahun_anggaran = $scrapedData['tahun_anggaran'];

           $dataSirup->lokasi_pekerjaan = $scrapedData['lokasi_pekerjaan'];

           $dataSirup->volume_pekerjaan = $scrapedData['volume_pekerjaan'] ?? null;
           $dataSirup->uraian_pekerjaan = $scrapedData['uraian_pekerjaan'] ?? null;
           $dataSirup->spesifikasi_pekerjaan = $scrapedData['spesifikasi_pekerjaan'] ?? null;

           $dataSirup->produk_dalam_negeri = $scrapedData['produk_dalam_negeri'] ?? false;
           $dataSirup->umkm = $scrapedData['umkm'] ?? false;
           $dataSirup->pra_dipa_dpa = $scrapedData['pra_dipa_dpa'] ?? false;

           $dataSirup->sumber_dana = $scrapedData['sumber_dana'];
           $dataSirup->total_pagu = $scrapedData['total_pagu'];
           $dataSirup->metode_pemilihan = $scrapedData['metode_pemilihan'];
           $dataSirup->jenis_pengadaan =  $scrapedData['jenis_pengadaan'];

           $dataSirup->pemanfaatan_barang_jasa = $scrapedData['pemanfaatan_barang_jasa'];
           $dataSirup->jadwal_pelaksanaan_kontrak = $scrapedData['jadwal_pelaksanaan_kontrak'];
           $dataSirup->jadwal_pemilihan_penyedia = $scrapedData['jadwal_pemilihan_penyedia'];
           $dataSirup->tanggal_paket_diumumkan = $scrapedData['tanggal_paket_diumumkan'] ?? null;

           $dataSirup->save();
           DB::commit();

           // Update progress
           CommandProgress::updateProgress(
               $this->getName(),
               100,
               100,
               "Berhasil menyimpan data RUP ID: $rupId ke database",
               [
                   'stage' => 'save_success',
                   'rup_id' => $rupId,
                   'memory_usage' => $this->getMemoryUsage()
               ]
           );

           return true;
       } catch (\Exception $e) {
           DB::rollBack();
           $this->error("Error saving RUP data for RUP ID {$rupId}: " . $e->getMessage());

           // Log system error
           $this->logSystemError($e, "saveRupData for RUP ID: $rupId");

           // Update progress
           CommandProgress::updateProgress(
               $this->getName(),
               100,
               100,
               "Gagal menyimpan data RUP ID: $rupId ke database",
               [
                   'stage' => 'save_failed',
                   'rup_id' => $rupId,
                   'error' => $e->getMessage(),
                   'memory_usage' => $this->getMemoryUsage()
               ]
           );

           return false;
       }
   }

    protected function parseRupDetails(Crawler $crawler)
    {
        $data = [
            'nama_paket' => null,
            'nama_klpd' => null,
            'satuan_kerja' => null,
            'tahun_anggaran' => null,
            'lokasi_pekerjaan' => [],
            'volume_pekerjaan' => null,
            'uraian_pekerjaan' => null,
            'spesifikasi_pekerjaan' => null,
            'produk_dalam_negeri' => null,
            'umkm' => null,
            'pra_dipa_dpa' => null,
            'sumber_dana' => [],
            'jenis_pengadaan' => [],
            'total_pagu' => null,
            'metode_pemilihan' => null,
            'pemanfaatan_barang_jasa' => [],
            'jadwal_pelaksanaan_kontrak' => [],
            'jadwal_pemilihan_penyedia' => [],
            'tanggal_paket_diumumkan' => null,
        ];

        $crawler->filter('table.table > tr')->each(function (Crawler $tr) use (&$data) {
            $tds = $tr->filter('td');
            if ($tds->count() < 2) {
                return;
            }

            $label = trim($tds->eq(0)->text());
            $valueNode = $tds->eq(1);

            switch ($label) {
                case 'Nama Paket':
                    $data['nama_paket'] = trim($valueNode->text());
                    break;
                case 'Nama KLPD':
                    $data['nama_klpd'] = trim($valueNode->text());
                    break;
                case 'Satuan Kerja':
                    $data['satuan_kerja'] = trim($valueNode->text());
                    break;
                case 'Tahun Anggaran':
                    $data['tahun_anggaran'] = intval(trim($valueNode->text()));
                    break;
                case 'Lokasi Pekerjaan':
                    $lokasi = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $lokTr, $j) use (&$lokasi) {
                        if ($j == 0)
                            return;
                        $tdsLok = $lokTr->filter('td');
                        if ($tdsLok->count() >= 4) {
                            $lokasi[] = [
                                'no' => trim($tdsLok->eq(0)->text(), '.'),
                                'provinsi' => trim($tdsLok->eq(1)->text()),
                                'kabupaten_kota' => trim($tdsLok->eq(2)->text()),
                                'detail_lokasi' => trim($tdsLok->eq(3)->text()),
                            ];
                        }
                    });
                    $data['lokasi_pekerjaan'] = $lokasi;
                    break;
                case 'Volume Pekerjaan':
                    $data['volume_pekerjaan'] = trim($valueNode->text());
                    break;
                case 'Uraian Pekerjaan':
                    $data['uraian_pekerjaan'] = trim($valueNode->text());
                    break;
                case 'Spesifikasi Pekerjaan':
                    $data['spesifikasi_pekerjaan'] = trim($valueNode->text());
                    break;
                case 'Produk Dalam Negeri':
                    $data['produk_dalam_negeri'] = $this->parseBadge($valueNode);
                    break;
                case 'Usaha Kecil/Koperasi':
                    $data['umkm'] = $this->parseBadge($valueNode);
                    break;
                case 'Pra DIPA / DPA':
                    $data['pra_dipa_dpa'] = $this->parseBadge($valueNode);
                    break;
                case 'Sumber Dana':
                    $sumberDana = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $sdTr, $k) use (&$sumberDana) {
                        if ($k == 0)
                            return;
                        $tdsSd = $sdTr->filter('td');
                        if ($tdsSd->count() >= 6) {
                            $sumberDana[] = [
                                'no' => trim($tdsSd->eq(0)->text(), '.'),
                                'sumber_dana' => trim($tdsSd->eq(1)->text()),
                                'ta' => trim($tdsSd->eq(2)->text()),
                                'klpd' => trim($tdsSd->eq(3)->text()),
                                'mak' => trim($tdsSd->eq(4)->text()),
                                'pagu' => $this->parseRupiah($tdsSd->eq(5)->text()),
                            ];
                        }
                    });
                    $data['sumber_dana'] = $sumberDana;
                    break;
                case 'Jenis Pengadaan':
                    $jenisPengadaan = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $jpTr, $m) use (&$jenisPengadaan) {
                        if ($m == 0)
                            return;
                        $tdsJp = $jpTr->filter('td');
                        if ($tdsJp->count() >= 3) {
                            $jenisPengadaan[] = [
                                'no' => trim($tdsJp->eq(0)->text(), '.'),
                                'jenis_pengadaan' => trim($tdsJp->eq(1)->text()),
                                'pagu_jenis_pengadaan' => $this->parseRupiah($tdsJp->eq(2)->text()),
                            ];
                        }
                    });
                    $data['jenis_pengadaan'] = $jenisPengadaan;
                    break;
                case 'Total Pagu':
                    $data['total_pagu'] = $this->parseRupiah(trim($valueNode->text()));
                    break;
                case 'Metode Pemilihan':
                    $data['metode_pemilihan'] = trim($valueNode->text());
                    break;
                case 'Pemanfaatan Barang/Jasa':
                    $pemanfaatan = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $pbjTr, $n) use (&$pemanfaatan) {
                        if ($n == 0)
                            return;
                        $tdsPbj = $pbjTr->filter('td');
                        if ($tdsPbj->count() >= 2) {
                            $pemanfaatan[] = [
                                'mulai' => trim($tdsPbj->eq(0)->text()),
                                'akhir' => trim($tdsPbj->eq(1)->text()),
                            ];
                        }
                    });
                    $data['pemanfaatan_barang_jasa'] = $pemanfaatan;
                    break;
                case 'Jadwal Pelaksanaan Kontrak':
                    $jadwalKontrak = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $jpKontrakTr, $o) use (&$jadwalKontrak) {
                        if ($o == 0)
                            return;
                        $tdsJpKontrak = $jpKontrakTr->filter('td');
                        if ($tdsJpKontrak->count() >= 2) {
                            $jadwalKontrak[] = [
                                'mulai' => trim($tdsJpKontrak->eq(0)->text()),
                                'akhir' => trim($tdsJpKontrak->eq(1)->text()),
                            ];
                        }
                    });
                    $data['jadwal_pelaksanaan_kontrak'] = $jadwalKontrak;
                    break;
                case 'Jadwal Pemilihan Penyedia':
                    $jadwalPenyedia = [];
                    $valueNode->filter('table.table-striped tr')->each(function (Crawler $jpPenyediaTr, $p) use (&$jadwalPenyedia) {
                        if ($p == 0)
                            return;
                        $tdsJpPenyedia = $jpPenyediaTr->filter('td');
                        if ($tdsJpPenyedia->count() >= 2) {
                            $jadwalPenyedia[] = [
                                'mulai' => trim($tdsJpPenyedia->eq(0)->text()),
                                'akhir' => trim($tdsJpPenyedia->eq(1)->text()),
                            ];
                        }
                    });
                    $data['jadwal_pemilihan_penyedia'] = $jadwalPenyedia;
                    break;
                case 'Tanggal Umumkan Paket':
                    $tanggal = trim($valueNode->text());
                    $tanggalTranslated = $this->translateMonth($tanggal);
                    try {
                        $data['tanggal_paket_diumumkan'] = Carbon::createFromFormat('d F Y H:i', $tanggalTranslated);
                    } catch (\Exception $e) {
                        $data['tanggal_paket_diumumkan'] = null;
                        Log::warning("Format tanggal tidak sesuai: {$tanggal}");
                    }
                    break;
                default:
                    break;
            }
        });

        return $data;
    }

    protected function mergeScrapedData(DataSirup $dataSirup, array $scrapedData)
    {
        foreach ($scrapedData as $key => $value) {
            $dataSirup->{$key} = $value;
        }
    }

    protected function parseDate($dateString)
    {
        try {
            $translations = [
                'Januari' => 'January', 'Februari' => 'February',
                'Maret' => 'March', 'April' => 'April',
                'Mei' => 'May', 'Juni' => 'June',
                'Juli' => 'July', 'Agustus' => 'August',
                'September' => 'September', 'Oktober' => 'October',
                'November' => 'November', 'Desember' => 'December'
            ];

            foreach ($translations as $ind => $eng) {
                $dateString = str_replace($ind, $eng, $dateString);
            }

            return Carbon::parse($dateString);
        } catch (\Exception $e) {
            Log::warning("Gagal parsing tanggal: {$dateString}");
            return null;
        }
    }

    protected function translateMonth(string $dateString): string
    {
        foreach ($this->bulanMapping as $indonesian => $english) {
            if (stripos($dateString, $indonesian) !== false) {
                $dateString = str_ireplace($indonesian, $english, $dateString);
            }
        }

        return $dateString;
    }

    protected function parseBadge(Crawler $node)
    {
        if ($node->filter('.badge')->count() > 0) {
            $badgeText = strtolower(trim($node->filter('.badge')->text()));

            if ($badgeText === 'ya') {
                return true;
            } elseif ($badgeText === 'tidak') {
                return false;
            }
        }

        return null;
    }

    protected function parseRupiah($text)
    {
        $number = preg_replace('/[^\d]/', '', $text);

        return $number ? intval($number) : null;
    }
}
