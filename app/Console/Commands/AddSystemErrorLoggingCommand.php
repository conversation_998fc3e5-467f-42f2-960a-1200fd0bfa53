<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class AddSystemErrorLoggingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:system-error-logging
                            {cmd_name? : Specific command to add system error logging to}
                            {--all : Add system error logging to all commands}
                            {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add SystemError logging to commands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $commandName = $this->argument('cmd_name');
        $all = $this->option('all');
        $dryRun = $this->option('dry-run');

        if (!$commandName && !$all) {
            $this->error('Please specify a command name or use --all option.');
            return Command::FAILURE;
        }

        $commandsPath = app_path('Console/Commands');
        $commands = [];

        if ($all) {
            $files = File::files($commandsPath);
            foreach ($files as $file) {
                if ($file->getExtension() === 'php') {
                    $commands[] = $file->getPathname();
                }
            }

            $this->info('Found ' . count($commands) . ' command files.');
        } else {
            $commandFile = $commandsPath . '/' . $commandName . '.php';
            if (!File::exists($commandFile)) {
                $commandFile = $commandsPath . '/' . Str::studly($commandName) . 'Command.php';
                if (!File::exists($commandFile)) {
                    $this->error("Command file for '{$commandName}' not found.");
                    return Command::FAILURE;
                }
            }

            $commands[] = $commandFile;
            $this->info("Found command file: {$commandFile}");
        }

        $modifiedCount = 0;
        $skippedCount = 0;

        foreach ($commands as $commandFile) {
            $content = File::get($commandFile);
            $className = basename($commandFile, '.php');

            // Skip utility commands
            if (in_array($className, ['AddCommandOptionsCommand', 'ImplementCommandManagementCommand', 'MonitorCommandsCommand', 'AddCommandProgressCommand', 'AddSystemErrorLoggingCommand'])) {
                $this->info("Skipping utility command: {$className}");
                $skippedCount++;
                continue;
            }

            // Check if CommandManagementTrait is used
            if (Str::contains($content, 'use App\Traits\CommandManagementTrait;') ||
                Str::contains($content, 'use CommandManagementTrait;')) {
                $this->info("Command {$className} already uses CommandManagementTrait which includes system error logging. Skipping.");
                $skippedCount++;
                continue;
            }

            // Check if SystemError is already used
            if (Str::contains($content, 'use App\Models\SystemError;')) {
                $this->info("Command {$className} already uses SystemError. Skipping.");
                $skippedCount++;
                continue;
            }

            // Check if logSystemError method already exists
            if (Str::contains($content, 'function logSystemError')) {
                $this->info("Command {$className} already has logSystemError method. Skipping.");
                $skippedCount++;
                continue;
            }

            $this->info("Adding SystemError logging to {$className}...");

            // Add use statement
            $newContent = preg_replace(
                '/(namespace App\\\\Console\\\\Commands;.*?)(\s*class\s+' . $className . '\s+extends\s+Command)/s',
                "$1\n\nuse App\\Models\\SystemError;$2",
                $content
            );

            // Add logSystemError method
            $newContent = preg_replace(
                '/(class\s+' . $className . '\s+extends\s+Command.*?)(}\s*$)/s',
                "$1\n    /**\n     * Log error to system_errors table\n     *\n     * @param \\Exception \$exception\n     * @param string \$context\n     * @return void\n     */\n    private function logSystemError(\\Exception \$exception, string \$context = ''): void\n    {\n        try {\n            \$errorData = [\n                'type' => get_class(\$exception),\n                'message' => \$exception->getMessage(),\n                'code' => \$exception->getCode(),\n                'file' => \$exception->getFile(),\n                'line' => \$exception->getLine(),\n                'trace' => \$exception->getTrace(),\n                'request_method' => 'CLI',\n                'request_url' => 'artisan ' . \$this->getName(),\n                'user_id' => null,\n                'user_email' => null,\n                'ip_address' => '127.0.0.1',\n                'user_agent' => 'Artisan CLI',\n                'request_input' => [\n                    'command' => \$this->getName(),\n                    'arguments' => \$this->arguments(),\n                    'options' => \$this->options(),\n                    'context' => \$context\n                ],\n                'resolved' => false\n            ];\n            \n            SystemError::create(\$errorData);\n            \$this->error(\"Error logged to system_errors table: \" . \$exception->getMessage());\n        } catch (\\Exception \$e) {\n            \$this->error(\"Failed to log error to system_errors: \" . \$e->getMessage());\n        }\n    }$2",
                $newContent
            );

            // Add try-catch with system error logging to handle method if it exists
            if (preg_match('/public\s+function\s+handle\s*\(\s*\)\s*{/s', $newContent) &&
                !Str::contains($newContent, 'try {') &&
                !Str::contains($newContent, 'catch (\\Exception $e)')) {

                $newContent = preg_replace(
                    '/(public\s+function\s+handle\s*\(\s*\)\s*{)/s',
                    "$1\n        try {",
                    $newContent
                );

                $newContent = preg_replace(
                    '/(public\s+function\s+handle\s*\(\s*\).*?)(return\s+.*?;)\s*}/s',
                    "$1$2\n        } catch (\\Exception \$e) {\n            \$this->error(\"An error occurred: \" . \$e->getMessage());\n            \$this->logSystemError(\$e, \"handle method\");\n            return Command::FAILURE;\n        }",
                    $newContent
                );
            }

            if ($dryRun) {
                $this->info("Would modify {$className} (dry run)");
                $this->line("--- Changes ---");
                $this->line(Str::limit($newContent, 500));
                $this->line("--- End of Changes ---");
            } else {
                File::put($commandFile, $newContent);
                $this->info("SystemError logging added to {$className}.");
            }

            $modifiedCount++;
        }

        $this->info("SystemError logging added to {$modifiedCount} commands, {$skippedCount} commands skipped.");

        if (!$dryRun && $modifiedCount > 0) {
            $this->warn("Please review the changes and make any necessary adjustments.");
            $this->warn("You may need to add additional error handling in command-specific logic.");
        }

        return Command::SUCCESS;
    }
}
