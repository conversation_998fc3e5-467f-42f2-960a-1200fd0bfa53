<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class UpdateNilaiKontrakPaketPengadaan extends Command
{
    use CommandManagementTrait;

    protected $signature = 'update:nilai-kontrak
                            {--noPaket= : Nomor paket spesifik yang akan diupdate}
                            {--updateNULL= : Jika "Yes", hanya update paket dengan nilai_kontrak 0 atau NULL}
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--lock-name= : Custom lock name for this command instance}
                            {--force : Force run even if command is already running}';
    protected $description = 'Update nilai kontrak pada paket pengadaan dengan opsi filter berdasarkan nomor paket atau nilai kontrak null/0, dengan proses per chunk untuk efisiensi.';

    /**
     * Get elapsed time in seconds
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTime(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                "Memulai proses update nilai kontrak paket pengadaan...",
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $query = DB::table('ekatalog_paket_pengadaan');

            if ($noPaket = $this->option('noPaket')) {
                $query->where('nomor_paket', $noPaket);
                $this->info("Filter: Hanya paket dengan nomor {$noPaket} yang akan diupdate.");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    5,
                    100,
                    "Filter: Hanya paket dengan nomor {$noPaket} yang akan diupdate",
                    [
                        'no_paket' => $noPaket,
                        'stage' => 'filtering_by_no_paket',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );
            }

            if (strtolower($this->option('updateNULL')) === 'yes') {
                $query->where(function ($q) {
                    $q->whereNull('nilai_kontrakz')
                      ->orWhere('nilai_kontrakz', 0);
                });
                $this->info("Filter: Hanya paket dengan nilai_kontrak NULL atau 0 yang akan diupdate.");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    10,
                    100,
                    "Filter: Hanya paket dengan nilai_kontrak NULL atau 0 yang akan diupdate",
                    [
                        'update_null' => 'yes',
                        'stage' => 'filtering_by_null_value',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );
            }

            // Count total packages to be processed
            $totalPackages = $query->count();
            $this->info("Total paket yang akan diproses: {$totalPackages}");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                15,
                100,
                "Total paket yang akan diproses: {$totalPackages}",
                [
                    'total_packages' => $totalPackages,
                    'stage' => 'counting_packages',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $totalUpdated = 0;
            $chunkSize = 100;
            $chunkCount = 0;

            $query->chunkById($chunkSize, function ($packages) use (&$totalUpdated, &$chunkCount, $totalPackages, $chunkSize, $startTime) {
                $chunkCount++;
                $progress = min(90, 15 + round(($chunkCount * $chunkSize / $totalPackages) * 75));

                // Update progress for chunk
                CommandProgress::updateProgress(
                    $this->getName(),
                    $progress,
                    100,
                    "Memproses chunk {$chunkCount} ({$totalUpdated} paket diupdate)",
                    [
                        'chunk_count' => $chunkCount,
                        'total_updated' => $totalUpdated,
                        'stage' => 'processing_chunk',
                        'memory_usage' => $this->getMemoryUsage(),
                        'elapsed_time' => $this->getElapsedTime($startTime)
                    ]
                );

                $updatedPackages = [];

                foreach ($packages as $package) {
                    try {
                        $newNilai = DB::table('ekatalog_transaksi_paket')
                            ->where('id_paket', $package->id_paket)
                            ->select(DB::raw("IFNULL(SUM(COALESCE(total_harga, kuantitas_produk * harga_satuan + harga_ongkos_kirim)), 0) as total"))
                            ->value('total');

                        DB::table('ekatalog_paket_pengadaan')
                            ->where('id_paket', $package->id_paket)
                            ->update(['nilai_kontrakz' => $newNilai]);

                        $updatedPackages[] = [
                            'Nomor Paket'        => $package->nomor_paket,
                            'Nilai Kontrak Baru' => number_format($newNilai, 2, ',', '.'),
                        ];

                        $totalUpdated++;
                        $this->info("Updated: {$package->nomor_paket} → Rp " . number_format($newNilai, 2, ',', '.'));
                    } catch (\Exception $e) {
                        $this->error("Error updating package {$package->nomor_paket}: " . $e->getMessage());

                        // Log system error
                        $this->logSystemError($e, "handle method - updating package {$package->nomor_paket}");
                    }
                }

                if (!empty($updatedPackages)) {
                    $this->table(['Nomor Paket', 'Nilai Kontrak Baru'], $updatedPackages);
                }
            }, 'id_paket');

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            $message = "Update selesai. Total paket yang diupdate: {$totalUpdated} dalam " . round($executionTime, 2) . " detik.";

            $this->info($message);

            // Mark command as completed
            CommandProgress::markAsCompleted(
                $this->getName(),
                $message,
                [
                    'total_updated' => $totalUpdated,
                    'total_chunks' => $chunkCount,
                    'execution_time' => round($executionTime, 2),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }
}
