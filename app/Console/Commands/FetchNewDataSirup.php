<?php

namespace App\Console\Commands;

use App\Models\DataSirup;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Crawler\CustomClientSquid;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class FetchNewDataSirup extends Command
{
    use CommandManagementTrait;

    protected $signature = 'sirup:fetch
                            {start=0 : Starting index for data fetching}
                            {length=2500 : Number of records to fetch per batch}
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}
                            {--lock-name= : Custom lock name for this command instance}';
    protected $description = 'Fetch and process SIRUP data from external API';

    protected $client;

    public function __construct()
    {
        parent::__construct();

        // Inisialisasi CustomClientSquid dengan opsi default
        $this->client = new CustomClientSquid([
            'base_uri' => 'https://sirup.lkpp.go.id',
            'timeout' => 30.0, // Timeout 30 detik untuk request HTTP
            'connect_timeout' => 10.0, // Timeout koneksi 10 detik
        ]);
    }

    /**
     * Get elapsed time in seconds from a specific start time
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTimeFromStart(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    /**
     * Get elapsed time in seconds from command start time
     * This method is required by CommandManagementTrait
     *
     * @return float
     */
    protected function getElapsedTime(): float
    {
        // Menggunakan commandStartTime dari CommandManagementTrait
        return round(microtime(true) - $this->commandStartTime, 2);
    }

    public function fetchSirupData($tahun, $start = 0, $length = 1000)
    {
        $params = [
            'tahunAnggaran' => $tahun,
            'draw' => 3,
            'start' => $start,
            'length' => $length,
            'metodePengadaan' => '9,15,13,14',
            'order[0][column]' => 5,
            'order[0][dir]' => 'DESC',
        ];

        $baseUrl = "https://sirup.lkpp.go.id/sirup/caripaketctr/search";
        $fullUrl = $baseUrl . '?' . http_build_query($params);

        try {
            // Tambahkan jeda waktu acak antara request untuk mengurangi risiko diblokir
            $acak = random_int(200000, 500000); // 200-500ms
            usleep($acak);

            // Gunakan CustomClientSquid untuk request
            $response = $this->client->get($fullUrl, [
                'timeout' => 30.0, // Timeout 30 detik untuk request HTTP
            ]);

            $this->newLine();
            $this->info("Fetching data for start={$start}");

            return json_decode($response->getBody(), true);
        } catch (\Exception $e) {
            $errorMessage = 'Error fetching data from API: ' . $e->getMessage();
            Log::error($errorMessage);

            // Log system error
            $this->logSystemError($e, "fetchSirupData - fetching data for start={$start}");

            // Jika mendapat error 429, throw exception dengan kode 429
            if (strpos($e->getMessage(), '429') !== false) {
                $this->error("Terjadi HTTP 429 Too Many Requests. Proses scraping dihentikan untuk menghindari pemblokiran.");
                Log::error("HTTP 429 Too Many Requests diterima. Proses scraping dihentikan.");

                throw new \Exception("Terjadi HTTP 429 Too Many Requests. Proses scraping dihentikan.", 429);
            }

            return null;
        }
    }

    public function prosesAllDataSirup($tahun, $start = 0, $length = 1000, $startTime)
    {
        $totalRecords = 0;
        $processedRecords = 0; // Total data processed
        $newRecords = 0; // Total new data added
        $iterasi = 0;

        // Fetch total records in the first call
        $this->info("Fetching initial data...");

        // Update progress
        CommandProgress::updateProgress(
            $this->getName(),
            10,
            100,
            "Fetching initial data for year {$tahun}",
            [
                'tahun' => $tahun,
                'start' => $start,
                'length' => $length,
                'stage' => 'initial_fetch',
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        $data = $this->fetchSirupData($tahun, $start, $length);

        if (empty($data) || !isset($data['data'])) {
            $errorMessage = "Failed to fetch data for starts: $start";
            $this->error($errorMessage);

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                100,
                100,
                $errorMessage,
                [
                    'tahun' => $tahun,
                    'start' => $start,
                    'length' => $length,
                    'stage' => 'fetch_failed',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            return [
                'processedRecords' => 0,
                'newRecords' => 0,
                'success' => false
            ];
        }

        $totalRecords = $data['recordsFiltered'] ?? 0;
        $this->info("Fetching SIRUP data for year {$tahun}...");
        $this->info("Total Records to Process: {$totalRecords}\n");

        // Update progress
        CommandProgress::updateProgress(
            $this->getName(),
            15,
            100,
            "Total Records to Process: {$totalRecords}",
            [
                'tahun' => $tahun,
                'total_records' => $totalRecords,
                'stage' => 'counting_records',
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        // Initialize progress bar
        $progressBar = $this->output->createProgressBar($totalRecords);
        $progressBar->start();

        do {
            try {
                $iterasi++;
                $progress = min(90, 15 + round(($processedRecords / $totalRecords) * 75));

                $this->line("\nIterasi Ke {$iterasi} - Start: {$start}, Length: {$length}");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    $progress,
                    100,
                    "Iterasi Ke {$iterasi} - Start: {$start}, Length: {$length}",
                    [
                        'tahun' => $tahun,
                        'iterasi' => $iterasi,
                        'start' => $start,
                        'length' => $length,
                        'processed_records' => $processedRecords,
                        'new_records' => $newRecords,
                        'stage' => 'processing_batch',
                        'memory_usage' => $this->getMemoryUsage(),
                        'elapsed_time' => $this->getElapsedTimeFromStart($startTime)
                    ]
                );

                // Periksa apakah perlu berhenti karena batas waktu
                if (!$this->checkTimeLimit("Berhenti karena batas waktu tercapai")) {
                    $this->warn("Berhenti karena batas waktu tercapai");
                    break;
                }

                $paketData = $data['data'];
                $processedRecords += count($paketData);

                $newData = [];
                if (!empty($paketData)) {
                    try {
                        $kodeRups = array_column($paketData, 'id');
                        $existingSirups = DataSirup::whereIn('kode_rup', $kodeRups)->pluck('kode_rup')->toArray();

                        foreach ($paketData as $item) {
                            if (in_array($item['id'], $existingSirups)) {
                                continue;
                            }

                            $newData[] = [
                                'kode_rup' => $item['id'],
                                'nama_paket' => $item['paket'],
                                'satuan_kerja' => $item['satuanKerja'],
                                'produk_dalam_negeri' => $item['isPDN'],
                                'umkm' => $item['isUMK'],
                                'total_pagu' => $item['pagu'],
                                'metode_pemilihan' => $item['metode'],
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                        }

                        if (!empty($newData)) {
                            DataSirup::insert($newData);
                            $newRecords += count($newData);
                            $this->info(count($newData) . " new records added in this iteration.");
                        } else {
                            $this->info("No new records to add in this iteration.");
                        }
                    } catch (\Exception $e) {
                        $errorMessage = "Error processing data batch: " . $e->getMessage();
                        $this->error($errorMessage);
                        Log::error($errorMessage);

                        // Log system error
                        $this->logSystemError($e, "prosesAllDataSirup - processing data batch in iteration {$iterasi}");
                    }
                }

                // Update progress bar
                $progressBar->advance(count($paketData));

                // Fetch next batch
                $start += $length;

                // Fetch next batch of data
                $data = $this->fetchSirupData($tahun, $start, $length);

                if (empty($data) || !isset($data['data'])) {
                    $warningMessage = "Failed to fetch data for start: $start, stopping process";
                    $this->warn($warningMessage);
                    Log::warning($warningMessage);
                    break;
                }

                $totalRecords = $data['recordsFiltered'] ?? 0;
            } catch (\Exception $e) {
                // Jika mendapat error 429, hentikan seluruh proses
                if ($e->getCode() == 429) {
                    $this->error("Menghentikan seluruh proses scraping karena error 429 Too Many Requests");

                    // Update progress
                    CommandProgress::markAsFailed(
                        $this->getName(),
                        "Proses scraping dihentikan karena error 429 Too Many Requests",
                        [
                            'processed' => $processedRecords,
                            'total' => $totalRecords,
                            'percentage' => round(($processedRecords / $totalRecords) * 100),
                            'memory_usage' => $this->getMemoryUsage(),
                            'elapsed_time' => $this->getElapsedTimeFromStart($startTime),
                            'error_code' => 429
                        ]
                    );

                    // Keluar dari loop
                    break;
                } else {
                    // Untuk error lain, log dan lanjutkan
                    $this->error("Error pada iterasi {$iterasi}: " . $e->getMessage());
                    continue;
                }
            }

        } while ($start < $totalRecords);

        // Finish progress bar
        $progressBar->finish();

        // Final Summary
        $this->line("\n\nSummary:");
        $this->info("Total Records Processed: {$processedRecords}");
        $this->info("Total New Records Added: {$newRecords}");

        return [
            'processedRecords' => $processedRecords,
            'newRecords' => $newRecords,
            'success' => true
        ];
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            $tahun = Carbon::now()->year;
            $start = $this->argument('start');
            $length = $this->argument('length');

            $message = "Starting SIRUP data fetching process for year {$tahun}";
            $this->info($message);

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                $message,
                [
                    'tahun' => $tahun,
                    'start' => $start,
                    'length' => $length,
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $result = $this->prosesAllDataSirup($tahun, $start, $length, $startTime);

            if (!$result['success']) {
                $errorMessage = "SIRUP data fetching process failed.";
                $this->error($errorMessage);

                // Mark command as failed
                CommandProgress::markAsFailed(
                    $this->getName(),
                    $errorMessage,
                    [
                        'tahun' => $tahun,
                        'start' => $start,
                        'length' => $length,
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage()
                    ]
                );

                return SymfonyCommand::FAILURE;
            }

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            $message = "SIRUP data fetching process completed in " . round($executionTime, 2) . " seconds. Processed: {$result['processedRecords']}, Added: {$result['newRecords']}";

            $this->info($message);

            // Mark command as completed
            CommandProgress::markAsCompleted(
                $this->getName(),
                $message,
                [
                    'tahun' => $tahun,
                    'processed_records' => $result['processedRecords'],
                    'new_records' => $result['newRecords'],
                    'execution_time' => round($executionTime, 2),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());
            Log::error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }
}