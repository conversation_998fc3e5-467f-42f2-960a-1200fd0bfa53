<?php

namespace App\Console\Commands;

use App\Helpers\WhatsAppHelper;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendWhatsAppWelcomeMessageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:send-welcome {user_id : ID pengguna yang akan dikirim pesan}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mengirim pesan WhatsApp selamat datang kepada pengguna';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userId = $this->argument('user_id');

        $user = User::find($userId);
        if (!$user) {
            $this->error("Pengguna dengan ID {$userId} tidak ditemukan.");
            return 1;
        }

        if (!$user->phone) {
            $this->error("Pengguna dengan ID {$userId} tidak memiliki nomor telepon.");
            return 1;
        }

        // Format nomor telepon jika perlu
        $phone = $this->formatPhoneNumber($user->phone);
        
        // Log nomor telepon sebelum dan sesudah format
        Log::debug('Phone number before and after formatting', [
            'user_id' => $user->id,
            'before' => $user->phone,
            'after' => $phone
        ]);

        // Prepare complete user data
        $userData = [
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $phone,
        ];

        $this->info("Mengirim pesan selamat datang ke pengguna: {$user->name} ({$phone})");
        $this->info("Data pengguna: " . json_encode($userData, JSON_PRETTY_PRINT));

        try {
            // Kirim pesan selamat datang
            $success = WhatsAppHelper::sendWelcomeMessage($phone, $userData);

            if ($success) {
                $this->info("Pesan berhasil dikirim!");
                
                Log::info('WhatsApp welcome message sent successfully via command', [
                    'user_id' => $user->id,
                    'phone' => $phone
                ]);
                
                return 0;
            } else {
                $this->error("Gagal mengirim pesan!");
                
                Log::error('Failed to send WhatsApp welcome message via command', [
                    'user_id' => $user->id,
                    'phone' => $phone
                ]);
                
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("Terjadi kesalahan: " . $e->getMessage());
            
            Log::error('Exception when sending WhatsApp welcome message via command', [
                'user_id' => $user->id,
                'phone' => $phone,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }

    /**
     * Format nomor telepon untuk memastikan format yang benar
     *
     * @param string $phone
     * @return string
     */
    private function formatPhoneNumber($phone)
    {
        // Log nomor telepon sebelum format
        Log::debug('Formatting phone number in Command', [
            'original' => $phone
        ]);
        
        // Hapus semua karakter non-digit
        $phone = preg_replace('/\D/', '', $phone);
        
        // Pastikan nomor dimulai dengan 62 (kode negara Indonesia)
        if (substr($phone, 0, 2) !== '62') {
            // Jika dimulai dengan 0, ganti dengan 62
            if (substr($phone, 0, 1) === '0') {
                $phone = '62' . substr($phone, 1);
                Log::debug('Phone number starts with 0, replacing with 62', [
                    'formatted' => $phone
                ]);
            } else {
                // Jika tidak dimulai dengan 0, tambahkan 62 di depan
                $phone = '62' . $phone;
                Log::debug('Phone number does not start with 0 or 62, adding 62', [
                    'formatted' => $phone
                ]);
            }
        } else {
            Log::debug('Phone number already starts with 62', [
                'formatted' => $phone
            ]);
        }
        
        // Log nomor telepon setelah format
        Log::debug('Phone number formatted in Command', [
            'formatted' => $phone
        ]);
        
        return $phone;
    }
}
