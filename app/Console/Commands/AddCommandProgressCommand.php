<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class AddCommandProgressCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:command-progress
                            {cmd_name? : Specific command to add progress to}
                            {--all : Add progress to all commands}
                            {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add CommandProgress to commands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $commandName = $this->argument('cmd_name');
        $all = $this->option('all');
        $dryRun = $this->option('dry-run');

        if (!$commandName && !$all) {
            $this->error('Please specify a command name or use --all option.');
            return Command::FAILURE;
        }

        $commandsPath = app_path('Console/Commands');
        $commands = [];

        if ($all) {
            $files = File::files($commandsPath);
            foreach ($files as $file) {
                if ($file->getExtension() === 'php') {
                    $commands[] = $file->getPathname();
                }
            }

            $this->info('Found ' . count($commands) . ' command files.');
        } else {
            $commandFile = $commandsPath . '/' . $commandName . '.php';
            if (!File::exists($commandFile)) {
                $commandFile = $commandsPath . '/' . Str::studly($commandName) . 'Command.php';
                if (!File::exists($commandFile)) {
                    $this->error("Command file for '{$commandName}' not found.");
                    return Command::FAILURE;
                }
            }

            $commands[] = $commandFile;
            $this->info("Found command file: {$commandFile}");
        }

        $modifiedCount = 0;
        $skippedCount = 0;

        foreach ($commands as $commandFile) {
            $content = File::get($commandFile);
            $className = basename($commandFile, '.php');

            // Skip utility commands
            if (in_array($className, ['AddCommandOptionsCommand', 'ImplementCommandManagementCommand', 'MonitorCommandsCommand', 'AddCommandProgressCommand'])) {
                $this->info("Skipping utility command: {$className}");
                $skippedCount++;
                continue;
            }

            // Check if CommandManagementTrait is used
            if (Str::contains($content, 'use App\Traits\CommandManagementTrait;') ||
                Str::contains($content, 'use CommandManagementTrait;')) {
                $this->info("Command {$className} already uses CommandManagementTrait which includes progress. Skipping.");
                $skippedCount++;
                continue;
            }

            // Check if CommandProgress is already used
            if (Str::contains($content, 'use App\Models\CommandProgress;')) {
                $this->info("Command {$className} already uses CommandProgress. Skipping.");
                $skippedCount++;
                continue;
            }

            // Check if handle method exists
            if (!preg_match('/public\s+function\s+handle\s*\(\s*\)\s*{/s', $content)) {
                $this->warn("Could not find handle method in {$className}. Skipping.");
                $skippedCount++;
                continue;
            }

            $this->info("Adding CommandProgress to {$className}...");

            // Add use statement
            $newContent = preg_replace(
                '/(namespace App\\\\Console\\\\Commands;.*?)(\s*class\s+' . $className . '\s+extends\s+Command)/s',
                "$1\n\nuse App\\Models\\CommandProgress;$2",
                $content
            );

            // Add progress initialization at the beginning of handle method
            $newContent = preg_replace(
                '/(public\s+function\s+handle\s*\(\s*\)\s*{)/s',
                "$1\n        // Initialize command progress\n        \$commandName = \$this->getName();\n        CommandProgress::updateProgress(\n            \$commandName,\n            0,\n            100,\n            \"Starting {\$commandName}...\",\n            [\n                'started_at' => now()->toDateTimeString(),\n                'arguments' => \$this->arguments(),\n                'options' => \$this->options()\n            ]\n        );\n        \n        \$startTime = microtime(true); // Start measuring time\n        \n        try {",
                $newContent
            );

            // Add completion at the end of handle method
            $newContent = preg_replace(
                '/(public\s+function\s+handle\s*\(\s*\).*?)(return\s+.*?;)\s*}/s',
                "$1\n        \$endTime = microtime(true);\n        \$executionTime = \$endTime - \$startTime;\n        \$message = \"Command completed in \" . round(\$executionTime, 2) . \" seconds.\";\n        \n        // Mark command as completed\n        CommandProgress::markAsCompleted(\n            \$commandName,\n            \$message,\n            [\n                'execution_time' => round(\$executionTime, 2),\n                'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . ' MB',\n                'peak_memory_usage' => round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB'\n            ]\n        );\n        \n        $2\n        } catch (\\Exception \$e) {\n            \$this->error(\"An error occurred: \" . \$e->getMessage());\n            \n            // Mark command as failed\n            CommandProgress::markAsFailed(\n                \$commandName,\n                \"Command failed: \" . \$e->getMessage(),\n                [\n                    'error' => \$e->getMessage(),\n                    'trace' => \$e->getTraceAsString(),\n                    'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . ' MB',\n                    'peak_memory_usage' => round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB'\n                ]\n            );\n            \n            return Command::FAILURE;\n        }",
                $newContent
            );

            if ($dryRun) {
                $this->info("Would modify {$className} (dry run)");
                $this->line("--- Changes ---");
                $this->line(Str::limit($newContent, 500));
                $this->line("--- End of Changes ---");
            } else {
                File::put($commandFile, $newContent);
                $this->info("CommandProgress added to {$className}.");
            }

            $modifiedCount++;
        }

        $this->info("CommandProgress added to {$modifiedCount} commands, {$skippedCount} commands skipped.");

        if (!$dryRun && $modifiedCount > 0) {
            $this->warn("Please review the changes and make any necessary adjustments.");
            $this->warn("You may need to add additional progress updates in command-specific logic.");
        }

        return Command::SUCCESS;
    }
}
