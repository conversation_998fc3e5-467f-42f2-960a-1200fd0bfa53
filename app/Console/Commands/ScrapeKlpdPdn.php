<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use GuzzleHttp\Client;
use App\Models\Klpd;
use Exception;
use Illuminate\Support\Facades\Log;

class ScrapeKlpdPdn extends Command
{
    protected $signature = 'scrape:klpd-pdn {--year= : Tahun data KLPD (default: tahun sekarang)}';
    protected $description = 'Scraping data KLPD dari API PDN LKPP dan menyimpannya ke dalam database';

    private Client $client;

    private array $klpdTypes = [
        '1.1' => 'KEMENTERIAN',
        '1.2' => 'LEMBAGA',
        '2.3' => 'PROVINSI',
        '2.4' => 'KABUPATEN',
        '2.5' => 'KOTA'
    ];

    public function __construct()
    {
        parent::__construct();
        $this->client = new Client([
            'base_uri' => 'https://data-pdn.inaproc.id/bigenvelope/public/api/',
            'timeout' => 30.0,
            'headers' => [
                'Accept' => '*/*',
                'Accept-Language' => 'en-US,en;q=0.9,id;q=0.8',
                'Authorization' => 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6Imh0dHBzOlwvXC9kYXRhLXBkbi5pbmFwcm9jLmlkIiwidXNlcl9pcCI6IiAifQ._O4yc-9DEzL94NYshEqLlGKT4zdIDSS0iicEkEB4Gis',
                'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
                'Origin' => 'https://data-pdn.inaproc.id',
                'Referer' => 'https://data-pdn.inaproc.id/public/page/id/888',
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        ]);
    }

    public function handle(): void
    {
        $logger = Log::channel('scrapeKLPD');
        $year = $this->option('year');
        if (!$year) {
            $year = date('Y');
        }

        $this->info('Mulai scraping data KLPD dari PDN...');
        $logger->info("Mulai scraping data KLPD dari PDN untuk tahun {$year}.");

        foreach ($this->klpdTypes as $code => $type) {
            $this->info("\nMemproses data {$type}...");
            $logger->info("Memproses data {$type}");

            try {
                $response = $this->client->post('id/508', [
                    'form_params' => [
                        'kode_jenis_klpd' => $code,
                        'tahun' => $year
                    ]
                ]);

                $responseData = json_decode($response->getBody()->getContents(), true);

                if (!isset($responseData['transaction']) || !$responseData['transaction'] || empty($responseData['data'])) {
                    $this->warn("Tidak ada data untuk {$type}");
                    $logger->warning("Tidak ada data untuk {$type}");
                    continue;
                }

                $this->processKlpdData($responseData['data'], $type, $logger);

            } catch (Exception $e) {
                $this->error("Gagal mengambil data {$type}: " . $e->getMessage());
                $logger->error("Gagal mengambil data {$type}: " . $e->getMessage());
                continue;
            }
        }

        $this->info("\nScraping selesai.");
        $logger->info('Scraping selesai.');
    }

    private function processKlpdData(array $data, string $type, $logger): void
    {
        $count = 0;
        foreach ($data as $item) {
            try {
                if (empty($item['kode_klpd'])) {
                    $this->warn("Melewati data tanpa kode_klpd");
                    continue;
                }

                $klpd = Klpd::updateOrCreate(
                    ['kd_klpd' => $item['kode_klpd']],
                    [
                        'nama_klpd' => $item['nama_klpd'] ?? '',
                        'jenis_klpd' => $type,
                    ]
                );

                $count++;
                if ($count % 10 === 0) {
                    $this->info("Berhasil memproses {$count} data {$type}");
                }

            } catch (Exception $e) {
                $this->error("Gagal memproses KLPD {$item['kode_klpd']}: " . $e->getMessage());
                $logger->error("Gagal memproses KLPD {$item['kode_klpd']}: " . $e->getMessage());
            }
        }

        $this->info("Total {$count} data {$type} berhasil diproses");
        $logger->info("Total {$count} data {$type} berhasil diproses");
    }
}
