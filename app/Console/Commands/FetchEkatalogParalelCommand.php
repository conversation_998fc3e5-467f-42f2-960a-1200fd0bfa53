<?php

namespace App\Console\Commands;

use App\Jobs\ProcessEkatalogChunkJob;
use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Models\PaketPengadaan;
use Illuminate\Support\Facades\DB;
use DateTime;
use App\Crawler\CustomClientSquid;
use App\Models\SystemError;
use App\Models\CommandProgress;

class FetchEkatalogParalelCommand extends Command
{
    protected $signature = 'fetch:ekatalog-paralel {start?} {end?} {--direct}';
    protected $description = 'Ambil data dari Redash Ekatalog dengan pemrosesan paralel';

    private const REDASH_API_URL = "https://redash-e-katalog.lkpp.go.id/api/queries/157/results";
    private const REFRESH_REDASH_API_URL = "https://redash-e-katalog.lkpp.go.id/api/queries/157/refresh";
    private const AUTH_HEADER = 'Key BnXT185EjSxCxNa4YgFPoy7oiA6M4VKtD2sa4W8L';

    private $proxy;
    private $customClient;

    public function __construct()
    {
        parent::__construct();
        $this->proxy = env('PROXIES_SSH');
        $this->customClient = new CustomClientSquid();
    }

    public function handle()
    {
        register_shutdown_function([$this, 'cleanup']);

        if ($this->isCommandRunning()) {
            $this->error("Command 'fetch:ekatalog-paralel' is already running.");
            return Command::FAILURE;
        }

        $this->markCommandAsRunning();

        // Initialize command progress
        CommandProgress::updateProgress(
            'fetch:ekatalog-paralel',
            0,
            100,
            'Starting E-Katalog data fetch in parallel mode...'
        );

        try {
            $startTime = microtime(true); // Start measuring time

            $startDate = $this->argument('start') ?? $this->getLastStartDate();
            $endDate = $this->argument('end') ?? now()->format('Y-m-d H:i:s');
            $isDirect = $this->option('direct');

            // Update progress with processing mode
            CommandProgress::updateProgress(
                'fetch:ekatalog-paralel',
                0,
                100,
                "Fetching data from $startDate to $endDate...",
                [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'mode' => $isDirect ? 'direct' : 'daily',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            if ($isDirect) {
                $this->info("Memproses data langsung dari $startDate hingga $endDate.");
                $this->processDirectRange($startDate, $endDate);
            } else {
                $this->info("Memproses data harian dari $startDate hingga $endDate.");
                $this->processDailyRange($startDate, $endDate);
            }

            $endTime = microtime(true); // End measuring time
            $executionTime = $endTime - $startTime;
            $message = "Fetching data from $startDate to $endDate completed in " . round($executionTime, 2) . " seconds.";
            $this->info($message);

            // Mark command as completed
            CommandProgress::markAsCompleted(
                'fetch:ekatalog-paralel',
                $message,
                [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'execution_time' => round($executionTime, 2),
                    'mode' => $isDirect ? 'direct' : 'daily',
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );
        } catch (\Exception $e) {
            $errorMessage = "An error occurred: " . $e->getMessage();
            $this->error($errorMessage);

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                'fetch:ekatalog-paralel',
                $errorMessage,
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );
        }

        $this->markCommandAsNotRunning();
        return Command::SUCCESS;
    }

    public function cleanup()
    {
        if ($this->isCommandRunning()) {
            $this->markCommandAsNotRunning();
            $this->info("Command cleanup executed, marked as not running.");
        }
    }

    private function getMemoryUsage(): string
    {
        return round(memory_get_usage() / 1024 / 1024, 2) . ' MB';
    }

    private function getPeakMemoryUsage(): string
    {
        return round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB';
    }

    private function processDailyRange(string $startDate, string $endDate)
    {
        $currentDate = Carbon::parse($startDate);
        $finalDate = Carbon::parse($endDate);

        $totalDays = $currentDate->diffInDays($finalDate) + 1;
        $processedDays = 0;

        $this->info("Total days to process: $totalDays");

        // Update progress with total days
        CommandProgress::updateProgress(
            'fetch:ekatalog-paralel',
            0,
            $totalDays,
            "Processing data day by day from $startDate to $endDate...",
            [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'total_days' => $totalDays,
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        while ($currentDate <= $finalDate) {
            $this->processDateInParallel($currentDate);
            $currentDate->addDay();
            $processedDays++;

            // Update progress
            $progressPercentage = min(99, round(($processedDays / $totalDays) * 100));
            CommandProgress::updateProgress(
                'fetch:ekatalog-paralel',
                $processedDays,
                $totalDays,
                "Processed $processedDays of $totalDays days. Current date: " . $currentDate->subDay()->format('Y-m-d'),
                [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'current_date' => $currentDate->format('Y-m-d'),
                    'percentage' => $progressPercentage,
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );
            $currentDate->addDay(); // Add back the day we subtracted for display
        }
    }

    private function processDirectRange(string $startDate, string $endDate)
    {
        $payload = $this->buildPayload($startDate, $endDate);

        $this->info("Mengambil data untuk rentang tanggal: $startDate hingga $endDate");
        $maxAttempts = 50;
        $attempt = 0;
        $data = null;

        // Update progress for direct range processing
        CommandProgress::updateProgress(
            'fetch:ekatalog-paralel',
            0,
            $maxAttempts,
            "Fetching data for date range: $startDate to $endDate (direct mode)",
            [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'mode' => 'direct',
                'max_attempts' => $maxAttempts,
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        $options = [];
        if ($this->proxy) {
            $options['proxy'] = $this->proxy;
            $this->info("Using proxy: {$this->proxy}");
        } else {
            $this->info("No proxy configured, making direct request.");
        }

        do {
            try {
                $this->info("Menggunakan CustomClientSquid untuk request");

                // Konversi payload ke JSON
                $jsonPayload = json_encode($payload);

                // Siapkan headers
                $headers = $this->getHeaders();

                // Tambahkan Content-Type header
                $headers['Content-Type'] = 'application/json';

                // Buat opsi untuk curl
                $options = [
                    'headers' => $headers,
                    'body' => $jsonPayload
                ];

                // Lakukan request dengan CustomClientSquid (POST method)
                $response = $this->customClient->post(self::REDASH_API_URL, $options);

                // Ambil status code
                $statusCode = $response->getStatusCode();
                $this->info("HTTP Status: $statusCode");

                // Ambil body response
                $body = $response->getBody()->getContents();

                // Parse JSON response
                $resultData = json_decode($body, true);

                if ($statusCode >= 200 && $statusCode < 300) {
                    if (isset($resultData['query_result']['data']['rows'])) {
                        $data = $resultData['query_result']['data']['rows'];
                        $jumlahData = count($data);
                        $fetchDate = (new DateTime($resultData['query_result']['retrieved_at']))->format('Y-m-d H:i:s');
                        $this->info("Jumlah Data Ditemukan : $jumlahData. Tanggal Fetch : $fetchDate");
                        break;
                    }
                } else {
                    $this->error("Gagal mengambil data untuk rentang tanggal, HTTP Status: $statusCode");
                }
            } catch (\Exception $e) {
                $this->error("Error making request: " . $e->getMessage());
                $this->logSystemError($e, "processDirectRange for date range: $startDate to $endDate");
            }

            $attempt++;

            // Update progress for each attempt
            CommandProgress::updateProgress(
                'fetch:ekatalog-paralel',
                $attempt,
                $maxAttempts,
                "Attempt $attempt of $maxAttempts for date range: $startDate to $endDate",
                [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'mode' => 'direct',
                    'current_attempt' => $attempt,
                    'max_attempts' => $maxAttempts,
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            sleep(2);
        } while ($attempt < $maxAttempts);

        if ($data === null) {
            $this->error("Tidak ada data ditemukan untuk rentang tanggal setelah $maxAttempts percobaan.");
            return;
        }

        $this->processDataInParallel($data);
    }

    private function processDateInParallel(Carbon $currentDate)
    {
        $currentStart = $currentDate->format('Y-m-d');
        $this->info("Mengambil data untuk tanggal: $currentStart ");

        $payload = $this->buildPayload($currentStart, $currentStart);
        $maxAttempts = 50;
        $attempt = 0;
        $data = null;

        // Update progress for single date processing
        CommandProgress::updateProgress(
            'fetch:ekatalog-paralel',
            0,
            $maxAttempts,
            "Fetching data for date: $currentStart",
            [
                'current_date' => $currentStart,
                'mode' => 'daily',
                'max_attempts' => $maxAttempts,
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        $options = [];
        if ($this->proxy) {
            $options['proxy'] = $this->proxy;
            $this->info("Using proxy: {$this->proxy}");
        } else {
            $this->info("No proxy configured, making direct request.");
        }

        do {
            try {
                $this->info("Menggunakan CustomClientSquid untuk request");

                // Konversi payload ke JSON
                $jsonPayload = json_encode($payload);

                // Siapkan headers
                $headers = $this->getHeaders();

                // Tambahkan Content-Type header
                $headers['Content-Type'] = 'application/json';

                // Buat opsi untuk curl
                $options = [
                    'headers' => $headers,
                    'body' => $jsonPayload
                ];

                // Lakukan request dengan CustomClientSquid (POST method)
                $response = $this->customClient->post(self::REDASH_API_URL, $options);

                // Ambil status code
                $statusCode = $response->getStatusCode();
                $this->info("HTTP Status: $statusCode");

                // Ambil body response
                $body = $response->getBody()->getContents();

                // Parse JSON response
                $resultData = json_decode($body, true);

                if ($statusCode >= 200 && $statusCode < 300) {
                    if (isset($resultData['query_result']['data']['rows'])) {
                        $data = $resultData['query_result']['data']['rows'];
                        $jumlahData = count($data);
                        $fetchDate = (new DateTime($resultData['query_result']['retrieved_at']))->format('Y-m-d H:i:s');
                        $this->info("Jumlah Data Ditemukan : $jumlahData. Tanggal Fetch : $fetchDate");
                        break;
                    }
                } else {
                    $this->error("Gagal mengambil data untuk $currentStart, HTTP Status: $statusCode");
                }
            } catch (\Exception $e) {
                $this->error("Error making request: " . $e->getMessage());
                $this->logSystemError($e, "processDateInParallel for date: $currentStart");
            }

            $attempt++;

            // Update progress for each attempt
            CommandProgress::updateProgress(
                'fetch:ekatalog-paralel',
                $attempt,
                $maxAttempts,
                "Attempt $attempt of $maxAttempts for date: $currentStart",
                [
                    'current_date' => $currentStart,
                    'mode' => 'daily',
                    'current_attempt' => $attempt,
                    'max_attempts' => $maxAttempts,
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            sleep(2);
        } while ($attempt < $maxAttempts);

        if ($data === null) {
            $this->error("Tidak ada data ditemukan untuk $currentStart setelah $maxAttempts percobaan.");
            return;
        }

        $this->processDataInParallel($data);
    }

	private function processDataInParallel(array $data)
	{
    	$originalCount = count($data);
    	$this->info("Data yang akan diproses: $originalCount");

    	// Update progress for data processing
        CommandProgress::updateProgress(
            'fetch:ekatalog-paralel',
            0,
            100,
            "Processing $originalCount data items...",
            [
                'total_data' => $originalCount,
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

    	$nomorPaketData = array_column($data, 'Nomor Paket');
    	$existingNomorPaket = [];

    	// Batch nomorPaketData untuk menghindari terlalu banyak placeholder
    	$batchSize = 10000; // Sesuaikan batch size
    	$batches = array_chunk($nomorPaketData, $batchSize);

    	$batchCount = count($batches);
    	$processedBatches = 0;

    	// Update progress for batch processing
        CommandProgress::updateProgress(
            'fetch:ekatalog-paralel',
            0,
            $batchCount,
            "Checking existing data in $batchCount batches...",
            [
                'total_batches' => $batchCount,
                'batch_size' => $batchSize,
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

    	foreach ($batches as $batch) {
        	$existingNomorPaket = array_merge(
            	$existingNomorPaket,
            	PaketPengadaan::whereIn('nomor_paket', $batch)->pluck('nomor_paket')->toArray()
        	);

        	$processedBatches++;

        	// Update progress for each batch
            CommandProgress::updateProgress(
                'fetch:ekatalog-paralel',
                $processedBatches,
                $batchCount,
                "Processed $processedBatches of $batchCount batches...",
                [
                    'processed_batches' => $processedBatches,
                    'total_batches' => $batchCount,
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );
    	}

    	// Filter data yang sudah ada di database
    	$data = array_filter($data, fn($item) => !in_array($item['Nomor Paket'], $existingNomorPaket));

    	$filteredCount = count($data);
    	$this->info("Data yang sudah ada di database: " . ($originalCount - $filteredCount));
    	$this->info("Data yang akan diproses setelah filter: $filteredCount");

    	// Update progress after filtering
        CommandProgress::updateProgress(
            'fetch:ekatalog-paralel',
            0,
            100,
            "Filtered data: $filteredCount items to process...",
            [
                'original_count' => $originalCount,
                'filtered_count' => $filteredCount,
                'existing_count' => $originalCount - $filteredCount,
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

    	if (empty($data)) {
        	$this->info("Tidak ada data yang perlu diproses setelah pengecekan data yang sudah ada.");
        	return;
    	}

    	$chunks = array_chunk($data, 100);
    	$chunkCount = count($chunks);
    	$this->info("Data dibagi menjadi $chunkCount chunks untuk diproses secara paralel.");

    	// Update progress for job dispatching
        CommandProgress::updateProgress(
            'fetch:ekatalog-paralel',
            0,
            $chunkCount,
            "Dispatching $chunkCount job chunks...",
            [
                'chunk_count' => $chunkCount,
                'chunk_size' => 100,
                'total_items' => $filteredCount,
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

    	$processedChunks = 0;

    	foreach ($chunks as $chunk) {
        	ProcessEkatalogChunkJob::dispatch($chunk);
        	$processedChunks++;

        	// Update progress for each 10 chunks
        	if ($processedChunks % 10 === 0 || $processedChunks === $chunkCount) {
            	CommandProgress::updateProgress(
                	'fetch:ekatalog-paralel',
                	$processedChunks,
                	$chunkCount,
                	"Dispatched $processedChunks of $chunkCount job chunks...",
                	[
                    	'processed_chunks' => $processedChunks,
                    	'total_chunks' => $chunkCount,
                    	'memory_usage' => $this->getMemoryUsage()
                	]
            	);
        	}
    	}

    	$this->info("Job telah dikirim untuk " . count($data) . " item.");

    	// Final update for this batch
        CommandProgress::updateProgress(
            'fetch:ekatalog-paralel',
            100,
            100,
            "All jobs dispatched successfully for " . count($data) . " items.",
            [
                'total_items' => count($data),
                'total_chunks' => $chunkCount,
                'memory_usage' => $this->getMemoryUsage()
            ]
        );
	}


    private function buildPayload(string $startDate, string $endDate): array
    {
        return [
            "queryId" => 157,
            "parameters" => [
                "Tanggal" => [
                    "start" => $startDate,
                    "end" => $endDate
                ],
                "Jenis_Katalog" => ["ALL"],
                "pengelola" => ["ALL"],
                "provinsi" => ["ALL"],
                "Tahun Anggaran" => ["2025"],
                "Nama Satker Transaksi" => ["ALL"],
                "klpd" => ["ALL"]
            ]
        ];
    }

    private function getHeaders(): array
    {
        return [
            'Authorization' => self::AUTH_HEADER,
            'Accept' => 'application/json, text/plain, */*',
            'Content-Type' => 'application/json;charset=UTF-8',
            'Referer' => 'https://redash-e-katalog.lkpp.go.id/public/dashboards/' . self::AUTH_HEADER
        ];
    }

    private function getLastStartDate(): string
    {
        $lastDate = DB::table('ekatalog_paket_pengadaan')
            ->orderBy('tanggal_paket', 'desc')
            ->value('tanggal_paket');

        return $lastDate ?: now()->subYear()->format('Y-m-d H:i:s');
    }

    /**
     * Log error to system_errors table
     *
     * @param \Exception $exception
     * @param string $context
     * @return void
     */
    private function logSystemError(\Exception $exception, string $context = ''): void
    {
        try {
            $errorData = [
                'type' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTrace(),
                'request_method' => 'CLI',
                'request_url' => 'artisan fetch:ekatalog-paralel',
                'user_id' => null,
                'user_email' => null,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Artisan CLI',
                'request_input' => [
                    'command' => 'fetch:ekatalog-paralel',
                    'context' => $context
                ],
                'resolved' => false
            ];

            SystemError::create($errorData);
            $this->error("Error logged to system_errors table: " . $exception->getMessage());
        } catch (\Exception $e) {
            $this->error("Failed to log error to system_errors: " . $e->getMessage());
        }
    }

    private function isCommandRunning(): bool
    {
        return DB::table('commands_status')
            ->where('command_name', 'fetch:ekatalog-paralel')
            ->where('is_running', true)
            ->exists();
    }

    private function markCommandAsRunning(): void
    {
        DB::table('commands_status')->updateOrInsert(
            ['command_name' => 'fetch:ekatalog-paralel'],
            ['is_running' => true, 'started_at' => now()]
        );
    }

    private function markCommandAsNotRunning(): void
    {
        DB::table('commands_status')
            ->where('command_name', 'fetch:ekatalog-paralel')
            ->update(['is_running' => false, 'finished_at' => now()]);
    }

    private function refreshRedashQuery(): bool
    {
        try {
            $this->info("Menggunakan CustomClientSquid untuk refresh Redash");

            // Siapkan headers
            $headers = $this->getHeaders();

            // Tambahkan Content-Type header
            $headers['Content-Type'] = 'application/json';

            // Buat opsi untuk curl
            $options = [
                'headers' => $headers
            ];

            // Lakukan request dengan CustomClientSquid (POST method)
            $response = $this->customClient->post(self::REFRESH_REDASH_API_URL, $options);

            // Ambil status code
            $statusCode = $response->getStatusCode();
            $this->info("HTTP Status for refresh: $statusCode");

            if ($statusCode >= 200 && $statusCode < 300) {
                $this->info("Berhasil melakukan refresh Redash.");
                return true;
            } else {
                $this->error("Gagal melakukan refresh Redash, HTTP Status: $statusCode");
                return false;
            }
        } catch (\Exception $e) {
            $this->error("Error refreshing Redash: " . $e->getMessage());
            $this->logSystemError($e, "refreshRedashQuery");
            return false;
        }
    }
}
