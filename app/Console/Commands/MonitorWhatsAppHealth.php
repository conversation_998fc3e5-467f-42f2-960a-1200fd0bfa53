<?php

namespace App\Console\Commands;

use App\Helpers\WhatsAppHelper;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class MonitorWhatsAppHealth extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:monitor {--notify : Send notification if there are issues}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor WhatsApp service health and send notifications if there are issues';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Checking WhatsApp service health...');
        
        $whatsappServiceUrl = config('services.whatsapp.url');
        $apiKey = config('services.whatsapp.api_key');
        
        try {
            // Check if service is reachable
            $healthResponse = Http::withHeaders([
                'X-API-KEY' => $apiKey,
                'Accept' => 'application/json',
            ])->get("{$whatsappServiceUrl}/health");
            
            if (!$healthResponse->successful()) {
                $this->error('WhatsApp service health endpoint is not responding properly');
                $this->logAndNotify('WhatsApp service health endpoint is not responding properly', 'error');
                return 1;
            }
            
            // Check WhatsApp connection status
            $statusResponse = Http::withHeaders([
                'X-API-KEY' => $apiKey,
                'Accept' => 'application/json',
            ])->get("{$whatsappServiceUrl}/auth/status");
            
            if (!$statusResponse->successful()) {
                $this->error('Failed to get WhatsApp connection status');
                $this->logAndNotify('Failed to get WhatsApp connection status', 'error');
                return 1;
            }
            
            $status = $statusResponse->json();
            
            if (!isset($status['status']['ready']) || !$status['status']['ready']) {
                $this->warn('WhatsApp is not connected');
                
                // Check if we've already notified about this issue recently
                if (!Cache::has('whatsapp_disconnected_notified')) {
                    $this->logAndNotify('WhatsApp is not connected. Please check the service.', 'warning');
                    
                    // Set a flag to avoid sending too many notifications
                    Cache::put('whatsapp_disconnected_notified', true, 60 * 60); // 1 hour
                }
                
                return 1;
            } else {
                // If WhatsApp is now connected and we previously notified about disconnection,
                // send a recovery notification and clear the flag
                if (Cache::has('whatsapp_disconnected_notified')) {
                    $this->logAndNotify('WhatsApp connection has been restored.', 'info');
                    Cache::forget('whatsapp_disconnected_notified');
                }
                
                $this->info('WhatsApp is connected and ready');
            }
            
            $this->info('WhatsApp service health check completed successfully');
            return 0;
        } catch (\Exception $e) {
            $this->error('Error checking WhatsApp service health: ' . $e->getMessage());
            $this->logAndNotify('Error checking WhatsApp service health: ' . $e->getMessage(), 'error');
            return 1;
        }
    }
    
    /**
     * Log the message and send notification if needed
     */
    private function logAndNotify($message, $level = 'info')
    {
        // Log the message
        Log::channel('whatsapp')->$level($message);
        
        // Send notification if --notify option is provided
        if ($this->option('notify') && in_array($level, ['error', 'warning'])) {
            try {
                $adminPhone = '6281343596588'; // Admin phone number
                $notificationMessage = "[{$level}] WhatsApp Monitoring: {$message}";
                
                WhatsAppHelper::sendSystemNotification($adminPhone, $notificationMessage);
                
                $this->info('Notification sent to admin');
            } catch (\Exception $e) {
                $this->error('Failed to send notification: ' . $e->getMessage());
                Log::error('Failed to send WhatsApp monitoring notification: ' . $e->getMessage());
            }
        }
    }
}
