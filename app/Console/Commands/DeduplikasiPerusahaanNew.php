<?php  

namespace App\Console\Commands;  

use Illuminate\Console\Command;  
use Illuminate\Support\Facades\DB;  
use Illuminate\Support\Facades\Log;  
use App\Models\ListPerusahaanTender;  
use App\Models\PesertaTender;  
use App\Models\PemenangLpse;  
use App\Models\PaketPengadaan;  
use Carbon\Carbon;  

class DeduplikasiPerusahaanNew extends Command  
{  
    protected $signature = 'xperusahaan:new-deduplikasi   
        {--dry-run : <PERSON><PERSON><PERSON><PERSON> proses tanpa perubahan}  
        {--batch-size=100 : Jumlah batch proses}  
        {--max-process=1000 : Maksimal entri yang diproses}  
        {--log-path= : Path custom log}  
        {--filter-name= : Filter nama perusahaan}  
    ';  

    protected $description = 'Proses Deduplikasi Data Perusahaan Tender';  

    private $logPath;  
    private $dryRun;  
    private $batchSize;  
    private $maxProcess;  
    private $filterName;  

    private $statsTotal = [  
        'total_duplicates' => 0,  
        'merged_entries' => 0,  
        'skipped_entries' => 0,  
        'deleted_entries' => 0,  
        'failed_merges' => 0  
    ];  

    public function handle()  
    {  
        $startTime = microtime(true);  
        
        $this->initializeOptions();  
        $this->validateOptions();  
        
        $this->info('🔍 Memulai Proses Deduplikasi Perusahaan');  
        $this->newLine();  

        try {  
            $this->processDeduplication();  
            $this->displayResults($startTime);  
        } catch (\Exception $e) {  
            $this->error("❌ Proses Gagal: " . $e->getMessage());  
            Log::error('Deduplikasi Perusahaan Gagal', [  
                'error' => $e->getMessage(),  
                'trace' => $e->getTraceAsString()  
            ]);  
        }  
    }  

    private function initializeOptions()  
    {  
        $this->dryRun = $this->option('dry-run');  
        $this->batchSize = (int) $this->option('batch-size');  
        $this->maxProcess = (int) $this->option('max-process');  
        $this->filterName = $this->option('filter-name');  
        
        $this->logPath = $this->option('log-path')   
            ?? storage_path('logs/perusahaan_deduplikasi_' . now()->format('Y-m-d_H-i-s') . '.log');  
    }  

    private function validateOptions()  
    {  
        if ($this->batchSize <= 0) {  
            throw new \InvalidArgumentException('Batch size harus positif');  
        }  

        if ($this->maxProcess <= 0) {  
            throw new \InvalidArgumentException('Max process harus positif');  
        }  
    }  

    private function processDeduplication()  
    {  
        $duplicateGroups = $this->findDuplicateGroups();  
        
        $this->withProgressBar($duplicateGroups, function ($group) {  
            $this->processDuplicateGroup($group->nama_peserta, $group->npwp);  
        });  
    }  

    private function findDuplicateGroups()  
    {  
        $query = ListPerusahaanTender::select('nama_peserta', 'npwp')  
            ->groupBy('nama_peserta', 'npwp')  
            ->havingRaw('COUNT(*) > 1');  

        if ($this->filterName) {  
            $query->where('nama_peserta', 'LIKE', "%{$this->filterName}%");  
        }  

        return $query->limit($this->maxProcess)->get();  
    }  

    private function processDuplicateGroup($namaPerusahaan, $npwp)  
    {  
        $perusahaanEntries = ListPerusahaanTender::where('nama_peserta', $namaPerusahaan)
            ->where(function ($query) use ($npwp) {
                if ($npwp) {
                    $query->where('npwp', $npwp);
                } else {
                    $query->whereNull('npwp');
                }
            })
            ->orderBy('created_at', 'desc')  
            ->get();  

        if ($perusahaanEntries->count() <= 1) return;  

        $this->statsTotal['total_duplicates'] += $perusahaanEntries->count();  

        $mainEntry = $this->selectMainEntry($perusahaanEntries);  

        foreach ($perusahaanEntries as $entry) {  
            if ($entry->id === $mainEntry->id) continue;  

            $this->mergeEntryData($mainEntry, $entry);  
        }  
    }  

    private function selectMainEntry($entries)  
    {  
        return $entries->sortByDesc(function ($entry) {  
            $score = 0;  
            $score += !empty($entry->npwp) ? 10 : 0;  
            $score += !empty($entry->npwp_blur) ? 5 : 0;  
            $score += !empty($entry->alamat) ? 3 : 0;  
            return $score;  
        })->first();  
    }  

    private function mergeEntryData($mainEntry, $duplicateEntry)  
    {  
        if ($this->dryRun) {  
            $this->statsTotal['skipped_entries']++;  
            return;  
        }  

        DB::beginTransaction();  
        try {  
            $this->updateMainEntryData($mainEntry, $duplicateEntry);  
            $this->updateRelatedTables($mainEntry, $duplicateEntry);  
            
            $duplicateEntry->delete();  
            
            $this->statsTotal['merged_entries']++;  
            $this->statsTotal['deleted_entries']++;  

            DB::commit();  
        } catch (\Exception $e) {  
            DB::rollBack();  
            $this->statsTotal['failed_merges']++;  
            
            Log::channel('deduplikasi')->error('Merge Gagal', [  
                'main_entry_id' => $mainEntry->id,  
                'duplicate_entry_id' => $duplicateEntry->id,  
                'error' => $e->getMessage()  
            ]);  
        }  
    }  

    private function updateMainEntryData($mainEntry, $duplicateEntry)  
    {  
        $updateData = [];  

        if (empty($mainEntry->npwp) && !empty($duplicateEntry->npwp)) {  
            $updateData['npwp'] = $duplicateEntry->npwp;  
        }  

        if (empty($mainEntry->npwp_blur) && !empty($duplicateEntry->npwp_blur)) {  
            $updateData['npwp_blur'] = $duplicateEntry->npwp_blur;  
        }  

        if (empty($mainEntry->alamat) && !empty($duplicateEntry->alamat)) {  
            $updateData['alamat'] = $duplicateEntry->alamat;  
        }  

        if (!empty($updateData)) {  
            $mainEntry->update($updateData);  
        }  
    }  

    private function updateRelatedTables($mainEntry, $duplicateEntry)  
    {  
        PesertaTender::where('id_peserta', $duplicateEntry->id)->update(['id_peserta' => $mainEntry->id]);  
        PemenangLpse::where('id_peserta', $duplicateEntry->id)->update(['id_peserta' => $mainEntry->id]);  
        PaketPengadaan::where('id_penyedia', $duplicateEntry->id)->update(['id_penyedia' => $mainEntry->id]);  
        PaketPengadaan::where('id_pelaksana', $duplicateEntry->id)->update(['id_pelaksana' => $mainEntry->id]);  
    }  

    private function displayResults($startTime)  
    {  
        $duration = round(microtime(true) - $startTime, 2);  

        $this->newLine();  
        $this->info('📊 Laporan Deduplikasi:');  
        $this->table(  
            ['Metrik', 'Jumlah'],  
            [  
                ['Total Duplikat', $this->statsTotal['total_duplicates']],  
                ['Entri Digabung', $this->statsTotal['merged_entries']],  
                ['Entri Dihapus', $this->statsTotal['deleted_entries']],  
                ['Entri Gagal', $this->statsTotal['failed_merges']],  
                ['Waktu Proses', $duration . ' detik']  
            ]  
        );  

        if ($this->dryRun) {  
            $this->warn('⚠️ Mode Dry Run - Tidak ada perubahan aktual');  
        }  

        Log::channel('deduplikasi')->info('Proses Deduplikasi Selesai', $this->statsTotal);  
    }  
}