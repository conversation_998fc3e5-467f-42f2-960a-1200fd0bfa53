<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class TestSlowQuery extends Command
{
    protected $signature = 'test:slow-query';
    protected $description = 'Test slow query logging functionality';

    public function handle()
    {
        $this->info('Testing slow query logging...');
        
        try {
            // Test 1: Simulate a slow query with SLEEP
            $this->info('Test 1: Running intentionally slow query...');
            $result = DB::select('SELECT SLEEP(0.2) as test_sleep');
            $this->info('✓ Slow query executed successfully');
            
            // Test 2: Query with complex operation
            $this->info('Test 2: Running complex query...');
            $result = DB::select('
                SELECT COUNT(*) as total_users, 
                       MAX(created_at) as latest_user,
                       MIN(created_at) as earliest_user
                FROM users 
                WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 YEAR)
            ');
            $this->info('✓ Complex query executed successfully');
            
            // Test 3: Check if log files exist and are writable
            $this->info('Test 3: Checking log files...');
            $logFiles = [
                storage_path('logs/query/slow_query-' . date('Y-m-d') . '.log'),
                storage_path('logs/query/critical_query-' . date('Y-m-d') . '.log'),
            ];
            
            foreach ($logFiles as $logFile) {
                if (file_exists($logFile)) {
                    $this->info("✓ Log file exists: " . basename($logFile));
                    if (is_writable($logFile)) {
                        $this->info("✓ Log file is writable: " . basename($logFile));
                    } else {
                        $this->warn("⚠ Log file is not writable: " . basename($logFile));
                    }
                } else {
                    $this->warn("⚠ Log file does not exist: " . basename($logFile));
                }
            }
            
            // Test 4: Check recent log entries
            $this->info('Test 4: Checking recent log entries...');
            $todayLogFile = storage_path('logs/query/slow_query-' . date('Y-m-d') . '.log');
            if (file_exists($todayLogFile)) {
                $logContent = file_get_contents($todayLogFile);
                $lines = explode("\n", trim($logContent));
                $recentLines = array_slice($lines, -5); // Last 5 lines
                
                if (!empty($recentLines[0])) {
                    $this->info("✓ Found " . count($lines) . " log entries");
                    $this->info("Recent entries:");
                    foreach ($recentLines as $line) {
                        if (!empty(trim($line))) {
                            $this->line("  " . substr($line, 0, 100) . "...");
                        }
                    }
                } else {
                    $this->warn("⚠ No log entries found");
                }
            }
            
            $this->newLine();
            $this->info('🎉 Slow query logging test completed!');
            $this->info('Check the log files for detailed query information.');
            
        } catch (\Exception $e) {
            $this->error('❌ Test failed: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
