<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ListPerusahaanTender;
use Illuminate\Support\Facades\Log;

class UpdateExistingPerusahaanData extends Command
{
    protected $signature = 'perusahaan:update';
    protected $description = 'Update existing perusahaan data with normalized_name, partial_npwp, npwp_blur, and pembeda';

    public function handle()
    {
        $this->info("Starting to update perusahaan data...");
        Log::info("Starting to update perusahaan data...");

        // Ambil semua data perusahaan
        //$perusahaans = ListPerusahaanTender::all();
        $perusahaans = ListPerusahaanTender::where(function ($query) {
            $query->whereNull('pembeda')
                  ->orWhere(\DB::raw('LENGTH(partial_npwp)'), '>', 6);
        })->get();

        $this->info("Total perusahaan to process: " . $perusahaans->count());
        Log::info("Total perusahaan to process: " . $perusahaans->count());

        // Loop melalui setiap perusahaan
        foreach ($perusahaans as $perusahaan) {
            try {
                // Normalisasi nama perusahaan
                $normalizedName = ListPerusahaanTender::normalizeName($perusahaan->nama_peserta);
                $normalizedNoSpace = str_replace(' ', '', $normalizedName);

                // Pisahkan NPWP normal dan masked
                $npwp = $perusahaan->npwp;
                $npwpBlur = null;
                $partialNpwp = null;

                if ($npwp) {
                    if (strpos($npwp, '*') !== false) {
                        // Jika NPWP mengandung *, pindahkan ke npwp_blur
                        if (strlen($npwp) > 18) {
                            $npwpBlur = substr($npwp, 3); // Hapus 3 karakter pertama jika lebih panjang dari 18
                        } elseif (preg_match('/^N[0-9]\*/', $npwp)) {
                            $npwpBlur = substr($npwp, 1); // Hapus "N" di awal jika memenuhi pola "N0*"
                        } else {
                            $npwpBlur = $npwp;
                        }
                        $npwp = null; // Kosongkan npwp jika sudah masked
                    } else {
                        // Jika NPWP normal, buat versi masked
                        if (stripos($npwp, 'NONPWP') === 0) {
                            $npwp = null; // Jika mengandung NONPWP, set ke null
                        } else {
                            $partialNpwp = ListPerusahaanTender::extractPartialNpwp($npwp);
                            $npwpBlur = $this->maskNpwp($npwp); // Masking NPWP
                        }
                    }
                }

                // Buat nilai unik untuk field pembeda
                $pembeda = $normalizedNoSpace . ($partialNpwp ? $partialNpwp : '');

                // Update kolom yang diperlukan
                $perusahaan->update([
                    'normalized_name' => $normalizedName,
                    'partial_npwp'    => $partialNpwp,
                    'npwp'            => $npwp,
                    'npwp_blur'       => $npwpBlur,
                    'pembeda'         => $pembeda,
                ]);

                $this->info("Updated perusahaan: {$perusahaan->nama_peserta}");
            } catch (\Exception $e) {
                $this->error("Error updating perusahaan {$perusahaan->id}: " . $e->getMessage());
            }
        }

        $this->info("Perusahaan data update completed.");
    }

    function maskNpwp($npwp) {
        return preg_replace_callback(
            '/^(\d)\d\.(\d)\d\d\.(\d)\d\d\.(\d)-(\d)(\d)(\d)\.(\d)(\d)(\d)$/',
            function ($matches) {
                return "{$matches[1]}*.{$matches[2]}**.{$matches[3]}**.*-*{$matches[6]}{$matches[7]}.**{$matches[10]}";
            },
            $npwp
        );
    }
}
