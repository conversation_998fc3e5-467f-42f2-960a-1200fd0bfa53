<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ApplyOptimizations extends Command
{
    protected $signature = 'query:apply-optimizations 
                           {--analyze-tables : Run ANALYZE TABLE on large tables}
                           {--optimize-tables : Run OPTIMIZE TABLE on large tables}
                           {--dry-run : Show what would be done without executing}';

    protected $description = 'Apply database optimizations based on slow query analysis';

    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $analyzeTables = $this->option('analyze-tables');
        $optimizeTables = $this->option('optimize-tables');
        
        $this->info('🚀 Applying database optimizations...');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }
        
        $this->newLine();

        if ($analyzeTables) {
            $this->analyzeTableStatistics($dryRun);
        }
        
        if ($optimizeTables) {
            $this->optimizeTableStructure($dryRun);
        }
        
        $this->showOptimizationTips();
        
        $this->newLine();
        $this->info('✅ Database optimization process completed!');
    }

    private function analyzeTableStatistics(bool $dryRun): void
    {
        $this->info('📊 Analyzing table statistics...');
        
        $largeTables = [
            'list_perusahaan_tender',
            'peserta_tender', 
            'jadwal',
            'data_sirup',
            'ekatalog_paket_pengadaan',
            'ekatalog_produk'
        ];
        
        foreach ($largeTables as $table) {
            try {
                if ($dryRun) {
                    $this->line("Would run: ANALYZE TABLE {$table}");
                } else {
                    $this->line("Analyzing table: {$table}");
                    $result = DB::statement("ANALYZE TABLE {$table}");
                    
                    if ($result) {
                        $this->info("✓ {$table} analyzed successfully");
                    } else {
                        $this->warn("⚠ Failed to analyze {$table}");
                    }
                }
            } catch (\Exception $e) {
                $this->error("❌ Error analyzing {$table}: " . $e->getMessage());
            }
        }
    }

    private function optimizeTableStructure(bool $dryRun): void
    {
        $this->info('🔧 Optimizing table structure...');
        
        $tablesToOptimize = [
            'peserta_tender',
            'jadwal', 
            'list_perusahaan_tender'
        ];
        
        foreach ($tablesToOptimize as $table) {
            try {
                if ($dryRun) {
                    $this->line("Would run: OPTIMIZE TABLE {$table}");
                } else {
                    $this->line("Optimizing table: {$table}");
                    
                    // Check table size before optimization
                    $sizeBefore = $this->getTableSize($table);
                    
                    $result = DB::statement("OPTIMIZE TABLE {$table}");
                    
                    if ($result) {
                        $sizeAfter = $this->getTableSize($table);
                        $this->info("✓ {$table} optimized successfully");
                        $this->line("  Size before: {$sizeBefore['data_mb']} MB data, {$sizeBefore['index_mb']} MB index");
                        $this->line("  Size after:  {$sizeAfter['data_mb']} MB data, {$sizeAfter['index_mb']} MB index");
                    } else {
                        $this->warn("⚠ Failed to optimize {$table}");
                    }
                }
            } catch (\Exception $e) {
                $this->error("❌ Error optimizing {$table}: " . $e->getMessage());
            }
        }
    }

    private function getTableSize(string $table): array
    {
        try {
            $result = DB::select("
                SELECT 
                    ROUND(data_length / 1024 / 1024, 2) as data_mb,
                    ROUND(index_length / 1024 / 1024, 2) as index_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = ?
            ", [$table]);
            
            return [
                'data_mb' => $result[0]->data_mb ?? 0,
                'index_mb' => $result[0]->index_mb ?? 0
            ];
        } catch (\Exception $e) {
            return ['data_mb' => 0, 'index_mb' => 0];
        }
    }

    private function showOptimizationTips(): void
    {
        $this->newLine();
        $this->info('💡 OPTIMIZATION TIPS:');
        $this->newLine();
        
        $this->line('🔍 <comment>Replace LIKE queries with FULLTEXT search:</comment>');
        $this->line('   // Instead of:');
        $this->line('   WHERE normalized_name LIKE "%search%"');
        $this->line('   // Use:');
        $this->line('   WHERE MATCH(normalized_name) AGAINST("search" IN BOOLEAN MODE)');
        $this->newLine();
        
        $this->line('📦 <comment>Use batch operations:</comment>');
        $this->line('   // Instead of individual inserts:');
        $this->line('   foreach($data as $item) { DB::table("table")->insert($item); }');
        $this->line('   // Use batch insert:');
        $this->line('   DB::table("table")->insert($batchData);');
        $this->newLine();
        
        $this->line('⚡ <comment>Optimize command_progress updates:</comment>');
        $this->line('   // Reduce update frequency');
        $this->line('   // Use updateOrCreate with specific conditions');
        $this->line('   // Consider caching progress data');
        $this->newLine();
        
        $this->line('📊 <comment>Monitor performance:</comment>');
        $this->line('   php artisan query:analyze-slow --days=7');
        $this->line('   php artisan query:optimize --dry-run');
        $this->line('   tail -f storage/logs/query/slow_query-$(date +%Y-%m-%d).log');
        $this->newLine();
        
        $this->line('🔄 <comment>Regular maintenance:</comment>');
        $this->line('   php artisan query:apply-optimizations --analyze-tables');
        $this->line('   ./create-log-files.sh  # Ensure log permissions');
        $this->line('   ./docker-log-permissions.sh  # Fix permissions');
    }
}
