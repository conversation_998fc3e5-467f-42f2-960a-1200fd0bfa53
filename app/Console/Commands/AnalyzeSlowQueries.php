<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class AnalyzeSlowQueries extends Command
{
    protected $signature = 'query:analyze-slow 
                           {--days=7 : Number of days to analyze}
                           {--threshold=100 : Minimum execution time in ms}
                           {--limit=20 : Number of top queries to show}';

    protected $description = 'Analyze slow queries from logs and provide optimization recommendations';

    public function handle()
    {
        $days = $this->option('days');
        $threshold = $this->option('threshold');
        $limit = $this->option('limit');

        $this->info("Analyzing slow queries from the last {$days} days...");
        $this->info("Threshold: {$threshold}ms, Showing top {$limit} queries");
        $this->newLine();

        $logFiles = $this->getLogFiles($days);
        $slowQueries = $this->parseLogFiles($logFiles, $threshold);
        
        if (empty($slowQueries)) {
            $this->warn('No slow queries found in the specified period.');
            return;
        }

        $this->displayTopSlowQueries($slowQueries, $limit);
        $this->displayQueryPatterns($slowQueries);
        $this->displayOptimizationRecommendations($slowQueries);
        $this->checkDatabaseIndexes();
    }

    private function getLogFiles(int $days): array
    {
        $logFiles = [];
        $logPath = storage_path('logs/query');
        
        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $filename = "slow_query-{$date}.log";
            $filepath = "{$logPath}/{$filename}";
            
            if (File::exists($filepath)) {
                $logFiles[] = $filepath;
            }
        }
        
        return $logFiles;
    }

    private function parseLogFiles(array $logFiles, int $threshold): array
    {
        $slowQueries = [];
        
        foreach ($logFiles as $logFile) {
            $content = File::get($logFile);
            $lines = explode("\n", $content);
            
            foreach ($lines as $line) {
                if (preg_match('/Query Lambat \[(\d+(?:\.\d+)?)ms\]: (.+) \| Bindings: (.*)/', $line, $matches)) {
                    $executionTime = (float) $matches[1];
                    
                    if ($executionTime >= $threshold) {
                        $sql = $matches[2];
                        $bindings = $matches[3];
                        
                        $queryPattern = $this->normalizeQuery($sql);
                        
                        if (!isset($slowQueries[$queryPattern])) {
                            $slowQueries[$queryPattern] = [
                                'pattern' => $queryPattern,
                                'original_sql' => $sql,
                                'count' => 0,
                                'total_time' => 0,
                                'max_time' => 0,
                                'min_time' => PHP_FLOAT_MAX,
                                'avg_time' => 0,
                                'examples' => []
                            ];
                        }
                        
                        $slowQueries[$queryPattern]['count']++;
                        $slowQueries[$queryPattern]['total_time'] += $executionTime;
                        $slowQueries[$queryPattern]['max_time'] = max($slowQueries[$queryPattern]['max_time'], $executionTime);
                        $slowQueries[$queryPattern]['min_time'] = min($slowQueries[$queryPattern]['min_time'], $executionTime);
                        $slowQueries[$queryPattern]['avg_time'] = $slowQueries[$queryPattern]['total_time'] / $slowQueries[$queryPattern]['count'];
                        
                        if (count($slowQueries[$queryPattern]['examples']) < 3) {
                            $slowQueries[$queryPattern]['examples'][] = [
                                'sql' => $sql,
                                'bindings' => $bindings,
                                'time' => $executionTime
                            ];
                        }
                    }
                }
            }
        }
        
        // Sort by total impact (count * avg_time)
        uasort($slowQueries, function ($a, $b) {
            $impactA = $a['count'] * $a['avg_time'];
            $impactB = $b['count'] * $b['avg_time'];
            return $impactB <=> $impactA;
        });
        
        return $slowQueries;
    }

    private function normalizeQuery(string $sql): string
    {
        // Replace specific values with placeholders for pattern matching
        $normalized = preg_replace('/\b\d+\b/', '?', $sql);
        $normalized = preg_replace("/'[^']*'/", '?', $normalized);
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        return trim($normalized);
    }

    private function displayTopSlowQueries(array $slowQueries, int $limit): void
    {
        $this->info('🐌 TOP SLOW QUERIES:');
        $this->newLine();
        
        $headers = ['Pattern', 'Count', 'Avg Time (ms)', 'Max Time (ms)', 'Total Impact'];
        $rows = [];
        
        $count = 0;
        foreach ($slowQueries as $query) {
            if ($count >= $limit) break;
            
            $impact = round($query['count'] * $query['avg_time'], 2);
            $rows[] = [
                substr($query['pattern'], 0, 60) . (strlen($query['pattern']) > 60 ? '...' : ''),
                $query['count'],
                round($query['avg_time'], 2),
                round($query['max_time'], 2),
                $impact
            ];
            $count++;
        }
        
        $this->table($headers, $rows);
    }

    private function displayQueryPatterns(array $slowQueries): void
    {
        $this->newLine();
        $this->info('📊 QUERY PATTERNS ANALYSIS:');
        $this->newLine();
        
        $patterns = [
            'LIKE queries' => 0,
            'UPDATE queries' => 0,
            'SELECT queries' => 0,
            'INSERT queries' => 0,
            'JOIN queries' => 0,
            'GROUP BY queries' => 0,
            'ORDER BY queries' => 0,
        ];
        
        foreach ($slowQueries as $query) {
            $sql = strtoupper($query['pattern']);
            
            if (strpos($sql, 'LIKE') !== false) $patterns['LIKE queries'] += $query['count'];
            if (strpos($sql, 'UPDATE') !== false) $patterns['UPDATE queries'] += $query['count'];
            if (strpos($sql, 'SELECT') !== false) $patterns['SELECT queries'] += $query['count'];
            if (strpos($sql, 'INSERT') !== false) $patterns['INSERT queries'] += $query['count'];
            if (strpos($sql, 'JOIN') !== false) $patterns['JOIN queries'] += $query['count'];
            if (strpos($sql, 'GROUP BY') !== false) $patterns['GROUP BY queries'] += $query['count'];
            if (strpos($sql, 'ORDER BY') !== false) $patterns['ORDER BY queries'] += $query['count'];
        }
        
        foreach ($patterns as $pattern => $count) {
            if ($count > 0) {
                $this->line("• {$pattern}: {$count} occurrences");
            }
        }
    }

    private function displayOptimizationRecommendations(array $slowQueries): void
    {
        $this->newLine();
        $this->info('💡 OPTIMIZATION RECOMMENDATIONS:');
        $this->newLine();
        
        $recommendations = [];
        
        foreach ($slowQueries as $query) {
            $sql = strtoupper($query['pattern']);
            
            if (strpos($sql, 'LIKE') !== false && strpos($sql, '%') !== false) {
                $recommendations[] = "• Consider full-text search or better indexing for LIKE queries";
            }
            
            if (strpos($sql, 'NORMALIZED_NAME') !== false) {
                $recommendations[] = "• Add index on normalized_name column if not exists";
            }
            
            if (strpos($sql, 'UPDATE') !== false && $query['avg_time'] > 200) {
                $recommendations[] = "• Review UPDATE queries - consider batch updates or better WHERE conditions";
            }
            
            if (strpos($sql, 'SELECT *') !== false) {
                $recommendations[] = "• Avoid SELECT * - specify only needed columns";
            }
        }
        
        $recommendations = array_unique($recommendations);
        
        if (empty($recommendations)) {
            $this->line('No specific recommendations found.');
        } else {
            foreach ($recommendations as $recommendation) {
                $this->line($recommendation);
            }
        }
    }

    private function checkDatabaseIndexes(): void
    {
        $this->newLine();
        $this->info('🔍 DATABASE INDEX ANALYSIS:');
        $this->newLine();
        
        try {
            // Check for missing indexes on commonly queried columns
            $tables = ['list_perusahaan_tender', 'ekatalog_produk', 'ekatalog_transaksi_paket'];
            
            foreach ($tables as $table) {
                $indexes = DB::select("SHOW INDEX FROM {$table}");
                $this->line("Table: {$table}");
                
                foreach ($indexes as $index) {
                    $this->line("  • {$index->Key_name} on {$index->Column_name}");
                }
                $this->newLine();
            }
        } catch (\Exception $e) {
            $this->error("Could not analyze database indexes: " . $e->getMessage());
        }
    }
}
