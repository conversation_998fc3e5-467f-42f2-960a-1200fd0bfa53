<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class OptimizeSlowQueries extends Command
{
    protected $signature = 'query:optimize
                           {--dry-run : Show what would be optimized without making changes}';

    protected $description = 'Apply optimizations for commonly slow queries';

    public function handle()
    {
        $dryRun = $this->option('dry-run');

        $this->info('🚀 Starting query optimization process...');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        $this->newLine();

        $this->optimizeFulltextSearch($dryRun);
        $this->optimizeBatchOperations($dryRun);
        $this->optimizeCommandProgress($dryRun);
        $this->analyzeTableStats($dryRun);

        $this->newLine();
        $this->info('✅ Query optimization process completed!');
    }

    private function optimizeFulltextSearch(bool $dryRun): void
    {
        $this->info('🔍 Optimizing fulltext search capabilities...');

        // Check if fulltext indexes are being used effectively
        $likeQueries = DB::select("
            SELECT COUNT(*) as count
            FROM information_schema.processlist
            WHERE info LIKE '%normalized_name%LIKE%'
        ");

        if ($likeQueries[0]->count > 0) {
            $this->warn("Found active LIKE queries on normalized_name");
            $this->info("💡 Consider using MATCH AGAINST for better performance:");
            $this->line("   SELECT * FROM list_perusahaan_tender");
            $this->line("   WHERE MATCH(normalized_name) AGAINST('search_term' IN BOOLEAN MODE)");
        }

        // Check fulltext index usage
        $this->info("✓ Fulltext indexes are available for optimization");
    }

    private function optimizeBatchOperations(bool $dryRun): void
    {
        $this->info('📦 Analyzing batch operation opportunities...');

        // Analyze table sizes for batch operation candidates
        $largeTables = DB::select("
            SELECT
                table_name,
                table_rows,
                ROUND(data_length / 1024 / 1024, 2) as data_size_mb
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name IN ('peserta_tender', 'jadwal', 'command_progress')
            ORDER BY table_rows DESC
        ");

        if (!empty($largeTables)) {
            $this->info("Tables suitable for batch operations:");
            foreach ($largeTables as $table) {
                $this->line("  • {$table->table_name}: " . number_format($table->table_rows) . " rows ({$table->data_size_mb} MB)");
            }
        }

        // Check for recent high-frequency operations from our slow query logs
        $logFile = storage_path('logs/query/slow_query-' . date('Y-m-d') . '.log');
        if (file_exists($logFile)) {
            $this->info("Analyzing today's slow query patterns...");
            $content = file_get_contents($logFile);

            $insertCount = substr_count($content, 'INSERT INTO');
            $updateCount = substr_count($content, 'UPDATE');

            $this->line("  • INSERT operations in slow log: {$insertCount}");
            $this->line("  • UPDATE operations in slow log: {$updateCount}");
        }

        $this->info("💡 Recommendations:");
        $this->line("  • Use batch inserts for peserta_tender");
        $this->line("  • Use batch inserts for jadwal");
        $this->line("  • Consider using INSERT IGNORE for duplicate handling");
        $this->line("  • Use updateOrCreate with batch processing");
    }

    private function optimizeCommandProgress(bool $dryRun): void
    {
        $this->info('⚡ Optimizing command_progress updates...');

        // Check command_progress table size and update frequency
        $stats = DB::select("
            SELECT COUNT(*) as total_records,
                   COUNT(DISTINCT command_name) as unique_commands,
                   MAX(updated_at) as last_update
            FROM command_progress
        ");

        if (!empty($stats)) {
            $stat = $stats[0];
            $this->info("Command Progress Stats:");
            $this->line("  • Total records: {$stat->total_records}");
            $this->line("  • Unique commands: {$stat->unique_commands}");
            $this->line("  • Last update: {$stat->last_update}");
        }

        // Check for frequently updated commands
        $frequentUpdates = DB::select("
            SELECT command_name,
                   COUNT(*) as update_count,
                   MAX(updated_at) as last_update
            FROM command_progress
            WHERE updated_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            GROUP BY command_name
            ORDER BY update_count DESC
            LIMIT 5
        ");

        if (!empty($frequentUpdates)) {
            $this->info("Most frequently updated commands:");
            foreach ($frequentUpdates as $cmd) {
                $this->line("  • {$cmd->command_name}: {$cmd->update_count} updates");
            }
        }

        $this->info("💡 Recommendations:");
        $this->line("  • Reduce update frequency for progress tracking");
        $this->line("  • Use batch updates where possible");
        $this->line("  • Consider caching progress data");
    }

    private function analyzeTableStats(bool $dryRun): void
    {
        $this->info('📊 Analyzing table statistics...');

        $tables = [
            'list_perusahaan_tender',
            'peserta_tender',
            'command_progress',
            'jadwal'
        ];

        foreach ($tables as $table) {
            try {
                $stats = DB::select("
                    SELECT
                        table_rows as row_count,
                        ROUND(data_length / 1024 / 1024, 2) as data_size_mb,
                        ROUND(index_length / 1024 / 1024, 2) as index_size_mb
                    FROM information_schema.tables
                    WHERE table_schema = DATABASE()
                    AND table_name = ?
                ", [$table]);

                if (!empty($stats)) {
                    $stat = $stats[0];
                    $this->line("📋 {$table}:");
                    $this->line("    Rows: " . number_format($stat->row_count));
                    $this->line("    Data: {$stat->data_size_mb} MB");
                    $this->line("    Index: {$stat->index_size_mb} MB");
                }
            } catch (\Exception $e) {
                $this->warn("Could not analyze {$table}: " . $e->getMessage());
            }
        }

        $this->newLine();
        $this->info("💡 General Recommendations:");
        $this->line("  • Monitor table growth and consider partitioning for large tables");
        $this->line("  • Regular ANALYZE TABLE to update statistics");
        $this->line("  • Consider archiving old data");
    }
}
