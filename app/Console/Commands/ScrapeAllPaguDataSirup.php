<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DataSirup;
use GuzzleHttp\Client;
use Symfony\Component\DomCrawler\Crawler;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class ScrapeAllPaguDataSirup extends Command
{
    use CommandManagementTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixkan:all-pagu-datasirup
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}
                            {--lock-name= : Custom lock name for this command instance}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scrape ulang data total pagu bermasalah pada DataSirup';

    /**
     * HTTP Client
     *
     * @var Client
     */
    protected $client;

    /**
     * Base URL
     *
     * @var string
     */
    protected $baseUrl = 'https://hostestapp.xyz/?url=https://sirup.lkpp.go.id';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => 120.0,
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (compatible; PaguDataSirupScraper/1.0)',
            ],
        ]);
    }

    /**
     * Get elapsed time in seconds
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTime(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            $message = 'Memulai proses scraping ulang total pagu bermasalah...';
            $this->info($message);

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                $message,
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $dataCount = DataSirup::where('is_processing', 0)->count();

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                5,
                100,
                "Ditemukan {$dataCount} data yang perlu diproses",
                [
                    'data_count' => $dataCount,
                    'stage' => 'counting_data',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            if ($dataCount === 0) {
                $message = 'Tidak ada data yang perlu diproses.';
                $this->warn($message);

                // Mark command as completed
                CommandProgress::markAsCompleted(
                    $this->getName(),
                    $message,
                    [
                        'data_count' => 0,
                        'execution_time' => $this->getElapsedTime($startTime),
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage()
                    ]
                );

                return SymfonyCommand::SUCCESS;
            }

            $processedCount = 0;
            $successCount = 0;
            $errorCount = 0;
            $updatedCount = 0;

            $bar = $this->output->createProgressBar($dataCount); // Membuat progress bar
            $bar->start();

            DataSirup::where('is_processing', 0)
                ->chunkById(1000, function ($dataSirups) use (&$processedCount, &$successCount, &$errorCount, &$updatedCount, $bar, $dataCount, $startTime) {
                    $dataSirups = $dataSirups->shuffle();

                    foreach ($dataSirups as $dataSirup) {
                        $processedCount++;
                        $progress = min(95, 5 + round(($processedCount / $dataCount) * 90));

                        // Update progress
                        CommandProgress::updateProgress(
                            $this->getName(),
                            $progress,
                            100,
                            "Memproses data {$processedCount}/{$dataCount}",
                            [
                                'processed' => $processedCount,
                                'total' => $dataCount,
                                'success' => $successCount,
                                'error' => $errorCount,
                                'updated' => $updatedCount,
                                'stage' => 'processing_data',
                                'memory_usage' => $this->getMemoryUsage(),
                                'elapsed_time' => $this->getElapsedTime($startTime)
                            ]
                        );

                        DB::beginTransaction();

                        try {
                            $record = DataSirup::where('id', $dataSirup->id)
                                ->where('is_processing', 0)
                                ->lockForUpdate()
                                ->first();

                            if (!$record) {
                                DB::rollBack();
                                $this->info("RUP $dataSirup->kode_rup sudah diproses");
                                continue;
                            }

                            DB::commit();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            $errorMessage = "Gagal mengunci record ID {$dataSirup->id}: " . $e->getMessage();
                            $this->error($errorMessage);
                            Log::error($errorMessage);

                            // Log system error
                            $this->logSystemError($e, "handle method - locking record ID: {$dataSirup->id}");

                            $errorCount++;
                            $bar->advance();
                            continue;
                        }

                        $url = "/sirup/rup/detailPaketPenyedia2020?idPaket={$record->kode_rup}";
                        $fullUrl = $this->baseUrl . $url;

                        try {
                            $detailMessage = "[$processedCount] Mengambil data dari URL: {$fullUrl}";
                            $this->updateProgressBarWithDetail($bar, $detailMessage);

                            $response = $this->client->request('GET', $fullUrl);
                            $html = (string) $response->getBody();
                            $crawler = new Crawler($html);

                            $totalPagu = $this->extractTotalPagu($crawler);

                            $oldTotalPagu = $record->total_pagu;

                            if ($totalPagu != $oldTotalPagu && $totalPagu != 0) {
                                $record->update(['total_pagu' => $totalPagu]);
                                $detailMessage = "[$processedCount] Total pagu untuk RUP {$record->kode_rup} diperbarui dari {$oldTotalPagu} menjadi {$totalPagu}.";
                                $updatedCount++;
                            } else {
                                $detailMessage = "[$processedCount] Total pagu untuk RUP {$record->kode_rup} tidak berubah ({$totalPagu}).";
                            }

                            $record->update(['is_processing' => 1]);
                            $successCount++;
                        } catch (\Exception $e) {
                            $errorMessage = "Error scraping kode RUP {$record->kode_rup}: " . $e->getMessage();
                            Log::error($errorMessage);

                            // Log system error
                            $this->logSystemError($e, "handle method - scraping RUP: {$record->kode_rup}");

                            $record->update(['is_processing' => 0]); // Reset is_processing jika gagal
                            $detailMessage = "[$processedCount] Gagal mengambil data untuk RUP {$record->kode_rup}. Error: " . $e->getMessage();
                            $errorCount++;
                        }

                        $this->updateProgressBarWithDetail($bar, $detailMessage);
                        usleep(100000);
                        $bar->advance();
                        $this->newline(2);
                    }
                });

            $bar->finish();
            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            $message = "\nProses scraping selesai. Total data diproses: {$processedCount}, berhasil: {$successCount}, error: {$errorCount}, diperbarui: {$updatedCount} dalam " . round($executionTime, 2) . " detik.";

            $this->info($message);

            // Mark command as completed
            CommandProgress::markAsCompleted(
                $this->getName(),
                $message,
                [
                    'total_processed' => $processedCount,
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'updated_count' => $updatedCount,
                    'execution_time' => round($executionTime, 2),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());
            Log::error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }

    /**
     * Extract total pagu from HTML content
     *
     * @param Crawler $crawler
     * @return float|null
     */
    protected function extractTotalPagu(Crawler $crawler)
    {
        $totalPagu = null;

        $crawler->filter('table.table > tr')->each(function (Crawler $tr) use (&$totalPagu) {
            $tds = $tr->filter('td');
            if ($tds->count() < 2) {
                return;
            }

            $label = trim($tds->eq(0)->text());
            $value = trim($tds->eq(1)->text());

            if ($label === 'Total Pagu') {
                $totalPagu = $this->parseRupiah($value);
            }
        });

        return $totalPagu;
    }

    /**
     * Parse rupiah value
     *
     * @param string $value
     * @return float
     */
    protected function parseRupiah($value)
    {
        $value = preg_replace('/[^\d-]/', '', $value);
        return (float) $value;
    }

    /**
     * Update progress bar with detail message
     *
     * @param $bar
     * @param string $detailMessage
     */
    protected function updateProgressBarWithDetail($bar, $detailMessage)
    {
        $bar->clear();
        $this->line($detailMessage);
        $bar->display();
    }
}
