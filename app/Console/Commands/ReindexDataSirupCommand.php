<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DataSirup;
use <PERSON><PERSON>\Scout\Searchable;
use Meilisearch\Client;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class ReindexDataSirupCommand extends Command
{
    use Searchable;
    use CommandManagementTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scout:reindex-datasirup
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}
                            {--lock-name= : Custom lock name for this command instance}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reindex DataSirup model to Meilisearch with proper settings';

    /**
     * Get elapsed time in seconds
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTime(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            $message = 'Starting reindexing DataSirup to Meilisearch...';
            $this->info($message);

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                $message,
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Get Meilisearch client
            $host = config('scout.meilisearch.host');
            $key = config('scout.meilisearch.key');
            $client = new Client($host, $key);

            // Get the index name
            $indexName = (new DataSirup())->searchableAs();
            $this->info("Using index: {$indexName}");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                5,
                100,
                "Using index: {$indexName}",
                [
                    'index_name' => $indexName,
                    'stage' => 'init',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            try {
                // Check if index exists and delete it
                try {
                    $this->info("Checking if index exists: {$indexName}");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        10,
                        100,
                        "Checking if index exists: {$indexName}",
                        [
                            'index_name' => $indexName,
                            'stage' => 'check_index',
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    $indexes = $client->getIndexes();
                    $indexExists = false;

                    foreach ($indexes as $index) {
                        if ($index->getUid() === $indexName) {
                            $indexExists = true;
                            break;
                        }
                    }

                    if ($indexExists) {
                        $this->info("Deleting existing index: {$indexName}");

                        // Update progress
                        CommandProgress::updateProgress(
                            $this->getName(),
                            15,
                            100,
                            "Deleting existing index: {$indexName}",
                            [
                                'index_name' => $indexName,
                                'stage' => 'delete_index',
                                'memory_usage' => $this->getMemoryUsage()
                            ]
                        );

                        $client->index($indexName)->delete();
                    }
                } catch (\Exception $e) {
                    $warningMessage = "Error checking index: " . $e->getMessage();
                    $this->warn($warningMessage);

                    // Log system error but continue
                    $this->logSystemError($e, "handle method - checking index: {$indexName}");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        15,
                        100,
                        $warningMessage,
                        [
                            'index_name' => $indexName,
                            'stage' => 'check_index_error',
                            'error' => $e->getMessage(),
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    // Continue anyway
                }

                // Create the index
                $this->info("Creating index: {$indexName}");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    20,
                    100,
                    "Creating index: {$indexName}",
                    [
                        'index_name' => $indexName,
                        'stage' => 'create_index',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                try {
                    $client->createIndex($indexName, ['primaryKey' => 'kode_rup']);
                } catch (\Exception $e) {
                    // If error is about index already existing, continue
                    if (strpos($e->getMessage(), 'already exists') !== false) {
                        $warningMessage = "Index already exists, continuing with configuration";
                        $this->warn($warningMessage);

                        // Update progress
                        CommandProgress::updateProgress(
                            $this->getName(),
                            25,
                            100,
                            $warningMessage,
                            [
                                'index_name' => $indexName,
                                'stage' => 'index_exists',
                                'memory_usage' => $this->getMemoryUsage()
                            ]
                        );
                    } else {
                        throw $e;
                    }
                }

                // Configure the index
                $this->info("Configuring index settings...");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    30,
                    100,
                    "Configuring index settings...",
                    [
                        'index_name' => $indexName,
                        'stage' => 'configure_index',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                try {
                    // Set filterable attributes
                    $this->info("Setting filterable attributes...");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        35,
                        100,
                        "Setting filterable attributes...",
                        [
                            'index_name' => $indexName,
                            'stage' => 'set_filterable',
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    $client->index($indexName)->updateFilterableAttributes([
                        'tahun_anggaran',
                        'nama_klpd',
                        'metode_pemilihan',
                        'total_pagu',
                        'jenis_pengadaan',
                        'produk_dalam_negeri',
                        'umkm'
                    ]);

                    // Set sortable attributes
                    $this->info("Setting sortable attributes...");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        40,
                        100,
                        "Setting sortable attributes...",
                        [
                            'index_name' => $indexName,
                            'stage' => 'set_sortable',
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    $client->index($indexName)->updateSortableAttributes([
                        'tahun_anggaran',
                        'total_pagu',
                        'tanggal_paket_diumumkan'
                    ]);

                    // Set searchable attributes
                    $this->info("Setting searchable attributes...");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        45,
                        100,
                        "Setting searchable attributes...",
                        [
                            'index_name' => $indexName,
                            'stage' => 'set_searchable',
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    $client->index($indexName)->updateSearchableAttributes([
                        'kode_rup',
                        'nama_paket',
                        'nama_klpd',
                        'satuan_kerja',
                        'lokasi_pekerjaan',
                        'uraian_pekerjaan',
                        'spesifikasi_pekerjaan'
                    ]);

                    // Set ranking rules
                    $this->info("Setting ranking rules...");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        50,
                        100,
                        "Setting ranking rules...",
                        [
                            'index_name' => $indexName,
                            'stage' => 'set_ranking',
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    $client->index($indexName)->updateRankingRules([
                        'words',
                        'typo',
                        'proximity',
                        'attribute',
                        'sort',
                        'exactness'
                    ]);
                } catch (\Exception $e) {
                    $warningMessage = "Error configuring index: " . $e->getMessage();
                    $this->warn($warningMessage);

                    // Log system error but continue
                    $this->logSystemError($e, "handle method - configuring index: {$indexName}");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        50,
                        100,
                        $warningMessage,
                        [
                            'index_name' => $indexName,
                            'stage' => 'configure_error',
                            'error' => $e->getMessage(),
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    // Continue anyway
                }

                // Import the data in chunks
                $this->info("Importing data in chunks...");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    55,
                    100,
                    "Importing data in chunks...",
                    [
                        'index_name' => $indexName,
                        'stage' => 'import_data',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                // Menggunakan chunk yang lebih kecil untuk menghindari masalah memori
                $chunkSize = 200;

                // Menggunakan query builder untuk mengoptimalkan performa
                $totalRecords = DataSirup::count();
                $totalChunks = ceil($totalRecords / $chunkSize);

                $this->info("Total records: {$totalRecords}, Total chunks: {$totalChunks}");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    60,
                    100,
                    "Total records: {$totalRecords}, Total chunks: {$totalChunks}",
                    [
                        'index_name' => $indexName,
                        'total_records' => $totalRecords,
                        'total_chunks' => $totalChunks,
                        'chunk_size' => $chunkSize,
                        'stage' => 'count_chunks',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $bar = $this->output->createProgressBar($totalChunks);
                $bar->start();

                $chunkCount = 0;
                $lastId = 0;
                $processedCount = 0;

                // Menggunakan pendekatan cursor dengan ID untuk mengurangi penggunaan memori
                while ($processedCount < $totalRecords) {
                    $records = DataSirup::where('id', '>', $lastId)
                        ->orderBy('id')
                        ->limit($chunkSize)
                        ->get();

                    if ($records->isEmpty()) {
                        break;
                    }

                    $chunkCount++;
                    $progress = min(95, 60 + round(($chunkCount / $totalChunks) * 35));
                    $lastId = $records->last()->id;
                    $processedCount += $records->count();

                    // Menampilkan informasi memori untuk debugging
                    $memoryUsage = $this->getMemoryUsage();
                    $peakMemoryUsage = $this->getPeakMemoryUsage();
                    $elapsedTime = $this->getElapsedTime($startTime);

                    $this->info("Memory: {$memoryUsage}MB, Peak: {$peakMemoryUsage}MB, Time: {$elapsedTime}s");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        $progress,
                        100,
                        "Processing chunk {$chunkCount}/{$totalChunks} - {$processedCount}/{$totalRecords} records",
                        [
                            'current_chunk' => $chunkCount,
                            'total_chunks' => $totalChunks,
                            'processed_records' => $processedCount,
                            'total_records' => $totalRecords,
                            'stage' => 'processing_chunk',
                            'memory_usage' => $memoryUsage,
                            'peak_memory_usage' => $peakMemoryUsage,
                            'elapsed_time' => $elapsedTime
                        ]
                    );

                    // Mengindeks batch saat ini
                    $records->searchable();

                    // Membebaskan memori
                    unset($records);

                    // Memaksa garbage collection
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }

                    $bar->advance();

                    // Menambahkan jeda kecil untuk mengurangi beban pada server
                    if ($chunkCount % 5 === 0) {
                        sleep(2);
                    }
                }

                $bar->finish();
                $this->newLine();

                $endTime = microtime(true);
                $executionTime = $endTime - $startTime;
                $message = "Reindexing completed successfully in " . round($executionTime, 2) . " seconds!";

                $this->info($message);

                // Mark command as completed
                CommandProgress::markAsCompleted(
                    $this->getName(),
                    $message,
                    [
                        'index_name' => $indexName,
                        'total_records' => $totalRecords,
                        'total_chunks' => $totalChunks,
                        'execution_time' => round($executionTime, 2),
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage()
                    ]
                );

            } catch (\Exception $e) {
                $errorMessage = "Error during reindexing: " . $e->getMessage();
                $this->error($errorMessage);

                // Log system error
                $this->logSystemError($e, "handle method - reindexing process");

                // Mark command as failed
                CommandProgress::markAsFailed(
                    $this->getName(),
                    $errorMessage,
                    [
                        'index_name' => $indexName,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage()
                    ]
                );

                return SymfonyCommand::FAILURE;
            }

            return SymfonyCommand::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }
}
