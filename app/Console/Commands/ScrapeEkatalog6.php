<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use GuzzleHttp\Client;
use App\Models\Ekatalog6;
use App\Models\Klpd;
use App\Models\ListPerusahaanTender;
use Exception;
use Illuminate\Support\Facades\Log;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class ScrapeEkatalog6 extends Command
{
    use CommandManagementTrait;

    protected $signature = 'scrape:ekatalog6
                            {--year=2025 : Tahun data (default: 2025)}
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}
                            {--lock-name= : Custom lock name for this command instance}';
    protected $description = 'Scraping data E-Katalog 6.0 dari API PDN LKPP';

    private Client $client;
    private array $klpdTypes = [
        '1.1' => 'KEMENTERIAN',
        '1.2' => 'LEMBAGA',
        '2.3' => 'PROVINSI',
        '2.4' => 'KABUPATEN',
        '2.5' => 'KOTA'
    ];

    private array $metrics = [
        'total_new' => 0,
        'total_updated' => 0,
        'total_error' => 0,
        'penyedia_new' => 0,
        'penyedia_matched' => 0,
        'by_type' => []
    ];

    public function __construct()
    {
        parent::__construct();
        $this->client = new Client([
            'base_uri' => 'https://data-pdn.inaproc.id/bigenvelope/public/api/',
            'timeout' => 30.0,
            'headers' => [
                'Accept' => '*/*',
                'Accept-Language' => 'en-US,en;q=0.9,id;q=0.8',
                'Authorization' => 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6Imh0dHBzOlwvXC9kYXRhLXBkbi5pbmFwcm9jLmlkIiwidXNlcl9pcCI6IiAifQ._O4yc-9DEzL94NYshEqLlGKT4zdIDSS0iicEkEB4Gis',
                'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
                'Origin' => 'https://data-pdn.inaproc.id',
                'Referer' => 'https://data-pdn.inaproc.id/public/page/id/888',
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With' => 'XMLHttpRequest'
            ]
        ]);
    }

    /**
     * Get elapsed time in seconds
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTime(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    public function handle(): int
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            $logger = Log::channel('scrapeKLPD');
            $year = $this->option('year');

            $message = "Mulai scraping data E-Katalog 6.0 untuk tahun {$year}...";
            $this->info($message);
            $logger->info($message);

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                $message,
                [
                    'year' => $year,
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $klpdTypeCount = count($this->klpdTypes);
            $klpdTypeIndex = 0;

            foreach ($this->klpdTypes as $jenisKlpd => $namaJenis) {
                $klpdTypeIndex++;
                $klpdTypeProgress = min(90, round(($klpdTypeIndex / $klpdTypeCount) * 90));

                $this->metrics['by_type'][$namaJenis] = [
                    'new' => 0,
                    'updated' => 0,
                    'error' => 0,
                    'penyedia_new' => 0,
                    'penyedia_matched' => 0,
                    'by_klpd' => []
                ];

                $message = "Memproses {$namaJenis}... ({$klpdTypeIndex}/{$klpdTypeCount})";
                $this->info("\n" . $message);

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    $klpdTypeProgress,
                    100,
                    $message,
                    [
                        'jenis_klpd' => $namaJenis,
                        'current_type' => $klpdTypeIndex,
                        'total_types' => $klpdTypeCount,
                        'stage' => 'processing_klpd_type',
                        'memory_usage' => $this->getMemoryUsage(),
                        'elapsed_time' => $this->getElapsedTime($startTime)
                    ]
                );

                // Get all KLPDs of this type
                $klpds = Klpd::where('jenis_klpd', $namaJenis)->get();
                $klpdCount = $klpds->count();

                $message = "Ditemukan {$klpdCount} KLPD dengan jenis {$namaJenis}";
                $this->info($message);
                $logger->info($message);

                $klpdIndex = 0;

                foreach ($klpds as $klpd) {
                    $klpdIndex++;

                    $message = "Memproses KLPD: {$klpd->nama_klpd} ({$klpd->kd_klpd}) - {$klpdIndex}/{$klpdCount}";
                    $this->info("\n" . $message);
                    $logger->info($message);

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        $klpdTypeProgress,
                        100,
                        $message,
                        [
                            'jenis_klpd' => $namaJenis,
                            'kd_klpd' => $klpd->kd_klpd,
                            'nama_klpd' => $klpd->nama_klpd,
                            'current_klpd' => $klpdIndex,
                            'total_klpds' => $klpdCount,
                            'stage' => 'processing_klpd',
                            'memory_usage' => $this->getMemoryUsage(),
                            'elapsed_time' => $this->getElapsedTime($startTime)
                        ]
                    );

                    $this->metrics['by_type'][$namaJenis]['by_klpd'][$klpd->kd_klpd] = [
                        'nama' => $klpd->nama_klpd,
                        'new' => 0,
                        'updated' => 0,
                        'error' => 0,
                        'penyedia_new' => 0,
                        'penyedia_matched' => 0
                    ];

                    try {
                        $this->scrapeKlpdData($klpd->kd_klpd, $jenisKlpd, $year, $logger);
                    } catch (Exception $e) {
                        $errorMessage = "Error scraping KLPD {$klpd->nama_klpd}: " . $e->getMessage();
                        $this->error($errorMessage);
                        $logger->error($errorMessage);

                        // Log system error
                        $this->logSystemError($e, "handle method - scraping KLPD: {$klpd->kd_klpd}");
                    }
                }
            }

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            $duration = round($executionTime, 2);

            $this->displayMetrics($duration);
            $this->logMetrics($logger, $duration);

            $message = "Scraping data E-Katalog 6.0 selesai dalam " . gmdate("H:i:s", $duration) . " detik.";

            // Mark command as completed
            CommandProgress::markAsCompleted(
                $this->getName(),
                $message,
                [
                    'year' => $year,
                    'total_new' => $this->metrics['total_new'],
                    'total_updated' => $this->metrics['total_updated'],
                    'total_error' => $this->metrics['total_error'],
                    'penyedia_new' => $this->metrics['penyedia_new'],
                    'penyedia_matched' => $this->metrics['penyedia_matched'],
                    'execution_time' => $duration,
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::SUCCESS;
        } catch (Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());
            Log::channel('scrapeKLPD')->error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }

    private function scrapeKlpdData(string $kodeKlpd, string $jenisKlpd, string $year, $logger): void
    {
        $page = 1;
        $perPage = 1000;
        $totalPages = null;

        do {
            try {
                $this->info("  Memproses halaman {$page}...");
                sleep(2);
                $this->info("Berhenti sejenak selama 2 detik...");
                $response = $this->client->post('id/554', [
                    'form_params' => [
                        'datatable[pagination][page]' => $page,
                        'datatable[pagination][perpage]' => $perPage,
                        'datatable[sort][sort]' => 'desc',
                        'datatable[sort][field]' => 'sumberdata',
                        'datatable[query]' => '',
                        'pagination[page]' => $page,
                        'pagination[perpage]' => $perPage,
                        'sort[sort]' => 'desc',
                        'sort[field]' => 'kode_rup',
                        'query' => '',
                        'kode_jenis_klpd' => $jenisKlpd,
                        'kode_klpd' => $kodeKlpd,
                        'kode_eselon' => 'all',
                        'kode_satker' => 'all',
                        'tahun' => $year,
                        'is_swakelola' => 'E-Katalog 6.0',
                        'type' => 'pelaksanaan'
                    ]
                ]);

                $responseData = json_decode($response->getBody()->getContents(), true);

                if (!isset($responseData['transaction']) || !$responseData['transaction'] || empty($responseData['data'])) {
                    $this->warn("    Tidak ada data pada halaman {$page}");
                    break;
                }

                if ($totalPages === null) {
                    $totalPages = $responseData['meta']['pages'];
                    $this->info("    Total halaman: {$totalPages}");
                }

                $this->processEkatalogData($responseData['data'], $kodeKlpd, $logger);

                if ($page >= $totalPages) {
                    break;
                }

                $page++;

            } catch (Exception $e) {
                $errorMessage = "Gagal mengambil data halaman {$page}: " . $e->getMessage();
                $this->error("    " . $errorMessage);
                $logger->error($errorMessage);

                // Log system error
                $this->logSystemError($e, "scrapeKlpdData - fetching page {$page} for KLPD {$kodeKlpd}");
                continue;
            }
        } while (true);
    }

    private function processEkatalogData(array $data, string $kodeKlpd, $logger): void
    {
        $currentKlpd = null;
        $currentType = null;

        // Find current KLPD and its type
        foreach ($this->klpdTypes as $jenisKlpd => $namaJenis) {
            if (isset($this->metrics['by_type'][$namaJenis]['by_klpd'][$kodeKlpd])) {
                $currentKlpd = &$this->metrics['by_type'][$namaJenis]['by_klpd'][$kodeKlpd];
                $currentType = &$this->metrics['by_type'][$namaJenis];
                break;
            }
        }

        // Collect all kode values from data
        $allKodes = array_filter(array_map(fn($item) => $item['kode'] ?? null, $data));

        // Chunk the kodes to check existing in batches of 100
        $existingKodes = [];
        foreach (array_chunk($allKodes, 100) as $chunk) {
            $existingChunk = Ekatalog6::whereIn('kode', $chunk)->pluck('kode')->toArray();
            $existingKodes = array_merge($existingKodes, $existingChunk);
        }
        $existingKodes = array_unique($existingKodes);

        // Filter data to only new records
        $newData = array_filter($data, fn($item) => !in_array($item['kode'], $existingKodes));

        foreach ($newData as $item) {
            try {
                if (empty($item['kode'])) {
                    $this->warn("    Melewati data tanpa kode");
                    continue;
                }

                // Gunakan metode createOrUpdatePerusahaan yang baru
                $penyedia = ListPerusahaanTender::createOrUpdatePerusahaan(
                    $item['nama_penyedia'],
                    null // NPWP tidak tersedia dari API
                );

                // Cek apakah perusahaan baru dibuat atau sudah ada
                if ($penyedia->wasRecentlyCreated) {
                    $currentKlpd['penyedia_new']++;
                    $currentType['penyedia_new']++;
                    $this->metrics['penyedia_new']++;
                } else {
                    $currentKlpd['penyedia_matched']++;
                    $currentType['penyedia_matched']++;
                    $this->metrics['penyedia_matched']++;
                }

                $ekatalog = Ekatalog6::create(
                    [
                        'kode' => $item['kode'],
                        'kode_rup' => $item['kode_rup'],
                        'kode_klpd' => $kodeKlpd,
                        'tahun_anggaran' => $item['tahun_anggaran'],
                        'nama_satker' => $item['nama_satker'],
                        'sumberdata' => $item['sumberdata'],
                        'id_penyedia' => $penyedia->id,
                        'nama_paket' => $item['nama_paket'],
                        'total_pelaksanaan' => $item['total_pelaksanaan'],
                        'pdn' => $item['pdn']
                    ]
                );

                $currentKlpd['new']++;
                $currentType['new']++;
                $this->metrics['total_new']++;
                $this->info("    Data baru: {$item['nama_paket']}");

                // Dispatch Sirup notification job for new Ekatalog6
                $dataSirup = \App\Models\DataSirup::where('kode_rup', $ekatalog->kode_rup)->first();
                if ($dataSirup) {
                    \App\Jobs\SendSirupNotifications::dispatch($dataSirup);
                } else {
                    $this->info("DataSirup not found for kode_rup: {$ekatalog->kode_rup}, skipping notification.");
                }

            } catch (Exception $e) {
                $currentKlpd['error']++;
                $currentType['error']++;
                $this->metrics['total_error']++;
                $errorMessage = "Gagal memproses data {$item['kode']}: " . $e->getMessage();
                $this->error("    " . $errorMessage);
                $logger->error($errorMessage);

                // Log system error
                $this->logSystemError($e, "processEkatalogData - processing item {$item['kode']} for KLPD {$kodeKlpd}");
            }
        }
    }

    private function displayMetrics(int $duration): void
    {
        $this->newLine();
        $this->info('=== RINGKASAN HASIL SCRAPING ===');
        $this->info("Waktu eksekusi: " . gmdate("H:i:s", $duration));
        $this->info("Total data baru: {$this->metrics['total_new']}");
        $this->info("Total data diperbarui: {$this->metrics['total_updated']}");
        $this->info("Total error: {$this->metrics['total_error']}");
        $this->info("\nStatistik Penyedia:");
        $this->info("- Penyedia baru: {$this->metrics['penyedia_new']}");
        $this->info("- Penyedia terkait: {$this->metrics['penyedia_matched']}");

        foreach ($this->klpdTypes as $namaJenis) {
            if (isset($this->metrics['by_type'][$namaJenis])) {
                $typeMetrics = $this->metrics['by_type'][$namaJenis];

                $this->newLine();
                $this->info("=== {$namaJenis} ===");
                $this->info("Data baru: {$typeMetrics['new']}");
                $this->info("Data diperbarui: {$typeMetrics['updated']}");
                $this->info("Error: {$typeMetrics['error']}");
                $this->info("Penyedia baru: {$typeMetrics['penyedia_new']}");
                $this->info("Penyedia terkait: {$typeMetrics['penyedia_matched']}");

                foreach ($typeMetrics['by_klpd'] as $kodeKlpd => $klpdMetrics) {
                    if ($klpdMetrics['new'] > 0 || $klpdMetrics['updated'] > 0 || $klpdMetrics['error'] > 0) {
                        $this->info("  {$klpdMetrics['nama']} ({$kodeKlpd}):");
                        $this->info("    - Data baru: {$klpdMetrics['new']}");
                        $this->info("    - Data diperbarui: {$klpdMetrics['updated']}");
                        $this->info("    - Error: {$klpdMetrics['error']}");
                        $this->info("    - Penyedia baru: {$klpdMetrics['penyedia_new']}");
                        $this->info("    - Penyedia terkait: {$klpdMetrics['penyedia_matched']}");
                    }
                }
            }
        }
    }

    private function logMetrics($logger, int $duration): void
    {
        $logger->info('=== RINGKASAN HASIL SCRAPING ===');
        $logger->info("Waktu eksekusi: " . gmdate("H:i:s", $duration));
        $logger->info("Total data baru: {$this->metrics['total_new']}");
        $logger->info("Total data diperbarui: {$this->metrics['total_updated']}");
        $logger->info("Total error: {$this->metrics['total_error']}");
        $logger->info("\nStatistik Penyedia:");
        $logger->info("- Penyedia baru: {$this->metrics['penyedia_new']}");
        $logger->info("- Penyedia terkait: {$this->metrics['penyedia_matched']}");

        foreach ($this->klpdTypes as $namaJenis) {
            if (isset($this->metrics['by_type'][$namaJenis])) {
                $typeMetrics = $this->metrics['by_type'][$namaJenis];

                $logger->info("=== {$namaJenis} ===");
                $logger->info("Data baru: {$typeMetrics['new']}");
                $logger->info("Data diperbarui: {$typeMetrics['updated']}");
                $logger->info("Error: {$typeMetrics['error']}");
                $logger->info("Penyedia baru: {$typeMetrics['penyedia_new']}");
                $logger->info("Penyedia terkait: {$typeMetrics['penyedia_matched']}");

                foreach ($typeMetrics['by_klpd'] as $kodeKlpd => $klpdMetrics) {
                    if ($klpdMetrics['new'] > 0 || $klpdMetrics['updated'] > 0 || $klpdMetrics['error'] > 0) {
                        $logger->info("  {$klpdMetrics['nama']} ({$kodeKlpd}):");
                        $logger->info("    - Data baru: {$klpdMetrics['new']}");
                        $logger->info("    - Data diperbarui: {$klpdMetrics['updated']}");
                        $logger->info("    - Error: {$klpdMetrics['error']}");
                        $logger->info("    - Penyedia baru: {$klpdMetrics['penyedia_new']}");
                        $logger->info("    - Penyedia terkait: {$klpdMetrics['penyedia_matched']}");
                    }
                }
            }
        }
    }
}
