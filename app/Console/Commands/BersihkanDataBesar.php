<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class BersihkanDataBesar extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'bersihkan:data-besar';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Membersihkan dan mengoptimalkan data dari tabel besar seperti Telescope dan sync_logs';

	/**
	 * Execute the console command.
	 */
	public function handle()
	{
		$this->info('Menghapus data dari tabel besar (Telescope, sync_logs, page_access_logs, dan login_logs)...');
		DB::statement('SET FOREIGN_KEY_CHECKS = 0');

		// Truncate Telescope tables
		DB::statement('TRUNCATE TABLE telescope_entries');
		DB::statement('TRUNCATE TABLE telescope_entries_tags');
		DB::statement('TRUNCATE TABLE telescope_monitoring');
		$this->info('Data Telescope berhasil dihapus.');

		// Truncate sync_logs table
		DB::statement('TRUNCATE TABLE sync_logs');
		$this->info('Data sync_logs berhasil dihapus.');

		// Truncate page_access_logs dan login_logs tables
		DB::statement('TRUNCATE TABLE page_access_logs');
		DB::statement('TRUNCATE TABLE login_logs');
		$this->info('Data page_access_logs dan login_logs berhasil dihapus.');

		// Optimize Telescope tables
		$this->info('Mengoptimalkan tabel Telescope...');
		DB::statement('OPTIMIZE TABLE telescope_entries');
		DB::statement('OPTIMIZE TABLE telescope_entries_tags');
		DB::statement('OPTIMIZE TABLE telescope_monitoring');
		$this->info('Tabel Telescope berhasil dioptimalkan.');

		// Optimize sync_logs table
		$this->info('Mengoptimalkan tabel sync_logs...');
		DB::statement('OPTIMIZE TABLE sync_logs');
		$this->info('Tabel sync_logs berhasil dioptimalkan.');

		// Optimize page_access_logs dan login_logs tables
		$this->info('Mengoptimalkan tabel page_access_logs dan login_logs...');
		DB::statement('OPTIMIZE TABLE page_access_logs');
		DB::statement('OPTIMIZE TABLE login_logs');
		$this->info('Tabel page_access_logs dan login_logs berhasil dioptimalkan.');

		$this->info('Proses pembersihan selesai.');
	}
}
