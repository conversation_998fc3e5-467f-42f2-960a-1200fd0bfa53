<?php

namespace App\Console\Commands;

use App\Models\Jadwal;
use App\Models\TenderLpse;
use App\Models\CommandProgress;
use App\Models\SystemError;
use App\Crawler\CustomClient;
use App\Traits\CommandManagementTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CheckMismatchedJadwal extends Command
{
    use CommandManagementTrait;

    protected $signature = 'check:mismatched-jadwal
                            {--scrape : Automatically scrape mismatched tenders}
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}
                            {--lock-name= : Custom lock name for this command instance}';
    protected $description = 'Check tenders with mismatched jadwal tahap and tahap_tender, optionally scrape them';

    protected $client;

    public function __construct()
    {
        parent::__construct();
        $this->client = new CustomClient();
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return 1;
        }

        try {
            $startTime = microtime(true); // Start measuring time
            $now = Carbon::now();

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                "Starting check for mismatched jadwal",
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Get jadwal entries where current date is between tanggal_mulai and tanggal_selesai
            $jadwals = Jadwal::where('tanggal_mulai', '<=', $now)
                ->where('tanggal_selesai', '>=', $now)
                ->get()
                ->shuffle()
                ->groupBy('kode_tender');

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                10,
                100,
                "Found " . $jadwals->count() . " active jadwal entries",
                [
                    'jadwal_count' => $jadwals->count(),
                    'stage' => 'jadwal_fetched',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $mismatchedTenders = [];

            $kodeTenderList = $jadwals->keys()->shuffle()->unique()->values()->all();

            $total = count($kodeTenderList);
            $this->info("Total unique kode_tender to process: {$total}");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                20,
                100,
                "Processing {$total} unique tenders",
                [
                    'total_tenders' => $total,
                    'stage' => 'processing_tenders',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $processedCount = 0;
            foreach ($kodeTenderList as $index => $kodeTender) {
                $processedCount++;
                $progress = min(90, 20 + round(($processedCount / $total) * 70));

                $this->info("Processing iteration " . ($index + 1) . " of {$total} for kode_tender: {$kodeTender}");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    $progress,
                    100,
                    "Processing tender {$processedCount} of {$total}",
                    [
                        'kode_tender' => $kodeTender,
                        'current' => $processedCount,
                        'total' => $total,
                        'stage' => 'processing_tender',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $jadwalGroup = $jadwals->get($kodeTender);
                if (!$jadwalGroup) {
                    continue;
                }

                // Pick the jadwal with the smallest step number
                $jadwal = $jadwalGroup->sortBy('kode_tender')->first();

                $tender = TenderLpse::where('kode_tender', $kodeTender)
                    ->whereIn('status_tender', ['Aktif', 'Tender Sedang Berjalan'])
                    ->first();

                if (!$tender) {
                    Log::warning("Tender not found or not active for kode_tender: {$kodeTender}");
                    continue;
                }

                $jadwalTahap = trim($jadwal->tahap);
                $tenderTahap = trim($tender->tahap_tender ?? '');

                if ($jadwalTahap !== $tenderTahap) {
                    $mismatchedTenders[$kodeTender] = [
                        'jadwal_tahap' => $jadwalTahap,
                        'tender_tahap' => $tenderTahap,
                        'nama_paket' => $tender->nama_paket,
                    ];
                }
            }

            // Update progress
            $mismatchedCount = count($mismatchedTenders);
            CommandProgress::updateProgress(
                $this->getName(),
                95,
                100,
                "Processing completed, found {$mismatchedCount} mismatched tenders",
                [
                    'mismatched_count' => $mismatchedCount,
                    'stage' => 'processing_completed',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            if (empty($mismatchedTenders)) {
                $this->info("No mismatched tenders found.");

                // Mark command as completed
                $endTime = microtime(true);
                $executionTime = $endTime - $startTime;
                CommandProgress::markAsCompleted(
                    $this->getName(),
                    "No mismatched tenders found",
                    [
                        'execution_time' => round($executionTime, 2),
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage()
                    ]
                );

                return 0;
            }

            $mismatchedCount = count($mismatchedTenders);
            $this->info("Mismatched tenders found: {$mismatchedCount}");
            foreach ($mismatchedTenders as $kodeTender => $stages) {
                $this->line("Kode Tender: {$kodeTender} | Nama: {$stages['nama_paket']} | Jadwal Tahap: {$stages['jadwal_tahap']} | Tender Tahap: {$stages['tender_tahap']}");
            }

            if ($this->option('scrape')) {
                $mismatchedCount = count($mismatchedTenders);
                $this->info("Starting scraping for {$mismatchedCount} mismatched tenders...");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    0,
                    100,
                    "Starting scraping for {$mismatchedCount} mismatched tenders",
                    [
                        'mismatched_count' => $mismatchedCount,
                        'stage' => 'scraping_started',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $scrapedCount = 0;
                foreach (array_keys($mismatchedTenders) as $kodeTender) {
                    $scrapedCount++;
                    $mismatchedCount = count($mismatchedTenders);
                    $this->info("Scraping tender {$scrapedCount} of {$mismatchedCount}: {$kodeTender}");

                    try {
                        $this->call('scrape:tender-details', [
                            'kodeTender' => $kodeTender,
                            '--lock-name' => "scrape-tender-{$kodeTender}",
                            '--force' => true
                        ]);
                    } catch (\Exception $e) {
                        $this->error("Error scraping tender {$kodeTender}: " . $e->getMessage());

                        // Log system error
                        $this->logSystemError($e, "Error scraping tender {$kodeTender}");
                    }

                    $this->info("Sleeping for 2 seconds to avoid rate limiting...");
                    sleep(2);
                    $this->newLine();
                }

                $this->info("Scraping completed.");
            } else {
                $this->info("Run this command with --scrape option to automatically scrape mismatched tenders.");
            }

            // Mark command as completed
            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            CommandProgress::markAsCompleted(
                $this->getName(),
                "Command completed successfully",
                [
                    'mismatched_count' => $mismatchedCount,
                    'scraped' => $this->option('scrape'),
                    'execution_time' => round($executionTime, 2),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return 0;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());
            Log::error("CheckMismatchedJadwal error: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return 1;
        }
    }
}
