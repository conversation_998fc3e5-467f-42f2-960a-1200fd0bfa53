<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\DataSirup;
use Carbon\Carbon;

class HapusDuplikatDataSirup extends Command
{
    protected $signature = 'sirup:hapus-duplikat
        {--dry-run : Si<PERSON><PERSON>i proses tanpa perubahan}
        {--batch-size=100 : Jumlah batch proses}
        {--max-process=100000 : Maksimal entri yang diproses}
        {--log-path= : Path custom log}
        {--filter-kode-rup= : Filter kode_rup untuk duplikat}
    ';

    protected $description = 'Menghapus Data Sirup yang Duplikat berdasarkan kode_rup';

    private $logPath;
    private $dryRun;
    private $batchSize;
    private $maxProcess;
    private $filterKodeRup;

    private $statsTotal = [
        'total_duplicates' => 0,
        'deleted_entries' => 0,
        'skipped_entries' => 0,
        'failed_deletes' => 0
    ];

    public function handle()
    {
        $startTime = microtime(true);

        $this->initializeOptions();
        $this->validateOptions();

        $this->info('🔍 Memulai Proses Hapus Duplikat Data Sirup');
        $this->newLine();

        try {
            $this->processDeduplication();
            $this->displayResults($startTime);
        } catch (\Exception $e) {
            $this->error("❌ Proses Gagal: " . $e->getMessage());
            Log::error('Hapus Duplikat Data Sirup Gagal', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    private function initializeOptions()
    {
        $this->dryRun = $this->option('dry-run');
        $this->batchSize = (int) $this->option('batch-size');
        $this->maxProcess = (int) $this->option('max-process');
        $this->filterKodeRup = $this->option('filter-kode-rup');

        $this->logPath = $this->option('log-path')
            ?? storage_path('logs/hapus_duplikat_sirup_' . now()->format('Y-m-d_H-i-s') . '.log');
    }

    private function validateOptions()
    {
        if ($this->batchSize <= 0) {
            throw new \InvalidArgumentException('Batch size harus positif');
        }

        if ($this->maxProcess <= 0) {
            throw new \InvalidArgumentException('Max process harus positif');
        }
    }

    private function processDeduplication()
    {
        $duplicateGroups = $this->findDuplicateGroups();

        $this->withProgressBar($duplicateGroups, function ($kodeRup) {
            $this->processDuplicateGroup($kodeRup);
        });
    }

    private function findDuplicateGroups()
    {
        $query = DataSirup::select('kode_rup')
            ->groupBy('kode_rup')
            ->havingRaw('COUNT(*) > 1');

        if ($this->filterKodeRup) {
            $query->where('kode_rup', 'LIKE', "%{$this->filterKodeRup}%");
        }

        return $query->limit($this->maxProcess)->pluck('kode_rup');
    }

    private function processDuplicateGroup($kodeRup)
    {
        $sirupEntries = DataSirup::where('kode_rup', $kodeRup)
            ->orderBy('created_at', 'desc')
            ->get();

        if ($sirupEntries->count() <= 1) return;

        $this->statsTotal['total_duplicates'] += $sirupEntries->count();

        // Hapus semua kecuali satu entri terbaru berdasarkan created_at
        $sirupEntries->skip(1)->each(function ($entry) {
            $this->deleteEntry($entry);
        });
    }

    private function deleteEntry($entry)
    {
        if ($this->dryRun) {
            $this->statsTotal['skipped_entries']++;
            return;
        }

        try {
            $entry->delete();
            $this->statsTotal['deleted_entries']++;
        } catch (\Exception $e) {
            $this->statsTotal['failed_deletes']++;
            Log::channel('deduplikasi')->error('Hapus Duplikat Gagal', [
                'entry_id' => $entry->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function displayResults($startTime)
    {
        $duration = round(microtime(true) - $startTime, 2);

        $this->newLine();
        $this->info('📊 Laporan Penghapusan Duplikat:');
        $this->table(
            ['Metrik', 'Jumlah'],
            [
                ['Total Duplikat', $this->statsTotal['total_duplicates']],
                ['Entri Dihapus', $this->statsTotal['deleted_entries']],
                ['Entri Dilewati', $this->statsTotal['skipped_entries']],
                ['Entri Gagal Dihapus', $this->statsTotal['failed_deletes']],
                ['Waktu Proses', $duration . ' detik']
            ]
        );

        if ($this->dryRun) {
            $this->warn('⚠️ Mode Dry Run - Tidak ada perubahan aktual');
        }

        Log::channel('deduplikasi')->info('Proses Hapus Duplikat Selesai', $this->statsTotal);
    }
}
