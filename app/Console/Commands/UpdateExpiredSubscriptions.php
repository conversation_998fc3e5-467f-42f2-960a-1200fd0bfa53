<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserSubscription;

class UpdateExpiredSubscriptions extends Command
{
    protected $signature = 'subscriptions:update-expired';
    protected $description = 'Update status of expired subscriptions';

    public function handle()
    {
        $updated = UserSubscription::where('status', 'active')
            ->where('end_date', '<', now())
            ->update([
                'status' => 'expired',
                'is_active' => 0
            ]);

        $this->info("Updated {$updated} expired subscriptions");
    }
}
