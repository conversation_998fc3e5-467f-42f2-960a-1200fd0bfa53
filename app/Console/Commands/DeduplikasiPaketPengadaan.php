<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\PaketPengadaan;
use App\Models\TransaksiPaket;

class DeduplikasiPaketPengadaan extends Command
{
    protected $signature = 'paket-pengadaan:deduplikasi
        {--dry-run : Simulasi proses tanpa perubahan data}';

    protected $description = 'Deduplikasi data Paket Pengadaan berdasarkan nomor_paket';

    private $statsTotal = [
        'total_duplicates' => 0,
        'updated_transactions' => 0,
        'deleted_entries' => 0,
        'skipped_entries' => 0,
        'failed_updates' => 0,
    ];

    public function handle()
    {
        $this->info('🔍 Memulai proses deduplikasi Paket Pengadaan...');
        $this->newLine();

        try {
            $this->processDeduplication();
            $this->displayResults();
        } catch (\Exception $e) {
            $this->error('❌ <PERSON><PERSON><PERSON>di k<PERSON>han selama proses: ' . $e->getMessage());
            Log::error('Error during deduplication', ['error' => $e->getMessage()]);
        }
    }

    private function processDeduplication()
    {
        $this->info('📂 Mengambil data paket pengadaan yang duplikat...');

        // Cari nomor_paket yang memiliki lebih dari 1 entri
        $duplicateGroups = PaketPengadaan::select('nomor_paket', DB::raw('COUNT(*) as total'))
            ->groupBy('nomor_paket')
            ->having('total', '>', 1)
            ->get();

        $this->statsTotal['total_duplicates'] = $duplicateGroups->count();
        $this->info('🔢 Total grup duplikat ditemukan: ' . $this->statsTotal['total_duplicates']);
        $this->newLine();

        $bar = $this->output->createProgressBar($this->statsTotal['total_duplicates']);
        $bar->start();

        foreach ($duplicateGroups as $group) {
            $this->deduplicateGroup($group->nomor_paket);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('✅ Proses deduplikasi selesai.');
    }

    private function deduplicateGroup($nomorPaket)
    {
        $this->line("🔍 Memproses nomor paket: {$nomorPaket}");

        // Ambil semua entri dengan nomor_paket yang sama
        $entries = PaketPengadaan::where('nomor_paket', $nomorPaket)
            ->orderBy('created_at', 'desc') // Prioritaskan data terbaru
            ->get();

        if ($entries->count() <= 1) {
            $this->line("⏩ Tidak ada duplikat untuk nomor paket: {$nomorPaket}");
            $this->statsTotal['skipped_entries']++;
            return;
        }

        // Pilih entri utama (data terbaru)
        $mainEntry = $entries->first();

        // Proses entri lainnya sebagai duplikat
        foreach ($entries->slice(1) as $duplicateEntry) {
            $this->mergeEntryData($mainEntry, $duplicateEntry);
        }
    }

    private function mergeEntryData($mainEntry, $duplicateEntry)
    {
        DB::beginTransaction();

        try {
            // Update TransaksiPaket
            $updatedRows = TransaksiPaket::where('id_paket', $duplicateEntry->id_paket)
                ->update(['id_paket' => $mainEntry->id_paket]);

            // Output detail proses
            $this->line("🔄 Memperbarui transaksi untuk id_paket {$duplicateEntry->id_paket} ke id_paket {$mainEntry->id_paket} - {$updatedRows} baris diperbarui");

            // Hapus entri duplikat
            if (!$this->option('dry-run')) {
                $duplicateEntry->delete();
                $this->line("🗑️ Menghapus duplikat id_paket: {$duplicateEntry->id_paket}");
            } else {
                $this->line("🔎 Simulasi: duplikat id_paket {$duplicateEntry->id_paket} tidak dihapus.");
            }

            $this->statsTotal['updated_transactions'] += $updatedRows;
            $this->statsTotal['deleted_entries']++;

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->statsTotal['failed_updates']++;
            $this->error("❌ Gagal memperbarui duplikat id_paket: {$duplicateEntry->id_paket}");
            Log::error('Error updating duplicate entry', [
                'main_entry_id' => $mainEntry->id_paket,
                'duplicate_entry_id' => $duplicateEntry->id_paket,
                'error' => $e->getMessage(),
            ]);
        }
    }

    private function displayResults()
    {
        $this->newLine();
        $this->info('📊 Statistik Deduplikasi:');
        $this->table(
            ['Metrik', 'Jumlah'],
            [
                ['Total Duplikat', $this->statsTotal['total_duplicates']],
                ['Transaksi Diperbarui', $this->statsTotal['updated_transactions']],
                ['Entri Dihapus', $this->statsTotal['deleted_entries']],
                ['Entri Terlewat', $this->statsTotal['skipped_entries']],
                ['Gagal Memperbarui', $this->statsTotal['failed_updates']],
            ]
        );
    }
}
