<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use App\Models\TenderLpse;
use App\Models\Anggaran;
use App\Models\InstansiSatker;
use App\Models\Jadwal;
use App\Models\LokasiPaket;
use App\Models\PesertaTender;
use App\Models\PemenangLpse;
use App\Models\SyncLog;
use App\Models\ListPerusahaanTender;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class SyncTenderRecently extends Command
{
    use CommandManagementTrait;

    protected $signature = 'sync:tender-recently
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}
                            {--lock-name= : Custom lock name for this command instance}';
    protected $description = 'Sync Tender data from API';

    /**
     * Get elapsed time in seconds
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTime(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            Log::info("Starting tender synchronization");
            $this->info("Starting tender synchronization");

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                "Starting tender synchronization",
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $this->syncRecentlyData();

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            $message = "All LPSE data sync completed in " . round($executionTime, 2) . " seconds.";

            $this->info($message);
            Log::info($message);

            // Mark command as completed
            CommandProgress::markAsCompleted(
                $this->getName(),
                $message,
                [
                    'execution_time' => round($executionTime, 2),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());
            Log::error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }

    private function syncRecentlyData()
    {
        // Update progress
        CommandProgress::updateProgress(
            $this->getName(),
            10,
            100,
            "Fetching data from API",
            [
                'stage' => 'api_request',
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        $apiUrl = "https://tenderid.mcmtestapp.com/api/tender/terbaru";

        try {
            $response = Http::timeout(120)->get($apiUrl);

            if ($response->successful()) {
                $data = $response->json();
                $totalTenders = count($data);

                $this->info("Received {$totalTenders} tenders from API");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    20,
                    100,
                    "Received {$totalTenders} tenders from API",
                    [
                        'total_tenders' => $totalTenders,
                        'stage' => 'data_received',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                DB::beginTransaction();
                try {
                    $processedCount = 0;

                    foreach ($data as $tenderData) {
                        $processedCount++;
                        $progress = min(90, 20 + round(($processedCount / $totalTenders) * 70));
                        $kodeTender = $tenderData['Kode Tender'] ?? 'unknown';

                        // Update progress
                        CommandProgress::updateProgress(
                            $this->getName(),
                            $progress,
                            100,
                            "Processing tender {$processedCount}/{$totalTenders}: {$kodeTender}",
                            [
                                'kode_tender' => $kodeTender,
                                'current' => $processedCount,
                                'total' => $totalTenders,
                                'stage' => 'processing_tender',
                                'memory_usage' => $this->getMemoryUsage()
                            ]
                        );

                        try {
                            $this->processLpseData($tenderData);
                        } catch (\Exception $e) {
                            $this->logError(
                                $kodeTender,
                                "Error processing tender data: " . $e->getMessage()
                            );

                            // Log system error
                            $this->logSystemError($e, "syncRecentlyData - processing tender: {$kodeTender}");
                        }
                    }

                    DB::commit();
                    $this->logSuccess("Data sync completed successfully");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        95,
                        100,
                        "Data sync completed successfully",
                        [
                            'processed_count' => $processedCount,
                            'total_tenders' => $totalTenders,
                            'stage' => 'sync_completed',
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );
                } catch (\Exception $e) {
                    DB::rollBack();
                    $this->logError('global', "Transaction failed: " . $e->getMessage());

                    // Log system error
                    $this->logSystemError($e, "syncRecentlyData - transaction failed");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        100,
                        100,
                        "Transaction failed: " . $e->getMessage(),
                        [
                            'stage' => 'transaction_failed',
                            'error' => $e->getMessage(),
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    throw $e;
                }
            } else {
                $errorMessage = "API request failed with status: " . $response->status();
                $this->logError('global', $errorMessage);

                // Log system error
                $this->logSystemError(
                    new \Exception($errorMessage),
                    "syncRecentlyData - API request failed"
                );

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    100,
                    100,
                    $errorMessage,
                    [
                        'status_code' => $response->status(),
                        'stage' => 'api_request_failed',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            $errorMessage = "Error connecting to API: " . $e->getMessage();
            $this->logError('global', $errorMessage);

            // Log system error
            $this->logSystemError($e, "syncRecentlyData - API connection error");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                100,
                100,
                $errorMessage,
                [
                    'stage' => 'api_connection_error',
                    'error' => $e->getMessage(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            throw $e;
        }
    }

    private function processLpseData($tenderData)
    {
        $kodeTender = $tenderData['Kode Tender'];
        $existingTender = TenderLpse::find($kodeTender);

        if ($existingTender) {
            $this->updateTender($existingTender, $tenderData);
        } else {
            $this->createTender($tenderData);
        }
    }

    private function updateTender($existingTender, $newData)
    {
        $changes = [];
        $mappedData = $this->mapTenderData($newData);

        foreach ($mappedData as $key => $value) {
            if ($existingTender->$key != $value) {
                $changes[$key] = [
                    'old' => $existingTender->$key,
                    'new' => $value
                ];
                $existingTender->$key = $value;
            }
        }

        if (!empty($changes)) {
            $existingTender->save();
            $this->logChanges($existingTender->kode_tender, 'update', $changes);
            $this->logSuccess("Updated tender: {$existingTender->kode_tender}");
        }

        $this->updateRelatedData($existingTender, $newData);
    }

    private function createTender($data)
    {
        $tender = TenderLpse::create($this->mapTenderData($data));
        $this->logChanges($tender->kode_tender, 'create', $tender->toArray());
        $this->logSuccess("Created new tender: {$tender->kode_tender}");
        $this->updateRelatedData($tender, $data);
    }

    private function updateRelatedData($tender, $data)
    {
        $this->processAnggaran($tender, $data['anggaran'] ?? []);
        $this->processInstansiSatker($tender, $data['Instansi dan Satker'] ?? []);
        $this->processJadwal($tender, $data['jadwal'] ?? []);
        $this->processLokasi($tender, $data['lokasi_paket'] ?? []);
        $this->processPeserta($tender, $data['Daftar Peserta'] ?? []);
        $this->processPemenang($tender, $data['pemenang'] ?? null, $data);
    }

    private function processAnggaran($tender, $anggaranData)
    {
        Anggaran::where('kode_tender', $tender->kode_tender)->delete();
        foreach ($anggaranData as $data) {
            $tender->anggaran()->create($data);
        }
    }

    private function processInstansiSatker($tender, $satkerData)
    {
        InstansiSatker::where('kode_tender', $tender->kode_tender)->delete();
        foreach ($satkerData as $data) {
            $tender->instansiSatker()->create([
                'rup_id' => $data['rup_id'] ?? 0,
                'nama_satker' => $data['stk_nama'] ?? 'N/A',
                'nama_instansi' => $data['nama_instansi'] ?? 'N/A',
                'jenis_instansi' => $data['jenis_instansi'] ?? 'N/A'
            ]);
        }
    }

    private function processJadwal($tender, $jadwalData)
    {
        Jadwal::where('kode_tender', $tender->kode_tender)->delete();
        foreach ($jadwalData as $data) {
            try {
                $tender->jadwal()->create([
                    'step' => $data['no'],
                    'tahap' => $data['tahap'],
                    'tanggal_mulai' => Carbon::parse($data['mulai']),
                    'tanggal_selesai' => Carbon::parse($data['sampai']),
                    'keterangan' => $data['perubahan'] ?? null
                ]);
            } catch (\Exception $e) {
                $this->logError(
                    $tender->kode_tender,
                    "Jadwal processing error: " . $e->getMessage()
                );
            }
        }
    }

    private function processLokasi($tender, $lokasiData)
    {
        LokasiPaket::where('kode_tender', $tender->kode_tender)->delete();
        foreach ($lokasiData as $data) {
            $tender->lokasiPaket()->create($data['lokasi']);
        }
    }

    private function processPeserta($tender, $pesertaData)
    {
        foreach ($pesertaData as $data) {
            try {
                $perusahaanId = $this->getOrCreatePerusahaan($data);
                PesertaTender::firstOrCreate([
                    'kode_tender' => $tender->kode_tender,
                    'id_peserta' => $perusahaanId
                ], ['status' => 'Peserta']);
            } catch (\Exception $e) {
                $this->logError(
                    $tender->kode_tender,
                    "Peserta processing error: " . $e->getMessage()
                );
            }
        }
    }

    private function processPemenang($tender, $pemenangData, $fullData)
    {
        if ($pemenangData) {
            try {
                $perusahaanId = $this->getOrCreatePerusahaan($pemenangData);
                PemenangLpse::updateOrCreate(
                    [
                        'kode_tender' => $tender->kode_tender,
                        'id_peserta' => $perusahaanId
                    ],
                    [
                        'harga_penawaran' => $fullData['harga_penawaran'] ?? null,
                        'harga_terkoreksi' => $fullData['harga_terkoreksi'] ?? null
                    ]
                );
            } catch (\Exception $e) {
                $this->logError(
                    $tender->kode_tender,
                    "Pemenang processing error: " . $e->getMessage()
                );
            }
        }
    }

    private function getOrCreatePerusahaan($perusahaanData)
    {
        try {
            return ListPerusahaanTender::createOrUpdatePerusahaan(
                $perusahaanData['Nama Perusahaan'],
                $perusahaanData['NPWP'], // Default NPWP jika tidak ada
                $perusahaanData['Alamat'] ?? null
            )->id;
        } catch (\Exception $e) {
            $this->logError(
                'perusahaan',
                "Perusahaan processing error: " . $e->getMessage()
            );
            throw $e;
        }
    }

    private function mapTenderData(array $tenderData): array
    {
        return [
            'kode_tender' => (string) ($tenderData['Kode Tender'] ?? ''),
            'kd_lpse' => $tenderData['Kode LPSE'] ?? null,
            'status_tender' => $tenderData['Status_Tender'] ?? null,
            'nama_paket' => $tenderData['Nama Paket'] ?? 'Paket Tanpa Nama',
            'pagu' => $this->formatNumber($tenderData['Pagu'] ?? 0),
            'hps' => $this->formatNumber($tenderData['HPS'] ?? 0),
            'tanggal_paket_dibuat' => $this->parseDate($tenderData['tanggal paket dibuat'] ?? null),
            'tanggal_paket_tayang' => $this->parseDate($tenderData['tanggal paket tayang'] ?? null),
            'kategori_pekerjaan' => $tenderData['Kategori Pekerjaan'] ?? 'Lainnya',
            'metode_pemilihan' => $tenderData['Metode Pemilihan'] ?? 'Tender',
            'metode_pengadaan' => $tenderData['Metode Pengadaan'] ?? 'Elektronik',
            'metode_evaluasi' => $tenderData['Metode Evaluasi'] ?? 'Sistem Gugur',
            'cara_pembayaran' => $tenderData['Cara Pembayaran'] ?? 'LS',
            'jenis_penetapan_pemenang' => $tenderData['Jenis Penetapan Pemenang'] ?? 'Penunjukan Langsung',
            'apakah_paket_konsolidasi' => $tenderData['Apakah paket konsolidasi'] ?? false,
            'jumlah_pendaftar' => $tenderData['Jumlah Pendaftar'] ?? 0,
            'jumlah_penawar' => $tenderData['Jumlah Penawar'] ?? 0,
            'jumlah_kirim_kualifikasi' => $tenderData['jumlah_kirim_kualifikasi'] ?? 0,
            'durasi_tender' => $tenderData['Durasi Tender'] ?? '1 Hari',
            'versi_paket_spse' => $tenderData['Versi_spse_paket'] ?? '1.0',
            'kualifikasi_usaha' => $tenderData['Kualifikasi Usaha'] ?? 'Kecil',
            'tahap_tender' => $tenderData['tahap_tender'] ?? 'Pengumuman',
            'syarat_kualifikasi' => $tenderData['syarat_kualifikasi'] ?? 'Memiliki NPWP',
        ];
    }

    private function formatNumber($value)
    {
        return is_numeric($value) ? (float) $value : 0;
    }

    private function parseDate($dateString)
    {
        try {
            return $dateString ? Carbon::parse($dateString) : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function logChanges($id, $action, $changes, $table = 'tender_umum_publik')
    {
        SyncLog::create([
            'table' => $table,
            'record_id' => $id,
            'action' => $action,
            'changes' => json_encode($changes),
            'synced_at' => now()
        ]);
    }

    private function logSuccess($message)
    {
        $this->info($message);
        //Log::info($message);
    }

    private function logError($context, $message)
    {
        $fullMessage = "[$context] $message";
        $this->error($fullMessage);
        //Log::error($fullMessage);
    }
}