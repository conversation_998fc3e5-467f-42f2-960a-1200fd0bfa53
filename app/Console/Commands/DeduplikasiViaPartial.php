<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\ListPerusahaanTender;
use App\Models\PesertaTender;
use App\Models\PemenangLpse;
use App\Models\PaketPengadaan;

class DeduplikasiViaPartial extends Command
{
    protected $signature = 'perusahaan:deduplikasi-via-partial
        {--dry-run : <PERSON><PERSON><PERSON><PERSON> proses tanpa perubahan}
        {--batch-size=100 : Jumlah batch proses}
        {--max-process=1000 : Maksimal entri yang diproses}
        {--log-path= : Path custom log}
        {--filter-name= : Filter nama perusahaan}
    ';

    protected $description = 'Deduplikasi perusahaan berdasarkan partial_npwp dan normalized_name';

    private $logPath;
    private $dryRun;
    private $batchSize;
    private $maxProcess;
    private $filterName;

    private $statsTotal = [
        'total_duplicates' => 0,
        'merged_entries' => 0,
        'skipped_entries' => 0,
        'deleted_entries' => 0,
        'failed_merges' => 0
    ];

    public function handle()
    {
        $startTime = microtime(true);
        
        $this->initializeOptions();
        $this->validateOptions();
        
        $this->info('🔍 Memulai Deduplikasi via Partial NPWP + Nama');
        $this->newLine();

        try {
            $this->processDeduplication();
            $this->displayResults($startTime);
            return 0;
        } catch (\Exception $e) {
            $this->error("❌ Gagal: " . $e->getMessage());
            Log::channel('deduplikasi')->error('Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    private function initializeOptions()
    {
        $this->dryRun = $this->option('dry-run');
        $this->batchSize = (int) $this->option('batch-size');
        $this->maxProcess = (int) $this->option('max-process');
        $this->filterName = $this->option('filter-name');
        
        $this->logPath = $this->option('log-path') 
            ?? storage_path('logs/deduplikasi_partial_' . now()->format('Ymd_His') . '.log');
    }

    private function validateOptions()
    {
        if ($this->batchSize <= 0 || $this->maxProcess <= 0) {
            throw new \InvalidArgumentException('Batch size dan max process harus > 0');
        }
    }

    private function processDeduplication()
    {
        $duplicateGroups = $this->findDuplicateGroups();
        
        $this->withProgressBar($duplicateGroups, function ($groupKey) {
            $this->processDuplicateGroup($groupKey);
        });
    }

    private function findDuplicateGroups()
    {
        $query = ListPerusahaanTender::query()
            ->select([
                DB::raw('CASE 
                    WHEN LENGTH(partial_npwp) = 6 THEN partial_npwp 
                    ELSE SUBSTRING(partial_npwp, 2) 
                END as base_npwp'),
                'normalized_name'
            ])
            ->where(function ($query) {
                $query->whereRaw('LENGTH(partial_npwp) = 6')
                    ->orWhereRaw('LENGTH(partial_npwp) > 6');
            })
            ->groupBy('base_npwp', 'normalized_name')
            ->havingRaw('COUNT(*) > 1');

        if ($this->filterName) {
            $query->where('normalized_name', 'LIKE', "%{$this->filterName}%");
        }

        return $query->limit($this->maxProcess)
            ->get()
            ->map(function ($item) {
                return $item->base_npwp . '||' . $item->normalized_name;
            });
    }

    private function processDuplicateGroup($groupKey)
    {
        [$baseNpwp, $normalizedName] = explode('||', $groupKey, 2);

        $entries = ListPerusahaanTender::where(function ($query) use ($baseNpwp, $normalizedName) {
            $query->where(function ($q) use ($baseNpwp, $normalizedName) {
                $q->where('partial_npwp', $baseNpwp)
                  ->where('normalized_name', $normalizedName)
                  ->whereRaw('LENGTH(partial_npwp) = 6');
            })->orWhere(function ($q) use ($baseNpwp, $normalizedName) {
                $q->where('partial_npwp', 'LIKE', '_'.$baseNpwp)
                  ->where('normalized_name', $normalizedName)
                  ->whereRaw('LENGTH(partial_npwp) > 6');
            });
        })->orderBy('created_at')->get();

        if ($entries->count() < 2) return;

        $this->statsTotal['total_duplicates'] += $entries->count();

        $mainEntry = $this->selectMainEntry($entries);
        
        foreach ($entries as $entry) {
            if ($entry->id === $mainEntry->id) continue;
            
            $this->mergeEntries($mainEntry, $entry);
        }
    }

    private function selectMainEntry($entries)
    {
        // Prioritaskan yang partial_npwp 6 digit
        $sixDigit = $entries->filter(fn($e) => strlen($e->partial_npwp) === 6);
        
        if ($sixDigit->isNotEmpty()) {
            return $sixDigit->sortByDesc(fn($e) => (
                ($e->npwp ? 10 : 0) + 
                ($e->npwp_blur ? 5 : 0) + 
                ($e->alamat ? 3 : 0)
            ))->first();
        }

        return $entries->sortByDesc('created_at')->first();
    }

    private function mergeEntries($mainEntry, $duplicateEntry)
    {
        if ($this->dryRun) {
            $this->statsTotal['skipped_entries']++;
            return;
        }

        DB::beginTransaction();
        try {
            // Update data utama jika ada field kosong
            $this->updateMainEntry($mainEntry, $duplicateEntry);
            
            // Update relasi
            $this->updateRelations($mainEntry, $duplicateEntry);
            
            // Hapus duplikat
            $duplicateEntry->delete();

            $this->statsTotal['merged_entries']++;
            $this->statsTotal['deleted_entries']++;
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->statsTotal['failed_merges']++;
            
            Log::channel('deduplikasi')->error('Gagal Merge', [
                'main' => $mainEntry->id,
                'duplicate' => $duplicateEntry->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function updateMainEntry($mainEntry, $duplicateEntry)
    {
        $updates = [];
        
        foreach (['npwp', 'npwp_blur', 'alamat'] as $field) {
            if (empty($mainEntry->$field)) {
                $updates[$field] = $duplicateEntry->$field;
            }
        }
        
        if (!empty($updates)) {
            $mainEntry->update($updates);
        }
    }

    private function updateRelations($mainEntry, $duplicateEntry)
    {
        $tables = [
            PesertaTender::class => 'id_peserta',
            PemenangLpse::class => 'id_peserta',
            PaketPengadaan::class => ['id_penyedia', 'id_pelaksana']
        ];

        foreach ($tables as $model => $columns) {
            if (is_array($columns)) {
                foreach ($columns as $column) {
                    $model::where($column, $duplicateEntry->id)
                        ->update([$column => $mainEntry->id]);
                }
            } else {
                $model::where($columns, $duplicateEntry->id)
                    ->update([$columns => $mainEntry->id]);
            }
        }
    }

    private function displayResults($startTime)
    {
        $duration = number_format(microtime(true) - $startTime, 2);
        
        $this->newLine(2);
        $this->info("📊 Hasil Deduplikasi");
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Duplikat', $this->statsTotal['total_duplicates']],
                ['Merged Entries', $this->statsTotal['merged_entries']],
                ['Deleted Entries', $this->statsTotal['deleted_entries']],
                ['Gagal Merge', $this->statsTotal['failed_merges']],
                ['Durasi', $duration . ' detik']
            ]
        );

        if ($this->dryRun) {
            $this->warn('⚠️ DRY RUN - Tidak ada perubahan aktual');
        }

        Log::channel('deduplikasi')->info('Laporan Deduplikasi', $this->statsTotal);
    }
}