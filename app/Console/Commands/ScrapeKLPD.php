<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use GuzzleHttp\Client;
use App\Models\Klpd;
use Exception;
use Illuminate\Support\Facades\Log;

class ScrapeKLPD extends Command
{
    protected $signature = 'scrape:klpd';
    protected $description = 'Scraping data KLPD dari API LKPP dan menyimpannya ke dalam database';

    private Client $client;

    public function __construct()
    {
        parent::__construct();
        $this->client = new Client([
            'base_uri' => 'https://isb.lkpp.go.id/isb-2/api/satudata/',
            'timeout' => 10.0,
            'proxy' => env('PROXIES_SSH'),  
        ]);
    }

    public function handle(): void
    {
        // Menggunakan log khusus untuk ScrapeKLPD
        $logger = Log::channel('scrapeKLPD');

        $this->info('Mulai scraping data KLPD...');
        $logger->info('Mulai scraping data KLPD.');

        try {
            $response = $this->client->get('MasterKLPD');
            $data = json_decode($response->getBody()->getContents(), true);

            $this->info('Data berhasil diambil dari API.');
            $logger->info('Data berhasil diambil dari API.');

            foreach ($data as $item) {
                $this->info("Memproses KLPD: {$item['nama_klpd']} ({$item['kd_klpd']})");
                $logger->info("Memproses KLPD: {$item['nama_klpd']} ({$item['kd_klpd']})");

                $klpd = Klpd::updateOrCreate(
                    ['kd_klpd' => $item['kd_klpd']],
                    [
                        'nama_klpd' => $item['nama_klpd'],
                        'jenis_klpd' => $item['jenis_klpd'],
                        'kd_provinsi' => $item['kd_provinsi'] ?? null,
                        'kd_kabupaten' => $item['kd_kabupaten'] ?? null,
                    ]
                );


                $this->info("Data KLPD berhasil disimpan atau diperbarui: {$klpd->nama_klpd}");
                $logger->info("Data KLPD berhasil disimpan atau diperbarui: {$klpd->nama_klpd}");
            }

            $this->info('Scraping selesai.');
            $logger->info('Scraping selesai.');
        } catch (Exception $e) {
            $this->error('Gagal mengambil data: ' . $e->getMessage());
            $logger->error('Gagal mengambil data: ' . $e->getMessage());
        }
    }
}
