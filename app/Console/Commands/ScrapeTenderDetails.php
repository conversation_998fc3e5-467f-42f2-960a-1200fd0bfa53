<?php

namespace App\Console\Commands;

use App\Models\TenderLpse;
use App\Models\Lpse;
use App\Models\Jadwal;
use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Symfony\Component\DomCrawler\Crawler;
use App\Crawler\CustomClient;
use Carbon\Carbon;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class ScrapeTenderDetails extends Command
{
    use CommandManagementTrait;

    protected $signature = 'scrape:tender-details
                            {kodeTender? : Kode tender yang akan di-scrape. Jika tidak diisi, akan mengambil semua tender dengan tahap_tender NULL}
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}
                            {--preview : Only show preview information without full processing}
                            {--lock-name= : Custom lock name for this command instance}';
    protected $description = 'Scrape tahap_tender, kualifikasi_usaha, syarat_kualifikasi, and keterangan for TenderLpse by kodeTender';

    protected $client;

    /**
     * Validate and get HTML content from response
     *
     * @param string $url
     * @param \Psr\Http\Message\ResponseInterface $response
     * @return array Returns array with 'html' and 'crawler'
     * @throws \Exception
     */
    protected function validateAndGetContent($url, $response, $htmlContent = null)
    {
        $statusCode = $response->getStatusCode();

        // Daftar kode status HTTP yang harus di-skip (tidak perlu retry)
        $skipStatusCodes = [403, 429, 404, 500, 502, 503];

        if ($statusCode !== 200) {
            if (in_array($statusCode, $skipStatusCodes)) {
                // Untuk kode status tertentu, kita langsung throw exception dengan pesan khusus
                // agar tidak di-retry lagi
                $errorMessage = "SKIP_RETRY: HTTP Error: Received status code {$statusCode} for URL: {$url}";
                throw new \Exception($errorMessage);
            } else {
                // Untuk kode status lain, throw exception biasa (akan di-retry)
                throw new \Exception("HTTP Error: Received status code {$statusCode} for URL: {$url}");
            }
        }

        // Jika HTML sudah disediakan, gunakan itu
        // Jika tidak, ambil dari response
        $html = $htmlContent ?? $response->getBody()->getContents();

        // Validate if the response is actually HTML
        if (!preg_match('/<[^>]*>/', $html)) {
            throw new \Exception("Invalid HTML response received from URL: {$url}");
        }

        $crawler = new Crawler($html);

        // Check if we got a Cloudflare or other security challenge
        if ($crawler->filter('title')->count() > 0) {
            $title = $crawler->filter('title')->text();
            if (stripos($title, 'security check') !== false ||
                stripos($title, 'cloudflare') !== false ||
                stripos($title, 'attention required') !== false) {
                throw new \Exception("Security challenge detected: {$title}");
            }
        }

        // Check if we got redirected to a login page
        if (stripos($html, 'login') !== false &&
            (stripos($html, 'password') !== false || stripos($html, 'kata sandi') !== false)) {
            throw new \Exception("Redirected to login page");
        }

        return [
            'html' => $html,
            'crawler' => $crawler
        ];
    }

    public function __construct()
    {
        parent::__construct();
        $this->client = new CustomClient();
    }

    /**
     * Get elapsed time in seconds
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTime(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');
        $preview = $this->option('preview');

        // Jika preview mode, tampilkan informasi saja tanpa inisialisasi command management
        if ($preview) {
            $kodeTender = $this->argument('kodeTender');
            if (!$kodeTender) {
                $this->error("Kode tender harus disediakan dalam mode preview");
                return SymfonyCommand::FAILURE;
            }

            $tender = TenderLpse::with('lpse')->where('kode_tender', $kodeTender)->first();
            if (!$tender) {
                $this->error("Tender tidak ditemukan: {$kodeTender}");
                return SymfonyCommand::FAILURE;
            }

            $this->info("=== PREVIEW MODE ===");
            $this->info("Tender: {$tender->nama_paket}");
            $this->info("Kode: {$tender->kode_tender}");
            $this->info("LPSE: {$tender->lpse->nama_lpse}");
            $this->info("URL Tender: {$tender->lpse->url}/lelang/{$tender->kode_tender}/pengumumanlelang");
            $this->info("URL Jadwal: {$tender->lpse->url}/lelang/{$tender->kode_tender}/jadwal");
            $this->info("Command akan dijalankan di background untuk mengambil data tender dan jadwal.");
            return SymfonyCommand::SUCCESS;
        }

        // Jika force=true, kita akan tetap menjalankan command meskipun command lain sedang berjalan
        if (!$this->initCommandManagement($timeout, $force)) {
            if (!$force) {
                return SymfonyCommand::FAILURE;
            }
            $this->warn("Command sedang berjalan, tetapi melanjutkan karena opsi --force digunakan.");
        }

        try {
            $startTime = microtime(true); // Start measuring time

            $kodeTender = $this->argument('kodeTender');

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                "Starting scrape tender details command",
                [
                    'kode_tender' => $kodeTender ?? 'all',
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            if ($kodeTender === null) {
                // No kodeTender provided, fetch all with tahap_tender NULL
                $tenders = TenderLpse::whereNull('tahap_tender')->pluck('kode_tender')->toArray();
                $totalTenders = count($tenders);

                $message = "No kodeTender provided, processing {$totalTenders} tenders with tahap_tender NULL.";
                Log::channel('scrape_tender_detail')->info($message);
                $this->info($message);

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    5,
                    100,
                    $message,
                    [
                        'total_tenders' => $totalTenders,
                        'stage' => 'fetching_tenders',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                // Randomize the order of tenders to avoid hitting the same LPSE consecutively
                shuffle($tenders);

                $processedCount = 0;

                foreach ($tenders as $kodeTender) {
                    $processedCount++;
                    $progress = min(95, 5 + round(($processedCount / $totalTenders) * 90));

                    $message = "Starting scraping tender details for kodeTender: {$kodeTender} ({$processedCount}/{$totalTenders})";
                    Log::channel('scrape_tender_detail')->info($message);
                    $this->info($message);

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        $progress,
                        100,
                        $message,
                        [
                            'kode_tender' => $kodeTender,
                            'current' => $processedCount,
                            'total' => $totalTenders,
                            'stage' => 'processing_tender',
                            'memory_usage' => $this->getMemoryUsage(),
                            'elapsed_time' => $this->getElapsedTime($startTime)
                        ]
                    );

                    try {
                        $this->scrapeTenderByKode($kodeTender);
                        $this->info("Scraping tender details completed.");

                        $this->info("Scraping jadwal tender for kodeTender: {$kodeTender}");
                        $this->scrapeJadwalByKode($kodeTender);
                        $this->info("Scraping jadwal tender completed.");
                    } catch (\Exception $e) {
                        $errorMessage = "Error processing tender {$kodeTender}: " . $e->getMessage();
                        $this->error($errorMessage);
                        Log::channel('scrape_tender_detail')->error($errorMessage);

                        // Log system error
                        $this->logSystemError($e, "handle method - processing tender: {$kodeTender}");
                    }

                    $this->newLine();
                    usleep(2000000); // 2 seconds delay
                }

                $endTime = microtime(true);
                $executionTime = $endTime - $startTime;
                $message = "Completed scraping all tenders with tahap_tender NULL in " . round($executionTime, 2) . " seconds.";

                Log::channel('scrape_tender_detail')->info($message);
                $this->info($message);

                // Mark command as completed
                CommandProgress::markAsCompleted(
                    $this->getName(),
                    $message,
                    [
                        'total_tenders' => $totalTenders,
                        'processed' => $processedCount,
                        'execution_time' => round($executionTime, 2),
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage()
                    ]
                );
            } else {
                // Single kodeTender provided
                $message = "Starting scraping tender details for kodeTender: {$kodeTender}";
                Log::channel('scrape_tender_detail')->info($message);
                $this->info($message);

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    10,
                    100,
                    $message,
                    [
                        'kode_tender' => $kodeTender,
                        'stage' => 'processing_single_tender',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                try {
                    $this->scrapeTenderByKode($kodeTender);
                    $this->info("Scraping tender details completed.");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        50,
                        100,
                        "Scraping tender details completed for {$kodeTender}",
                        [
                            'kode_tender' => $kodeTender,
                            'stage' => 'tender_details_completed',
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    $this->info("Scraping jadwal tender for kodeTender: {$kodeTender}");
                    $this->scrapeJadwalByKode($kodeTender);
                    $this->info("Scraping jadwal tender completed.");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        90,
                        100,
                        "Scraping jadwal tender completed for {$kodeTender}",
                        [
                            'kode_tender' => $kodeTender,
                            'stage' => 'jadwal_completed',
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );
                } catch (\Exception $e) {
                    $errorMessage = "Error processing tender {$kodeTender}: " . $e->getMessage();
                    $this->error($errorMessage);
                    Log::channel('scrape_tender_detail')->error($errorMessage);

                    // Log system error
                    $this->logSystemError($e, "handle method - processing single tender: {$kodeTender}");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        100,
                        100,
                        $errorMessage,
                        [
                            'kode_tender' => $kodeTender,
                            'stage' => 'error_processing_tender',
                            'error' => $e->getMessage(),
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    throw $e;
                }

                $endTime = microtime(true);
                $executionTime = $endTime - $startTime;
                $message = "Scraping tender details completed for {$kodeTender} in " . round($executionTime, 2) . " seconds.";

                $this->info($message);

                // Mark command as completed
                CommandProgress::markAsCompleted(
                    $this->getName(),
                    $message,
                    [
                        'kode_tender' => $kodeTender,
                        'execution_time' => round($executionTime, 2),
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage()
                    ]
                );
            }

            return SymfonyCommand::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());
            Log::channel('scrape_tender_detail')->error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }

    /**
     * Fetch URL with exponential backoff retry
     *
     * @param string $url
     * @param int $maxRetries
     * @return array
     * @throws \Exception
     */
    protected function fetchUrlWithRetry($url, $maxRetries = 5)
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $maxRetries) {
            try {
                $attempt++;

                // Exponential backoff: 2^attempt * 1000ms with some jitter
                if ($attempt > 1) {
                    $backoff = (1.2 ** ($attempt - 1)) * 1000 + rand(100, 1000);
                    $backoffSeconds = round($backoff / 1000, 2);
                    $this->warn("Retry attempt {$attempt}/{$maxRetries} after {$backoffSeconds}s backoff...");
                    usleep($backoff * 1000); // Convert to microseconds
                }

                // Use different request options for each retry
                $options = [];
                if ($attempt > 1) {
                    // Add different user agent for retries
                    $userAgents = [
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
                        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36'
                    ];
                    $options['headers'] = [
                        'User-Agent' => $userAgents[array_rand($userAgents)],
                        'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language' => 'en-US,en;q=0.5',
                        'Cache-Control' => 'no-cache',
                        'Pragma' => 'no-cache'
                    ];

                    // Add random timeout
                    $options['timeout'] = rand(30, 60);
                }

                $response = $this->client->get($url, $options);
                $html = $response->getBody()->getContents();

                // Check if redirected to login page
                if (strpos($html, 'login') !== false && strpos($html, 'password') !== false) {
                    if ($attempt < $maxRetries) {
                        $this->warn("Redirected to login page. Will retry...");
                        $lastException = new \Exception("Redirected to login page");
                        continue;
                    } else {
                        throw new \Exception("Redirected to login page after {$maxRetries} attempts");
                    }
                }

                // Check for empty or invalid response
                if (empty($html) || !preg_match('/<[^>]*>/', $html)) {
                    if ($attempt < $maxRetries) {
                        $this->warn("Empty or invalid HTML response. Will retry...");
                        $lastException = new \Exception("Empty or invalid HTML response");
                        continue;
                    } else {
                        throw new \Exception("Empty or invalid HTML response after {$maxRetries} attempts");
                    }
                }

                $result = $this->validateAndGetContent($url, $response, $html);
                return $result;

            } catch (\GuzzleHttp\Exception\ConnectException $e) {
                $lastException = $e;
                $this->warn("Connection error on attempt {$attempt}/{$maxRetries}: " . $e->getMessage());

                // If this is the last attempt, rethrow
                if ($attempt >= $maxRetries) {
                    throw new \Exception("Failed to connect after {$maxRetries} attempts: " . $e->getMessage(), 0, $e);
                }
            } catch (\Exception $e) {
                $lastException = $e;

                // Periksa apakah pesan error mengandung "SKIP_RETRY"
                if (strpos($e->getMessage(), 'SKIP_RETRY:') === 0) {
                    $cleanMessage = str_replace('SKIP_RETRY: ', '', $e->getMessage());
                    $this->warn("Skipping retry: " . $cleanMessage);
                    // Log ke system error
                    $this->logSystemError($e, "Skipped retry for URL: {$url}");
                    // Langsung throw exception tanpa retry
                    throw new \Exception($cleanMessage, 0, $e);
                }

                $this->warn("Error on attempt {$attempt}/{$maxRetries}: " . $e->getMessage());

                // If this is the last attempt, rethrow
                if ($attempt >= $maxRetries) {
                    throw new \Exception("Failed after {$maxRetries} attempts: " . $e->getMessage(), 0, $e);
                }
            }
        }

        // If we get here, all retries failed
        throw $lastException ?: new \Exception("Failed after {$maxRetries} attempts");
    }

    protected function scrapeTenderByKode($kodeTender)
    {
        $tender = TenderLpse::where('kode_tender', $kodeTender)->first();

        if (!$tender) {
            Log::channel('scrape_tender_detail')->error("Tender not found for kode_tender: {$kodeTender}");
            $this->error("Tender not found for kode_tender: {$kodeTender}");
            return;
        }

        $lpse = Lpse::where('kd_lpse', $tender->kd_lpse)->first();
        if (!$lpse) {
            Log::channel('scrape_tender_detail')->error("LPSE not found for kd_lpse: {$tender->kd_lpse}");
            $this->error("LPSE not found for kd_lpse: {$tender->kd_lpse}");
            return;
        }

        $url = $lpse->url . '/lelang/' . $tender->kode_tender . '/pengumumanlelang';
        Log::channel('scrape_tender_detail')->info("Fetching URL: {$url}");

        try {
            // Fetch URL with retry mechanism
            try {
                $result = $this->fetchUrlWithRetry($url);
                $crawler = $result['crawler'];

                // Untuk Kode Tender
                $kodeTender = '';
                if ($crawler->filter('th:contains("Kode Tender")')->count() > 0) {
                    $kodeTender = $crawler->filter('th:contains("Kode Tender")')->siblings()->text();
                }

                // Untuk Alasan
                $alasan = '';
                if ($crawler->filter('th:contains("Alasan")')->count() > 0) {
                    $alasan = $crawler->filter('th:contains("Alasan")')->siblings()->text();
                }

                // Untuk syarat kualifikasi
                $syaratKualifikasi = '';
                if ($crawler->filter('th:contains("Syarat Kualifikasi")')->count() > 0) {
                    $syaratKualifikasi = $crawler->filter('th:contains("Syarat Kualifikasi")')->siblings()->html();
                }

                // untuk Kualifikasi Usaha
                $kualifikasiUsaha = '';
                if ($crawler->filter('th:contains("Kualifikasi Usaha")')->count() > 0) {
                    $kualifikasiUsaha = $crawler->filter('th:contains("Kualifikasi Usaha")')->siblings()->text();
                }

                // Untuk Tahap Tender
                $tahapTender = '';
                if ($crawler->filter('th:contains("Tahap Tender Saat Ini")')->count() > 0) {
                    $tahapTender = rapikan_str_tahap_tender(
                        $crawler->filter('th:contains("Tahap Tender Saat Ini")')->siblings()->text()
                    );
                }

                if (!empty($kodeTender)) {
                    $kodeTender = trim($kodeTender);
                    if (!empty($tahapTender)) {
                        $tahapTender = rapikan_str_tahap_tender(trim($tahapTender));
                        $tender = TenderLpse::where('kode_tender', $kodeTender)->first();
                        if ($tender) {
                            $batalStages = ['Tender Batal', 'Seleksi Batal'];

                            if (in_array($tahapTender, $batalStages)) {
                                $tender->status_tender = 'Ditutup';
                                $tender->save();
                                $this->info("Status tender {$tender->kode_tender} updated to Ditutup.");
                                Log::channel('scrape_tender_detail')->info("Status tender {$tender->kode_tender} updated to Ditutup.");
                            }

                            if ($tender->syarat_kualifikasi == null && $syaratKualifikasi != '') {
                                $tender->syarat_kualifikasi = $syaratKualifikasi;
                                $tender->save();
                                $this->info("Syarat Kualifikasi tender {$tender->kode_tender} with new data.");
                            }

                            if ($tender->kualifikasi_usaha == null && $kualifikasiUsaha != '') {
                                $tender->kualifikasi_usaha = $kualifikasiUsaha;
                                $tender->save();
                                $this->info("Kualifikasi Usaha tender {$tender->kode_tender} with new data {$kualifikasiUsaha}.");
                            }

                            if ($tender->keterangan == null && $alasan != '') {
                                $tender->keterangan = $alasan;
                                $tender->save();
                                $this->info("Alasan tender di-xxx kan {$tender->kode_tender} with new data.");
                            }

                            if ($tender->tahap_tender != $tahapTender) {
                                $this->info("Tahap Tender tender {$tender->kode_tender} diubah dari {$tender->tahap_tender} menjadi {$tahapTender}.");
                                $tender->tahap_tender = $tahapTender;
                                $tender->save();
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Periksa apakah error ini adalah HTTP 403, 429, dll yang harus di-skip
                if (strpos($e->getMessage(), 'HTTP Error: Received status code') !== false) {
                    $statusCode = null;
                    if (preg_match('/status code (\d+)/', $e->getMessage(), $matches)) {
                        $statusCode = (int)$matches[1];
                    }

                    // Daftar kode status yang harus di-skip
                    $skipStatusCodes = [403, 429, 404, 500, 502, 503];

                    if ($statusCode && in_array($statusCode, $skipStatusCodes)) {
                        $this->warn("Skipping tender {$tender->kode_tender} due to HTTP status {$statusCode}");
                        Log::channel('scrape_tender_detail')->warning("Skipping tender {$tender->kode_tender} due to HTTP status {$statusCode}");

                        // Log ke system error untuk monitoring
                        $this->logSystemError($e, "Skipped tender {$tender->kode_tender} due to HTTP status {$statusCode}");

                        // Tidak perlu throw exception, cukup return saja
                        return;
                    }
                }

                // Untuk error lainnya, throw exception agar ditangani oleh catch di luar
                throw $e;
            }

        } catch (\Exception $e) {
            Log::channel('scrape_tender_detail')->error("Error scraping tender {$tender->kode_tender}: " . $e->getMessage());
            $this->error("Error scraping tender {$tender->kode_tender}: " . $e->getMessage());

            // Log ke system error
            $this->logSystemError($e, "Error scraping tender {$tender->kode_tender}");
        }

        Log::channel('scrape_tender_detail')->info("Scraping tender details completed.");
    }

    public function scrapeJadwalByKode($kode_tender)
    {
        $tender = TenderLpse::where('kode_tender', $kode_tender)->first();

        if (!$tender) {
            Log::channel('scrape_tender_detail')->error("Tender not found for kode_tender: {$kode_tender}");
            $this->error("Tender not found for kode_tender: {$kode_tender}");
            return;
        }

        $lpse = Lpse::where('kd_lpse', $tender->kd_lpse)->first();
        if (!$lpse) {
            Log::channel('scrape_tender_detail')->error("LPSE not found for kd_lpse: {$tender->kd_lpse}");
            $this->error("LPSE not found for kd_lpse: {$tender->kd_lpse}");
            return;
        }

        $url = $lpse->url . '/lelang/' . $kode_tender . '/jadwal';
        Log::channel('scrape_tender_detail')->info("Fetching Jadwal URL: {$url}");

        try {
            // Fetch URL with retry mechanism
            try {
                $result = $this->fetchUrlWithRetry($url);
                $crawler = $result['crawler'];

                $jadwalTender = [];
                $crawler->filter('table.table-sm tr')->each(function (Crawler $row, $i) use (&$jadwalTender) {
                    if ($i > 0) { // Skip header tabel
                        $jadwalTender[] = [
                            'no' => trim($row->filter('td')->eq(0)->text()),
                            'tahap' => trim($row->filter('td')->eq(1)->text()),
                            'mulai' => $this->parseDateTime(trim($row->filter('td')->eq(2)->text())),
                            'sampai' => $this->parseDateTime(trim($row->filter('td')->eq(3)->text())),
                            'perubahan' => trim($row->filter('td')->eq(4)->text())
                        ];
                    }
                });

                $this->processJadwal($tender, $jadwalTender);
            } catch (\Exception $e) {
                // Periksa apakah error ini adalah HTTP 403, 429, dll yang harus di-skip
                if (strpos($e->getMessage(), 'HTTP Error: Received status code') !== false) {
                    $statusCode = null;
                    if (preg_match('/status code (\d+)/', $e->getMessage(), $matches)) {
                        $statusCode = (int)$matches[1];
                    }

                    // Daftar kode status yang harus di-skip
                    $skipStatusCodes = [403, 429, 404, 500, 502, 503];

                    if ($statusCode && in_array($statusCode, $skipStatusCodes)) {
                        $this->warn("Skipping jadwal tender {$kode_tender} due to HTTP status {$statusCode}");
                        Log::channel('scrape_tender_detail')->warning("Skipping jadwal tender {$kode_tender} due to HTTP status {$statusCode}");

                        // Log ke system error untuk monitoring
                        $this->logSystemError($e, "Skipped jadwal tender {$kode_tender} due to HTTP status {$statusCode}");

                        // Tidak perlu throw exception, cukup return saja
                        return;
                    }
                }

                // Untuk error lainnya, throw exception agar ditangani oleh catch di luar
                throw $e;
            }
        } catch (\Exception $e) {
            Log::channel('scrape_tender_detail')->error("Error scraping jadwal tender {$kode_tender}: " . $e->getMessage());
            $this->error("Error scraping jadwal tender {$kode_tender}: " . $e->getMessage());

            // Log ke system error
            $this->logSystemError($e, "Error scraping jadwal tender {$kode_tender}");
        }
    }

    private function processJadwal($tender, $jadwalData)
    {
        // Hapus jadwal yang ada
        $deletedCount = Jadwal::where('kode_tender', $tender->kode_tender)->delete();
        $this->info("Deleted {$deletedCount} existing jadwal entries for tender {$tender->kode_tender}.");
        Log::channel('scrape_tender_detail')->info("Deleted {$deletedCount} existing jadwal entries for tender {$tender->kode_tender}.");

        // Jika tidak ada data jadwal, log dan return
        if (empty($jadwalData)) {
            $this->warn("No jadwal data found for tender {$tender->kode_tender}.");
            Log::channel('scrape_tender_detail')->warning("No jadwal data found for tender {$tender->kode_tender}.");
            return;
        }

        $this->info("Processing {$tender->kode_tender} jadwal with " . count($jadwalData) . " entries.");
        Log::channel('scrape_tender_detail')->info("Processing {$tender->kode_tender} jadwal with " . count($jadwalData) . " entries.");

        $successCount = 0;
        $errorCount = 0;

        foreach ($jadwalData as $data) {
            try {
                // Validasi data
                if (empty($data['mulai']) || empty($data['sampai'])) {
                    $this->warn("Invalid date data for jadwal step {$data['no']}: mulai={$data['mulai']}, sampai={$data['sampai']}");
                    $errorCount++;
                    continue;
                }

                $tender->jadwal()->create([
                    'step' => $data['no'],
                    'tahap' => $data['tahap'],
                    'tanggal_mulai' => Carbon::parse($data['mulai']),
                    'tanggal_selesai' => Carbon::parse($data['sampai']),
                    'keterangan' => $data['perubahan'] ?? null
                ]);

                $successCount++;
            } catch (\Exception $e) {
                $errorCount++;
                $this->logError(
                    $tender->kode_tender,
                    "Jadwal processing error for step {$data['no']}: " . $e->getMessage()
                );
            }
        }

        $this->info("Jadwal processing completed: {$successCount} entries added, {$errorCount} errors.");
        Log::channel('scrape_tender_detail')->info("Jadwal processing completed: {$successCount} entries added, {$errorCount} errors.");
    }

    private function logError($kodeTender, $message)
    {
        Log::channel('scrape_tender_detail')->error("Tender {$kodeTender}: {$message}");

        // Log to system errors
        $this->logSystemError(
            new \Exception($message),
            "Tender {$kodeTender}"
        );
    }

    private function parseDateTime($dateString)
    {
        try {
            // Try parsing with Indonesian month names
            $months = [
                'Januari' => 'January',
                'Februari' => 'February',
                'Maret' => 'March',
                'April' => 'April',
                'Mei' => 'May',
                'Juni' => 'June',
                'Juli' => 'July',
                'Agustus' => 'August',
                'September' => 'September',
                'Oktober' => 'October',
                'November' => 'November',
                'Desember' => 'December',
            ];

            foreach ($months as $id => $en) {
                $dateString = str_replace($id, $en, $dateString);
            }

            return Carbon::createFromFormat('d F Y H:i', $dateString)->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            Log::channel('scrape_tender_detail')->error("Date parse error for '{$dateString}': " . $e->getMessage());
            return null;
        }
    }
}
