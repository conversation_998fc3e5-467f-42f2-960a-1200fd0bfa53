<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ListPerusahaanTender;
use App\Models\PesertaTender;
use App\Models\PemenangLpse;
use App\Models\PaketPengadaan;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class DeduplikasiPerusahaanCommand extends Command
{
    use CommandManagementTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'perusahaan:deduplikasi
                            {--force : Force run even if command is already running}
                            {--timeout=3600 : Timeout in seconds}
                            {--lock-name= : Custom lock name for command}
                            {--batch-size=100 : Number of records to process in each batch}
                            {--dry-run : Show what would be changed without making changes}
                            {--only-npwp : Only deduplicate companies with NPWP}
                            {--only-name : Only deduplicate companies with similar names}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deduplikasi perusahaan dalam tabel list_perusahaan_tender dan update referensi di tabel terkait';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time
            $batchSize = (int)$this->option('batch-size');
            $dryRun = $this->option('dry-run');
            $onlyNpwp = $this->option('only-npwp');
            $onlyName = $this->option('only-name');

            $message = 'Memulai deduplikasi perusahaan...';
            $this->info($message);

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                $message,
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'batch_size' => $batchSize,
                    'dry_run' => $dryRun,
                    'only_npwp' => $onlyNpwp,
                    'only_name' => $onlyName
                ]
            );

            if ($dryRun) {
                $this->warn('Mode dry-run aktif. Tidak ada perubahan yang akan disimpan ke database.');
            }

            // Deduplikasi berdasarkan NPWP
            if (!$onlyName) {
                $this->deduplikasiByNpwp($dryRun);
            }

            // Deduplikasi berdasarkan nama
            if (!$onlyNpwp) {
                $this->deduplikasiByName($dryRun);
            }

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            $message = "Deduplikasi perusahaan selesai dalam " . round($executionTime, 2) . " detik!";

            $this->info($message);

            // Mark command as completed
            CommandProgress::markAsCompleted(
                $this->getName(),
                $message,
                [
                    'execution_time' => round($executionTime, 2),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage(),
                    'dry_run' => $dryRun
                ]
            );

            return SymfonyCommand::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Terjadi kesalahan: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command gagal: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }

    /**
     * Deduplikasi perusahaan berdasarkan NPWP
     *
     * @param bool $dryRun
     * @return void
     */
    private function deduplikasiByNpwp(bool $dryRun): void
    {
        $this->info('Memulai deduplikasi berdasarkan NPWP...');

        // Cari perusahaan dengan NPWP yang sama (tidak null)
        $duplicateNpwps = DB::table('list_perusahaan_tender')
            ->select('npwp', DB::raw('COUNT(*) as count'))
            ->whereNotNull('npwp')
            ->groupBy('npwp')
            ->having('count', '>', 1)
            ->get();

        $totalDuplicates = count($duplicateNpwps);
        $this->info("Ditemukan {$totalDuplicates} NPWP duplikat.");

        if ($totalDuplicates === 0) {
            return;
        }

        // Update progress
        CommandProgress::updateProgress(
            $this->getName(),
            0,
            $totalDuplicates,
            "Memproses {$totalDuplicates} NPWP duplikat...",
            [
                'stage' => 'deduplikasi_npwp',
                'total_duplicates' => $totalDuplicates,
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        $processedCount = 0;
        $mergedCount = 0;

        foreach ($duplicateNpwps as $duplicate) {
            $processedCount++;
            $progress = min(50, round(($processedCount / $totalDuplicates) * 50));

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                $progress,
                100,
                "Memproses NPWP duplikat {$processedCount}/{$totalDuplicates}: {$duplicate->npwp}",
                [
                    'stage' => 'deduplikasi_npwp',
                    'processed' => $processedCount,
                    'total' => $totalDuplicates,
                    'merged' => $mergedCount,
                    'current_npwp' => $duplicate->npwp,
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Ambil semua perusahaan dengan NPWP yang sama
            $companies = ListPerusahaanTender::where('npwp', $duplicate->npwp)
                ->orderBy('id')
                ->get();

            if ($companies->count() <= 1) {
                continue;
            }

            // Pilih perusahaan utama (yang pertama berdasarkan ID)
            $mainCompany = $companies->first();
            $duplicateCompanies = $companies->slice(1);

            $this->info("Menggabungkan {$duplicateCompanies->count()} perusahaan duplikat dengan NPWP {$duplicate->npwp} ke perusahaan ID {$mainCompany->id}");

            if (!$dryRun) {
                $this->mergeCompanies($mainCompany, $duplicateCompanies);
                $mergedCount += $duplicateCompanies->count();
            } else {
                $this->info("  [DRY RUN] Akan menggabungkan: " . $duplicateCompanies->pluck('id')->implode(', ') . " ke {$mainCompany->id}");
            }
        }

        $this->info("Deduplikasi berdasarkan NPWP selesai. {$mergedCount} perusahaan digabungkan.");
    }

    /**
     * Deduplikasi perusahaan berdasarkan nama
     *
     * @param bool $dryRun
     * @return void
     */
    private function deduplikasiByName(bool $dryRun): void
    {
        $this->info('Memulai deduplikasi berdasarkan nama...');

        // Ambil semua perusahaan yang belum memiliki NPWP
        $companies = ListPerusahaanTender::whereNull('npwp')
            ->orderBy('id')
            ->get();

        $totalCompanies = $companies->count();
        $this->info("Ditemukan {$totalCompanies} perusahaan tanpa NPWP untuk diproses.");

        if ($totalCompanies === 0) {
            return;
        }

        // Update progress
        CommandProgress::updateProgress(
            $this->getName(),
            50,
            100,
            "Memproses {$totalCompanies} perusahaan tanpa NPWP...",
            [
                'stage' => 'deduplikasi_nama',
                'total_companies' => $totalCompanies,
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        $processedCount = 0;
        $mergedCount = 0;
        $processed = [];

        foreach ($companies as $company) {
            // Skip jika perusahaan sudah diproses
            if (in_array($company->id, $processed)) {
                continue;
            }

            $processedCount++;
            $progress = min(99, 50 + round(($processedCount / $totalCompanies) * 50));

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                $progress,
                100,
                "Memproses perusahaan tanpa NPWP {$processedCount}/{$totalCompanies}: {$company->nama_peserta}",
                [
                    'stage' => 'deduplikasi_nama',
                    'processed' => $processedCount,
                    'total' => $totalCompanies,
                    'merged' => $mergedCount,
                    'current_company' => $company->nama_peserta,
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Cari perusahaan dengan nama yang mirip
            $similarCompanies = $this->findSimilarCompanies($company, $companies);

            if ($similarCompanies->isEmpty()) {
                continue;
            }

            $this->info("Ditemukan {$similarCompanies->count()} perusahaan dengan nama mirip dengan {$company->nama_peserta} (ID: {$company->id})");

            if (!$dryRun) {
                $this->mergeCompanies($company, $similarCompanies);
                $mergedCount += $similarCompanies->count();

                // Tandai perusahaan yang sudah digabungkan
                $processed = array_merge($processed, $similarCompanies->pluck('id')->toArray());
            } else {
                $this->info("  [DRY RUN] Akan menggabungkan: " . $similarCompanies->pluck('id')->implode(', ') . " ke {$company->id}");
            }
        }

        $this->info("Deduplikasi berdasarkan nama selesai. {$mergedCount} perusahaan digabungkan.");
    }

    /**
     * Cari perusahaan dengan nama yang mirip
     *
     * @param ListPerusahaanTender $company
     * @param \Illuminate\Support\Collection $companies
     * @return \Illuminate\Support\Collection
     */
    private function findSimilarCompanies(ListPerusahaanTender $company, $companies)
    {
        $similarCompanies = collect();

        foreach ($companies as $otherCompany) {
            // Skip jika perusahaan sama
            if ($company->id === $otherCompany->id) {
                continue;
            }

            // Hitung skor kecocokan
            $score = ListPerusahaanTender::matchScore(
                $company->normalized_name,
                $otherCompany->normalized_name
            );

            // Jika skor di atas threshold, tambahkan ke daftar
            if ($score >= 85) {
                $similarCompanies->push($otherCompany);
            }
        }

        return $similarCompanies;
    }

    /**
     * Gabungkan perusahaan duplikat ke perusahaan utama
     *
     * @param ListPerusahaanTender $mainCompany
     * @param \Illuminate\Support\Collection $duplicateCompanies
     * @return void
     */
    private function mergeCompanies(ListPerusahaanTender $mainCompany, $duplicateCompanies): void
    {
        DB::beginTransaction();

        try {
            foreach ($duplicateCompanies as $duplicateCompany) {
                $this->info("  Menggabungkan perusahaan ID {$duplicateCompany->id} ({$duplicateCompany->nama_peserta}) ke ID {$mainCompany->id} ({$mainCompany->nama_peserta})");

                // Update data perusahaan utama jika ada informasi tambahan dari duplikat
                $this->updateMainCompanyData($mainCompany, $duplicateCompany);

                // Update referensi di tabel PesertaTender
                $this->updatePesertaTender($mainCompany->id, $duplicateCompany->id);

                // Update referensi di tabel PemenangLpse
                $this->updatePemenangLpse($mainCompany->id, $duplicateCompany->id);

                // Update referensi di tabel PaketPengadaan (id_penyedia)
                $this->updatePaketPengadaanPenyedia($mainCompany->id, $duplicateCompany->id);

                // Update referensi di tabel PaketPengadaan (id_pelaksana)
                $this->updatePaketPengadaanPelaksana($mainCompany->id, $duplicateCompany->id);

                // Hapus perusahaan duplikat
                $duplicateCompany->delete();
                $this->info("  Perusahaan ID {$duplicateCompany->id} berhasil dihapus");
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("  Gagal menggabungkan perusahaan: " . $e->getMessage());
            $this->logSystemError($e, "mergeCompanies for mainCompany ID {$mainCompany->id}");
        }
    }

    /**
     * Update data perusahaan utama dengan informasi dari perusahaan duplikat
     *
     * @param ListPerusahaanTender $mainCompany
     * @param ListPerusahaanTender $duplicateCompany
     * @return void
     */
    private function updateMainCompanyData(ListPerusahaanTender $mainCompany, ListPerusahaanTender $duplicateCompany): void
    {
        $updated = false;

        // Jika perusahaan utama tidak memiliki alamat tapi duplikat punya
        if (empty($mainCompany->alamat) && !empty($duplicateCompany->alamat)) {
            $mainCompany->alamat = $duplicateCompany->alamat;
            $updated = true;
            $this->info("    Memperbarui alamat perusahaan utama");
        }

        // Jika perusahaan utama tidak memiliki NPWP tapi duplikat punya
        if (empty($mainCompany->npwp) && !empty($duplicateCompany->npwp)) {
            $mainCompany->npwp = $duplicateCompany->npwp;
            $updated = true;
            $this->info("    Memperbarui NPWP perusahaan utama");
        }

        // Jika perusahaan utama tidak memiliki npwp_blur tapi duplikat punya
        if (empty($mainCompany->npwp_blur) && !empty($duplicateCompany->npwp_blur)) {
            $mainCompany->npwp_blur = $duplicateCompany->npwp_blur;
            $updated = true;
            $this->info("    Memperbarui NPWP blur perusahaan utama");
        }

        // Jika perusahaan utama tidak memiliki partial_npwp tapi duplikat punya
        if (empty($mainCompany->partial_npwp) && !empty($duplicateCompany->partial_npwp)) {
            $mainCompany->partial_npwp = $duplicateCompany->partial_npwp;
            $updated = true;
            $this->info("    Memperbarui partial NPWP perusahaan utama");
        }

        // Update pembeda jika perlu
        if (!empty($mainCompany->partial_npwp) && $mainCompany->pembeda !== str_replace(' ', '', $mainCompany->normalized_name) . $mainCompany->partial_npwp) {
            $mainCompany->pembeda = str_replace(' ', '', $mainCompany->normalized_name) . $mainCompany->partial_npwp;
            $updated = true;
            $this->info("    Memperbarui pembeda perusahaan utama");
        }

        if ($updated) {
            $mainCompany->save();
        }
    }

    /**
     * Update referensi di tabel PesertaTender
     *
     * @param int $mainCompanyId
     * @param int $duplicateCompanyId
     * @return void
     */
    private function updatePesertaTender(int $mainCompanyId, int $duplicateCompanyId): void
    {
        $count = PesertaTender::where('id_peserta', $duplicateCompanyId)->count();

        if ($count > 0) {
            // Cek apakah ada konflik (peserta yang sama di tender yang sama)
            $conflicts = DB::table('peserta_tender as pt1')
                ->join('peserta_tender as pt2', function ($join) use ($mainCompanyId, $duplicateCompanyId) {
                    $join->on('pt1.kode_tender', '=', 'pt2.kode_tender')
                        ->where('pt1.id_peserta', '=', $mainCompanyId)
                        ->where('pt2.id_peserta', '=', $duplicateCompanyId);
                })
                ->count();

            if ($conflicts > 0) {
                $this->warn("    Ditemukan {$conflicts} konflik di tabel peserta_tender. Menghapus entri duplikat.");
                PesertaTender::where('id_peserta', $duplicateCompanyId)->delete();
            } else {
                PesertaTender::where('id_peserta', $duplicateCompanyId)
                    ->update(['id_peserta' => $mainCompanyId]);
                $this->info("    Memperbarui {$count} referensi di tabel peserta_tender");
            }
        }
    }

    /**
     * Update referensi di tabel PemenangLpse
     *
     * @param int $mainCompanyId
     * @param int $duplicateCompanyId
     * @return void
     */
    private function updatePemenangLpse(int $mainCompanyId, int $duplicateCompanyId): void
    {
        $count = PemenangLpse::where('id_peserta', $duplicateCompanyId)->count();

        if ($count > 0) {
            // Cek apakah ada konflik (pemenang yang sama di tender yang sama)
            $conflicts = DB::table('pemenang_lpse as p1')
                ->join('pemenang_lpse as p2', function ($join) use ($mainCompanyId, $duplicateCompanyId) {
                    $join->on('p1.kode_tender', '=', 'p2.kode_tender')
                        ->where('p1.id_peserta', '=', $mainCompanyId)
                        ->where('p2.id_peserta', '=', $duplicateCompanyId);
                })
                ->count();

            if ($conflicts > 0) {
                $this->warn("    Ditemukan {$conflicts} konflik di tabel pemenang_lpse. Menghapus entri duplikat.");
                PemenangLpse::where('id_peserta', $duplicateCompanyId)->delete();
            } else {
                PemenangLpse::where('id_peserta', $duplicateCompanyId)
                    ->update(['id_peserta' => $mainCompanyId]);
                $this->info("    Memperbarui {$count} referensi di tabel pemenang_lpse");
            }
        }
    }

    /**
     * Update referensi di tabel PaketPengadaan (id_penyedia)
     *
     * @param int $mainCompanyId
     * @param int $duplicateCompanyId
     * @return void
     */
    private function updatePaketPengadaanPenyedia(int $mainCompanyId, int $duplicateCompanyId): void
    {
        $count = PaketPengadaan::where('id_penyedia', $duplicateCompanyId)->count();

        if ($count > 0) {
            PaketPengadaan::where('id_penyedia', $duplicateCompanyId)
                ->update(['id_penyedia' => $mainCompanyId]);
            $this->info("    Memperbarui {$count} referensi id_penyedia di tabel ekatalog_paket_pengadaan");
        }
    }

    /**
     * Update referensi di tabel PaketPengadaan (id_pelaksana)
     *
     * @param int $mainCompanyId
     * @param int $duplicateCompanyId
     * @return void
     */
    private function updatePaketPengadaanPelaksana(int $mainCompanyId, int $duplicateCompanyId): void
    {
        $count = PaketPengadaan::where('id_pelaksana', $duplicateCompanyId)->count();

        if ($count > 0) {
            PaketPengadaan::where('id_pelaksana', $duplicateCompanyId)
                ->update(['id_pelaksana' => $mainCompanyId]);
            $this->info("    Memperbarui {$count} referensi id_pelaksana di tabel ekatalog_paket_pengadaan");
        }
    }
}
