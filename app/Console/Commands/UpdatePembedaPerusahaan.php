<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ListPerusahaanTender;
use Illuminate\Support\Facades\Log;

class UpdatePembedaPerusahaan extends Command
{
    protected $signature = 'update:pembeda-perusahaan';
    protected $description = 'Update pembeda field for ListPerusahaanTender where pembeda is NULL or empty';

    public function handle()
    {
        $this->info('Memulai proses update pembeda perusahaan...');
        $query = ListPerusahaanTender::whereNull('pembeda')
            ->orWhere('pembeda', '');

        $total = $query->count();
        $this->info("Total perusahaan yang perlu diupdate: {$total}");

        $updatedCount = 0;
        $bar = $this->output->createProgressBar($total);
        $bar->start();

        $query->chunkById(100, function ($perusahaans) use (&$updatedCount, $bar) {
            foreach ($perusahaans as $perusahaan) {
                try {
                    // Use model's normalizeName method and extractPartialNpwp
                    $normalizedNameNoSpaces = $perusahaan->normalized_name;
                    $partialNpwp = $perusahaan->partial_npwp;

                    $pembeda = $normalizedNameNoSpaces . $partialNpwp;

                    $perusahaan->pembeda = $pembeda;
                    $perusahaan->save();

                    $updatedCount++;
                } catch (\Exception $e) {
                    $this->error("Gagal update perusahaan ID {$perusahaan->id}: " . $e->getMessage());
                    Log::error("Gagal update pembeda perusahaan ID {$perusahaan->id}: " . $e->getMessage());
                }
                $bar->advance();
            }
        });

        $bar->finish();
        $this->newLine();
        $this->info("Proses update selesai. Total perusahaan diupdate: {$updatedCount}");
    }
}
