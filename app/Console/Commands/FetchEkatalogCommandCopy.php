<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;
use App\Models\PaketPengadaan;
use App\Models\Produk;
use App\Models\TransaksiPaket;
use App\Models\ListPerusahaanTender;
use Illuminate\Support\Facades\DB;
use Exception;
use DateTime;

class FetchEkatalogCommandCopy extends Command
{
    protected $signature = 'copyfetch:ekatalog {start?} {end?}';
    protected $description = 'Fetch data from Redash Ekatalog looping by date range';

    private const REDASH_API_URL = "https://redash-e-katalog.lkpp.go.id/api/queries/157/results";
    private const MAX_WAIT_TIME = 300; // Maximum wait time in seconds  
    private const AUTH_HEADER = 'Key BnXT185EjSxCxNa4YgFPoy7oiA6M4VKtD2sa4W8L';

    private $proxy;

    public function __construct()
    {
        parent::__construct();
        $this->proxy = env('PROXIES_SSH');
    }

    public function handle()
    {
        register_shutdown_function([$this, 'cleanup']);

        if ($this->isCommandRunning()) {
            $this->error("Command 'fetch:ekatalog' is already running.");
            return Command::FAILURE;
        }

        $this->markCommandAsRunning();

        try {
            $startTime = microtime(true); // Start measuring time

            $startDate = $this->argument('start') ?? $this->getLastStartDate();
            $endDate = $this->argument('end') ?? now()->format('Y-m-d H:i:s');

            $currentDate = Carbon::parse($startDate);
            $finalDate = Carbon::parse($endDate);

            $this->info("Fetching data from Redash Ekatalog from $startDate to $endDate...");

            while ($currentDate <= $finalDate) {
                $this->fetchDataForDate($currentDate);
                $currentDate->addDay();
            }

            $endTime = microtime(true); // End measuring time
            $executionTime = $endTime - $startTime;
            $this->info("Fetching data from $startDate to $endDate completed in " . round($executionTime, 2) . " seconds.");
        } catch (Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());
        }

        $this->markCommandAsNotRunning();
        return Command::SUCCESS;
    }

    public function cleanup()
    {
        if ($this->isCommandRunning()) {
            $this->markCommandAsNotRunning();
            $this->info("Command cleanup executed, marked as not running.");
        }
    }

    private function isCommandRunning(): bool
    {
        return DB::table('commands_status')
            ->where('command_name', 'fetch:ekatalog')
            ->where('is_running', true)
            ->exists();
    }

    private function markCommandAsRunning(): void
    {
        DB::table('commands_status')->updateOrInsert(
            ['command_name' => 'fetch:ekatalog'],
            ['is_running' => true, 'started_at' => now()]
        );
    }

    private function jagaStatusRunning(): void
    {
        DB::table('commands_status')->updateOrInsert(
            ['command_name' => 'fetch:ekatalog'],
            ['is_running' => true]
        );
    }

    private function markCommandAsNotRunning(): void
    {
        DB::table('commands_status')
            ->where('command_name', 'fetch:ekatalog')
            ->update(['is_running' => false, 'finished_at' => now()]);
    }

    private function getLastStartDate(): string
    {
        $lastDate = DB::table('ekatalog_paket_pengadaan')
            ->orderBy('tanggal_paket', 'desc')
            ->value('tanggal_paket');

        return $lastDate ?: now()->subYear()->format('Y-m-d H:i:s');
    }

    private function fetchDataForDate(Carbon $currentDate): void
    {
        $currentStart = $currentDate->format('Y-m-d');
        $this->info("Fetching data for date: $currentStart");

        $payload = $this->buildPayload($currentStart);

        $options = [];
        if ($this->proxy) {
            $options['proxy'] = $this->proxy;
            $this->info("Using proxy: {$this->proxy}");
        } else {
            $this->info("No proxy configured, making direct request.");
        }

        $response = Http::withHeaders($this->getHeaders())
                ->timeout(360)
                ->withOptions($options)
                ->post(self::REDASH_API_URL, $payload);
        $this->info("HTTP Status: " . $response->status());

        if ($response->successful()) {
            $this->handleJobResponse($response->json(), $currentStart);
        } else {
            $this->error("Failed to initiate job for $currentStart. HTTP Status: " . $response->status());
        }
    }

    private function buildPayload(string $currentStart): array
    {
        return [
            "queryId" => 157,
            "parameters" => [
                "Tanggal" => [
                    "start" => $currentStart,
                    "end" => $currentStart
                ],
                "Jenis_Katalog" => ["ALL"],
                "pengelola" => ["ALL"],
                "provinsi" => ["ALL"],
                "Tahun Anggaran" => ["ALL"],
                "Nama Satker Transaksi" => ["ALL"],
                "klpd" => ["ALL"]
            ]
        ];
    }

    private function getHeaders(): array
    {
        return [
            'Authorization' => self::AUTH_HEADER,
            'Accept' => 'application/json, text/plain, */*',
            'Content-Type' => 'application/json;charset=UTF-8',
            'Referer' => 'https://redash-e-katalog.lkpp.go.id/public/dashboards/' . self::AUTH_HEADER
        ];
    }

    private function handleJobResponse(array $jobData, string $currentStart): void
    {
        $maxWaitTime = self::MAX_WAIT_TIME;
        $elapsedTime = 0;

        while (isset($jobData['job']) && in_array($jobData['job']['status'], [1, 2])) {
            $this->info("Job for $currentStart is still in progress. Waiting...");
            sleep(3);
            $elapsedTime += 3;

            if ($elapsedTime > $maxWaitTime) {
                $this->error("Job for $currentStart exceeded the maximum wait time of $maxWaitTime seconds.");
                return;
            }

            $jobId = $jobData['job']['id'];
            $statusUrl = "https://redash-e-katalog.lkpp.go.id/api/jobs/$jobId";

            $options = [];
            if ($this->proxy) {
                $options['proxy'] = $this->proxy;
                $this->info("Using proxy: {$this->proxy}");
            } else {
                $this->info("No proxy configured, making direct request.");
            }

            $jobResponse = Http::withHeaders($this->getHeaders())
                ->timeout(360)
                ->withOptions($options)
                ->get($statusUrl);

            $this->info("Checking job status at URL: $statusUrl");
            if ($jobResponse->successful()) {
                $jobData = $jobResponse->json();
            } else {
                $this->error("Failed to fetch job status for $currentStart. HTTP Status: " . $jobResponse->status());
                return;
            }
        }

        if (isset($jobData['job']) && $jobData['job']['status'] == 3) {
            $this->fetchJobResults($currentStart);
        } else {
            if ($jobData['query_result']) {
                $this->fetchJobResults($currentStart);
            } else {
                $this->error("Job for $currentStart did not complete successfully.");
            }
        }
    }

    private function fetchJobResults(string $currentStart): void
    {
        $payload = $this->buildPayload($currentStart);

        $options = [];
        if ($this->proxy) {
            $options['proxy'] = $this->proxy;
            $this->info("Using proxy: {$this->proxy}");
        } else {
            $this->info("No proxy configured, making direct request.");
        }

        $resultResponse = Http::withHeaders($this->getHeaders())
                ->timeout(360)
                ->withOptions($options)
                ->post(self::REDASH_API_URL, $payload);
        $this->info("HTTP Status for result response: " . $resultResponse->status());

        if ($resultResponse->successful()) {
            $resultData = $resultResponse->json();
            $fetchDate = (new DateTime($resultData['query_result']['retrieved_at']))->format('Y-m-d H:i:s');
                    $this->info(" Tanggal Fetch : $fetchDate");
            if (isset($resultData['query_result']['data']['rows'])) {
                $rows = $resultData['query_result']['data']['rows'];
                if (count($rows) > 0) {
                    $this->info("Jumlah rows: " . count($rows));
                    $this->processFetchedData($rows, $currentStart);
                } else {
                    $this->error("Rows ditemukan, namun jumlahnya adalah 0.");
                }
            } else {
                $this->error("query_result atau query_result->data tidak ditemukan di response body.");
            }
        } else {
            $this->error("Failed to fetch completed job result for $currentStart. HTTP Status: " . $resultResponse->status());
        }
    }

    private function processFetchedData(array $data, $currentStart): void
    {
        $originalCount = count($data);
        $this->info("Data yang akan diproses: $originalCount");

        $nomorPaketData = array_column($data, 'Nomor Paket');

        $existingNomorPaket = PaketPengadaan::whereIn('nomor_paket', $nomorPaketData)->pluck('nomor_paket')->toArray();

        $data = array_filter($data, function ($item) use ($existingNomorPaket) {
            return !in_array($item['Nomor Paket'], $existingNomorPaket);
        });

        $filteredCount = count($data);
        $this->info("Data yang sudah ada di database: " . ($originalCount - $filteredCount));
        $this->info("Sisa data yang akan diproses: $filteredCount");

        // Chunk the data for batch insertion
$chunks = array_chunk($data, 1500); // Reduce chunk size to 500

        foreach ($chunks as $chunkIndex => $chunk) {
            $this->info("Processing chunk $chunkIndex of size " . count($chunk));

            $chunkStartTime = microtime(true); // Start measuring time for chunk

            $progressBar = $this->output->createProgressBar(count($chunk));
            $progressBar->start();

            foreach ($chunk as $rowIndex => $item) {
                if (empty($item['Nama Penyedia']) || empty($item['Nama Pelaksana Pekerjaan'])) {
                    $this->info("Skipping row $rowIndex due to missing 'Nama Penyedia' or 'Nama Pelaksana Pekerjaan'");
                    $progressBar->advance();
                    continue;
                }

                $this->jagaStatusRunning();

                $retries = 3;
                while ($retries > 0) {
                    try {
                        DB::transaction(function () use ($item) {
                            $penyedia = $this->saveOrGetPerusahaan($item['Nama Penyedia']);
                            $pelaksana = $this->saveOrGetPerusahaan($item['Nama Pelaksana Pekerjaan']);
                            $paket = $this->saveOrUpdatePaketPengadaan($item, $penyedia->id, $pelaksana->id);

                            if ($paket) {
                                $produk = $this->saveOrGetProduk($item);
                                if ($produk) {
                                    $this->saveTransaksiPaket($item, $paket->id_paket, $produk->id_produk);
                                }
                            }
                        });

                        // Jika berhasil, keluar dari loop retry
                        break;
                    } catch (\Exception $e) {
                        $this->error("An error occurred: " . $e->getMessage());
                        $retries--;
                        if ($retries <= 0) {
                            throw $e; // Lempar kembali exception setelah habis retry
                        }
                        sleep(1); // Beri waktu sejenak sebelum mencoba kembali
                    }
                }

                $progressBar->advance();
            }

            $progressBar->finish();
            $this->info("\nChunk $chunkIndex completed.");

            $chunkEndTime = microtime(true); // End measuring time for chunk
            $chunkExecutionTime = $chunkEndTime - $chunkStartTime;
            $this->info("Chunk $chunkIndex processed in " . round($chunkExecutionTime, 2) . " seconds.");
        }
    }

    private function saveOrGetPerusahaan(string $namaPerusahaan)
    {
        $normalizedName    = ListPerusahaanTender::normalizeName($namaPerusahaan);
        $normalizedNoSpace = str_replace(' ', '', $normalizedName);

        // Mencari berdasarkan pembeda jika tersedia
        $perusahaan = ListPerusahaanTender::where('pembeda', $normalizedNoSpace)->first();

        if (! $perusahaan) {
            // Mencari berdasarkan nama perusahaan
            $perusahaan = ListPerusahaanTender::where('normalized_name', $normalizedName)->first();
        }

        if ($perusahaan) {
            return $perusahaan;
        } else {
            try {
                $pembeda     = $normalizedNoSpace;
                $npwp        = null;
                $npwpBlur    = null;
                $partialNpwp = null;
                $perusahaan  = ListPerusahaanTender::create([
                    'npwp'            => $npwp,
                    'npwp_blur'       => $npwpBlur,
                    'nama_peserta'    => $namaPerusahaan,
                    'normalized_name' => $normalizedName,
                    'partial_npwp'    => $partialNpwp,
                    'pembeda'         => $pembeda,
                    'alamat'          => null,
                ]);
                // $this->info("Created new perusahaan: {$perusahaan->nama_peserta}");
            } catch (\Illuminate\Database\QueryException $e) {
                if ($e->getCode() == 23000) {
                    $randomPrefix = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 3);
                    //  $this->warn("Duplikasi NPWP terdeteksi untuk {$namaPerusahaan}, menambahkan prefix '{$randomPrefix}'");
                    $perusahaan = ListPerusahaanTender::create([
                        'npwp'            => $npwp,
                        'npwp_blur'       => $npwpBlur,
                        'nama_peserta'    => $namaPerusahaan,
                        'normalized_name' => $normalizedName,
                        'partial_npwp'    => $partialNpwp,
                        'pembeda'         => $pembeda,
                        'alamat'          => null,
                    ]);
                    // $this->info("Created new perusahaan dengan NPWP unik: {$perusahaan->nama_peserta}");
                } else {
                    throw $e;
                }
            }
        }

        return $perusahaan;
    }

    private function maskNpwp($npwp)
    {
        return preg_replace_callback(
            '/^(\d)\d\.(\d)\d\d\.(\d)\d\d\.(\d)-(\d)(\d)(\d)\.(\d)(\d)(\d)$/',
            function ($matches) {
                return "{$matches[1]}*.{$matches[2]}**.{$matches[3]}**.*-*{$matches[6]}{$matches[7]}.**{$matches[10]}";
            },
            $npwp
        );
    }

    private function saveOrUpdatePaketPengadaan(array $item, int $idPenyedia, int $idPelaksana)
    {
        $paket = PaketPengadaan::updateOrCreate(
            ['nomor_paket' => $item['Nomor Paket']],
            [
                'nama_paket' => $item['Nama Paket'] ?? null,
                'pengelola' => $item['Pengelola'] ?? null,
                'satuan_kerja' => $item['Satuan Kerja'] ?? null,
                'instansi_pembeli' => $item['Instansi Pembeli'] ?? null,
                'jenis_katalog' => $item['Jenis Katalog'] ?? null,
                'etalase' => $item['Etalase'] ?? null,
                'tanggal_paket' => $item['Tanggal Paket'] ?? now(),
                'status_paket' => $item['Status Paket'] ?? null,
                'rup_id' => $item['rup_id'] ?? null,
                'status_umkm' => $item['Status UMKM'] ?? null,
                'id_penyedia' => $idPenyedia,
                'id_pelaksana' => $idPelaksana
            ]
        );

        // Dispatch Sirup notification job if new record
        // if ($paket->wasRecentlyCreated) {
        //     $dataSirup = \App\Models\DataSirup::where('kode_rup', $paket->nomor_paket)->first();
        //     if ($dataSirup) {
        //         \App\Jobs\SendSirupNotifications::dispatch($dataSirup);
        //     } else {
        //         $this->info("DataSirup not found for kode_rup: {$paket->nomor_paket}, skipping notification.");
        //     }
        // }

        return $paket;
    }

    private function saveOrGetProduk(array $item)
    {
        return Produk::updateOrCreate(
            ['nama_produk' => $item['Nama Produk'],
            'nama_manufaktur' => $item['Nama Manufaktur']
        ],
            [
                'kategori_lv1' => $item['Kategori lv1'] ?? null,
                'kategori_lv2' => $item['Kategori lv2'] ?? null,
                'nama_manufaktur' => $item['Nama Manufaktur'] ?? null,
                'jenis_produk' => $item['Jenis Produk'] ?? null,
            ]
        );
    }

    private function saveTransaksiPaket(array $item, int $idPaket, int $idProduk)
    {
        return TransaksiPaket::updateOrCreate(
            [
                'id_paket' => $idPaket,
                'id_produk' => $idProduk
            ],
            [
                'kuantitas_produk' => $item['Kuantitas Produk'] ?? 0,
                'harga_satuan' => $item['Harga Satuan Produk'] ?? 0,
                'harga_ongkos_kirim' => $item['Harga Ongkos Kirim'] ?? 0,
                'total_harga' => $item['Total Harga Produk'] ?? 0
            ]
        );
    }
}
