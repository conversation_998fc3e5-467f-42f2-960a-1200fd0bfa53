<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Models\PaketPengadaan;
use App\Models\Produk;
use App\Models\TransaksiPaket;
use App\Models\ListPerusahaanTender;
use App\Models\CommandProgress;
use Illuminate\Support\Facades\DB;
use Exception;
use DateTime;
use App\Crawler\CustomClientSquid;
use App\Models\SystemError;
use App\Traits\CommandManagementTrait;

class FetchEkatalogCommand extends Command
{
    use CommandManagementTrait;
    protected $signature = 'fetch:ekatalog
                            {start? : Tanggal mulai (format: Y-m-d). <PERSON><PERSON> tidak diisi, akan menggunakan tanggal terakhir di database}
                            {end? : Tanggal akhir (format: Y-m-d). <PERSON>ka tidak diisi, akan menggunakan tanggal hari ini}
                            {--timeout=7200 : Waktu maksimum dalam detik sebelum command dianggap macet}
                            {--force : <PERSON>sa menjalankan meskipun command sudah berjalan}
                            {--lock-name= : <PERSON>a kunci khusus untuk instance command ini}';
    protected $description = 'Fetch data from Redash Ekatalog looping by date range';

    private const REDASH_API_URL = "https://redash-e-katalog.lkpp.go.id/api/queries/157/results";
    private const MAX_WAIT_TIME = 300; // Maximum wait time in seconds
    private const AUTH_HEADER = 'Key BnXT185EjSxCxNa4YgFPoy7oiA6M4VKtD2sa4W8L';

    private $proxy;
    private $customClient;

    public function __construct()
    {
        parent::__construct();
        $this->proxy = env('PROXIES_SSH');
        $this->customClient = new CustomClientSquid();
    }

    public function handle()
    {
        // Initialize command management with timeout dari parameter
        $timeout = (int)$this->option('timeout');
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return Command::FAILURE;
        }

        try {
            $startDate = $this->argument('start') ?? $this->getLastStartDate();
            $endDate = $this->argument('end') ?? now()->format('Y-m-d H:i:s');

            $currentDate = Carbon::parse($startDate);
            $finalDate = Carbon::parse($endDate);

            $totalDays = $currentDate->diffInDays($finalDate) + 1;
            $processedDays = 0;

            $this->info("Fetching data from Redash Ekatalog from $startDate to $endDate...");

            // Update progress with total days
            $this->updateCommandProgress(
                0,
                $totalDays,
                "Fetching data from $startDate to $endDate...",
                [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'total_days' => $totalDays
                ]
            );

            while ($currentDate <= $finalDate) {
                $this->fetchDataForDate($currentDate);
                $currentDate->addDay();
                $processedDays++;

                // Update progress
                $progressPercentage = min(99, round(($processedDays / $totalDays) * 100));
                $this->updateCommandProgress(
                    $processedDays,
                    $totalDays,
                    "Processed $processedDays of $totalDays days. Current date: " . $currentDate->subDay()->format('Y-m-d'),
                    [
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'current_date' => $currentDate->format('Y-m-d'),
                        'percentage' => $progressPercentage
                    ]
                );
                $currentDate->addDay(); // Add back the day we subtracted for display
            }

            $message = "Fetching data from $startDate to $endDate completed in " . $this->getElapsedTime() . " seconds.";
            $this->info($message);

            // Mark command as completed
            $this->markCommandAsCompleted(
                $message,
                [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'total_days_processed' => $totalDays
                ]
            );
        } catch (Exception $e) {
            $errorMessage = "An error occurred: " . $e->getMessage();
            $this->error($errorMessage);

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                $errorMessage,
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    // Metode jagaStatusRunning sudah tidak diperlukan karena sudah ditangani oleh CommandManagementTrait

    private function getLastStartDate(): string
    {
        $lastDate = DB::table('ekatalog_paket_pengadaan')
            ->orderBy('tanggal_paket', 'desc')
            ->value('tanggal_paket');

        return $lastDate ?: now()->subYear()->format('Y-m-d H:i:s');
    }

    private function fetchDataForDate(Carbon $currentDate): void
    {
        $currentStart = $currentDate->format('Y-m-d');
        $this->info("Fetching data for date: $currentStart");

        $payload = $this->buildPayload($currentStart);

        try {
            $this->info("Using CustomClientSquid for request");

            // Konversi payload ke JSON
            $jsonPayload = json_encode($payload);

            // Siapkan headers
            $headers = $this->getHeaders();

            // Buat URL untuk request POST
            $url = self::REDASH_API_URL;

            // Tambahkan Content-Type header
            $headers['Content-Type'] = 'application/json';

            // Buat opsi untuk curl
            $options = [
                'headers' => $headers,
                'body' => $jsonPayload
            ];

            // Lakukan request dengan CustomClientSquid (POST method)
            $response = $this->customClient->post($url, $options);

            // Ambil status code
            $statusCode = $response->getStatusCode();
            $this->info("HTTP Status: " . $statusCode);

            // Ambil body response
            $body = $response->getBody()->getContents();

            // Parse JSON response
            $responseData = json_decode($body, true);

            if ($statusCode >= 200 && $statusCode < 300) {
                $this->handleJobResponse($responseData, $currentStart);
            } else {
                $this->error("Failed to initiate job for $currentStart. HTTP Status: " . $statusCode);
            }
        } catch (Exception $e) {
            $this->error("Error making request: " . $e->getMessage());
            $this->logSystemError($e, "fetchDataForDate for date: $currentStart");
        }
    }

    private function buildPayload(string $currentStart): array
    {
        return [
            "queryId" => 157,
            "parameters" => [
                "Tanggal" => [
                    "start" => $currentStart,
                    "end" => $currentStart
                ],
                "Jenis_Katalog" => ["ALL"],
                "pengelola" => ["ALL"],
                "provinsi" => ["ALL"],
                "Tahun Anggaran" => ["ALL"],
                "Nama Satker Transaksi" => ["ALL"],
                "klpd" => ["ALL"]
            ]
        ];
    }

    private function getHeaders(): array
    {
        return [
            'Authorization' => self::AUTH_HEADER,
            'Accept' => 'application/json, text/plain, */*',
            'Content-Type' => 'application/json;charset=UTF-8',
            'Referer' => 'https://redash-e-katalog.lkpp.go.id/public/dashboards/' . self::AUTH_HEADER
        ];
    }

    private function handleJobResponse(array $jobData, string $currentStart): void
    {
        $maxWaitTime = self::MAX_WAIT_TIME;
        $elapsedTime = 0;

        while (isset($jobData['job']) && in_array($jobData['job']['status'], [1, 2])) {
            $this->info("Job for $currentStart is still in progress. Waiting...");
            sleep(3);
            $elapsedTime += 3;

            if ($elapsedTime > $maxWaitTime) {
                $this->error("Job for $currentStart exceeded the maximum wait time of $maxWaitTime seconds.");
                return;
            }

            $jobId = $jobData['job']['id'];
            $statusUrl = "https://redash-e-katalog.lkpp.go.id/api/jobs/$jobId";

            try {
                $this->info("Checking job status at URL: $statusUrl");

                // Siapkan headers
                $headers = $this->getHeaders();

                // Lakukan request dengan CustomClientSquid (GET method untuk status check)
                $response = $this->customClient->get($statusUrl, ['headers' => $headers]);

                // Ambil status code
                $statusCode = $response->getStatusCode();

                // Ambil body response
                $body = $response->getBody()->getContents();

                // Parse JSON response
                $responseData = json_decode($body, true);

                if ($statusCode >= 200 && $statusCode < 300) {
                    $jobData = $responseData;
                } else {
                    $this->error("Failed to fetch job status for $currentStart. HTTP Status: $statusCode");
                    return;
                }
            } catch (Exception $e) {
                $this->error("Error checking job status: " . $e->getMessage());
                $this->logSystemError($e, "handleJobResponse checking job status for date: $currentStart");
                return;
            }
        }

        if (isset($jobData['job']) && $jobData['job']['status'] == 3) {
            $this->fetchJobResults($currentStart);
        } else {
            if (isset($jobData['query_result']) && $jobData['query_result']) {
                $this->fetchJobResults($currentStart);
            } else {
                $this->error("Job for $currentStart did not complete successfully.");
            }
        }
    }

    private function fetchJobResults(string $currentStart): void
    {
        $payload = $this->buildPayload($currentStart);

        try {
            $this->info("Fetching job results using CustomClientSquid");

            // Konversi payload ke JSON
            $jsonPayload = json_encode($payload);

            // Siapkan headers
            $headers = $this->getHeaders();

            // Tambahkan Content-Type header
            $headers['Content-Type'] = 'application/json';

            // Buat opsi untuk curl
            $options = [
                'headers' => $headers,
                'body' => $jsonPayload
            ];

            // Lakukan request dengan CustomClientSquid (POST method)
            $response = $this->customClient->post(self::REDASH_API_URL, $options);

            // Ambil status code
            $statusCode = $response->getStatusCode();
            $this->info("HTTP Status for result response: $statusCode");

            // Ambil body response
            $body = $response->getBody()->getContents();

            // Parse JSON response
            $resultData = json_decode($body, true);

            if ($statusCode >= 200 && $statusCode < 300) {
                if (isset($resultData['query_result']['retrieved_at'])) {
                    $fetchDate = (new DateTime($resultData['query_result']['retrieved_at']))->format('Y-m-d H:i:s');
                    $this->info("Tanggal Fetch: $fetchDate");

                    if (isset($resultData['query_result']['data']['rows'])) {
                        $rows = $resultData['query_result']['data']['rows'];
                        if (count($rows) > 0) {
                            $this->info("Jumlah rows: " . count($rows));
                            $this->processFetchedData($rows, $currentStart);
                        } else {
                            $this->error("Rows ditemukan, namun jumlahnya adalah 0.");
                        }
                    } else {
                        $this->error("query_result->data->rows tidak ditemukan di response body.");
                    }
                } else {
                    $this->error("query_result->retrieved_at tidak ditemukan di response body.");
                }
            } else {
                $this->error("Failed to fetch completed job result for $currentStart. HTTP Status: $statusCode");
            }
        } catch (Exception $e) {
            $this->error("Error fetching job results: " . $e->getMessage());
            $this->logSystemError($e, "fetchJobResults for date: $currentStart");
        }
    }

    private function processFetchedData(array $data, $currentStart): void
    {
        $originalCount = count($data);
        $this->info("Data yang akan diproses: $originalCount");

        // Hitung jumlah data yang memiliki field yang diperlukan
        $validDataCount = 0;
        $missingNamaProdukCount = 0;
        $missingNamaManufakturCount = 0;
        $missingNamaPenyediaCount = 0;
        $missingNamaPelaksanaCount = 0;

        foreach ($data as $item) {
            $isValid = true;

            if (!isset($item['Nama Produk']) || trim($item['Nama Produk']) === '') {
                $missingNamaProdukCount++;
                $isValid = false;
            }

            if (!isset($item['Nama Manufaktur']) || trim($item['Nama Manufaktur']) === '') {
                $missingNamaManufakturCount++;
                $isValid = false;
            }

            if (!isset($item['Nama Penyedia']) || trim($item['Nama Penyedia']) === '') {
                $missingNamaPenyediaCount++;
                $isValid = false;
            }

            if (!isset($item['Nama Pelaksana Pekerjaan']) || trim($item['Nama Pelaksana Pekerjaan']) === '') {
                $missingNamaPelaksanaCount++;
                $isValid = false;
            }

            if ($isValid) {
                $validDataCount++;
            }
        }

        $this->info("Data dengan field lengkap: $validDataCount");
        $this->info("Data dengan Nama Produk kosong: $missingNamaProdukCount");
        $this->info("Data dengan Nama Manufaktur kosong: $missingNamaManufakturCount");
        $this->info("Data dengan Nama Penyedia kosong: $missingNamaPenyediaCount");
        $this->info("Data dengan Nama Pelaksana Pekerjaan kosong: $missingNamaPelaksanaCount");

        // Update progress dengan informasi validasi data
        $this->updateCommandProgress(
            0,
            $originalCount,
            "Validasi data untuk tanggal: " . ($currentStart ?? now()->format('Y-m-d')),
            [
                'total_data' => $originalCount,
                'valid_data' => $validDataCount,
                'missing_nama_produk' => $missingNamaProdukCount,
                'missing_nama_manufaktur' => $missingNamaManufakturCount,
                'missing_nama_penyedia' => $missingNamaPenyediaCount,
                'missing_nama_pelaksana' => $missingNamaPelaksanaCount,
                'current_date' => $currentStart ?? now()->format('Y-m-d')
            ]
        );

        // Step 1: Buat array untuk menyimpan nomor_paket dan status_paket dari data yang baru diambil
        $statusPaketMap = [];
        foreach ($data as $item) {
            if (isset($item['Nomor Paket']) && isset($item['Status Paket'])) {
                $statusPaketMap[$item['Nomor Paket']] = $item['Status Paket'];
            }
        }

        // Ambil data existing dari database untuk nomor paket yang ada di data baru
        $nomorPaketData = array_keys($statusPaketMap);
        $existingPakets = PaketPengadaan::whereIn('nomor_paket', $nomorPaketData)
            ->select('id_paket', 'nomor_paket', 'status_paket')
            ->get();

        // Hitung berapa banyak status yang perlu diupdate
        $statusUpdates = 0;

        // Update status_paket jika berbeda
        $statusUpdateDetails = [];
        foreach ($existingPakets as $paket) {
            if (isset($statusPaketMap[$paket->nomor_paket]) && $paket->status_paket !== $statusPaketMap[$paket->nomor_paket]) {
                $oldStatus = $paket->status_paket;
                $newStatus = $statusPaketMap[$paket->nomor_paket];

                $paket->status_paket = $newStatus;
                $paket->save();

                $statusUpdates++;
                $this->info("Updated status for paket {$paket->nomor_paket} from '{$oldStatus}' to '{$newStatus}'");

                // Simpan detail update untuk ditampilkan di monitoring
                $statusUpdateDetails[] = [
                    'nomor_paket' => $paket->nomor_paket,
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus,
                    'updated_at' => now()->format('Y-m-d H:i:s')
                ];
            }
        }

        $this->info("Total status updates: $statusUpdates");

        // Update progress dengan informasi status_paket yang diupdate
        if ($statusUpdates > 0) {
            CommandProgress::updateProgress(
                'fetch:ekatalog',
                0,
                $originalCount,
                "Updated $statusUpdates status_paket records for date: " . ($currentStart ?? now()->format('Y-m-d')),
                [
                    'status_updates_count' => $statusUpdates,
                    'status_update_details' => $statusUpdateDetails,
                    'current_date' => $currentStart ?? now()->format('Y-m-d')
                ]
            );
        }

        // Dapatkan nomor paket yang sudah ada di database untuk filter data baru
        $existingNomorPaket = $existingPakets->pluck('nomor_paket')->toArray();

        // Filter data untuk hanya memproses data yang belum ada di database
        $data = array_filter($data, function ($item) use ($existingNomorPaket) {
            return !in_array($item['Nomor Paket'], $existingNomorPaket);
        });

        $filteredCount = count($data);
        $this->info("Data yang sudah ada di database: " . ($originalCount - $filteredCount));
        $this->info("Sisa data yang akan diproses: $filteredCount");

        // Chunk the data for batch insertion
$chunks = array_chunk($data, 1500); // Reduce chunk size to 500

        $totalChunks = count($chunks);
        $totalRows = count($data);
        $processedRows = 0;
        $statusUpdates = isset($statusUpdates) ? $statusUpdates : 0;

        // Update progress with chunk information
        $this->updateCommandProgress(
            0,
            $totalRows,
            "Processing data in $totalChunks chunks. Total rows: $totalRows",
            [
                'total_chunks' => $totalChunks,
                'total_rows' => $totalRows,
                'status_updates' => $statusUpdates,
                'current_date' => $currentStart ?? now()->format('Y-m-d')
            ]
        );

        foreach ($chunks as $chunkIndex => $chunk) {
            $this->info("Processing chunk " . ($chunkIndex + 1) . " of $totalChunks. Size: " . count($chunk));

            $chunkStartTime = microtime(true); // Start measuring time for chunk

            $progressBar = $this->output->createProgressBar(count($chunk));
            $progressBar->start();

            $chunkSize = count($chunk);
            $chunkProcessed = 0;

            foreach ($chunk as $rowIndex => $item) {
                if (empty($item['Nama Penyedia']) || empty($item['Nama Pelaksana Pekerjaan'])) {
                    $this->info("Skipping row $rowIndex due to missing 'Nama Penyedia' or 'Nama Pelaksana Pekerjaan'");
                    $progressBar->advance();
                    $processedRows++;
                    $chunkProcessed++;
                    continue;
                }

                //$this->jagaStatusRunning();

                // Update progress every 100 rows
                if ($processedRows % 100 === 0) {
                    $progressPercentage = min(99, round(($processedRows / $totalRows) * 100));
                    $this->updateCommandProgress(
                        $processedRows,
                        $totalRows,
                        "Processing chunk " . ($chunkIndex + 1) . " of $totalChunks. Processed $processedRows of $totalRows rows.",
                        [
                            'current_chunk' => $chunkIndex + 1,
                            'total_chunks' => $totalChunks,
                            'chunk_progress' => round(($chunkProcessed / $chunkSize) * 100),
                            'total_progress' => $progressPercentage,
                            'status_updates' => $statusUpdates,
                            'current_date' => $currentStart ?? now()->format('Y-m-d')
                        ]
                    );
                }

                $retries = 3;
                while ($retries > 0) {
                    try {
                        // Gunakan try-catch manual daripada DB::transaction callback
                        // untuk menghindari masalah dengan nested transactions
                        DB::beginTransaction();

                        try {
                            $penyedia = $this->saveOrGetPerusahaan($item['Nama Penyedia']);
                            $pelaksana = $this->saveOrGetPerusahaan($item['Nama Pelaksana Pekerjaan']);

                            // Simpan paket pengadaan
                            $paket = $this->saveOrUpdatePaketPengadaan($item, $penyedia->id, $pelaksana->id);

                            // Commit transaksi setelah menyimpan paket
                            DB::commit();

                            // Jika paket berhasil disimpan, simpan produk dan transaksi dalam transaksi terpisah
                            if ($paket) {
                                DB::beginTransaction();
                                try {
                                    $produk = $this->saveOrGetProduk($item);

                                    // Cek apakah produk berhasil dibuat dan memiliki data yang diperlukan
                                    if ($produk && isset($item['Nama Produk']) && isset($item['Nama Manufaktur'])) {
                                        // Verifikasi bahwa paket masih ada di database
                                        $paketExists = PaketPengadaan::where('id_paket', $paket->id_paket)->exists();

                                        if ($paketExists) {
                                            $this->saveTransaksiPaket($item, $paket->id_paket, $produk->id_produk);
                                        } else {
                                            $this->error("Paket with ID {$paket->id_paket} no longer exists in the database.");
                                        }
                                    } else {
                                        // Jika produk null atau data tidak lengkap, lanjutkan tanpa error
                                        $this->info("Skipping transaksi for item due to missing product data");
                                    }

                                    DB::commit();
                                } catch (Exception $innerException2) {
                                    DB::rollBack();
                                    $this->error("Error in second transaction: " . $innerException2->getMessage());
                                }
                            }
                        } catch (Exception $innerException) {
                            DB::rollBack();
                            throw $innerException;
                        }

                        // Jika berhasil, keluar dari loop retry
                        break;
                    } catch (Exception $e) {
                        $this->error("An error occurred: " . $e->getMessage());
                        $retries--;
                        if ($retries <= 0) {
                            throw $e; // Lempar kembali exception setelah habis retry
                        }
                        sleep(1); // Beri waktu sejenak sebelum mencoba kembali
                    }
                }

                $progressBar->advance();
                $processedRows++;
                $chunkProcessed++;
            }

            $progressBar->finish();
            $this->info("\nChunk " . ($chunkIndex + 1) . " of $totalChunks completed.");

            $chunkEndTime = microtime(true); // End measuring time for chunk
            $chunkExecutionTime = $chunkEndTime - $chunkStartTime;
            $this->info("Chunk " . ($chunkIndex + 1) . " processed in " . round($chunkExecutionTime, 2) . " seconds.");

            // Update progress after each chunk
            $progressPercentage = min(99, round(($processedRows / $totalRows) * 100));
            $this->updateCommandProgress(
                $processedRows,
                $totalRows,
                "Completed chunk " . ($chunkIndex + 1) . " of $totalChunks. Processed $processedRows of $totalRows rows.",
                [
                    'current_chunk' => $chunkIndex + 1,
                    'total_chunks' => $totalChunks,
                    'chunk_execution_time' => round($chunkExecutionTime, 2),
                    'total_progress' => $progressPercentage,
                    'status_updates' => $statusUpdates,
                    'current_date' => $currentStart ?? now()->format('Y-m-d')
                ]
            );
        }
    }

    private function saveOrGetPerusahaan(string $namaPerusahaan)
    {
        try {
            // Gunakan metode createOrUpdatePerusahaan yang baru
            $perusahaan = ListPerusahaanTender::createOrUpdatePerusahaan(
                $namaPerusahaan,
                null, // NPWP tidak tersedia dari Ekatalog
                null  // Alamat tidak tersedia dari Ekatalog
            );

            return $perusahaan;
        } catch (\Exception $e) {
            // Log error
            $this->error("Error in createOrUpdatePerusahaan: " . $e->getMessage());
            $this->logSystemError($e, "saveOrGetPerusahaan for {$namaPerusahaan}");

            // Fallback ke metode lama jika terjadi error
            $normalizedName = ListPerusahaanTender::normalizeName($namaPerusahaan);
            $normalizedNoSpace = str_replace(' ', '', $normalizedName);
            $pembeda = $normalizedNoSpace;

            try {
                // Coba sekali lagi dengan firstOrCreate
                return ListPerusahaanTender::firstOrCreate(
                    ['pembeda' => $pembeda],
                    [
                        'npwp' => null,
                        'npwp_blur' => null,
                        'nama_peserta' => $namaPerusahaan,
                        'normalized_name' => $normalizedName,
                        'partial_npwp' => null,
                        'alamat' => null,
                    ]
                );
            } catch (\Illuminate\Database\QueryException $e) {
                // Jika masih terjadi error, coba dengan pembeda yang unik
                if ($e->getCode() == 23000) {
                    $uniquePembeda = $normalizedNoSpace . '_' . substr(md5(uniqid()), 0, 8);

                    return ListPerusahaanTender::create([
                        'npwp' => null,
                        'npwp_blur' => null,
                        'nama_peserta' => $namaPerusahaan,
                        'normalized_name' => $normalizedName,
                        'partial_npwp' => null,
                        'pembeda' => $uniquePembeda,
                        'alamat' => null,
                    ]);
                } else {
                    throw $e;
                }
            }
        }
    }

    private function maskNpwp($npwp)
    {
        return preg_replace_callback(
            '/^(\d)\d\.(\d)\d\d\.(\d)\d\d\.(\d)-(\d)(\d)(\d)\.(\d)(\d)(\d)$/',
            function ($matches) {
                return "{$matches[1]}*.{$matches[2]}**.{$matches[3]}**.*-*{$matches[6]}{$matches[7]}.**{$matches[10]}";
            },
            $npwp
        );
    }

    private function saveOrUpdatePaketPengadaan(array $item, int $idPenyedia, int $idPelaksana)
    {
        try {
            // Cek apakah paket sudah ada
            $existingPaket = PaketPengadaan::where('nomor_paket', $item['Nomor Paket'])->first();

            if ($existingPaket) {
                // Update paket yang sudah ada
                $existingPaket->update([
                    'nama_paket' => $item['Nama Paket'] ?? $existingPaket->nama_paket,
                    'pengelola' => $item['Pengelola'] ?? $existingPaket->pengelola,
                    'satuan_kerja' => $item['Satuan Kerja'] ?? $existingPaket->satuan_kerja,
                    'instansi_pembeli' => $item['Instansi Pembeli'] ?? $existingPaket->instansi_pembeli,
                    'jenis_katalog' => $item['Jenis Katalog'] ?? $existingPaket->jenis_katalog,
                    'etalase' => $item['Etalase'] ?? $existingPaket->etalase,
                    'tanggal_paket' => $item['Tanggal Paket'] ?? $existingPaket->tanggal_paket,
                    'status_paket' => $item['Status Paket'] ?? $existingPaket->status_paket,
                    'rup_id' => $item['rup_id'] ?? $existingPaket->rup_id,
                    'status_umkm' => $item['Status UMKM'] ?? $existingPaket->status_umkm,
                    'id_penyedia' => $idPenyedia,
                    'id_pelaksana' => $idPelaksana
                ]);

                return $existingPaket;
            } else {
                // Buat paket baru
                $paket = PaketPengadaan::create([
                    'nomor_paket' => $item['Nomor Paket'],
                    'nama_paket' => $item['Nama Paket'] ?? null,
                    'pengelola' => $item['Pengelola'] ?? null,
                    'satuan_kerja' => $item['Satuan Kerja'] ?? null,
                    'instansi_pembeli' => $item['Instansi Pembeli'] ?? null,
                    'jenis_katalog' => $item['Jenis Katalog'] ?? null,
                    'etalase' => $item['Etalase'] ?? null,
                    'tanggal_paket' => $item['Tanggal Paket'] ?? now(),
                    'status_paket' => $item['Status Paket'] ?? null,
                    'rup_id' => $item['rup_id'] ?? null,
                    'status_umkm' => $item['Status UMKM'] ?? null,
                    'id_penyedia' => $idPenyedia,
                    'id_pelaksana' => $idPelaksana
                ]);

                // Dispatch Sirup notification job if new record
                if ($paket->wasRecentlyCreated) {
                    $dataSirup = \App\Models\DataSirup::where('kode_rup', $paket->nomor_paket)->first();
                    if ($dataSirup) {
                        \App\Jobs\SendSirupNotifications::dispatch($dataSirup);
                    } else {
                        $this->info("DataSirup not found for kode_rup: {$paket->nomor_paket}, skipping notification.");
                    }
                }

                return $paket;
            }
        } catch (Exception $e) {
            // Log error dan coba sekali lagi dengan pendekatan yang berbeda
            $this->error("Error in saveOrUpdatePaketPengadaan: " . $e->getMessage());

            // Fallback ke updateOrCreate
            $paket = PaketPengadaan::updateOrCreate(
                ['nomor_paket' => $item['Nomor Paket']],
                [
                    'nama_paket' => $item['Nama Paket'] ?? null,
                    'pengelola' => $item['Pengelola'] ?? null,
                    'satuan_kerja' => $item['Satuan Kerja'] ?? null,
                    'instansi_pembeli' => $item['Instansi Pembeli'] ?? null,
                    'jenis_katalog' => $item['Jenis Katalog'] ?? null,
                    'etalase' => $item['Etalase'] ?? null,
                    'tanggal_paket' => $item['Tanggal Paket'] ?? now(),
                    'status_paket' => $item['Status Paket'] ?? null,
                    'rup_id' => $item['rup_id'] ?? null,
                    'status_umkm' => $item['Status UMKM'] ?? null,
                    'id_penyedia' => $idPenyedia,
                    'id_pelaksana' => $idPelaksana
                ]
            );

            return $paket;
        }
    }

    private function saveOrGetProduk(array $item)
    {
        // Validasi input
        // if (!isset($item['Nama Produk']) || !isset($item['Nama Manufaktur']) ||
        //     trim($item['Nama Produk']) === '' || trim($item['Nama Manufaktur']) === '') {
        //     // Gunakan info daripada error untuk mengurangi noise di log
        //     $this->info("Skipping produk: Missing required fields (Nama Produk or Nama Manufaktur)");
        //     return null;
        // }

        // Normalisasi data
        $item['Nama Produk'] = trim($item['Nama Produk']);
        $item['Nama Manufaktur'] = trim($item['Nama Manufaktur']);

        try {
            // Cek apakah produk sudah ada - dengan lock untuk menghindari race condition
            $existingProduk = Produk::where('nama_produk', $item['Nama Produk'])
                ->where('nama_manufaktur', $item['Nama Manufaktur'])
                ->lockForUpdate()
                ->first();

            if ($existingProduk) {
                // Update produk yang sudah ada
                $existingProduk->update([
                    'kategori_lv1' => $item['Kategori lv1'] ?? $existingProduk->kategori_lv1,
                    'kategori_lv2' => $item['Kategori lv2'] ?? $existingProduk->kategori_lv2,
                    'jenis_produk' => $item['Jenis Produk'] ?? $existingProduk->jenis_produk,
                ]);

                return $existingProduk;
            } else {
                // Coba sekali lagi untuk mencari, mungkin sudah dibuat oleh proses lain
                $existingProduk = Produk::where('nama_produk', $item['Nama Produk'])
                    ->where('nama_manufaktur', $item['Nama Manufaktur'])
                    ->first();

                if ($existingProduk) {
                    return $existingProduk;
                }

                // Buat produk baru dengan try-catch terpisah
                try {
                    return Produk::create([
                        'nama_produk' => $item['Nama Produk'],
                        'nama_manufaktur' => $item['Nama Manufaktur'],
                        'kategori_lv1' => $item['Kategori lv1'] ?? null,
                        'kategori_lv2' => $item['Kategori lv2'] ?? null,
                        'jenis_produk' => $item['Jenis Produk'] ?? null,
                    ]);
                } catch (Exception $createException) {
                    $this->error("Error creating produk: " . $createException->getMessage());

                    // Coba sekali lagi dengan delay
                    sleep(1);

                    // Cek lagi apakah produk sudah ada
                    $existingProduk = Produk::where('nama_produk', $item['Nama Produk'])
                        ->where('nama_manufaktur', $item['Nama Manufaktur'])
                        ->first();

                    if ($existingProduk) {
                        return $existingProduk;
                    }

                    // Coba lagi dengan pendekatan yang berbeda
                    return Produk::updateOrCreate(
                        [
                            'nama_produk' => $item['Nama Produk'],
                            'nama_manufaktur' => $item['Nama Manufaktur'],
                            'kategori_lv1' => $item['Kategori lv1'] ?? null,
                            'kategori_lv2' => $item['Kategori lv2'] ?? null,
                            'jenis_produk' => $item['Jenis Produk'] ?? null,
                        ],
                        [
                            
                        ]
                    );
                }
            }
        } catch (Exception $e) {
            // Log error dan coba sekali lagi dengan pendekatan yang berbeda
            $this->error("Error in saveOrGetProduk: " . $e->getMessage());

            // Coba sekali lagi dengan delay
            sleep(1);

            try {
                // Fallback ke updateOrCreate
                return Produk::updateOrCreate(
                    [
                        'nama_produk' => $item['Nama Produk'],
                        'nama_manufaktur' => $item['Nama Manufaktur'],
                        'kategori_lv1' => $item['Kategori lv1'] ?? null,
                        'kategori_lv2' => $item['Kategori lv2'] ?? null,
                        'jenis_produk' => $item['Jenis Produk'] ?? null,
                    ],
                    
                );
            } catch (Exception $finalException) {
                $this->error("Final error in saveOrGetProduk: " . $finalException->getMessage());
                return null;
            }
        }
    }

    private function saveTransaksiPaket(array $item, int $idPaket, int $idProduk)
    {
        // Validasi input
        if (!isset($item['Kuantitas Produk']) || !isset($item['Harga Satuan Produk']) ||
            !isset($item['Total Harga Produk'])) {
            $this->info("Skipping transaksi: Missing required fields (Kuantitas Produk, Harga Satuan Produk, or Total Harga Produk)");
            return null;
        }

        // Verifikasi ulang bahwa paket dan produk ada di database
        $paketExists = PaketPengadaan::where('id_paket', $idPaket)->exists();
        $produkExists = Produk::where('id_produk', $idProduk)->exists();

        if (!$paketExists) {
            $this->info("Cannot save transaksi: Paket with ID {$idPaket} does not exist in the database.");
            return null;
        }

        if (!$produkExists) {
            $this->info("Cannot save transaksi: Produk with ID {$idProduk} does not exist in the database.");
            return null;
        }

        // Normalisasi data
        $item['Kuantitas Produk'] = isset($item['Kuantitas Produk']) ? (int)$item['Kuantitas Produk'] : 0;
        $item['Harga Satuan Produk'] = isset($item['Harga Satuan Produk']) ? (float)$item['Harga Satuan Produk'] : 0;
        $item['Harga Ongkos Kirim'] = isset($item['Harga Ongkos Kirim']) ? (float)$item['Harga Ongkos Kirim'] : 0;
        $item['Total Harga Produk'] = isset($item['Total Harga Produk']) ? (float)$item['Total Harga Produk'] : 0;

        try {
            // Cek apakah transaksi paket sudah ada
            $existingTransaksi = TransaksiPaket::where('id_paket', $idPaket)
                ->where('id_produk', $idProduk)
                ->first();

            if ($existingTransaksi) {
                // Update transaksi yang sudah ada
                $existingTransaksi->update([
                    'kuantitas_produk' => $item['Kuantitas Produk'] ?? $existingTransaksi->kuantitas_produk,
                    'harga_satuan' => $item['Harga Satuan Produk'] ?? $existingTransaksi->harga_satuan,
                    'harga_ongkos_kirim' => $item['Harga Ongkos Kirim'] ?? $existingTransaksi->harga_ongkos_kirim,
                    'total_harga' => $item['Total Harga Produk'] ?? $existingTransaksi->total_harga
                ]);

                return $existingTransaksi;
            } else {
                // Verifikasi sekali lagi sebelum membuat transaksi baru
                $paketExists = PaketPengadaan::where('id_paket', $idPaket)->exists();
                $produkExists = Produk::where('id_produk', $idProduk)->exists();

                if (!$paketExists || !$produkExists) {
                    $this->error("Cannot create transaksi: Paket or Produk no longer exists in the database.");
                    return null;
                }

                // Buat transaksi baru dengan try-catch terpisah
                try {
                    return TransaksiPaket::create([
                        'id_paket' => $idPaket,
                        'id_produk' => $idProduk,
                        'kuantitas_produk' => $item['Kuantitas Produk'] ?? 0,
                        'harga_satuan' => $item['Harga Satuan Produk'] ?? 0,
                        'harga_ongkos_kirim' => $item['Harga Ongkos Kirim'] ?? 0,
                        'total_harga' => $item['Total Harga Produk'] ?? 0
                    ]);
                } catch (Exception $createException) {
                    $this->error("Error creating transaksi: " . $createException->getMessage());

                    // Coba sekali lagi dengan delay
                    sleep(1);

                    // Verifikasi lagi
                    $paketExists = PaketPengadaan::where('id_paket', $idPaket)->exists();
                    $produkExists = Produk::where('id_produk', $idProduk)->exists();

                    if (!$paketExists || !$produkExists) {
                        $this->error("Cannot create transaksi after retry: Paket or Produk no longer exists in the database.");
                        return null;
                    }

                    // Coba lagi dengan pendekatan yang berbeda
                    return TransaksiPaket::updateOrCreate(
                        [
                            'id_paket' => $idPaket,
                            'id_produk' => $idProduk,
                            'kuantitas_produk' => $item['Kuantitas Produk'] ?? 0,
                            'harga_satuan' => $item['Harga Satuan Produk'] ?? 0,
                            'harga_ongkos_kirim' => $item['Harga Ongkos Kirim'] ?? 0,
                            'total_harga' => $item['Total Harga Produk'] ?? 0
                        ]
                    );
                }
            }
        } catch (Exception $e) {
            // Log error dan coba sekali lagi dengan pendekatan yang berbeda
            $this->error("Error in saveTransaksiPaket: " . $e->getMessage());

            // Verifikasi lagi
            $paketExists = PaketPengadaan::where('id_paket', $idPaket)->exists();
            $produkExists = Produk::where('id_produk', $idProduk)->exists();

            if (!$paketExists || !$produkExists) {
                $this->error("Cannot save transaksi in fallback: Paket or Produk no longer exists in the database.");
                return null;
            }

            // Fallback ke updateOrCreate dengan delay
            sleep(1);

            try {
                return TransaksiPaket::updateOrCreate(
                    [
                        'id_paket' => $idPaket,
                        'id_produk' => $idProduk,
                        'kuantitas_produk' => $item['Kuantitas Produk'] ?? 0,
                        'harga_satuan' => $item['Harga Satuan Produk'] ?? 0,
                        'harga_ongkos_kirim' => $item['Harga Ongkos Kirim'] ?? 0,
                        'total_harga' => $item['Total Harga Produk'] ?? 0
                    ],
                );
            } catch (Exception $finalException) {
                $this->error("Final error in saveTransaksiPaket: " . $finalException->getMessage());
                return null;
            }
        }
    }
}
