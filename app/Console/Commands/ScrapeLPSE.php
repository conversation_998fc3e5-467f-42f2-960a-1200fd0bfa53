<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use GuzzleHttp\Client;
use App\Models\Lpse;
use Symfony\Component\DomCrawler\Crawler;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Crawler\CustomClientSquid;
use Exception;

class ScrapeLPSE extends Command
{
    protected $signature = 'scrape:lpse
                            {--limit=0 : Batasi jumlah LPSE yang diproses (0 = tidak dibatasi)}
                            {--no-proxy : <PERSON>an gunakan proxy untuk request}
                            {--delay=3 : Delay minimum antara request (dalam detik)}
                            {--max-delay=5 : Delay maksimum antara request (dalam detik)}';
    protected $description = 'Scraping data LPSE dari API LKPP dan mengambil detail dari halaman LPSE';

    private Client $client;
    private $crawlerClient;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $logger = Log::channel('scrapeLPSE');

        $this->info('<PERSON><PERSON> scraping data LPSE...');
        $logger->info('<PERSON><PERSON> scraping data LPSE.');

        // Inisialisasi client untuk API
        $this->client = new Client([
            'base_uri' => 'https://isb.lkpp.go.id/isb-2/api/satudata/',
            'timeout' => 10.0,
            'proxy' => env('PROXIES_SSH'),
            'headers' => [
                'Referer' => 'https://isb.lkpp.go.id/isb-2/api/satudata/',
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language' => 'en-US,en;q=0.9,id;q=0.8,sv;q=0.7,fi;q=0.6',
                'Accept-Encoding' => 'gzip, deflate, br, zstd',
                'Cache-Control' => 'max-age=0',
                'Connection' => 'keep-alive',
                'Upgrade-Insecure-Requests' => '1',
                'sec-ch-ua' => '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
                'sec-ch-ua-mobile' => '?0',
                'sec-ch-ua-platform' => '"Windows"',
                'sec-fetch-dest' => 'document',
                'sec-fetch-mode' => 'navigate',
                'sec-fetch-site' => 'same-origin',
                'sec-fetch-user' => '?1',
                'Priority' => 'u=0, i',
                'Cookie' => 'SPSE_SESSION=05bd57cc0524b4a1549917abaf9af2ba965f06b2-___TS=1745685683226&___ID=eb4c9836-d69b-4c33-ba3d-553703c55610; _cfuvid=orLWOMF4wSzyYi5N.UA7hE8Q6HC7lhEuGSq7BLablEg-1745681373354-*******-604800000'
            ],
        ]);

        // Cek apakah menggunakan proxy atau tidak
        $useProxy = !$this->option('no-proxy');

        if ($useProxy) {
            // Client untuk crawling data detail - menggunakan CustomClientSquid untuk mengatasi Cloudflare
            $this->crawlerClient = new CustomClientSquid([
                'timeout' => 30.0,
                'connect_timeout' => 15.0
            ]);
            $this->info("Menggunakan proxy untuk request");
        } else {
            // Client tanpa proxy
            $this->crawlerClient = new Client([
                'timeout' => 30.0,
                'connect_timeout' => 15.0,
                'headers' => [
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.9,id;q=0.8',
                    'Accept-Encoding' => 'gzip, deflate, br',
                    'Connection' => 'keep-alive',
                    'Upgrade-Insecure-Requests' => '1',
                    'Cache-Control' => 'max-age=0'
                ]
            ]);
            $this->info("Tidak menggunakan proxy untuk request");
        }

        try {
            // Step 1: Ambil data LPSE dari API
            $this->info("Mengambil data dari API LKPP...");
            $logger->info("Mengambil data dari API LKPP...");

            try {
                $response = $this->client->get('MasterLPSE');
                $data = json_decode($response->getBody()->getContents(), true);

                if (!is_array($data)) {
                    throw new Exception("Response dari API tidak valid: " . substr($response->getBody()->getContents(), 0, 100) . "...");
                }

                // Ambil limit dari opsi command
                $limit = (int) $this->option('limit');
                $totalLpse = count($data);

                if ($limit > 0 && $limit < $totalLpse) {
                    $this->info("Berhasil mengambil data dari API. Jumlah LPSE: {$totalLpse}, dibatasi menjadi {$limit}");
                    $logger->info("Berhasil mengambil data dari API. Jumlah LPSE: {$totalLpse}, dibatasi menjadi {$limit}");
                    $data = array_slice($data, 0, $limit);
                } else {
                    $this->info("Berhasil mengambil data dari API. Jumlah LPSE: {$totalLpse}");
                    $logger->info("Berhasil mengambil data dari API. Jumlah LPSE: {$totalLpse}");
                }

                $processedCount = 0;
                foreach ($data as $item) {
                    $processedCount++;
                    $totalToProcess = count($data);
                    $percentComplete = round(($processedCount / $totalToProcess) * 100);

                    if (!isset($item['kd_lpse']) || !isset($item['nama_lpse'])) {
                        $this->warn("Data LPSE tidak lengkap: " . json_encode($item));
                        $logger->warning("Data LPSE tidak lengkap: " . json_encode($item));
                        continue;
                    }

                    $kd_lpse = $item['kd_lpse'];
                    $this->info("Memproses LPSE {$processedCount}/{$totalToProcess} ({$percentComplete}%): {$item['nama_lpse']} ({$kd_lpse})");
                    $logger->info("Memproses LPSE {$processedCount}/{$totalToProcess}: {$item['nama_lpse']} ({$kd_lpse})");

                    // Ambil nilai delay dari opsi command
                    $minDelay = (int) $this->option('delay');
                    $maxDelay = (int) $this->option('max-delay');

                    // Pastikan max-delay lebih besar dari delay
                    if ($maxDelay < $minDelay) {
                        $maxDelay = $minDelay;
                    }

                    // Tambahkan delay acak antara nilai yang ditentukan
                    $delay = rand($minDelay, $maxDelay);
                    $this->info("Menunggu {$delay} detik sebelum request berikutnya...");
                    sleep($delay);

                    // Step 2: Ambil data detail LPSE dengan crawling
                    $detailData = $this->scrapeLpseDetail($kd_lpse);
                    $logger->info("Detail proses LPSE: {$item['nama_lpse']}" . json_encode($detailData, JSON_PRETTY_PRINT));

                    // Step 3: Update atau simpan data ke database
                    Lpse::updateOrCreate(
                        ['kd_lpse' => $kd_lpse],
                        [
                            'nama_lpse' => $item['nama_lpse'],
                            'provinsi' => $detailData['provinsi'],
                            'alamat' => $detailData['alamat'],
                            'email' => $detailData['email'],
                            'helpdesk' => $detailData['helpdesk'],
                            'versi_lpse' => $detailData['versi'],
                            'url' => $detailData['url'],
                            'logo_url' => $detailData['logo_url'],
                            'logo_path' => $detailData['logo_path'],
                        ]
                    );

                    $this->info("Data LPSE berhasil disimpan atau diperbarui: {$item['nama_lpse']}");
                    $logger->info("Data LPSE berhasil disimpan atau diperbarui: {$item['nama_lpse']}");
                }
            } catch (Exception $e) {
                $this->error("Gagal mengambil data dari API: " . $e->getMessage());
                $logger->error("Gagal mengambil data dari API: " . $e->getMessage());
                throw $e;
            }

            $this->info('Scraping selesai.');
            $logger->info('Scraping selesai.');
        } catch (Exception $e) {
            $this->error('Gagal mengambil data: ' . $e->getMessage());
            $logger->error('Gagal mengambil data: ' . $e->getMessage());
        }
    }

    private function scrapeLpseDetail($kd_lpse)
    {
        $data = [];
        $url = "https://eproc.lkpp.go.id/lpse/read/{$kd_lpse}/";

        try {
            // Cek apakah client memiliki metode get (CustomClientSquid) atau request (GuzzleHttp\Client)
            $response = method_exists($this->crawlerClient, 'get')
                ? $this->crawlerClient->get($url)
                : $this->crawlerClient->request('GET', $url);

            $content = $response->getBody()->getContents();

            // Cek apakah halaman berisi deteksi Cloudflare
            if (strpos($content, 'Cloudflare') !== false &&
                (strpos($content, 'challenge') !== false || strpos($content, 'security check') !== false)) {
                throw new Exception("Terdeteksi Cloudflare challenge. Coba lagi nanti atau gunakan proxy lain.");
            }

            $crawler = new Crawler($content);

            $data = [
                'provinsi' => $this->getCrawlerText($crawler, 'PROVINSI'),
                'alamat' => $this->getCrawlerText($crawler, 'ALAMAT'),
                'email' => $this->getCrawlerText($crawler, 'EMAIL'),
                'helpdesk' => $this->getCrawlerText($crawler, 'HELPDESK'),
                'versi' => $this->getCrawlerText($crawler, 'VERSI SPSE'),
                'url' => $crawler->filter('label:contains("URL") + p a')->count() ? $crawler->filter('label:contains("URL") + p a')->attr('href') : null,
                'meta_url' => $crawler->filter('meta[property="og:url"]')->count() ? $crawler->filter('meta[property="og:url"]')->attr('content') : null,
                'logo_url' => $crawler->filter('img.block')->count() ? $crawler->filter('img.block')->attr('src') : null,
            ];
        } catch (Exception $e) {
            $this->error("Gagal mengambil detail LPSE {$kd_lpse}: " . $e->getMessage());
            Log::error("Gagal mengambil detail LPSE {$kd_lpse}: " . $e->getMessage());

            return [
                'provinsi' => null,
                'alamat' => null,
                'email' => null,
                'helpdesk' => null,
                'versi' => null,
                'url' => null,
                'logo_url' => null,
                'logo_path' => null,
            ];
        }

        // Skip saving if meta_url is default
        if ($data['meta_url'] === "http://eproc.lkpp.go.id/lpse") {
            Log::info("Skipping LPSE with kd_lpse: {$kd_lpse} due to meta_url being default");
            return [
                'provinsi' => null,
                'alamat' => null,
                'email' => null,
                'helpdesk' => null,
                'versi' => null,
                'url' => null,
                'logo_url' => null,
                'logo_path' => null,
            ];
        }

        Log::info("Scraping LPSE detail: {$kd_lpse}", $data);

        // Download logo if available
        $logoUrl = $crawler->filter('img.block')->count() ? $crawler->filter('img.block')->attr('src') : null;
        $logoPath = null;
        if ($logoUrl) {
            try {
                $imageContents = file_get_contents($logoUrl);
                $imageName = basename($logoUrl);
                $logoPath = "public/logos/{$imageName}";
                Storage::put($logoPath, $imageContents);
            } catch (Exception $e) {
                Log::error("Failed to download logo for LPSE: {$kd_lpse}", ['error' => $e->getMessage()]);
            }
        } else {
            Log::info("No logo found for LPSE: {$kd_lpse}");
        }

        return [
            'provinsi' => $data['provinsi'],
            'alamat' => $data['alamat'],
            'email' => $data['email'],
            'helpdesk' => $data['helpdesk'],
            'versi' => $data['versi'],
            'url' => $data['url'],
            'logo_url' => $logoUrl,
            'logo_path' => $logoPath ? asset("storage/logos/" . basename($logoUrl)) : null,
        ];
    }

    private function getCrawlerText(Crawler $crawler, $label)
    {
        return $crawler->filter("label:contains(\"$label\") + p")->count()
            ? trim($crawler->filter("label:contains(\"$label\") + p")->text())
            : null;
    }
}
