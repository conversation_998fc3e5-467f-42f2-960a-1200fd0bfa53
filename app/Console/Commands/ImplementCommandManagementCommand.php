<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class ImplementCommandManagementCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:command-management
                            {cmd_name? : Specific command to implement management for}
                            {--all : Implement for all commands}
                            {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Implement CommandManagementTrait in commands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $commandName = $this->argument('cmd_name');
        $all = $this->option('all');
        $dryRun = $this->option('dry-run');

        if (!$commandName && !$all) {
            $this->error('Please specify a command name or use --all option.');
            return Command::FAILURE;
        }

        $commandsPath = app_path('Console/Commands');
        $commands = [];

        if ($all) {
            $files = File::files($commandsPath);
            foreach ($files as $file) {
                if ($file->getExtension() === 'php') {
                    $commands[] = $file->getPathname();
                }
            }

            $this->info('Found ' . count($commands) . ' command files.');
        } else {
            $commandFile = $commandsPath . '/' . $commandName . '.php';
            if (!File::exists($commandFile)) {
                $commandFile = $commandsPath . '/' . Str::studly($commandName) . 'Command.php';
                if (!File::exists($commandFile)) {
                    $this->error("Command file for '{$commandName}' not found.");
                    return Command::FAILURE;
                }
            }

            $commands[] = $commandFile;
            $this->info("Found command file: {$commandFile}");
        }

        $implementedCount = 0;
        $skippedCount = 0;

        foreach ($commands as $commandFile) {
            $content = File::get($commandFile);
            $className = basename($commandFile, '.php');

            // Skip this command
            if ($className === 'ImplementCommandManagementCommand') {
                $this->info("Skipping self: {$className}");
                $skippedCount++;
                continue;
            }

            // Check if trait is already implemented
            if (Str::contains($content, 'use App\Traits\CommandManagementTrait;') ||
                Str::contains($content, 'use CommandManagementTrait;')) {
                $this->info("Command {$className} already implements CommandManagementTrait. Skipping.");
                $skippedCount++;
                continue;
            }

            // Check if class extends Command
            if (!Str::contains($content, 'extends Command')) {
                $this->info("Command {$className} does not extend Command. Skipping.");
                $skippedCount++;
                continue;
            }

            $this->info("Implementing CommandManagementTrait in {$className}...");

            // Add use statement
            $newContent = preg_replace(
                '/(namespace App\\\\Console\\\\Commands;.*?)(\s*class\s+' . $className . '\s+extends\s+Command)/s',
                "$1\n\nuse App\\Traits\\CommandManagementTrait;$2",
                $content
            );

            // Add use trait statement
            $newContent = preg_replace(
                '/(class\s+' . $className . '\s+extends\s+Command.*?{)/s',
                "$1\n    use CommandManagementTrait;",
                $newContent
            );

            // Modify handle method
            if (preg_match('/public\s+function\s+handle\s*\(\s*\)\s*{/s', $newContent)) {
                $newContent = preg_replace(
                    '/(public\s+function\s+handle\s*\(\s*\)\s*{)/s',
                    "$1\n        // Initialize command management\n        if (!parent::hasOption('force') || !parent::option('force')) {\n            if (!parent::hasOption('timeout')) {\n                \$timeout = 3600; // Default timeout: 1 hour\n            } else {\n                \$timeout = (int)parent::option('timeout');\n            }\n            \n            if (!\$this->initCommandManagement(\$timeout, false)) {\n                return Command::FAILURE;\n            }\n        }\n        \n        try {",
                    $newContent
                );

                // Add catch block at the end of handle method
                $newContent = preg_replace(
                    '/(public\s+function\s+handle\s*\(\s*\).*?)(return\s+.*?;)\s*}/s',
                    "$1$2\n        } catch (\\Exception \$e) {\n            \$this->error(\"An error occurred: \" . \$e->getMessage());\n            \n            // Log to system errors\n            \$this->logSystemError(\$e, \"handle method - main command execution\");\n            \n            // Mark command as failed\n            \$this->markCommandAsCompleted(\n                \"Command failed: \" . \$e->getMessage(),\n                [\n                    'error' => \$e->getMessage(),\n                    'trace' => \$e->getTraceAsString()\n                ]\n            );\n            \n            return Command::FAILURE;\n        }",
                    $newContent
                );
            } else {
                $this->warn("Could not find handle method in {$className}. Manual implementation required.");
            }

            if ($dryRun) {
                $this->info("Would modify {$className} (dry run)");
                $this->line("--- Changes ---");
                $this->line(Str::limit($newContent, 500));
                $this->line("--- End of Changes ---");
            } else {
                File::put($commandFile, $newContent);
                $this->info("CommandManagementTrait implemented in {$className}.");
            }

            $implementedCount++;
        }

        $this->info("Implementation completed. {$implementedCount} commands modified, {$skippedCount} commands skipped.");

        if (!$dryRun && $implementedCount > 0) {
            $this->warn("Please review the changes and make any necessary adjustments.");
            $this->warn("You may need to add additional error handling or command-specific logic.");
        }

        return Command::SUCCESS;
    }
}
