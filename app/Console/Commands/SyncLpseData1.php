<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use App\Models\TenderLpse;
use App\Models\Anggaran;
use App\Models\InstansiSatker;
use App\Models\Jadwal;
use App\Models\LokasiPaket;
use App\Models\PesertaTender;
use App\Models\PemenangLpse;
use App\Models\SyncLog;
use App\Models\ListPerusahaanTender;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SyncLpseData1 extends Command
{
    protected $signature = 'sync:lpse-data11';
    protected $description = 'Sync LPSE data from API';

    public function handle()
    {
        // Ambil semua kd_lpse dari tabel lpse
        $kdLpses = \DB::table('lpse')->pluck('kd_lpse');
        Log::info("Starting");
        foreach ($kdLpses as $kdLpse) {
            $this->info("Syncing data for LPSE code: {$kdLpse}");
            Log::info("");
            Log::info("Syncing data for LPSE code: {$kdLpse}");
            $this->syncLpseData($kdLpse);
        }

        $this->info("All LPSE data sync completed.");
        Log::info("All LPSE data sync completed.");
    }


    private function syncLpseData1($kdLpse)
    {
        $apiUrl = "https://tenderid.mcmtestapp.com/api/tender/lpse/{$kdLpse}";
        $response = Http::timeout(120)->get($apiUrl);

        if ($response->successful()) {
            $data = $response->json();

            DB::beginTransaction();

            try {
                foreach ($data as $tenderData) {
                    $this->processTenderData($tenderData);
                }

                DB::commit();
                $this->info("Data sync completed successfully for LPSE code: {$kdLpse}");
                Log::info("Data sync completed successfully for LPSE code: {$kdLpse}");
            } catch (\Exception $e) {
                DB::rollBack();
                $this->error("Error occurred while syncing LPSE code {$kdLpse}: " . $e->getMessage());
                Log::error("Error occurred while syncing LPSE code {$kdLpse}: " . $e->getMessage());
            }
        } else {
            $this->error("Failed to fetch data from API for LPSE code: {$kdLpse}");
            Log::error("Failed to fetch data from API for LPSE code: {$kdLpse}");
        }
    }

    private function processTenderData($tenderData)
    {
        $tender = $this->updateOrCreateTender($tenderData);
        $this->updateRelatedData($tender, $tenderData);
    }

    private function updateOrCreateTender($tenderData)
    {
        $tender = TenderLpse::updateOrCreate(
            ['kode_tender' => $tenderData['Kode Tender']],
            $this->mapTenderData($tenderData)
        );

        if ($tender->wasRecentlyCreated) {
            $this->logChanges($tender->kode_tender, 'create', $tender->toArray(), 'tender_lpse');
        } elseif ($tender->wasChanged()) {
            $changes = $tender->getChanges();
            $this->logChanges($tender->kode_tender, 'update', $changes, 'tender_lpse');
        }

        return $tender;
    }

    private function updateRelatedData($tender, $tenderData)
    {
        if (isset($tenderData['Instansi dan Satker'])) {
            $this->updateInstansiSatker($tender, $tenderData['Instansi dan Satker']);
        }
    
        // Periksa apakah kunci 'jadwal' ada dan merupakan array
        if (isset($tenderData['jadwal']) && is_array($tenderData['jadwal'])) {
            $this->updateJadwal($tender, $tenderData['jadwal']);
        } else {
            // Jika tidak ada jadwal, coba cari data jadwal dari kunci lain
            $jadwalData = $this->extractJadwalData($tenderData);
            if (!empty($jadwalData)) {
                $this->updateJadwal($tender, $jadwalData);
            }
        }
    
        if (isset($tenderData['lokasi_paket'])) {
            $this->updateLokasiPaket($tender, $tenderData['lokasi_paket']);
        }
        
        if (isset($tenderData['pemenang'])) {
            $this->updatePemenangLpse($tender, $tenderData['pemenang'], $tenderData['harga_penawaran'] ?? null, $tenderData['harga_terkoreksi'] ?? null);
        }
    }
    
    private function extractJadwalData($tenderData)
    {
        $jadwalData = [];
        
        // Coba ekstrak data jadwal dari kunci-kunci yang mungkin ada
        $possibleKeys = [
            'jadwal_pengumuman' => 'Pengumuman Pascakualifikasi',
            'jadwal_penawaran' => 'Upload Dokumen Penawaran',
            // Tambahkan kunci lain yang mungkin berisi data jadwal
        ];
    
        foreach ($possibleKeys as $key => $tahap) {
            if (isset($tenderData[$key])) {
                $jadwalData[] = [
                    'tahap' => $tahap,
                    'tanggal_mulai' => $tenderData[$key]['tanggal_mulai'] ?? null,
                    'tanggal_selesai' => $tenderData[$key]['tanggal_akhir'] ?? null
                ];
            }
        }
    
        return $jadwalData;
    }
    
    private function updateJadwal($tender, $jadwalDataList)
    {
        foreach ($jadwalDataList as $jadwalData) {
            if (!isset($jadwalData['tahap']) || !isset($jadwalData['tanggal_mulai']) || !isset($jadwalData['tanggal_selesai'])) {
                continue; // Skip jika data tidak lengkap
            }
    
            $jadwal = Jadwal::updateOrCreate(
                [
                    'kode_tender' => $tender->kode_tender,
                    'tahap' => $jadwalData['tahap']
                ],
                [
                    'tanggal_mulai' => $jadwalData['tanggal_mulai'],
                    'tanggal_selesai' => $jadwalData['tanggal_selesai']
                ]
            );
    
            if ($jadwal->wasRecentlyCreated) {
                $this->logChanges($jadwal->id, 'create', $jadwalData, 'jadwal');
            } elseif ($jadwal->wasChanged()) {
                $changes = $jadwal->getChanges();
                $this->logChanges($jadwal->id, 'update', $changes, 'jadwal');
            }
        }
    }

    private function updateInstansiSatker($tender, $satkerDataList)
    {
        foreach ($satkerDataList as $satkerData) {
            $satker = InstansiSatker::updateOrCreate(
                [
                    'kode_tender' => $tender->kode_tender,
                    'rup_id' => $satkerData['rup_id']
                ],
                [
                    'nama_satker' => $satkerData['stk_nama'],
                    'nama_instansi' => $satkerData['nama_instansi'],
                    'jenis_instansi' => $satkerData['jenis_instansi']
                ]
            );

            if ($satker->wasRecentlyCreated) {
                $this->logChanges($satker->id, 'create', $satkerData, 'instansi_satker');
            } elseif ($satker->wasChanged()) {
                $changes = $satker->getChanges();
                $this->logChanges($satker->id, 'update', $changes, 'instansi_satker');
            }
        }
    }

    private function updateLokasiPaket($tender, $lokasiDataList)
{
    if (!is_array($lokasiDataList)) {
        Log::warning("LokasiPaket data is not an array for tender: {$tender->kode_tender}");
        return;
    }

    foreach ($lokasiDataList as $lokasiItem) {
        if (!isset($lokasiItem['lokasi']) || !is_array($lokasiItem['lokasi'])) {
            Log::warning("Invalid lokasi data format for tender: {$tender->kode_tender}");
            continue;
        }

        $lokasiData = $lokasiItem['lokasi'];

        $kabupaten = $lokasiData['kbp_nama'] ?? null;
        $provinsi = $lokasiData['prp_nama'] ?? null;
        $detail = $lokasiData['pkt_lokasi'] ?? null;

        if (!$kabupaten && !$provinsi && !$detail) {
            Log::warning("Insufficient location data for tender: {$tender->kode_tender}");
            continue;
        }

        $lokasi = LokasiPaket::updateOrCreate(
            [
                'kode_tender' => $tender->kode_tender,
                'prp_nama' => $provinsi,
                'kbp_nama' => $kabupaten,
                'pkt_lokasi' => $detail
            ]
        );

        if ($lokasi->wasRecentlyCreated) {
            $this->logChanges($lokasi->id, 'create', $lokasiData, 'lokasi_paket');
        } elseif ($lokasi->wasChanged()) {
            $changes = $lokasi->getChanges();
            $this->logChanges($lokasi->id, 'update', $changes, 'lokasi_paket');
        }
    }
}

    private function updatePemenangLpse($tender, $pemenangData, $hargaPenawaran, $hargaTerkoreksi)
    {
        $perusahaan = $this->getOrCreatePerusahaan($pemenangData);

        $pemenang = PemenangLpse::updateOrCreate(
            ['kode_tender' => $tender->kode_tender],
            [
                'id_peserta' => $perusahaan->id,
                'harga_penawaran' => $hargaPenawaran,
                'harga_terkoreksi' => $hargaTerkoreksi
            ]
        );

        if ($pemenang->wasRecentlyCreated) {
            $this->logChanges($pemenang->id, 'create', $pemenang->toArray(), 'pemenang_lpse');
        } elseif ($pemenang->wasChanged()) {
            $changes = $pemenang->getChanges();
            $this->logChanges($pemenang->id, 'update', $changes, 'pemenang_lpse');
        }
    }

    private function getOrCreatePerusahaan($perusahaanData)
    {
        $perusahaan = ListPerusahaanTender::updateOrCreate(
            ['npwp' => $perusahaanData['NPWP']],
            [
                'nama_peserta' => $perusahaanData['Nama Perusahaan'],
                'alamat' => $perusahaanData['Alamat'] ?? null,
            ]
        );

        if ($perusahaan->wasRecentlyCreated) {
            $this->logChanges($perusahaan->id, 'create', $perusahaan->toArray(), 'list_perusahaan_tender');
        } elseif ($perusahaan->wasChanged()) {
            $changes = $perusahaan->getChanges();
            $this->logChanges($perusahaan->id, 'update', $changes, 'list_perusahaan_tender');
        }

        return $perusahaan;
    }

    private function mapTenderData(array $tenderData): array
    {
        return [
            'kd_lpse' => $tenderData['Kode LPSE'] ?? null,
            'status_tender' => $tenderData['Status_Tender'] ?? null,
            'nama_paket' => $tenderData['Nama Paket'] ?? null,
            'pagu' => $tenderData['Pagu'],
            'hps' => $this->formatDecimal($tenderData['HPS']),
            'tanggal_paket_dibuat' => $tenderData['tanggal paket dibuat'] ?? null,
            'tanggal_paket_tayang' => $tenderData['tanggal paket tayang'] ?? null,
            'kategori_pekerjaan' => $tenderData['Kategori Pekerjaan'] ?? null,
            'metode_pemilihan' => $tenderData['Metode Pemilihan'] ?? null,
            'metode_pengadaan' => $tenderData['Metode Pengadaan'] ?? null,
            'metode_evaluasi' => $tenderData['Metode Evaluasi'] ?? null,
            'cara_pembayaran' => $tenderData['Cara Pembayaran'] ?? null,
            'jenis_penetapan_pemenang' => $tenderData['Jenis Penetapan Pemenang'] ?? null,
            'apakah_paket_konsolidasi' => $tenderData['Apakah paket konsolidasi'] ?? null,
            'jumlah_pendaftar' => $tenderData['Jumlah Pendaftar'] ?? null,
            'jumlah_penawar' => $tenderData['Jumlah Penawar'] ?? null,
            'jumlah_kirim_kualifikasi' => $tenderData['jumlah_kirim_kualifikasi'] ?? null,
            'durasi_tender' => $tenderData['Durasi Tender'] ?? null,
            'versi_paket_spse' => $tenderData['Versi_spse_paket'] ?? null,
            'kualifikasi_usaha' => $tenderData['Kualifikasi Usaha'] ?? null,
            'tahap_tender' => $tenderData['tahap_tender'] ?? null,
        ];
    }

    private function formatDecimal($value)
    {
        return is_numeric($value) ? number_format($value, 1, '.', '') : $value;
    }

    private function logChanges($id, $action, $changes, $table)
    {
        // Implement your logging logic here
        Log::info("Changes in $table: ID $id, Action: $action", $changes);
    }
}
