<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Meilisearch\Client;
use App\Models\DataSirup;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use Symfony\Component\Console\Command\Command as SymfonyCommand;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class RecoverDataSirupFromMeilisearch extends Command
{
    use CommandManagementTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sirup:recover-from-meilisearch
                            {--chunk=1000 : Jumlah data yang diproses per batch}
                            {--timeout=7200 : Maximum runtime in seconds before command is considered stuck}
                            {--force : Force run even if command is already running}
                            {--lock-name= : Custom lock name for this command instance}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recover data DataSirup dari Meilisearch ke database MySQL';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 7200;
        $force = $this->option('force');
        $chunkSize = $this->option('chunk') ? (int)$this->option('chunk') : 1000;

        if (!$this->initCommandManagement($timeout, $force)) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            $message = 'Memulai proses recovery data DataSirup dari Meilisearch...';
            $this->info($message);

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                $message,
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Get Meilisearch client
            $host = config('scout.meilisearch.host');
            $key = config('scout.meilisearch.key');
            $client = new Client($host, $key);

            // Get the index name
            $indexName = (new DataSirup())->searchableAs();
            $this->info("Menggunakan index: {$indexName}");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                5,
                100,
                "Menggunakan index: {$indexName}",
                [
                    'index_name' => $indexName,
                    'stage' => 'init',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Check if index exists
            try {
                $index = $client->index($indexName);
                $stats = $index->stats();
                $totalDocuments = $stats['numberOfDocuments'];

                if ($totalDocuments <= 0) {
                    $message = "Tidak ada dokumen di index Meilisearch: {$indexName}";
                    $this->error($message);
                    CommandProgress::markAsFailed(
                        $this->getName(),
                        $message,
                        [
                            'index_name' => $indexName,
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );
                    return SymfonyCommand::FAILURE;
                }

                $this->info("Total dokumen di Meilisearch: {$totalDocuments}");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    10,
                    100,
                    "Total dokumen di Meilisearch: {$totalDocuments}",
                    [
                        'index_name' => $indexName,
                        'total_documents' => $totalDocuments,
                        'stage' => 'counting',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                // Hitung jumlah batch yang akan diproses
                $totalBatches = ceil($totalDocuments / $chunkSize);
                $this->info("Total batch yang akan diproses: {$totalBatches}");

                // Variabel untuk tracking proses recovery
                $totalFound = 0;
                $totalAdded = 0;
                $totalProcessed = 0;
                $totalSuccess = 0;
                $totalDuplicate = 0;
                $totalError = 0;

                // Batch untuk pemrosesan langsung
                $currentBatch = [];

                // Coba ambil semua dokumen dengan pendekatan iteratif
                $this->info("Mengambil semua dokumen dari Meilisearch...");

                // Cek jumlah data di database yang perlu dilengkapi
                $totalDataToComplete = DataSirup::whereNull('tahun_anggaran')
                    ->orWhere('tahun_anggaran', '')
                    ->count();
                $this->info("Jumlah data di database yang perlu dilengkapi: {$totalDataToComplete}");

                // Batasi jumlah data yang akan diproses untuk testing
                $maxDataToProcess = min($totalDataToComplete, 10000);
                $this->info("Akan memproses maksimal {$maxDataToProcess} data");

                if ($totalDataToComplete === 0) {
                    $this->info("Tidak ada data yang perlu dilengkapi di database.");
                    return SymfonyCommand::SUCCESS;
                }

                // Ambil data yang perlu dilengkapi dari database
                $this->info("Mengambil data yang perlu dilengkapi dari database...");

                // Ambil data dalam batch untuk menghindari memory limit
                $batchSize = 1000;
                $page = 0;
                $totalProcessed = 0;
                $totalFound = 0;

                while ($totalProcessed < $maxDataToProcess && $page < 100) { // Batasi maksimal 100 halaman
                    $page++;

                    // Ambil batch data dari database
                    $dataToComplete = DataSirup::whereNull('tahun_anggaran')
                        ->orWhere('tahun_anggaran', '')
                        ->take($batchSize)
                        ->skip(($page - 1) * $batchSize)
                        ->get(['id', 'kode_rup', 'nama_paket']);

                    $batchCount = $dataToComplete->count();
                    $totalProcessed += $batchCount;

                    if ($batchCount === 0) {
                        break; // Tidak ada lagi data
                    }

                    $this->info("Batch {$page}: Memproses {$batchCount} data dari database");

                    // Kumpulkan semua kode_rup untuk pencarian batch
                    $kodeRups = [];
                    $kodeRupToData = [];

                    foreach ($dataToComplete as $data) {
                        if (!empty($data->kode_rup)) {
                            $kodeRups[] = $data->kode_rup;
                            $kodeRupToData[$data->kode_rup] = $data;
                        }
                    }

                    if (empty($kodeRups)) {
                        continue; // Skip jika tidak ada kode_rup
                    }

                    // Bagi kode_rup menjadi batch-batch kecil untuk pencarian
                    $kodeRupBatches = array_chunk($kodeRups, 20);

                    foreach ($kodeRupBatches as $batchIndex => $kodeRupBatch) {
                        $this->info("Memproses sub-batch " . ($batchIndex + 1) . " dari " . count($kodeRupBatches) . " dengan " . count($kodeRupBatch) . " kode_rup");

                        // Cari data di Meilisearch untuk setiap kode_rup dalam batch
                        foreach ($kodeRupBatch as $kodeRup) {
                            try {
                                // Karena kode_rup tidak dapat difilter, kita gunakan pencarian teks
                                $searchResults = $index->search($kodeRup, [
                                    'limit' => 5 // Ambil beberapa hasil untuk memastikan kita mendapatkan yang tepat
                                ]);

                                $hits = $searchResults->getHits();
                                $found = false;

                                // Cari data yang kode_rup-nya cocok persis
                                foreach ($hits as $hit) {
                                    // Konversi hit ke array jika belum
                                    $hitData = is_array($hit) ? $hit : (array)$hit;

                                    // Cek apakah kode_rup cocok persis
                                    if (isset($hitData['kode_rup']) && $hitData['kode_rup'] == $kodeRup) {
                                        $found = true;
                                        $totalFound++;

                                        // Pastikan data dari Meilisearch memiliki tahun_anggaran
                                        if (isset($hitData['tahun_anggaran']) && !empty($hitData['tahun_anggaran'])) {
                                            // Tambahkan ke batch saat ini
                                            $currentBatch[] = $hitData;
                                            $totalAdded++;

                                            // Jika batch sudah mencapai 100 data, proses ke database
                                            if (count($currentBatch) >= 100) {
                                                $this->processBatchToDatabase($currentBatch, $totalSuccess, $totalDuplicate, $totalError);
                                                $this->info("Batch diproses: {$totalSuccess} sukses, {$totalDuplicate} duplikat, {$totalError} error");
                                                $currentBatch = []; // Reset batch
                                            }

                                            // Update progress setiap 100 data
                                            if ($totalAdded % 100 === 0) {
                                                $this->info("Progress: {$totalAdded} data ditemukan untuk diupdate");
                                            }
                                        }

                                        break; // Keluar dari loop jika sudah ditemukan
                                    }
                                }

                                // Jika tidak ditemukan, coba dengan pendekatan lain
                                if (!$found && isset($kodeRupToData[$kodeRup]) && !empty($kodeRupToData[$kodeRup]->nama_paket)) {
                                    $namaPaket = $kodeRupToData[$kodeRup]->nama_paket;

                                    // Cari berdasarkan nama paket
                                    $searchResults = $index->search($namaPaket, [
                                        'limit' => 5
                                    ]);

                                    $hits = $searchResults->getHits();

                                    foreach ($hits as $hit) {
                                        $hitData = is_array($hit) ? $hit : (array)$hit;

                                        // Cek apakah kode_rup cocok persis
                                        if (isset($hitData['kode_rup']) && $hitData['kode_rup'] == $kodeRup) {
                                            $found = true;
                                            $totalFound++;

                                            // Pastikan data dari Meilisearch memiliki tahun_anggaran
                                            if (isset($hitData['tahun_anggaran']) && !empty($hitData['tahun_anggaran'])) {
                                                // Tambahkan ke batch saat ini
                                                $currentBatch[] = $hitData;
                                                $totalAdded++;

                                                // Jika batch sudah mencapai 100 data, proses ke database
                                                if (count($currentBatch) >= 100) {
                                                    $this->processBatchToDatabase($currentBatch, $totalSuccess, $totalDuplicate, $totalError);
                                                    $this->info("Batch diproses: {$totalSuccess} sukses, {$totalDuplicate} duplikat, {$totalError} error");
                                                    $currentBatch = []; // Reset batch
                                                }
                                            }

                                            break; // Keluar dari loop jika sudah ditemukan
                                        }
                                    }
                                }
                            } catch (\Exception $e) {
                                $this->error("Error saat mencari kode_rup {$kodeRup}: " . $e->getMessage());
                            }
                        }

                        // Jeda kecil antara sub-batch untuk mengurangi beban pada server
                        if ($batchIndex % 5 === 0 && $batchIndex > 0) {
                            usleep(100000); // 0.1 detik
                        }
                    }

                    $this->info("Batch {$page} selesai. Total data ditemukan: {$totalFound}, Total ditambahkan: {$totalAdded}");

                    // Jeda kecil untuk mengurangi beban pada server
                    usleep(200000); // 0.2 detik
                }

                // Proses sisa data dalam batch terakhir jika ada
                if (count($currentBatch) > 0) {
                    $this->info("Memproses batch terakhir dengan " . count($currentBatch) . " data...");
                    $this->processBatchToDatabase($currentBatch, $totalSuccess, $totalDuplicate, $totalError);
                    $this->info("Batch terakhir diproses: {$totalSuccess} sukses, {$totalDuplicate} duplikat, {$totalError} error");
                }

                $this->info("Total data yang berhasil diproses: {$totalAdded}");

                if ($totalAdded === 0) {
                    $this->info("Tidak ada dokumen yang perlu diproses. Semua data sudah lengkap.");
                    return SymfonyCommand::SUCCESS;
                }

                // Selesai, tidak perlu memproses batch lagi karena sudah diproses langsung
                $this->newLine(2);

                $endTime = microtime(true);
                $executionTime = $endTime - $startTime;
                $message = "Recovery data selesai dalam " . round($executionTime, 2) . " detik!";

                $this->newLine(2);
                $this->info("=== RINGKASAN PROSES RECOVERY ===");
                $this->info("Total dokumen di Meilisearch yang ditemukan: {$totalFound}");
                $this->info("Total dokumen yang ditambahkan ke batch: {$totalAdded}");
                $this->info("Data berhasil ditambahkan/diupdate: {$totalSuccess}");
                $this->info("Data duplikat (dilewati): {$totalDuplicate}");
                $this->info("Data error: {$totalError}");
                $this->info("Waktu eksekusi: " . round($executionTime, 2) . " detik");
                $this->info("================================");

                // Cek jumlah data yang masih perlu dilengkapi setelah proses
                $remainingToComplete = DataSirup::whereNull('tahun_anggaran')
                    ->orWhere('tahun_anggaran', '')
                    ->count();
                $this->info("Jumlah data yang masih perlu dilengkapi: {$remainingToComplete}");
                $this->info("Jumlah data yang berhasil dilengkapi: " . ($totalDataToComplete - $remainingToComplete));

                // Mark command as completed
                CommandProgress::markAsCompleted(
                    $this->getName(),
                    $message,
                    [
                        'index_name' => $indexName,
                        'total_found' => $totalFound,
                        'total_added' => $totalAdded,
                        'success_count' => $totalSuccess,
                        'duplicate_count' => $totalDuplicate,
                        'error_count' => $totalError,
                        'execution_time' => round($executionTime, 2),
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage()
                    ]
                );

                return SymfonyCommand::SUCCESS;

            } catch (\Exception $e) {
                $errorMessage = "Error saat mengakses index Meilisearch: " . $e->getMessage();
                $this->error($errorMessage);

                // Mark command as failed
                CommandProgress::markAsFailed(
                    $this->getName(),
                    $errorMessage,
                    [
                        'index_name' => $indexName,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                return SymfonyCommand::FAILURE;
            }

        } catch (\Exception $e) {
            $errorMessage = "Error saat menjalankan command: " . $e->getMessage();
            $this->error($errorMessage);

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                $errorMessage,
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }

    /**
     * Get memory usage in MB
     *
     * @return float
     */
    protected function getMemoryUsage(): float
    {
        return round(memory_get_usage(true) / 1024 / 1024, 2);
    }

    /**
     * Get peak memory usage in MB
     *
     * @return float
     */
    protected function getPeakMemoryUsage(): float
    {
        return round(memory_get_peak_usage(true) / 1024 / 1024, 2);
    }

    /**
     * Format tanggal dengan aman
     *
     * @param mixed $tanggal
     * @return string|null
     */
    protected function formatTanggal($tanggal): ?string
    {
        if (empty($tanggal)) {
            return null;
        }

        try {
            // Coba parse tanggal
            return date('Y-m-d H:i:s', strtotime($tanggal));
        } catch (\Exception $e) {
            // Jika gagal, log error dan return null
            Log::warning("Format tanggal tidak valid: {$tanggal}");
            return null;
        }
    }

    /**
     * Proses batch data ke database dengan optimasi
     *
     * @param array $batch
     * @param int &$totalSuccess
     * @param int &$totalDuplicate
     * @param int &$totalError
     * @return void
     */
    protected function processBatchToDatabase(array $batch, int &$totalSuccess, int &$totalDuplicate, int &$totalError): void
    {
        if (empty($batch)) {
            return;
        }

        // Ekstrak semua kode_rup dari batch
        $kodeRups = [];
        foreach ($batch as $hit) {
            $hitData = is_array($hit) ? $hit : (array)$hit;
            $kodeRup = $hitData['kode_rup'] ?? null;
            if ($kodeRup) {
                $kodeRups[] = $kodeRup;
            }
        }

        if (empty($kodeRups)) {
            return;
        }

        // Ambil semua data yang sudah ada di database dalam satu query
        $existingDataMap = [];
        $existingData = DataSirup::whereIn('kode_rup', $kodeRups)->get();
        foreach ($existingData as $data) {
            $existingDataMap[$data->kode_rup] = $data;
        }

        // Siapkan data untuk insert dan update
        $dataToInsert = [];
        $dataToUpdate = [];

        // Proses setiap data dalam batch
        foreach ($batch as $hit) {
            try {
                // Konversi hit ke array jika belum
                $hitData = is_array($hit) ? $hit : (array)$hit;

                // Prepare data untuk disimpan
                $kodeRup = $hitData['kode_rup'] ?? null;

                if (!$kodeRup) {
                    continue; // Skip jika tidak ada kode_rup
                }

                // Prepare data untuk disimpan/update
                $data = [
                    'kode_rup' => $kodeRup,
                    'nama_paket' => $hitData['nama_paket'] ?? null,
                    'nama_klpd' => $hitData['nama_klpd'] ?? null,
                    'satuan_kerja' => $hitData['satuan_kerja'] ?? null,
                    'tahun_anggaran' => $hitData['tahun_anggaran'] ?? null,
                    'lokasi_pekerjaan' => is_array($hitData['lokasi_pekerjaan'] ?? null) ? json_encode($hitData['lokasi_pekerjaan']) : $hitData['lokasi_pekerjaan'],
                    'volume_pekerjaan' => $hitData['volume_pekerjaan'] ?? null,
                    'uraian_pekerjaan' => $hitData['uraian_pekerjaan'] ?? null,
                    'spesifikasi_pekerjaan' => $hitData['spesifikasi_pekerjaan'] ?? null,
                    'produk_dalam_negeri' => $hitData['produk_dalam_negeri'] ?? null,
                    'umkm' => $hitData['umkm'] ?? null,
                    'pra_dipa_dpa' => $hitData['pra_dipa_dpa'] ?? null,
                    'sumber_dana' => is_array($hitData['sumber_dana'] ?? null) ? json_encode($hitData['sumber_dana']) : $hitData['sumber_dana'],
                    'jenis_pengadaan' => is_array($hitData['jenis_pengadaan'] ?? null) ? json_encode($hitData['jenis_pengadaan']) : $hitData['jenis_pengadaan'],
                    'total_pagu' => $hitData['total_pagu'] ?? null,
                    'metode_pemilihan' => $hitData['metode_pemilihan'] ?? null,
                    'pemanfaatan_barang_jasa' => is_array($hitData['pemanfaatan_barang_jasa'] ?? null) ? json_encode($hitData['pemanfaatan_barang_jasa']) : $hitData['pemanfaatan_barang_jasa'],
                    'jadwal_pelaksanaan_kontrak' => is_array($hitData['jadwal_pelaksanaan_kontrak'] ?? null) ? json_encode($hitData['jadwal_pelaksanaan_kontrak']) : $hitData['jadwal_pelaksanaan_kontrak'],
                    'jadwal_pemilihan_penyedia' => is_array($hitData['jadwal_pemilihan_penyedia'] ?? null) ? json_encode($hitData['jadwal_pemilihan_penyedia']) : $hitData['jadwal_pemilihan_penyedia'],
                    'is_processing' => $hitData['is_processing'] ?? 0,
                    'tanggal_paket_diumumkan' => $this->formatTanggal($hitData['tanggal_paket_diumumkan'] ?? null),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // Cek apakah data sudah ada di database
                if (isset($existingDataMap[$kodeRup])) {
                    $existingRecord = $existingDataMap[$kodeRup];

                    // Jika data sudah ada, cek tahun_anggaran
                    if ($existingRecord->tahun_anggaran === null || $existingRecord->tahun_anggaran === '') {
                        // Update data jika tahun_anggaran kosong
                        $dataToUpdate[$existingRecord->id] = $data;
                        $totalSuccess++;
                    } else {
                        // Skip jika tahun_anggaran sudah ada
                        $totalDuplicate++;
                    }
                } else {
                    // Jika data belum ada, tambahkan ke daftar untuk insert
                    $dataToInsert[] = $data;
                    $totalSuccess++;
                }
            } catch (\Exception $e) {
                $totalError++;
                Log::error("Error saat memproses data DataSirup: " . $e->getMessage(), [
                    'kode_rup' => $kodeRup ?? 'unknown',
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        // Lakukan insert batch jika ada data untuk diinsert
        if (!empty($dataToInsert)) {
            try {
                // Insert data dalam batch
                DB::table('data_sirup')->insert($dataToInsert);
            } catch (\Exception $e) {
                $totalError += count($dataToInsert);
                $totalSuccess -= count($dataToInsert);
                Log::error("Error saat melakukan insert batch: " . $e->getMessage(), [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        // Lakukan update untuk setiap data yang perlu diupdate
        foreach ($dataToUpdate as $id => $data) {
            try {
                DB::table('data_sirup')->where('id', $id)->update($data);
            } catch (\Exception $e) {
                $totalError++;
                $totalSuccess--;
                Log::error("Error saat melakukan update: " . $e->getMessage(), [
                    'id' => $id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
    }
}
