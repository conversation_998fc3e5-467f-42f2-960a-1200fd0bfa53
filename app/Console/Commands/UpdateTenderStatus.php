<?php

namespace App\Console\Commands;

use App\Models\Jadwal;
use App\Models\TenderLpse;
use App\Models\Lpse;
use App\Crawler\CustomClient;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Symfony\Component\DomCrawler\Crawler;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;

class UpdateTenderStatus extends Command
{
    use CommandManagementTrait;

    protected $signature = 'tender:update-status
                            {--timeout=3600 : Maximum runtime in seconds before command is considered stuck}
                            {--lock-name= : Custom lock name for this command instance}
                            {--force : Force run even if command is already running}';
    protected $description = 'Update tender status and jadwal for tenders with schedule ending in last hour';

    protected $client;

    public function __construct()
    {
        parent::__construct();
        $this->client = new CustomClient();
    }

    protected function validateAndGetContent($url, $response)
    {
        $statusCode = $response->getStatusCode();

        if ($statusCode !== 200) {
            throw new \Exception("HTTP Error: Received status code {$statusCode} for URL: {$url}");
        }

        $html = $response->getBody()->getContents();

        if (!preg_match('/<[^>]*>/', $html)) {
            throw new \Exception("Invalid HTML response received from URL: {$url}");
        }

        $crawler = new Crawler($html);

        if ($crawler->filter('title')->count() > 0) {
            $title = $crawler->filter('title')->text();
            if (
                stripos($title, 'security check') !== false ||
                stripos($title, 'cloudflare') !== false ||
                stripos($title, 'attention required') !== false
            ) {
                throw new \Exception("Security challenge detected: {$title}");
            }
        }

        return [
            'html' => $html,
            'crawler' => $crawler
        ];
    }

    /**
     * Get elapsed time in seconds
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTime(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return Command::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            $this->info('Starting tender status update...');
            Log::channel('tender_update')->info('Starting tender status update process');

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                "Starting tender status update...",
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            // Get tenders with jadwal ending in the last hour window
            $now = Carbon::now();
            $oneHourAgo = $now->copy()->subHour();

            $this->info("Checking jadwal entries ending between: ");
            $this->info("Start: " . $oneHourAgo->format('Y-m-d H:i:s'));
            $this->info("End: " . $now->format('Y-m-d H:i:s'));
            Log::channel('tender_update')->info("Checking time window: {$oneHourAgo->format('Y-m-d H:i:s')} to {$now->format('Y-m-d H:i:s')}");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                5,
                100,
                "Checking jadwal entries in time window",
                [
                    'start_time' => $oneHourAgo->format('Y-m-d H:i:s'),
                    'end_time' => $now->format('Y-m-d H:i:s'),
                    'stage' => 'checking_time_window',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $jadwals = Jadwal::with([
                'tender' => function ($query) {
                    $query->whereNotIn('tahap_tender', [
                        'Tender Sudah Selesai',
                        'Tender Batal',
                        'Seleksi Gagal'
                    ])
                        ->whereIn('status_tender', ['Tender Sedang Berjalan', 'Aktif']);
                }
            ])
                ->where(function ($query) use ($oneHourAgo, $now) {
                    $query->whereBetween('tanggal_selesai', [$oneHourAgo, $now])
                          ->orWhereBetween('tanggal_mulai', [$oneHourAgo, $now]);
                })
                ->get();

            $jadwalCount = count($jadwals);
            $this->info("Found " . $jadwalCount . " jadwal entries to process");
            Log::channel('tender_update')->info("Found " . $jadwalCount . " jadwal entries to process");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                10,
                100,
                "Found {$jadwalCount} jadwal entries to process",
                [
                    'jadwal_count' => $jadwalCount,
                    'stage' => 'jadwal_found',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

        $count = 0;
        $totalJadwals = count($jadwals);

        foreach ($jadwals as $index => $jadwal) {
            if (!$jadwal->tender) {
                continue;
            }

            // Initialize these variables for each tender
            $tahapTenderChanged = false;
            $jadwalChanged = false;

            $count++;
            $progress = min(90, 10 + round(($count / $totalJadwals) * 80));

            $this->info("\nProcessing tender [{$count}/{$totalJadwals}]: {$jadwal->kode_tender}");
            Log::channel('tender_update')->info("Processing tender: {$jadwal->kode_tender}");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                $progress,
                100,
                "Processing tender [{$count}/{$totalJadwals}]: {$jadwal->kode_tender}",
                [
                    'kode_tender' => $jadwal->kode_tender,
                    'current' => $count,
                    'total' => $totalJadwals,
                    'stage' => 'processing_tender',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            try {
                $tender = $jadwal->tender;
                $lpse = Lpse::where('kd_lpse', $tender->kd_lpse)->first();

                if (!$lpse) {
                    $this->error("LPSE not found for kd_lpse: {$tender->kd_lpse}");

                    // Update progress
                    CommandProgress::updateProgress(
                        $this->getName(),
                        $progress,
                        100,
                        "LPSE not found for kd_lpse: {$tender->kd_lpse}",
                        [
                            'kode_tender' => $tender->kode_tender,
                            'kd_lpse' => $tender->kd_lpse,
                            'stage' => 'lpse_not_found',
                            'memory_usage' => $this->getMemoryUsage()
                        ]
                    );

                    continue;
                }

                // 1. Update Tender Details
                $tenderUrl = $lpse->url . '/lelang/' . $tender->kode_tender . '/pengumumanlelang';
                $this->info("Fetching tender details from: {$tenderUrl}");

                $response = $this->client->get($tenderUrl);
                $result = $this->validateAndGetContent($tenderUrl, $response);
                $crawler = $result['crawler'];

                // Extract tender details
                $tahapTender = '';
                if ($crawler->filter('th:contains("Tahap Tender Saat Ini")')->count() > 0) {
                    $tahapTender = rapikan_str_tahap_tender(
                        $crawler->filter('th:contains("Tahap Tender Saat Ini")')->siblings()->text()
                    );
                }

                $syaratKualifikasi = '';
                if ($crawler->filter('th:contains("Syarat Kualifikasi")')->count() > 0) {
                    $syaratKualifikasi = $crawler->filter('th:contains("Syarat Kualifikasi")')->siblings()->html();
                }

                $kualifikasiUsaha = '';
                if ($crawler->filter('th:contains("Kualifikasi Usaha")')->count() > 0) {
                    $kualifikasiUsaha = $crawler->filter('th:contains("Kualifikasi Usaha")')->siblings()->text();
                }

                $alasan = '';
                if ($crawler->filter('th:contains("Alasan")')->count() > 0) {
                    $alasan = $crawler->filter('th:contains("Alasan")')->siblings()->text();
                }

                $updated = false;

                // Only update if tahap_tender has changed
                if ($tahapTender && $tahapTender !== $tender->tahap_tender) {
                    $this->info("Updating tahap tender from '{$tender->tahap_tender}' to '{$tahapTender}'");
                    $tender->tahap_tender = $tahapTender;
                    $updated = true;
                    $tahapTenderChanged = true;
                }

                if ($tender->syarat_kualifikasi === null && $syaratKualifikasi !== '') {
                    $this->info("Adding syarat kualifikasi");
                    $tender->syarat_kualifikasi = $syaratKualifikasi;
                    $updated = true;
                }

                if ($tender->kualifikasi_usaha === null && $kualifikasiUsaha !== '') {
                    $this->info("Adding kualifikasi usaha: {$kualifikasiUsaha}");
                    $tender->kualifikasi_usaha = $kualifikasiUsaha;
                    $updated = true;
                }

                if ($tender->keterangan === null && $alasan !== '') {
                    $this->info("Adding alasan: {$alasan}");
                    $tender->keterangan = $alasan;
                    $updated = true;
                }

                if (in_array($tahapTender, ['Tender Batal', 'Seleksi Gagal'])) {
                    $this->info("Updating status to 'Ditutup' due to tender completion/cancellation");
                    $tender->status_tender = 'Ditutup';
                    $updated = true;
                }

                if ($updated) {
                    $tender->save();
                    $this->info("Tender details updated successfully");
                } else {
                    $this->info("No updates needed for this tender");
                }

                // 2. Update Jadwal
                $jadwalUrl = $lpse->url . '/lelang/' . $tender->kode_tender . '/jadwal';
                $this->info("Fetching jadwal from: {$jadwalUrl}");

                $response = $this->client->get($jadwalUrl);
                $result = $this->validateAndGetContent($jadwalUrl, $response);
                $crawler = $result['crawler'];

                $jadwalTender = [];
                $crawler->filter('table.table-sm tr')->each(function (Crawler $row, $i) use (&$jadwalTender) {
                    if ($i > 0) {
                        $jadwalTender[] = [
                            'no' => trim($row->filter('td')->eq(0)->text()),
                            'tahap' => trim($row->filter('td')->eq(1)->text()),
                            'mulai' => $this->parseDateTime(trim($row->filter('td')->eq(2)->text())),
                            'sampai' => $this->parseDateTime(trim($row->filter('td')->eq(3)->text())),
                            'perubahan' => trim($row->filter('td')->eq(4)->text())
                        ];
                    }
                });

                // Check existing jadwal count
                $existingJadwalCount = Jadwal::where('kode_tender', $tender->kode_tender)->count();

                // Filter out invalid jadwal entries
                $validJadwalEntries = array_filter($jadwalTender, function ($data) {
                    return $data['mulai'] && $data['sampai'];
                });

                $newJadwalCount = count($validJadwalEntries);

                $this->info("Existing jadwal count: {$existingJadwalCount}");
                $this->info("New jadwal count: {$newJadwalCount}");

                // Only proceed if we have valid data
                if ($newJadwalCount > 0) {
                    // Compare with existing jadwal count
                    if ($existingJadwalCount > 0 && $newJadwalCount < $existingJadwalCount) {
                        $this->warn("New jadwal count ({$newJadwalCount}) is less than existing ({$existingJadwalCount}). Skipping jadwal update for safety.");
                        Log::channel('tender_update')->warning(
                            "Skipped jadwal update for tender {$tender->kode_tender} - " .
                            "New count ({$newJadwalCount}) < Existing count ({$existingJadwalCount})"
                        );
                    } else {
                        // Compare existing jadwal with new jadwal to detect changes
                        $existingJadwals = Jadwal::where('kode_tender', $tender->kode_tender)->get()->toArray();
                        $existingJadwalsSimplified = array_map(function ($item) {
                            return [
                                'step' => $item['step'],
                                'tahap' => $item['tahap'],
                                'tanggal_mulai' => $item['tanggal_mulai'],
                                'tanggal_selesai' => $item['tanggal_selesai'],
                                'keterangan' => $item['keterangan'],
                            ];
                        }, $existingJadwals);

                        $newJadwalsSimplified = array_map(function ($item) {
                            return [
                                'step' => $item['no'],
                                'tahap' => $item['tahap'],
                                'tanggal_mulai' => $item['mulai'],
                                'tanggal_selesai' => $item['sampai'],
                                'keterangan' => $item['perubahan'] ?? null,
                            ];
                        }, $validJadwalEntries);

                        // Detect changes by comparing serialized arrays
                        $existingSerialized = serialize($existingJadwalsSimplified);
                        $newSerialized = serialize($newJadwalsSimplified);

                        if ($existingSerialized !== $newSerialized) {
                            $jadwalChanged = true;
                        }

                        // Proceed with update
                        DB::beginTransaction();
                        try {
                            Jadwal::where('kode_tender', $tender->kode_tender)->delete();
                            foreach ($validJadwalEntries as $data) {
                                $tender->jadwal()->create([
                                    'step' => $data['no'],
                                    'tahap' => $data['tahap'],
                                    'tanggal_mulai' => Carbon::parse($data['mulai']),
                                    'tanggal_selesai' => Carbon::parse($data['sampai']),
                                    'keterangan' => $data['perubahan'] ?? null
                                ]);
                            }
                            DB::commit();
                            $this->info("Jadwal updated successfully ({$newJadwalCount} entries)");
                        } catch (\Exception $e) {
                            DB::rollBack();
                            throw $e;
                        }
                    }
                } else {
                    $this->warn("No valid jadwal entries found in scraped data. Keeping existing jadwal.");
                    Log::channel('tender_update')->warning(
                        "No valid jadwal entries found for tender {$tender->kode_tender}. Existing entries preserved."
                    );
                }


            } catch (\Exception $e) {
                $this->error("Error processing tender {$jadwal->kode_tender}: " . $e->getMessage());
                Log::channel('tender_update')->error("Error processing tender {$jadwal->kode_tender}: " . $e->getMessage());

                // Log system error
                $this->logSystemError($e, "handle method - processing tender: {$jadwal->kode_tender}");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    $progress,
                    100,
                    "Error processing tender: {$jadwal->kode_tender}",
                    [
                        'kode_tender' => $jadwal->kode_tender,
                        'error' => $e->getMessage(),
                        'stage' => 'tender_processing_error',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );
            }

            // Dispatch notification job only if tahap_tender or jadwal changed
            if ($tahapTenderChanged || $jadwalChanged) {
                $this->info("Detected changes in tahap tender or jadwal, dispatching notification job...");
                \App\Jobs\SendTenderLpseNotifications::dispatch($tender);
            } else {
                $this->info("No relevant changes detected, skipping notification job.");
            }

            // Add delay between requests
            if ($count < count($jadwals)) {
                $this->info("Waiting 2 seconds before next tender...");
                sleep(2);
            }
        }

        $this->info("\nCompleted processing {$count} tenders");
        Log::channel('tender_update')->info("Completed processing {$count} tenders");

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        $message = "Completed processing {$count} tenders in " . round($executionTime, 2) . " seconds";

        // Mark command as completed
        CommandProgress::markAsCompleted(
            $this->getName(),
            $message,
            [
                'processed_count' => $count,
                'total_jadwals' => $totalJadwals,
                'execution_time' => round($executionTime, 2),
                'memory_usage' => $this->getMemoryUsage(),
                'peak_memory_usage' => $this->getPeakMemoryUsage()
            ]
        );

        return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return Command::FAILURE;
        }
    }

    private function parseDateTime($dateString)
    {
        try {
            $months = [
                'Januari' => 'January',
                'Februari' => 'February',
                'Maret' => 'March',
                'April' => 'April',
                'Mei' => 'May',
                'Juni' => 'June',
                'Juli' => 'July',
                'Agustus' => 'August',
                'September' => 'September',
                'Oktober' => 'October',
                'November' => 'November',
                'Desember' => 'December',
            ];

            foreach ($months as $id => $en) {
                $dateString = str_replace($id, $en, $dateString);
            }

            return Carbon::createFromFormat('d F Y H:i', $dateString)->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            Log::channel('tender_update')->error("Date parse error for '{$dateString}': " . $e->getMessage());
            return null;
        }
    }
}
