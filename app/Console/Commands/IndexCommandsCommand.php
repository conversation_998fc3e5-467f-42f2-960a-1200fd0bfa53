<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class IndexCommandsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'commands:index';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Index all available commands in the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Indexing commands...');
        
        // Get all commands from Artisan
        $commands = $this->getApplication()->all();
        
        // Get command files from app/Console/Commands
        $commandFiles = File::files(app_path('Console/Commands'));
        
        $commandsData = [];
        
        // Process Artisan commands
        foreach ($commands as $name => $command) {
            // Skip Laravel internal commands
            if (Str::startsWith($name, ['_', 'help', 'list', 'env', 'inspire', 'migrate', 'db', 'cache', 'config', 'key', 'make', 'vendor', 'package', 'schedule', 'queue', 'route', 'storage', 'stub', 'test', 'view'])) {
                continue;
            }
            
            $description = $command->getDescription();
            
            $commandsData[$name] = [
                'command_name' => $name,
                'description' => $description,
                'class_name' => get_class($command),
                'is_running' => false,
                'created_at' => now(),
                'updated_at' => now()
            ];
        }
        
        // Process command files
        foreach ($commandFiles as $file) {
            $className = 'App\\Console\\Commands\\' . str_replace('.php', '', $file->getFilename());
            
            if (class_exists($className)) {
                $reflection = new \ReflectionClass($className);
                
                if ($reflection->isAbstract()) {
                    continue;
                }
                
                if ($reflection->isSubclassOf('Illuminate\\Console\\Command')) {
                    $instance = new $className();
                    $name = $instance->getName();
                    
                    if (!isset($commandsData[$name])) {
                        $commandsData[$name] = [
                            'command_name' => $name,
                            'description' => $instance->getDescription(),
                            'class_name' => $className,
                            'is_running' => false,
                            'created_at' => now(),
                            'updated_at' => now()
                        ];
                    }
                }
            }
        }
        
        $this->info('Found ' . count($commandsData) . ' commands.');
        
        // Update database
        foreach ($commandsData as $name => $data) {
            DB::table('commands_status')->updateOrInsert(
                ['command_name' => $name],
                $data
            );
        }
        
        $this->info('Commands indexed successfully.');
        
        return Command::SUCCESS;
    }
}
