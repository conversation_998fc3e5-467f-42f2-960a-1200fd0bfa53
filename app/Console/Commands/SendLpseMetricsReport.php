<?php

namespace App\Console\Commands;

use App\Models\Lpse;
use App\Models\TenderLpse;
use App\Jobs\SendLpseMetricsNotifications;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SendLpseMetricsReport extends Command
{
    protected $signature = 'lpse:metrics-report {period=weekly}';
    protected $description = 'Send LPSE metrics report to users who follow LPSE';

    public function handle()
    {
        $period = $this->argument('period');
        $this->info("Generating LPSE metrics report for {$period} period...");

        // Get date range based on period
        $dateRange = $this->getDateRange($period);
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        $this->info("Date range: {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");

        // Get all LPSE
        $lpseList = Lpse::all();
        $this->info("Found {$lpseList->count()} LPSE to process");

        foreach ($lpseList as $lpse) {
            $this->processLpseMetrics($lpse, $startDate, $endDate, $period);
        }

        $this->info("LPSE metrics report generation completed.");
        return 0;
    }

    /**
     * Process metrics for a single LPSE
     *
     * @param Lpse $lpse
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param string $period
     * @return void
     */
    private function processLpseMetrics(Lpse $lpse, Carbon $startDate, Carbon $endDate, string $period)
    {
        $this->info("Processing metrics for LPSE {$lpse->nama_lpse}...");

        // Get new tenders count
        $newTendersCount = TenderLpse::where('kd_lpse', $lpse->kd_lpse)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Get updated tenders count
        $updatedTendersCount = TenderLpse::where('kd_lpse', $lpse->kd_lpse)
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->where('created_at', '<', $startDate)
            ->count();

        // Get total tenders count
        $totalTendersCount = TenderLpse::where('kd_lpse', $lpse->kd_lpse)
            ->count();

        $metrics = [
            'new_tenders' => $newTendersCount,
            'updated_tenders' => $updatedTendersCount,
            'unchanged_tenders' => $totalTendersCount - ($newTendersCount + $updatedTendersCount),
            'total_tenders' => $totalTendersCount,
            'period' => $this->formatPeriod($period, $startDate, $endDate),
        ];

        $this->info("LPSE {$lpse->nama_lpse} metrics: New: {$metrics['new_tenders']}, Updated: {$metrics['updated_tenders']}, Total: {$metrics['total_tenders']}");

        // Only send notifications if there are new or updated tenders
        if ($metrics['new_tenders'] > 0 || $metrics['updated_tenders'] > 0) {
            // Dispatch job to send notifications
            SendLpseMetricsNotifications::dispatch($lpse, $metrics)
                ->onQueue('notifications')
                ->delay(now()->addSeconds(5));

            $this->info("Dispatched metrics notification for LPSE {$lpse->nama_lpse}");
        } else {
            $this->info("No new or updated tenders for LPSE {$lpse->nama_lpse}, skipping notification.");
        }
    }

    /**
     * Get date range based on period
     *
     * @param string $period
     * @return array
     */
    private function getDateRange(string $period): array
    {
        $now = Carbon::now();
        $endDate = $now->copy();

        switch ($period) {
            case 'daily':
                $startDate = $now->copy()->subDay();
                break;
            case 'weekly':
                $startDate = $now->copy()->subWeek();
                break;
            case 'monthly':
                $startDate = $now->copy()->subMonth();
                break;
            default:
                $startDate = $now->copy()->subWeek();
                break;
        }

        return [
            'start' => $startDate,
            'end' => $endDate,
        ];
    }

    /**
     * Format period for display
     *
     * @param string $period
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return string
     */
    private function formatPeriod(string $period, Carbon $startDate, Carbon $endDate): string
    {
        switch ($period) {
            case 'daily':
                return $startDate->format('d M Y');
            case 'weekly':
                return $startDate->format('d M Y') . ' - ' . $endDate->format('d M Y');
            case 'monthly':
                return $startDate->format('M Y');
            default:
                return $startDate->format('d M Y') . ' - ' . $endDate->format('d M Y');
        }
    }
}
