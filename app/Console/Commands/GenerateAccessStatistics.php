<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PageAccessLog;
use App\Models\LoginLog;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;
use Symfony\Component\Console\Command\Command as SymfonyCommand;

class GenerateAccessStatistics extends Command
{
    use CommandManagementTrait;
    protected $signature = 'stats:generate
                            {--period=daily : Periode untuk menghasilkan statistik (daily, weekly, monthly)}
                            {--days=30 : Jumlah hari yang akan disertakan dalam laporan}
                            {--timeout=3600 : Waktu maksimum dalam detik sebelum command dianggap macet}
                            {--force : Paksa menjalankan meskipun command sudah berjalan}
                            {--lock-name= : Nama kunci khusus untuk instance command ini}';

    protected $description = 'Menghasilkan statistik dari log akses dan login';

    public function handle()
    {
        // Inisialisasi manajemen command dengan timeout dari parameter
        if (!$this->initCommandManagement((int)$this->option('timeout'))) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $period = $this->option('period');
            $days = (int)$this->option('days');

            $startDate = Carbon::now()->subDays($days)->startOfDay();

            $this->info("Menghasilkan statistik {$period} untuk {$days} hari terakhir");

            // Update progress
            $this->updateCommandProgress(
                0,
                100,
                "Memulai pembuatan statistik {$period} untuk {$days} hari terakhir",
                [
                    'period' => $period,
                    'days' => $days,
                    'start_date' => $startDate->format('Y-m-d H:i:s')
                ]
            );

            // Statistik akses halaman
            $this->generatePageAccessStats($startDate, $period);
            $this->updateCommandProgress(25, 100, "Statistik akses halaman selesai");

            // Statistik login
            $this->generateLoginStats($startDate, $period);
            $this->updateCommandProgress(50, 100, "Statistik login selesai");

            // Statistik perangkat
            $this->generateDeviceStats($startDate);
            $this->updateCommandProgress(75, 100, "Statistik perangkat selesai");

            // Statistik lokasi
            $this->generateLocationStats($startDate);
            $this->updateCommandProgress(100, 100, "Semua statistik selesai");

            $this->info('Pembuatan statistik selesai');

            // Bersihkan command
            $this->cleanupCommand();

            return SymfonyCommand::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Error saat menghasilkan statistik: " . $e->getMessage());

            // Log error
            SystemError::create([
                'source' => 'GenerateAccessStatistics',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'is_resolved' => false
            ]);

            // Bersihkan command
            $this->cleanupCommand();

            return SymfonyCommand::FAILURE;
        }
    }

    protected function generatePageAccessStats($startDate, $period)
    {
        $this->info('Menghasilkan statistik akses halaman...');

        // Halaman yang paling banyak dikunjungi
        $topPages = PageAccessLog::select('url', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->groupBy('url')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        $this->info('10 Halaman Teratas:');
        $this->table(
            ['URL', 'Kunjungan'],
            $topPages->map(function ($page) {
                return [$page->url, $page->total];
            })
        );

        // Kunjungan per hari/minggu/bulan
        $groupFormat = $this->getGroupFormat($period);

        $visitsByPeriod = PageAccessLog::select(
                DB::raw("DATE_FORMAT(created_at, '{$groupFormat}') as period"),
                DB::raw('count(*) as total')
            )
            ->where('created_at', '>=', $startDate)
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        $this->info("Kunjungan per {$period}:");
        $this->table(
            ['Periode', 'Kunjungan'],
            $visitsByPeriod->map(function ($visit) {
                return [$visit->period, $visit->total];
            })
        );

        // Rata-rata waktu muat
        $avgLoadTime = PageAccessLog::where('created_at', '>=', $startDate)
            ->whereNotNull('load_time_ms')
            ->avg('load_time_ms');

        $this->info("Rata-rata waktu muat halaman: " . round($avgLoadTime, 2) . " ms");
    }

    protected function generateLoginStats($startDate, $period)
    {
        $this->info('Menghasilkan statistik login...');

        // Login per hari/minggu/bulan
        $groupFormat = $this->getGroupFormat($period);

        $loginsByPeriod = LoginLog::select(
                DB::raw("DATE_FORMAT(login_at, '{$groupFormat}') as period"),
                DB::raw('count(*) as total')
            )
            ->where('login_at', '>=', $startDate)
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        $this->info("Login per {$period}:");
        $this->table(
            ['Periode', 'Login'],
            $loginsByPeriod->map(function ($login) {
                return [$login->period, $login->total];
            })
        );

        // Pengguna paling aktif
        $activeUsers = LoginLog::select('user_id', DB::raw('count(*) as total'))
            ->with('user:id,name,email')
            ->where('login_at', '>=', $startDate)
            ->groupBy('user_id')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        $this->info('10 Pengguna Paling Aktif:');
        $this->table(
            ['Pengguna', 'Email', 'Login'],
            $activeUsers->map(function ($user) {
                return [
                    $user->user->name ?? 'Tidak diketahui',
                    $user->user->email ?? 'Tidak diketahui',
                    $user->total
                ];
            })
        );
    }

    protected function generateDeviceStats($startDate)
    {
        $this->info('Menghasilkan statistik perangkat...');

        // Browser yang paling banyak digunakan
        $browsers = PageAccessLog::select('browser', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('browser')
            ->groupBy('browser')
            ->orderBy('total', 'desc')
            ->get();

        $this->info('Penggunaan Browser:');
        $this->table(
            ['Browser', 'Jumlah'],
            $browsers->map(function ($browser) {
                return [$browser->browser, $browser->total];
            })
        );

        // Sistem operasi yang paling banyak digunakan
        $operatingSystems = PageAccessLog::select('os', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('os')
            ->groupBy('os')
            ->orderBy('total', 'desc')
            ->get();

        $this->info('Penggunaan Sistem Operasi:');
        $this->table(
            ['OS', 'Jumlah'],
            $operatingSystems->map(function ($os) {
                return [$os->os, $os->total];
            })
        );

        // Jenis perangkat
        $deviceTypes = PageAccessLog::select('device_type', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('device_type')
            ->groupBy('device_type')
            ->orderBy('total', 'desc')
            ->get();

        $this->info('Penggunaan Jenis Perangkat:');
        $this->table(
            ['Jenis Perangkat', 'Jumlah'],
            $deviceTypes->map(function ($type) {
                return [$type->device_type, $type->total];
            })
        );
    }

    protected function generateLocationStats($startDate)
    {
        $this->info('Menghasilkan statistik lokasi...');

        // Negara paling aktif
        $countries = PageAccessLog::select('country', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('country')
            ->groupBy('country')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        $this->info('10 Negara Teratas:');
        $this->table(
            ['Negara', 'Kunjungan'],
            $countries->map(function ($country) {
                return [$country->country, $country->total];
            })
        );

        // Kota paling aktif
        $cities = PageAccessLog::select('country', 'city', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('city')
            ->groupBy('country', 'city')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        $this->info('10 Kota Teratas:');
        $this->table(
            ['Negara', 'Kota', 'Kunjungan'],
            $cities->map(function ($city) {
                return [$city->country, $city->city, $city->total];
            })
        );
    }

    protected function getGroupFormat($period)
    {
        switch ($period) {
            case 'daily':
                return '%Y-%m-%d'; // Format: Tahun-Bulan-Hari
            case 'weekly':
                return '%x-W%v'; // Format: Tahun-Minggu
            case 'monthly':
                return '%Y-%m'; // Format: Tahun-Bulan
            default:
                return '%Y-%m-%d';
        }
    }
}
