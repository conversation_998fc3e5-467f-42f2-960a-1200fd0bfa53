<?php
namespace App\Console\Commands;

use App\Models\ListPerusahaanTender;
use App\Models\Lpse;
use App\Models\PemenangLpse;
use App\Models\PesertaTender;
use App\Models\TenderLpse;
use App\Crawler\CustomClient;
use Illuminate\Console\Command;
use Illuminate\Database\QueryException;
use Symfony\Component\DomCrawler\Crawler;
use Illuminate\Support\Facades\Log;
use App\Traits\CommandManagementTrait;
use App\Models\CommandProgress;
use App\Models\SystemError;

class SyncPesertaTender extends Command
{
    use CommandManagementTrait;

    protected $signature = 'sync:peserta-tender {kode_tender?} {--timeout=3600 : Maximum runtime in seconds before command is considered stuck} {--force : Force run even if command is already running} {--preview : Only show preview information without full processing} {--lock-name= : Custom lock name for this command instance}';
    protected $description = 'Mensinkronkan peserta tender dari LPSE';
    // Tambahkan konstanta untuk chunk size
    protected const CHUNK_SIZE = 500;

    protected $client;

    public function __construct()
    {
        parent::__construct();
        $this->client = new CustomClient();
    }

    /**
     * Get elapsed time in seconds
     *
     * @param float $startTime
     * @return float
     */
    protected function getElapsedTime(float $startTime): float
    {
        return round(microtime(true) - $startTime, 2);
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');
        $preview = $this->option('preview');

        // Jika preview mode, tampilkan informasi saja tanpa inisialisasi command management
        if ($preview) {
            $kode_tender = $this->argument('kode_tender');
            if (!$kode_tender) {
                $this->error("Kode tender harus disediakan dalam mode preview");
                return Command::FAILURE;
            }

            $tender = TenderLpse::with('lpse')->find($kode_tender);
            if (!$tender) {
                $this->error("Tender tidak ditemukan: {$kode_tender}");
                return Command::FAILURE;
            }

            $this->info("=== PREVIEW MODE ===");
            $this->info("Tender: {$tender->nama_paket}");
            $this->info("Kode: {$tender->kode_tender}");
            $this->info("LPSE: {$tender->lpse->nama_lpse}");
            $this->info("URL Peserta: {$tender->lpse->url}/lelang/{$tender->kode_tender}/peserta");
            $this->info("URL Pemenang: {$tender->lpse->url}/evaluasi/{$tender->kode_tender}/pemenang");
            $this->info("Command akan dijalankan di background untuk mengambil data peserta dan pemenang tender.");
            return Command::SUCCESS;
        }

        // Jika force=true, kita akan tetap menjalankan command meskipun command lain sedang berjalan
        if (!$this->initCommandManagement($timeout, $force)) {
            if (!$force) {
                return Command::FAILURE;
            }
            $this->warn("Command sedang berjalan, tetapi melanjutkan karena opsi --force digunakan.");
        }

        try {
            $startTime = microtime(true); // Start measuring time

            Log::channel('sync_peserta_tender')->info("Memulai sinkronisasi peserta tender...");

            // Initialize command progress
            CommandProgress::updateProgress(
                $this->getName(),
                0,
                100,
                "Memulai sinkronisasi peserta tender...",
                [
                    'started_at' => now()->toDateTimeString(),
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            if ($kode_tender = $this->argument('kode_tender')) {
                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    5,
                    100,
                    "Memproses single tender: {$kode_tender}",
                    [
                        'kode_tender' => $kode_tender,
                        'mode' => 'single',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $this->processSingleTender($kode_tender);
            } else {
                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    5,
                    100,
                    "Memproses batch tenders",
                    [
                        'mode' => 'batch',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $this->processBatchTenders();
            }

            Log::channel('sync_peserta_tender')->info("Sinkronisasi peserta tender selesai.");

            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            $message = "Sinkronisasi peserta tender selesai dalam " . round($executionTime, 2) . " detik.";

            // Mark command as completed
            CommandProgress::markAsCompleted(
                $this->getName(),
                $message,
                [
                    'execution_time' => round($executionTime, 2),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed
            CommandProgress::markAsFailed(
                $this->getName(),
                "Command failed: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage()
                ]
            );

            return Command::FAILURE;
        }
    }

    private function processSingleTender($kode_tender)
    {
        $this->info("Memproses single tender: {$kode_tender}");

        // Update progress
        CommandProgress::updateProgress(
            $this->getName(),
            10,
            100,
            "Mencari data tender: {$kode_tender}",
            [
                'kode_tender' => $kode_tender,
                'stage' => 'finding_tender',
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        $tender = TenderLpse::find($kode_tender);

        if (!$tender) {
            $this->error("Tender tidak ditemukan: {$kode_tender}");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                100,
                100,
                "Tender tidak ditemukan: {$kode_tender}",
                [
                    'kode_tender' => $kode_tender,
                    'stage' => 'tender_not_found',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            return;
        }

        // Update progress
        CommandProgress::updateProgress(
            $this->getName(),
            20,
            100,
            "Memproses tender: {$kode_tender}",
            [
                'kode_tender' => $kode_tender,
                'nama_paket' => $tender->nama_paket,
                'stage' => 'processing_tender',
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        $this->processTender($tender);

        // Update progress
        CommandProgress::updateProgress(
            $this->getName(),
            100,
            100,
            "Selesai memproses tender: {$kode_tender}",
            [
                'kode_tender' => $kode_tender,
                'nama_paket' => $tender->nama_paket,
                'stage' => 'tender_processed',
                'memory_usage' => $this->getMemoryUsage()
            ]
        );
    }

    private function processBatchTenders()
    {
        // Step 1: Cari kode tender dari perusahaan tanpa NPWP dengan chunking
        $kodeTendersFromNpwp = collect();

        // ListPerusahaanTender::whereNull('npwp')
        //     ->whereNull('npwp_blur')
        //     ->select('id')
        //     ->orderBy('id', 'asc')
        //     ->chunkById(self::CHUNK_SIZE, function ($perusahaan) use (&$kodeTendersFromNpwp) {
        //         $kodeTendersFromNpwp = $kodeTendersFromNpwp->merge(
        //             PesertaTender::whereIn('id_peserta', $perusahaan->pluck('id'))
        //                 ->distinct('kode_tender')
        //                 ->pluck('kode_tender')
        //         );
        //     });

        // Step 2: Jika tidak ada, ambil default tender selesai
        if ($kodeTendersFromNpwp->isEmpty()) {
            $this->info("Mengambil tender yang belum memiliki data peserta atau pemenang");
            TenderLpse::whereIn('tahap_tender', [
                'Pembukaan Dokumen Penawaran',
                'Evaluasi Administrasi, Kualifikasi, Teknis, dan Harga',
                'Pembuktian Kualifikasi',
                'Penetapan Pemenang',
                'Pengumuman Pemenang',
                'Masa Sanggah',
                'Tidak Ada Jadwal',
                'Pembukaan dan Evaluasi Penawaran File I: Administrasi dan Teknis',
                'Pengumuman Hasil Evaluasi Administrasi dan Teknis',
                'Pembukaan dan Evaluasi Penawaran File II: Harga',
                'Surat Penunjukan Penyedia Barang/Jasa',
                'Klarifikasi dan Negosiasi Teknis dan Biaya',
                'Penandatanganan Kontrak',
                'Tender Sudah Selesai'
            ])->whereIn('status_tender', ['Aktif', 'Tender Sedang Berjalan'])
                ->where(function($query) {
                    $query->doesntHave('peserta')
                          ->orDoesntHave('pemenang');
                })
                ->select('kode_tender')
                ->chunkById(self::CHUNK_SIZE, function ($tenders) use (&$kodeTendersFromNpwp) {
                    $kodeTendersFromNpwp = $kodeTendersFromNpwp->merge($tenders->pluck('kode_tender'));
                });
        }

        // Step 3: Proses dalam batch dengan cursor untuk hemat memory
        $uniqueKodeTenders = $kodeTendersFromNpwp->unique()->values()->toArray();
        shuffle($uniqueKodeTenders);

        $this->info("Total tender yang akan diproses: " . count($uniqueKodeTenders));

        // Process in chunks of CHUNK_SIZE
        foreach (array_chunk($uniqueKodeTenders, self::CHUNK_SIZE) as $index => $chunk) {
            $this->info("Memproses chunk ke-" . ($index + 1) . " (" . count($chunk) . " tender)");

            foreach ($chunk as $kodeTender) {
                $this->newLine();
                $tender = TenderLpse::with('lpse')->where('kode_tender', $kodeTender)->first();
                if ($tender) {
                    $this->info("Memproses tender: {$tender->kode_tender}");
                    $this->processTender($tender);
                    usleep(2000000); // Delay 2 detik antara setiap permintaan
                }
            }

            gc_collect_cycles();
        }
    }

    private function processTender($tender)
    {
        if (!$tender->lpse) {
            $this->warn("LPSE tidak ditemukan untuk tender: {$tender->kode_tender}");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                100,
                100,
                "LPSE tidak ditemukan untuk tender: {$tender->kode_tender}",
                [
                    'kode_tender' => $tender->kode_tender,
                    'stage' => 'lpse_not_found',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            return;
        }

        try {
            // Proses peserta
            $urlPeserta = $tender->lpse->url . '/lelang/' . $tender->kode_tender . '/peserta';
            $this->info("Mengakses URL peserta: $urlPeserta");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                30,
                100,
                "Mengakses URL peserta: $urlPeserta",
                [
                    'kode_tender' => $tender->kode_tender,
                    'url_peserta' => $urlPeserta,
                    'stage' => 'accessing_peserta_url',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $dataPeserta = $this->scrapeWithRetry($urlPeserta, 3);

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                40,
                100,
                "Data peserta berhasil diambil: " . count($dataPeserta) . " peserta",
                [
                    'kode_tender' => $tender->kode_tender,
                    'peserta_count' => count($dataPeserta),
                    'stage' => 'peserta_data_fetched',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            if (!empty($dataPeserta)) {
                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    50,
                    100,
                    "Menyinkronkan data peserta tender",
                    [
                        'kode_tender' => $tender->kode_tender,
                        'peserta_count' => count($dataPeserta),
                        'stage' => 'syncing_peserta',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $this->syncPesertaTender($tender, $dataPeserta);
            }

            // Proses pemenang
            $urlPemenang = $tender->lpse->url . '/evaluasi/' . $tender->kode_tender . '/pemenang';
            $this->info("Mengakses URL pemenang: $urlPemenang");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                70,
                100,
                "Mengakses URL pemenang: $urlPemenang",
                [
                    'kode_tender' => $tender->kode_tender,
                    'url_pemenang' => $urlPemenang,
                    'stage' => 'accessing_pemenang_url',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            $dataPemenang = $this->scrapeWithRetry($urlPemenang, 3, true);

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                80,
                100,
                "Data pemenang berhasil diambil: " . count($dataPemenang) . " pemenang",
                [
                    'kode_tender' => $tender->kode_tender,
                    'pemenang_count' => count($dataPemenang),
                    'stage' => 'pemenang_data_fetched',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );

            if (!empty($dataPemenang)) {
                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    90,
                    100,
                    "Menyinkronkan data pemenang tender",
                    [
                        'kode_tender' => $tender->kode_tender,
                        'pemenang_count' => count($dataPemenang),
                        'stage' => 'syncing_pemenang',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $this->syncPemenangTender($tender, $dataPemenang);
            }

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                95,
                100,
                "Selesai memproses tender: {$tender->kode_tender}",
                [
                    'kode_tender' => $tender->kode_tender,
                    'stage' => 'tender_processing_complete',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );
        } catch (\Exception $e) {
            $this->error("Error processing tender {$tender->kode_tender}: " . $e->getMessage());

            // Log system error
            $this->logSystemError($e, "processTender for tender: {$tender->kode_tender}");

            // Update progress
            CommandProgress::updateProgress(
                $this->getName(),
                100,
                100,
                "Error memproses tender: {$tender->kode_tender}",
                [
                    'kode_tender' => $tender->kode_tender,
                    'error' => $e->getMessage(),
                    'stage' => 'tender_processing_error',
                    'memory_usage' => $this->getMemoryUsage()
                ]
            );
        }
    }

    private function syncPemenangTender($tender, $data)
    {
        foreach ($data as $pemenang) {
            $perusahaanId = $this->getOrCreatePerusahaan($pemenang);
            if (!$perusahaanId) {
                continue;
            }

            PemenangLpse::updateOrCreate(
                ['kode_tender' => $tender->kode_tender, 'id_peserta' => $perusahaanId],
                ['harga_penawaran' => $pemenang['harga_penawaran'], 'harga_terkoreksi' => $pemenang['harga_terkoreksi']]
            );
            $this->info("Pemenang diperbarui: {$pemenang['Nama Perusahaan']}");
        }
    }

    private function syncPesertaTender($tender, $pesertaData)
    {
        $existingPeserta = PesertaTender::where('kode_tender', $tender->kode_tender)
            ->join('list_perusahaan_tender', 'peserta_tender.id_peserta', '=', 'list_perusahaan_tender.id')
            ->pluck('list_perusahaan_tender.pembeda')
            ->toArray();

        // Konversi existingPeserta menjadi lowercase agar perbandingan tidak case-sensitive
        $existingPeserta = array_map('strtolower', $existingPeserta);

        if (empty($pesertaData)) {
            $this->warn("Tidak ada data peserta yang ditemukan.");
            return;
        } else {
            $this->info("Total peserta yang ditemukan: " . count($pesertaData));

            // Hapus semua peserta yang ada untuk tender ini
            $deletedCount = PesertaTender::where('kode_tender', $tender->kode_tender)->delete();
            $this->info("Menghapus {$deletedCount} peserta yang ada untuk tender {$tender->kode_tender}");
        }

        foreach ($pesertaData as $index => $data) {
            $normalizedScrapedName = strtolower(str_replace(' ', '', ListPerusahaanTender::normalizeName($data['Nama Perusahaan'])));
            $npwpScrapePartial = ListPerusahaanTender::extractPartialNpwp($data['NPWP'] ?? '');
            $pembedaScraped = $normalizedScrapedName . $npwpScrapePartial;

            // if (! in_array($pembedaScraped, $existingPeserta)) {
            $perusahaanId = $this->getOrCreatePerusahaan($data);

            $exists = PesertaTender::where('kode_tender', $tender->kode_tender)
                ->where('id_peserta', $perusahaanId)
                ->exists();
            $no = $index + 1;
            if (!$exists) {
                try {
                    $peserta = PesertaTender::create([
                        'kode_tender' => $tender->kode_tender,
                        'id_peserta' => $perusahaanId,
                        'status' => 'Peserta',
                    ]);

                    $this->info("Peserta baru ditambahkan: {$no} - {$data['Nama Perusahaan']}");
                } catch (QueryException $e) {
                    $this->error("Gagal menambahkan peserta: " . $e->getMessage());
                }
            } else {
                $this->info("Peserta sudah ada: {$no} - {$data['Nama Perusahaan']}");
            }

            $newPeserta[] = $pembedaScraped;
            // }
        }

        // Normalisasi semua pembeda dari hasil scraping sebelum membandingkan
        $scrapedPembeda = array_map(
            fn($data) => strtolower(str_replace(' ', '', ListPerusahaanTender::normalizeName($data['Nama Perusahaan']))) .
            ListPerusahaanTender::extractPartialNpwp($data['NPWP'] ?? ''),
            $pesertaData
        );

        // Cari peserta yang ada di database tetapi tidak ada di hasil scraping
        $pesertaYangDihapus = array_diff($existingPeserta, $scrapedPembeda);

        // Hapus peserta yang sudah tidak ada dalam hasil scraping
        // PesertaTender::where('kode_tender', $tender->kode_tender)
        //     ->whereHas('perusahaan', function ($query) use ($pesertaYangDihapus) {
        //         $query->whereIn('pembeda', $pesertaYangDihapus);
        //     })
        //     ->delete();

        $this->info("Peserta yang dihapus: " . implode(', ', $pesertaYangDihapus));
    }

    private function getOrCreatePerusahaan($perusahaanData)
    {
        try {
            // Gunakan metode createOrUpdatePerusahaan yang baru
            $perusahaan = ListPerusahaanTender::createOrUpdatePerusahaan(
                $perusahaanData['Nama Perusahaan'],
                $perusahaanData['NPWP'],
                $perusahaanData['Alamat'] ?? null
            );

            // Log informasi
            if ($perusahaan->wasRecentlyCreated) {
                $this->info("Created new perusahaan: {$perusahaan->nama_peserta}");
            } else if ($perusahaan->wasChanged()) {
                $this->info("Updated perusahaan: {$perusahaan->nama_peserta}");

                // Log perubahan spesifik
                $changes = $perusahaan->getChanges();
                foreach ($changes as $field => $value) {
                    $this->info("  - Updated {$field}: {$value}");
                }
            } else {
                $this->info("Found existing perusahaan: {$perusahaan->nama_peserta}");
            }

            return $perusahaan->id;
        } catch (\Exception $e) {
            $this->error("Error processing perusahaan {$perusahaanData['Nama Perusahaan']}: " . $e->getMessage());

            // Log system error
            $this->logSystemError($e, "getOrCreatePerusahaan for {$perusahaanData['Nama Perusahaan']}");

            // Fallback ke metode lama jika terjadi error
            try {
                $normalizedName = ListPerusahaanTender::normalizeName($perusahaanData['Nama Perusahaan']);
                $npwpInput = $perusahaanData['NPWP'];
                $npwpData = ListPerusahaanTender::processNpwp($npwpInput);

                $perusahaan = ListPerusahaanTender::create([
                    'npwp' => $npwpData['npwp'],
                    'npwp_blur' => $npwpData['npwp_blur'],
                    'nama_peserta' => $perusahaanData['Nama Perusahaan'],
                    'normalized_name' => $normalizedName,
                    'partial_npwp' => $npwpData['partial_npwp'],
                    'pembeda' => str_replace(' ', '', $normalizedName) . $npwpData['partial_npwp'] . '_' . substr(md5(uniqid()), 0, 8),
                    'alamat' => $perusahaanData['Alamat'] ?? null,
                ]);

                $this->info("Created new perusahaan (fallback): {$perusahaan->nama_peserta}");
                return $perusahaan->id;
            } catch (\Exception $fallbackError) {
                $this->error("Fallback creation also failed: " . $fallbackError->getMessage());
                $this->logSystemError($fallbackError, "getOrCreatePerusahaan fallback for {$perusahaanData['Nama Perusahaan']}");
                throw $fallbackError;
            }
        }
    }

    /**
     * Fetch URL with exponential backoff retry
     *
     * @param string $url
     * @param int $maxRetries
     * @return string|false
     */
    private function getHtmlContentFromUrl($url, $maxRetries = 5)
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $maxRetries) {
            try {
                $attempt++;

                // Exponential backoff: 2^attempt * 1000ms with some jitter
                if ($attempt > 1) {
                    $backoff = (2 ** ($attempt - 1)) * 1000 + rand(100, 1000);
                    $backoffSeconds = round($backoff / 1000, 2);
                    $this->warn("Retry attempt {$attempt}/{$maxRetries} after {$backoffSeconds}s backoff...");
                    usleep($backoff * 1000); // Convert to microseconds
                }

                // Use different request options for each retry
                $options = [];
                if ($attempt > 1) {
                    // Add different user agent for retries
                    $userAgents = [
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
                        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36'
                    ];
                    $options['headers'] = [
                        'User-Agent' => $userAgents[array_rand($userAgents)],
                        'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language' => 'en-US,en;q=0.5',
                        'Cache-Control' => 'no-cache',
                        'Pragma' => 'no-cache'
                    ];

                    // Add random timeout
                    $options['timeout'] = rand(30, 60);
                }

                $response = $this->client->get($url, $options);
                $html = $response->getBody()->getContents();

                // Check if redirected to login page
                if (strpos($html, 'login') !== false && strpos($html, 'password') !== false) {
                    if ($attempt < $maxRetries) {
                        $this->warn("Redirected to login page. Will retry...");
                        $lastException = new \Exception("Redirected to login page");
                        continue;
                    } else {
                        throw new \Exception("Redirected to login page after {$maxRetries} attempts");
                    }
                }

                // Check for empty or invalid response
                if (empty($html) || !preg_match('/<[^>]*>/', $html)) {
                    if ($attempt < $maxRetries) {
                        $this->warn("Empty or invalid HTML response. Will retry...");
                        $lastException = new \Exception("Empty or invalid HTML response");
                        continue;
                    } else {
                        throw new \Exception("Empty or invalid HTML response after {$maxRetries} attempts");
                    }
                }

                // Mengembalikan isi HTML
                return $html;

            } catch (\GuzzleHttp\Exception\ConnectException $e) {
                $lastException = $e;
                $this->warn("Connection error on attempt {$attempt}/{$maxRetries}: " . $e->getMessage());

                // If this is the last attempt, log and return false
                if ($attempt >= $maxRetries) {
                    $this->error("Failed to connect after {$maxRetries} attempts: " . $e->getMessage());
                    $this->logSystemError($e, "getHtmlContentFromUrl - Failed to connect to URL: {$url} after {$maxRetries} attempts");
                    return false;
                }
            } catch (\Exception $e) {
                $lastException = $e;
                $this->warn("Error on attempt {$attempt}/{$maxRetries}: " . $e->getMessage());

                // If this is the last attempt, log and return false
                if ($attempt >= $maxRetries) {
                    $this->error("Failed after {$maxRetries} attempts: " . $e->getMessage());
                    $this->logSystemError($e, "getHtmlContentFromUrl - Error fetching URL: {$url} after {$maxRetries} attempts");
                    return false;
                }
            }
        }

        // If we get here, all retries failed
        $this->error("All retry attempts failed for URL: {$url}");
        $this->logSystemError($lastException ?: new \Exception("Failed after {$maxRetries} attempts"),
            "getHtmlContentFromUrl - All retry attempts failed for URL: {$url}");
        return false;
    }

    private function str_rupiah_to_decimal($value)
    {
        return (float) str_replace(["Rp", ".", " ", ","], ["", "", "", "."], trim($value));
    }

    private function scrapeWithRetry($url, $maxRetries = 3, $isPemenang = false)
    {
        // Update progress
        CommandProgress::updateProgress(
            $this->getName(),
            0,
            100,
            "Memulai scraping URL: $url",
            [
                'url' => $url,
                'is_pemenang' => $isPemenang,
                'max_retries' => $maxRetries,
                'stage' => 'scraping_start',
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        for ($i = 0; $i < $maxRetries; $i++) {
            try {
                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    ($i + 1) * 20,
                    100,
                    "Percobaan ke-" . ($i + 1) . " scraping URL: $url",
                    [
                        'url' => $url,
                        'is_pemenang' => $isPemenang,
                        'attempt' => $i + 1,
                        'max_retries' => $maxRetries,
                        'stage' => 'scraping_attempt',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                $html = $this->getHtmlContentFromUrl($url);
                if ($html !== false) {
                    try {
                        $crawler = new Crawler($html);

                        if ($isPemenang) {
                            $data = [];

                            // Periksa apakah tabel pemenang ada
                            if ($crawler->filter('table.table-sm')->count() > 1) {
                                try {
                                    $crawler->filter('table.table-sm')->eq(1)->filter('tr')->each(function ($node) use (&$data) {
                                        if ($node->filter('td')->count() > 5) {
                                            $data[] = [
                                                'Nama Perusahaan' => $node->filter('td')->eq(0)->text(),
                                                'Alamat' => $node->filter('td')->eq(1)->text(),
                                                'NPWP' => $node->filter('td')->eq(2)->text(),
                                                'harga_penawaran' => $this->str_rupiah_to_decimal($node->filter('td')->eq(3)->text()),
                                                'harga_terkoreksi' => $this->str_rupiah_to_decimal($node->filter('td')->eq(4)->text()),
                                            ];
                                        }
                                    });
                                } catch (\InvalidArgumentException $e) {
                                    // Tangani kasus "The current node list is empty"
                                    if (stripos($e->getMessage(), 'current node list is empty') !== false) {
                                        $this->warn("Tabel pemenang kosong atau tidak memiliki format yang diharapkan");
                                    } else {
                                        throw $e; // Rethrow jika bukan error yang diharapkan
                                    }
                                }
                            } else {
                                // Cek apakah ada pesan bahwa belum ada pemenang
                                if ($crawler->filter('div.content-wrap')->count() > 0) {
                                    $pageContent = $crawler->filter('div.content-wrap')->text();
                                    if (stripos($pageContent, 'belum ada pemenang') !== false ||
                                        stripos($pageContent, 'tidak ada pemenang') !== false) {
                                        $this->info("Belum ada data pemenang untuk tender ini");
                                    } else {
                                        $this->warn("Tabel pemenang tidak ditemukan di halaman");
                                    }
                                } else {
                                    $this->warn("Struktur halaman pemenang tidak sesuai yang diharapkan");
                                }
                            }
                        } else {
                            // Periksa apakah tabel peserta ada
                            if ($crawler->filter('table.table tbody tr')->count() > 0) {
                                $data = $crawler->filter('table.table tbody tr')->each(function ($node) {
                                    // Periksa apakah node memiliki cukup kolom
                                    if ($node->filter('td')->count() >= 3) {
                                        return [
                                            'Nama Perusahaan' => trim($node->filter('td')->eq(1)->text()),
                                            'NPWP' => trim($node->filter('td')->eq(2)->text()),
                                        ];
                                    }
                                    return null;
                                });

                                // Filter out null values
                                $data = array_filter($data);
                            } else {
                                // Jika tabel tidak ditemukan, cek apakah ada pesan "Belum ada peserta"
                                if ($crawler->filter('div.content-wrap')->count() > 0 &&
                                    stripos($crawler->filter('div.content-wrap')->text(), 'belum ada peserta') !== false) {
                                    $this->info("Belum ada data peserta untuk tender ini");
                                } else {
                                    // Cek apakah halaman menunjukkan tender tidak ditemukan atau error lain
                                    $this->warn("Tabel peserta tidak ditemukan di halaman. Kemungkinan tender belum memiliki data peserta.");
                                }
                                $data = [];
                            }
                        }

                        if (!empty($data)) {
                            // Update progress
                            CommandProgress::updateProgress(
                                $this->getName(),
                                100,
                                100,
                                "Berhasil scraping URL: $url, " . count($data) . " data ditemukan",
                                [
                                    'url' => $url,
                                    'is_pemenang' => $isPemenang,
                                    'data_count' => count($data),
                                    'stage' => 'scraping_success',
                                    'memory_usage' => $this->getMemoryUsage()
                                ]
                            );

                            return $data;
                        } else {
                            $this->warn("Data tidak ditemukan di URL: $url");

                            // Update progress
                            CommandProgress::updateProgress(
                                $this->getName(),
                                100,
                                100,
                                "Data tidak ditemukan di URL: $url",
                                [
                                    'url' => $url,
                                    'is_pemenang' => $isPemenang,
                                    'stage' => 'no_data_found',
                                    'memory_usage' => $this->getMemoryUsage()
                                ]
                            );

                            return [];
                        }
                    } catch (\InvalidArgumentException $e) {
                        // Tangani kasus "The current node list is empty" secara khusus
                        if (stripos($e->getMessage(), 'current node list is empty') !== false) {
                            $this->warn("Tabel data kosong atau tidak memiliki format yang diharapkan di URL: $url");

                            // Update progress
                            CommandProgress::updateProgress(
                                $this->getName(),
                                ($i + 1) * 20,
                                100,
                                "Tabel data kosong di URL: $url",
                                [
                                    'url' => $url,
                                    'is_pemenang' => $isPemenang,
                                    'attempt' => $i + 1,
                                    'error' => $e->getMessage(),
                                    'stage' => 'empty_node_list',
                                    'memory_usage' => $this->getMemoryUsage()
                                ]
                            );

                            // Kembalikan array kosong karena memang tidak ada data
                            return [];
                        } else {
                            throw $e; // Rethrow jika bukan error yang diharapkan
                        }
                    } catch (\Exception $e) {
                        $this->error("Error saat scraping URL: $url - " . $e->getMessage());

                        // Log system error
                        $this->logSystemError($e, "scrapeWithRetry - Error saat scraping URL: $url");

                        // Update progress
                        CommandProgress::updateProgress(
                            $this->getName(),
                            ($i + 1) * 20,
                            100,
                            "Error saat scraping URL: $url",
                            [
                                'url' => $url,
                                'is_pemenang' => $isPemenang,
                                'attempt' => $i + 1,
                                'error' => $e->getMessage(),
                                'stage' => 'scraping_error',
                                'memory_usage' => $this->getMemoryUsage()
                            ]
                        );
                    }
                }
            } catch (\GuzzleHttp\Exception\ConnectException $e) {
                $this->warn("Connection error while accessing URL: $url - " . $e->getMessage());

                // Log system error
                $this->logSystemError($e, "scrapeWithRetry - Connection error while accessing URL: $url");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    ($i + 1) * 20,
                    100,
                    "Connection error while accessing URL: $url",
                    [
                        'url' => $url,
                        'is_pemenang' => $isPemenang,
                        'attempt' => $i + 1,
                        'error' => $e->getMessage(),
                        'stage' => 'connection_error',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                return [];
            } catch (\Exception $e) {
                $this->warn("General error while accessing URL: $url - " . $e->getMessage());

                // Log system error
                $this->logSystemError($e, "scrapeWithRetry - General error while accessing URL: $url");

                // Update progress
                CommandProgress::updateProgress(
                    $this->getName(),
                    ($i + 1) * 20,
                    100,
                    "General error while accessing URL: $url",
                    [
                        'url' => $url,
                        'is_pemenang' => $isPemenang,
                        'attempt' => $i + 1,
                        'error' => $e->getMessage(),
                        'stage' => 'general_error',
                        'memory_usage' => $this->getMemoryUsage()
                    ]
                );

                return [];
            }
            $this->warn("Percobaan ke-" . ($i + 1) . " gagal, mencoba ulang...");
            sleep(1);
        }

        $this->error("Gagal mengambil data dari URL setelah $maxRetries percobaan.");

        // Update progress
        CommandProgress::updateProgress(
            $this->getName(),
            100,
            100,
            "Gagal mengambil data dari URL setelah $maxRetries percobaan",
            [
                'url' => $url,
                'is_pemenang' => $isPemenang,
                'max_retries' => $maxRetries,
                'stage' => 'max_retries_reached',
                'memory_usage' => $this->getMemoryUsage()
            ]
        );

        return [];
    }

}
