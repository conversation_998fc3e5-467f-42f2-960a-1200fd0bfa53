<?php

namespace App\Console\Commands;

use App\Models\TenderLpse;
use App\Models\InstansiSatker;
use App\Models\Anggaran;
use App\Models\LokasiPaket;
use App\Models\Lpse;
use App\Models\CommandProgress;
use App\Jobs\SendLpseMetricsNotifications;
use App\Crawler\CustomClientSquid;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;
use Symfony\Component\Console\Command\Command as SymfonyCommand;
use App\Traits\CommandManagementTrait;
use App\Models\SystemError;

class ScrapeLpseTender extends Command
{
    use CommandManagementTrait;

    protected $signature = 'fetch:tender-via-api {tahun?} {--timeout=3600 : Maximum runtime in seconds before command is considered stuck} {--force : Force run even if command is already running} {--lock-name= : Custom lock name for this command instance}';
    protected $description = 'Fetch tender data via API for all LPSE codes. Uses current year if no year is specified.';

    private $logger;
    private $client;

    public function __construct()
    {
        parent::__construct();
        $this->logger = Log::channel('scrapeTender');
        $this->client = new CustomClientSquid([
            'timeout' => 60.0,
        ]);
    }

    public function handle()
    {
        // Initialize command management
        $timeout = $this->option('timeout') ? (int)$this->option('timeout') : 3600;
        $force = $this->option('force');

        if (!$this->initCommandManagement($timeout, $force)) {
            return SymfonyCommand::FAILURE;
        }

        try {
            $startTime = microtime(true); // Start measuring time

            $tahun = $this->argument('tahun') ?? Carbon::now()->year;
            $this->logger->info('Mulai scraping data LPSE.');
            $this->info('Mulai scraping data LPSE.');
            $this->info("Mulai Scraping Tender Umum Publik Tahun {$tahun}");
            $this->logger->info("Scraping LPSE tender data for year: {$tahun}");

            // Initialize command progress
            CommandProgress::updateProgress(
                'fetch:tender-via-api',
                0,
                100,
                "Memulai scraping data LPSE untuk tahun {$tahun}..."
            );

            $lpseList = Lpse::all();
            $totalLpse = $lpseList->count();
            $this->info("Total LPSE yang akan diproses: {$totalLpse}");

            // Update progress with total LPSE
            CommandProgress::updateProgress(
                'fetch:tender-via-api',
                0,
                $totalLpse,
                "Akan memproses {$totalLpse} LPSE untuk tahun {$tahun}",
                [
                    'tahun' => $tahun,
                    'total_lpse' => $totalLpse
                ]
            );

            $lpseMetrics = [];
            $processedLpse = 0;

            $lpseList->chunk(10)->each(function ($chunk) use ($tahun, &$lpseMetrics, &$processedLpse, $totalLpse) {
                foreach ($chunk as $lpse) {
                    $processedLpse++;

                    // Update progress for each LPSE
                    CommandProgress::updateProgress(
                        'fetch:tender-via-api',
                        $processedLpse,
                        $totalLpse,
                        "Memproses LPSE {$processedLpse} dari {$totalLpse}: {$lpse->nama_lpse}",
                        [
                            'tahun' => $tahun,
                            'current_lpse' => $lpse->nama_lpse,
                            'kd_lpse' => $lpse->kd_lpse,
                            'progress_percentage' => round(($processedLpse / $totalLpse) * 100, 2)
                        ]
                    );

                    $metrics = $this->scrapeTender($tahun, $lpse->kd_lpse);
                    $lpseMetrics[$lpse->kd_lpse] = $metrics;

                    // Rate limiting delay of 2 seconds
                    $this->logger->info("Sleeping for 2 seconds to respect rate limiting.");
                    $this->info("Sleeping for 2 seconds to respect rate limiting.");
                    sleep(2);
                }
            });

            // Send metrics notifications
            $this->sendMetricsNotifications($lpseMetrics);

            $endTime = microtime(true); // End measuring time
            $executionTime = $endTime - $startTime;
            $message = "Scraping data LPSE untuk tahun {$tahun} selesai dalam " . round($executionTime, 2) . " detik.";

            $this->logger->info($message);
            $this->info($message);

            // Hitung total untuk semua metrik
            $totalNewTenders = array_sum(array_column($lpseMetrics, 'new_tenders'));
            $totalUpdatedTenders = array_sum(array_column($lpseMetrics, 'updated_tenders'));
            $totalUnchangedTenders = array_sum(array_column($lpseMetrics, 'unchanged_tenders'));
            $totalProcessedTenders = $totalNewTenders + $totalUpdatedTenders + $totalUnchangedTenders;

            // Hitung persentase
            $newPercentage = $totalProcessedTenders > 0 ? round(($totalNewTenders / $totalProcessedTenders) * 100, 2) : 0;
            $updatedPercentage = $totalProcessedTenders > 0 ? round(($totalUpdatedTenders / $totalProcessedTenders) * 100, 2) : 0;
            $unchangedPercentage = $totalProcessedTenders > 0 ? round(($totalUnchangedTenders / $totalProcessedTenders) * 100, 2) : 0;

            // Dapatkan LPSE dengan tender terbanyak
            $lpseWithMostTenders = null;
            $maxTenders = 0;
            foreach ($lpseMetrics as $kd_lpse => $metrics) {
                $totalTenders = $metrics['total_tenders'];
                if ($totalTenders > $maxTenders) {
                    $maxTenders = $totalTenders;
                    $lpseWithMostTenders = $kd_lpse;
                }
            }

            // Dapatkan nama LPSE dengan tender terbanyak
            $lpseInfo = null;
            if ($lpseWithMostTenders) {
                $lpseInfo = Lpse::where('kd_lpse', $lpseWithMostTenders)->first();
            }

            // Buat ringkasan detail
            $detailedSummary = "Ringkasan Proses Scraping LPSE Tahun {$tahun}:\n";
            $detailedSummary .= "- Total LPSE diproses: {$totalLpse}\n";
            $detailedSummary .= "- Total tender diproses: {$totalProcessedTenders}\n";
            $detailedSummary .= "- Tender baru: {$totalNewTenders} ({$newPercentage}%)\n";
            $detailedSummary .= "- Tender diperbarui: {$totalUpdatedTenders} ({$updatedPercentage}%)\n";
            $detailedSummary .= "- Tender tidak berubah: {$totalUnchangedTenders} ({$unchangedPercentage}%)\n";
            $detailedSummary .= "- Waktu eksekusi: " . round($executionTime, 2) . " detik\n";
            if ($lpseInfo) {
                $detailedSummary .= "- LPSE dengan tender terbanyak: {$lpseInfo->nama_lpse} ({$maxTenders} tender)";
            }

            $this->info($detailedSummary);
            $this->logger->info($detailedSummary);

            // Mark command as completed
            CommandProgress::markAsCompleted(
                'fetch:tender-via-api',
                $message,
                [
                    'tahun' => $tahun,
                    'total_lpse' => $totalLpse,
                    'execution_time' => round($executionTime, 2),
                    'total_new_tenders' => $totalNewTenders,
                    'total_updated_tenders' => $totalUpdatedTenders,
                    'total_unchanged_tenders' => $totalUnchangedTenders,
                    'total_processed_tenders' => $totalProcessedTenders,
                    'new_percentage' => $newPercentage,
                    'updated_percentage' => $updatedPercentage,
                    'unchanged_percentage' => $unchangedPercentage,
                    'lpse_with_most_tenders' => $lpseInfo ? [
                        'kd_lpse' => $lpseWithMostTenders,
                        'nama_lpse' => $lpseInfo->nama_lpse,
                        'total_tenders' => $maxTenders
                    ] : null,
                    'detailed_summary' => $detailedSummary,
                    'peak_memory_usage' => round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB',
                    'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . ' MB'
                ]
            );

            return SymfonyCommand::SUCCESS;

        } catch (Exception $e) {
            $errorMessage = "Terjadi kesalahan: " . $e->getMessage();
            $this->error($errorMessage);
            $this->logger->error($errorMessage);

            // Dapatkan informasi tambahan tentang error
            $file = $e->getFile();
            $line = $e->getLine();
            $code = $e->getCode();
            $trace = $e->getTraceAsString();

            // Log informasi error yang lebih detail
            $detailedError = "Error pada file: {$file}, baris: {$line}, kode: {$code}\n";
            $detailedError .= "Pesan: {$e->getMessage()}\n";
            $detailedError .= "Trace: {$trace}";

            $this->logger->error($detailedError);

            // Log to system errors
            $this->logSystemError($e, "handle method - main command execution");

            // Mark command as failed dengan informasi yang lebih detail
            CommandProgress::markAsFailed(
                'fetch:tender-via-api',
                $errorMessage,
                [
                    'error' => $e->getMessage(),
                    'file' => $file,
                    'line' => $line,
                    'code' => $code,
                    'trace' => $trace,
                    'memory_usage' => $this->getMemoryUsage(),
                    'peak_memory_usage' => $this->getPeakMemoryUsage(),
                    'execution_time' => isset($startTime) ? round(microtime(true) - $startTime, 2) . ' detik' : 'unknown'
                ]
            );

            return SymfonyCommand::FAILURE;
        }
    }

    private function scrapeTender(int $tahun, string $kd_lpse): array
    {
        $url = "https://isb.lkpp.go.id/isb-2/api/satudata/TenderUmumPublik/{$tahun}/{$kd_lpse}";

        $this->logger->info("Menggunakan CustomClientSquid untuk request ke {$url}");
        $this->info("Menggunakan CustomClientSquid untuk request ke {$url}");

        // Update command progress for current LPSE
        CommandProgress::updateProgress(
            'fetch:tender-via-api',
            0,
            100,
            "Mengambil data untuk LPSE kode: {$kd_lpse}, Tahun: {$tahun}",
            [
                'current_lpse' => $kd_lpse,
                'tahun' => $tahun,
                'url' => $url
            ]
        );

        $maxRetries = 3;
        $attempt = 0;
        $response = null;

        while ($attempt < $maxRetries) {
            try {
                $response = $this->client->get($url);
                $statusCode = $response->getStatusCode();

                if ($statusCode >= 200 && $statusCode < 300) {
                    break;
                } else {
                    $this->logger->warning("Attempt " . ($attempt + 1) . " failed for LPSE code: {$kd_lpse}, Year: {$tahun}. Response status: " . $statusCode);
                    $this->info("Attempt " . ($attempt + 1) . " failed for LPSE code: {$kd_lpse}, Year: {$tahun}. Retrying...");
                }
            } catch (Exception $e) {
                $this->logger->warning("Attempt " . ($attempt + 1) . " exception for LPSE code: {$kd_lpse}, Year: {$tahun}. Error: " . $e->getMessage());
                $this->info("Attempt " . ($attempt + 1) . " exception for LPSE code: {$kd_lpse}, Year: {$tahun}. Retrying...");

                // Log system error
                $this->logSystemError($e, "scrapeTender attempt " . ($attempt + 1) . " for LPSE code: {$kd_lpse}, Year: {$tahun}");
            }
            $attempt++;
            sleep(2);
        }

        $metrics = [
            'new_tenders' => 0,
            'updated_tenders' => 0,
            'unchanged_tenders' => 0,
            'total_tenders' => 0,
            'period' => $tahun,
        ];

        if (!$response || $response->getStatusCode() >= 300) {
            $this->logger->error("Failed to fetch data for LPSE code: {$kd_lpse}, Year: {$tahun} after {$maxRetries} attempts.");
            $this->info("Failed to fetch data for LPSE code: {$kd_lpse}, Year: {$tahun} after {$maxRetries} attempts.");
            $this->logger->info("=============================================================================================");
            $this->info("=============================================================================================");
            return $metrics;
        }

        $responseBody = $response->getBody()->getContents();
        $tenderDataList = json_decode($responseBody, true);

        if (!is_array($tenderDataList) || empty($tenderDataList)) {
            $this->logger->warning("No valid data returned for LPSE code: {$kd_lpse}, Year: {$tahun}");
            $this->info("No valid data returned for LPSE code: {$kd_lpse}, Year: {$tahun}");
            return $metrics;
        }

        $metrics['total_tenders'] = count($tenderDataList);

        $this->logger->info("Fetched data for LPSE code: {$kd_lpse}, Year: {$tahun}", [
            'count' => $metrics['total_tenders']
        ]);
        $this->info("Fetched data for LPSE code: {$kd_lpse}, Year: {$tahun}, count: " . $metrics['total_tenders']);

        // Update command progress with tender count
        CommandProgress::updateProgress(
            'fetch:tender-via-api',
            50,
            100,
            "Ditemukan {$metrics['total_tenders']} tender untuk LPSE kode: {$kd_lpse}, Tahun: {$tahun}",
            [
                'current_lpse' => $kd_lpse,
                'tahun' => $tahun,
                'total_tenders' => $metrics['total_tenders']
            ]
        );

        $newCount = 0;
        $updatedCount = 0;
        $unchangedCount = 0;

        DB::transaction(function () use ($tenderDataList, $kd_lpse, $tahun, &$newCount, &$updatedCount, &$unchangedCount) {
            foreach ($tenderDataList as $index => $tenderData) {
                if (!isset($tenderData['Kode Tender'])) {
                    $this->logger->warning("Invalid data structure for tender index {$index}, LPSE code: {$kd_lpse}, Year: {$tahun}. 'Kode Tender' is missing.");
                    $this->info("Invalid data structure for tender index {$index}, LPSE code: {$kd_lpse}, Year: {$tahun}. 'Kode Tender' is missing.");
                    continue;
                }

                $result = $this->processTender($tenderData, $kd_lpse, $index);
                if ($result === 'created') {
                    $newCount++;
                } elseif ($result === 'updated') {
                    $updatedCount++;
                } elseif ($result === 'unchanged') {
                    $unchangedCount++;
                }
            }
        });

        $metrics['new_tenders'] = $newCount;
        $metrics['updated_tenders'] = $updatedCount;
        $metrics['unchanged_tenders'] = $unchangedCount;

        // Hitung persentase untuk setiap kategori
        $totalProcessed = $newCount + $updatedCount + $unchangedCount;
        $newPercentage = $totalProcessed > 0 ? round(($newCount / $totalProcessed) * 100, 2) : 0;
        $updatedPercentage = $totalProcessed > 0 ? round(($updatedCount / $totalProcessed) * 100, 2) : 0;
        $unchangedPercentage = $totalProcessed > 0 ? round(($unchangedCount / $totalProcessed) * 100, 2) : 0;

        $this->logger->info("Processed " . $metrics['total_tenders'] . " tenders for LPSE code: {$kd_lpse}, Year: {$tahun}");
        $this->info("Processed " . $metrics['total_tenders'] . " tenders for LPSE code: {$kd_lpse}, Year: {$tahun}");
        $this->logger->info("LPSE code: {$kd_lpse} - New: {$newCount} ({$newPercentage}%), Updated: {$updatedCount} ({$updatedPercentage}%), Unchanged: {$unchangedCount} ({$unchangedPercentage}%)");
        $this->info("LPSE code: {$kd_lpse} - New: {$newCount} ({$newPercentage}%), Updated: {$updatedCount} ({$updatedPercentage}%), Unchanged: {$unchangedCount} ({$unchangedPercentage}%)");

        // Dapatkan nama LPSE
        $lpseInfo = Lpse::where('kd_lpse', $kd_lpse)->first();
        $lpseName = $lpseInfo ? $lpseInfo->nama_lpse : "LPSE {$kd_lpse}";

        // Update command progress with processing results
        CommandProgress::updateProgress(
            'fetch:tender-via-api',
            100,
            100,
            "Selesai memproses {$metrics['total_tenders']} tender untuk {$lpseName}, Tahun: {$tahun}",
            [
                'current_lpse' => $kd_lpse,
                'lpse_name' => $lpseName,
                'tahun' => $tahun,
                'total_tenders' => $metrics['total_tenders'],
                'new_tenders' => $newCount,
                'updated_tenders' => $updatedCount,
                'unchanged_tenders' => $unchangedCount,
                'new_percentage' => $newPercentage,
                'updated_percentage' => $updatedPercentage,
                'unchanged_percentage' => $unchangedPercentage,
                'processing_time' => round(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 2) . ' detik',
                'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . ' MB'
            ]
        );

        $this->logger->info("=============================================================================================");
        $this->info("=============================================================================================");

        return $metrics;
    }

    private function processTender(array $tenderData, string $kd_lpse, int $index): string
    {
        try {
            $newData = $this->mapTenderData($tenderData);

            $tender = TenderLpse::where('kode_tender', $newData['kode_tender'])->first();

            if ($tender) {
                $existingData = $tender->toArray();
                // Remove timestamps and other non-comparable fields if needed
                unset($existingData['created_at'], $existingData['updated_at']);

                // Compare existing data with new data and get changed fields
                $changedFields = $this->isDataDifferent($existingData, $newData);

                if (!empty($changedFields)) {
                    // Update command progress with detailed changes
                    CommandProgress::updateProgress(
                        'fetch:tender-via-api',
                        0,
                        100,
                        "Memperbarui tender {$newData['kode_tender']} - {$newData['nama_paket']}",
                        [
                            'kode_tender' => $newData['kode_tender'],
                            'nama_paket' => $newData['nama_paket'],
                            'kd_lpse' => $kd_lpse,
                            'action' => 'update',
                            'changed_fields' => $changedFields,
                            'total_changes' => count($changedFields)
                        ]
                    );

                    $tender->fill($newData);
                    $tender->save();
                    $this->logChanges('TenderLpse', false, true, array_merge($newData, ['changed_fields' => $changedFields]));
                    $result = 'updated';
                } else {
                    $result = 'unchanged';
                }
            } else {
                // Update command progress for new tender
                CommandProgress::updateProgress(
                    'fetch:tender-via-api',
                    0,
                    100,
                    "Menambahkan tender baru {$newData['kode_tender']} - {$newData['nama_paket']}",
                    [
                        'kode_tender' => $newData['kode_tender'],
                        'nama_paket' => $newData['nama_paket'],
                        'kd_lpse' => $kd_lpse,
                        'action' => 'create',
                        'data' => array_intersect_key($newData, array_flip([
                            'kode_tender', 'nama_paket', 'status_tender', 'pagu', 'hps',
                            'kategori_pekerjaan', 'metode_pemilihan', 'tanggal_paket_tayang'
                        ]))
                    ]
                );

                $tender = TenderLpse::create($newData);
                $this->logChanges('TenderLpse', true, false, $newData);
                $result = 'created';
            }

            $this->processAdditionalData($tenderData, $newData['kode_tender']);

            // Dispatch Sirup notification job for new tender
            if ($result === 'created') {
                $dataSirup = \App\Models\DataSirup::where('kode_rup', $tender->kode_tender)->first();
                if ($dataSirup) {
                    \App\Jobs\SendSirupNotifications::dispatch($dataSirup);

                    // Update command progress for notification
                    CommandProgress::updateProgress(
                        'fetch:tender-via-api',
                        0,
                        100,
                        "Mengirim notifikasi untuk tender baru {$newData['kode_tender']}",
                        [
                            'kode_tender' => $newData['kode_tender'],
                            'nama_paket' => $newData['nama_paket'],
                            'kd_lpse' => $kd_lpse,
                            'action' => 'notification',
                            'notification_type' => 'sirup'
                        ]
                    );
                } else {
                    $this->logger->info("DataSirup not found for kode_rup: {$tender->kode_tender}, skipping notification.");
                    $this->info("DataSirup not found for kode_rup: {$tender->kode_tender}, skipping notification.");
                }
            }

            return $result;
        } catch (Exception $e) {
            $this->logger->error("Error processing tender index {$index} for LPSE code: {$kd_lpse} Kode Tender = {$newData['kode_tender']}. Error: " . $e->getMessage());

            // Log system error
            $this->logSystemError($e, "processTender for index {$index}, LPSE code: {$kd_lpse}, Kode Tender: {$newData['kode_tender']}");

            // Update command progress for error
            CommandProgress::updateProgress(
                'fetch:tender-via-api',
                0,
                100,
                "Error memproses tender index {$index} untuk LPSE kode: {$kd_lpse}",
                [
                    'kode_tender' => $newData['kode_tender'] ?? 'unknown',
                    'kd_lpse' => $kd_lpse,
                    'action' => 'error',
                    'error' => $e->getMessage(),
                    'index' => $index
                ]
            );

            return 'error';
        }
    }

    private function isDataDifferent(array $existingData, array $newData): array
    {
        // Compare only keys that exist in both arrays
        $keysToCompare = array_intersect(array_keys($newData), array_keys($existingData));
        $changedFields = [];

        foreach ($keysToCompare as $key) {
            $existingValue = is_string($existingData[$key]) ? trim($existingData[$key]) : $existingData[$key];
            $newValue = is_string($newData[$key]) ? trim($newData[$key]) : $newData[$key];

            // Untuk field numerik, konversi ke float untuk perbandingan yang lebih akurat
            if (in_array($key, ['pagu', 'hps']) || is_numeric($existingValue) || is_numeric($newValue)) {
                // Jika salah satu nilai adalah string numerik, konversi keduanya ke float
                if ((is_string($existingValue) && is_numeric($existingValue)) ||
                    (is_string($newValue) && is_numeric($newValue))) {
                    $existingValueFloat = (float) $existingValue;
                    $newValueFloat = (float) $newValue;

                    // Untuk nilai besar, gunakan toleransi relatif
                    $maxValue = max(abs($existingValueFloat), abs($newValueFloat));
                    $tolerance = $maxValue * 0.0001; // Toleransi 0.01% dari nilai maksimum

                    // Bandingkan nilai float dengan toleransi yang sesuai untuk menghindari masalah presisi floating point
                    if (abs($existingValueFloat - $newValueFloat) < max(0.1, $tolerance)) {
                        // Nilai dianggap sama, lanjutkan ke field berikutnya
                        $this->logger->info("Ignoring small difference in {$key}: {$existingValue} vs {$newValue}");
                        continue;
                    }
                }
            }

            // Jika nilai berbeda setelah normalisasi, catat perubahan
            if ($existingValue !== $newValue) {
                $changedFields[$key] = [
                    'old' => $existingValue,
                    'new' => $newValue
                ];
            }
        }

        return $changedFields;
    }


    private function mapTenderData(array $tenderData): array
    {
        if($tenderData['Status_Tender'] === 'Tender Sedang Berjalan') {
            $tenderData['Status_Tender'] = 'Aktif';
        }

        if($tenderData['Status_Tender'] === 'Tender Ditutup') {
            $tenderData['Status_Tender'] = 'Ditutup';

        }

        return [
            'kode_tender' => (string) $tenderData['Kode Tender'],
            'kd_lpse' => $tenderData['Repo id LPSE'] ?? null,
            'status_tender' => $tenderData['Status_Tender'] ?? null,
            'nama_paket' => $tenderData['Nama Paket'] ?? null,
            'pagu' => $tenderData['Pagu'],
            'hps' => $this->formatDecimal($tenderData['HPS']),
            'tanggal_paket_dibuat' => $tenderData['tanggal paket dibuat'] ?? null,
            'tanggal_paket_tayang' => $tenderData['tanggal paket tayang'] ?? null,
            'kategori_pekerjaan' => $tenderData['Kategori Pekerjaan'] ?? null,
            'metode_pemilihan' => $tenderData['Metode Pemilihan'] ?? null,
            'metode_pengadaan' => $tenderData['Metode Pengadaan'] ?? null,
            'metode_evaluasi' => $tenderData['Metode Evaluasi'] ?? null,
            'cara_pembayaran' => $tenderData['Cara Pembayaran'] ?? null,
            'jenis_penetapan_pemenang' => $tenderData['Jenis Penetapan Pemenang'] ?? null,
            'apakah_paket_konsolidasi' => $tenderData['Apakah paket konsolidasi'] ?? null,
            'jumlah_pendaftar' => $tenderData['Jumlah Pendaftar'] ?? null,
            'jumlah_penawar' => $tenderData['Jumlah Penawar'] ?? null,
            'jumlah_kirim_kualifikasi' => $tenderData['jumlah_kirim_kualifikasi'] ?? null,
            'durasi_tender' => $tenderData['Durasi Tender'] ?? null,
            'versi_paket_spse' => $tenderData['Versi_spse_paket'] ?? null,
        ];
    }

    private function processAdditionalData(array $tenderData, string $kode_tender): void
    {
        if (isset($tenderData['Instansi dan Satker'])) {
            $this->processInstansiSatker($tenderData['Instansi dan Satker'], $kode_tender);
        }

        if (isset($tenderData['anggaran'])) {
            $this->processAnggaran($tenderData['anggaran'], $kode_tender);
        }

        if (isset($tenderData['lokasi_paket'])) {
            $this->processLokasiPaket($tenderData['lokasi_paket'], $kode_tender);
        }
    }

    private function processInstansiSatker(array $instansiSatkerData, string $kode_tender): void
    {
        foreach ($instansiSatkerData as $data) {
            InstansiSatker::updateOrCreate(
                [
                    'rup_id' => $data['rup_id'] ?? 0,
                    'kode_tender' => $kode_tender
                ],
                [
                    'nama_satker' => $this->mapNullToZero($data['stk_nama'], true),
                    'nama_instansi' => $this->mapNullToZero($data['nama_instansi'], true),
                    'jenis_instansi' => $this->mapNullToZero($data['jenis_instansi'], true)
                ]
            );
        }
    }

    private function processAnggaran(array $anggaranData, string $kode_tender): void
    {
        foreach ($anggaranData as $data) {
            Anggaran::updateOrCreate(
                [
                    'rup_id' => $data['rup_id'] ?? 0,
                    'kode_tender' => $kode_tender,
                    'ang_tahun' => $this->mapNullToZero($data['ang_tahun']),
                    'ang_nilai' => $this->mapNullToZero($data['ang_nilai']),
                    'sbd_id' => $this->mapNullToZero($data['sbd_id']),
                    'ang_koderekening' => $this->mapNullToZero($data['ang_koderekening'], true)
                ],
                [
                    // Add any additional fields that need to be updated here
                ]
            );
        }
    }


    private function processLokasiPaket(array $lokasiPaketData, string $kode_tender): void
    {
        foreach ($lokasiPaketData as $data) {
            LokasiPaket::updateOrCreate(
                [
                    'kode_tender' => $kode_tender,
                    'kbp_nama' => $this->mapNullToZero($data['lokasi']['kbp_nama'] ?? '', true),
                    'prp_nama' => $this->mapNullToZero($data['lokasi']['prp_nama'] ?? '', true),
                    'pkt_lokasi' => $this->mapNullToZero($data['lokasi']['pkt_lokasi'] ?? '', true),
                ]
            );
        }
    }
    private function formatDecimal($value): ?float
    {
        if (is_numeric($value)) {
            return (float) number_format((float) $value, 2, '.', '');
        }
        return null;
    }

    private function logChanges(string $modelName, bool $created, bool $updated, array $changes): void
    {
        if ($created) {
            $this->logger->info("Created new {$modelName}", $changes);
        } elseif ($updated) {
            $this->logger->info("Updated existing {$modelName}", $changes);
        }
    }

    private function mapNullToZero($value, $isString = false)
    {
        if ($value === null) {
            return $isString ? '' : 0;
        }
        return $value;
    }

    /**
     * Cleanup function called on shutdown
     */
    public function cleanup()
    {
        if ($this->isCommandRunning()) {
            $this->markCommandAsNotRunning();
            $this->info("Command cleanup executed, marked as not running.");

            // Dapatkan informasi tentang error yang mungkin terjadi
            $error = error_get_last();
            $errorInfo = [];
            $errorMessage = "Command terminated unexpectedly";

            if ($error !== null) {
                $errorInfo = [
                    'type' => $this->getErrorTypeName($error['type']),
                    'message' => $error['message'],
                    'file' => $error['file'],
                    'line' => $error['line']
                ];

                $errorMessage = "Command terminated unexpectedly: {$errorInfo['type']} - {$errorInfo['message']} in {$errorInfo['file']} on line {$errorInfo['line']}";
            } else {
                $errorMessage = "Command terminated unexpectedly without error information";
            }

            // Update command progress dengan informasi yang lebih detail
            CommandProgress::markAsFailed(
                'fetch:tender-via-api',
                $errorMessage,
                [
                    'terminated_at' => now()->format('Y-m-d H:i:s'),
                    'error_info' => $errorInfo,
                    'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . ' MB',
                    'peak_memory_usage' => round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB'
                ]
            );
        }
    }

    /**
     * Get error type name from error code
     */
    private function getErrorTypeName(int $type): string
    {
        $errorTypes = [
            E_ERROR => 'E_ERROR',
            E_WARNING => 'E_WARNING',
            E_PARSE => 'E_PARSE',
            E_NOTICE => 'E_NOTICE',
            E_CORE_ERROR => 'E_CORE_ERROR',
            E_CORE_WARNING => 'E_CORE_WARNING',
            E_COMPILE_ERROR => 'E_COMPILE_ERROR',
            E_COMPILE_WARNING => 'E_COMPILE_WARNING',
            E_USER_ERROR => 'E_USER_ERROR',
            E_USER_WARNING => 'E_USER_WARNING',
            E_USER_NOTICE => 'E_USER_NOTICE',
            E_STRICT => 'E_STRICT',
            E_RECOVERABLE_ERROR => 'E_RECOVERABLE_ERROR',
            E_DEPRECATED => 'E_DEPRECATED',
            E_USER_DEPRECATED => 'E_USER_DEPRECATED',
            E_ALL => 'E_ALL'
        ];

        return $errorTypes[$type] ?? "UNKNOWN_ERROR_TYPE ({$type})";
    }

    /**
     * Check if command is already running
     */
    private function isCommandRunning(): bool
    {
        return DB::table('commands_status')
            ->where('command_name', 'fetch:tender-via-api')
            ->where('is_running', true)
            ->exists();
    }

    /**
     * Mark command as running
     */
    private function markCommandAsRunning(): void
    {
        DB::table('commands_status')->updateOrInsert(
            ['command_name' => 'fetch:tender-via-api'],
            ['is_running' => true, 'started_at' => now()]
        );
    }

    /**
     * Mark command as not running
     */
    private function markCommandAsNotRunning(): void
    {
        DB::table('commands_status')
            ->where('command_name', 'fetch:tender-via-api')
            ->update(['is_running' => false, 'finished_at' => now()]);
    }

    /**
     * Send metrics notifications to users who follow LPSE
     *
     * @param array $lpseMetrics
     * @return void
     */
    private function sendMetricsNotifications(array $lpseMetrics)
    {
        $this->info('Sending LPSE metrics notifications...');
        $this->logger->info('Sending LPSE metrics notifications...');

        // Update command progress
        CommandProgress::updateProgress(
            'fetch:tender-via-api',
            100,
            100,
            "Mengirim notifikasi metrik LPSE...",
            [
                'total_lpse' => count($lpseMetrics),
                'total_new_tenders' => array_sum(array_column($lpseMetrics, 'new_tenders')),
                'total_updated_tenders' => array_sum(array_column($lpseMetrics, 'updated_tenders'))
            ]
        );

        foreach ($lpseMetrics as $kd_lpse => $metrics) {
            // Only send notifications if there are new or updated tenders
            if ($metrics['new_tenders'] > 0 || $metrics['updated_tenders'] > 0) {
                try {
                    $lpse = Lpse::where('kd_lpse', $kd_lpse)->first();
                    if ($lpse) {
                        // Format period as month and year
                        $metrics['period'] = Carbon::now()->format('F Y');

                        // Dispatch job to send notifications
                        SendLpseMetricsNotifications::dispatch($lpse, $metrics)
                            ->onQueue('notifications')
                            ->delay(now()->addSeconds(5));

                        $this->info("Dispatched metrics notification for LPSE {$lpse->nama_lpse}");
                        $this->logger->info("Dispatched metrics notification for LPSE {$lpse->nama_lpse}");
                    }
                } catch (Exception $e) {
                    $this->error("Failed to dispatch metrics notification for LPSE code: {$kd_lpse}. Error: " . $e->getMessage());
                    $this->logger->error("Failed to dispatch metrics notification for LPSE code: {$kd_lpse}. Error: " . $e->getMessage());
                }
            } else {
                $this->info("No new or updated tenders for LPSE code: {$kd_lpse}, skipping notification.");
                $this->logger->info("No new or updated tenders for LPSE code: {$kd_lpse}, skipping notification.");
            }
        }

        $this->info('LPSE metrics notifications dispatched.');
        $this->logger->info('LPSE metrics notifications dispatched.');
    }
}
