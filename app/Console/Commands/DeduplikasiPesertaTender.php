<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\PesertaTender;

class DeduplikasiPesertaTender extends Command
{
    protected $signature = 'peserta-tender:deduplikasi';
    protected $description = 'Menghapus data duplikat pada tabel peserta_tender berdasarkan kode_tender dan id_peserta';

    public function handle()
    {
        $this->info('🔍 Memulai proses deduplikasi peserta_tender...');
        $duplicates = PesertaTender::select('kode_tender', 'id_peserta', DB::raw('COUNT(*) as count'))
            ->groupBy('kode_tender', 'id_peserta')
            ->having('count', '>', 1)
            ->get();

        $totalDuplicates = $duplicates->count();
        $this->info("Total duplikat ditemukan: {$totalDuplicates}");

        if ($totalDuplicates === 0) {
            $this->info('Tidak ada duplikasi ditemukan.');
            return;
        }

        foreach ($duplicates as $duplicate) {
            $entries = PesertaTender::where('kode_tender', $duplicate->kode_tender)
                ->where('id_peserta', $duplicate->id_peserta)
                ->orderBy('created_at', 'desc') // Prioritaskan data terbaru
                ->get();

            // Simpan entri terbaru dan hapus sisanya
            $mainEntry = $entries->shift(); // Ambil entri pertama
            foreach ($entries as $entry) {
                $entry->delete();
                $this->info("Deleted duplicate entry: kode_tender={$entry->kode_tender}, id_peserta={$entry->id_peserta}");
            }
        }

        $this->info('✅ Proses deduplikasi selesai.');
    }
}
