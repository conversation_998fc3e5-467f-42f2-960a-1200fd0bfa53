<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class FixLogPermissions extends Command
{
    protected $signature = 'logs:fix-permissions
                           {--recursive : Fix permissions recursively}
                           {--dry-run : Show what would be changed without making changes}';

    protected $description = 'Fix log file and directory permissions to ensure proper access';

    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $recursive = $this->option('recursive');

        $this->info('🔧 Fixing log permissions...');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        $this->newLine();

        $logBasePath = storage_path('logs');
        $logDirectories = [
            $logBasePath,
            $logBasePath . '/query',
            $logBasePath . '/telegram',
            $logBasePath . '/whatsapp',
            $logBasePath . '/sirup',
            $logBasePath . '/performance',
        ];

        $this->fixDirectoryPermissions($logDirectories, $dryRun);
        $this->fixFilePermissions($logDirectories, $recursive, $dryRun);
        $this->createLogrotateConfig($dryRun);

        if (!$dryRun) {
            $this->info('✅ Log permissions have been fixed successfully!');
        } else {
            $this->info('✅ Dry run completed. Use without --dry-run to apply changes.');
        }
    }

    private function fixDirectoryPermissions(array $directories, bool $dryRun): void
    {
        $this->info('📁 Fixing directory permissions...');

        foreach ($directories as $directory) {
            if (!File::isDirectory($directory)) {
                if ($dryRun) {
                    $this->line("Would create directory: {$directory}");
                } else {
                    try {
                        File::makeDirectory($directory, 0755, true);
                        $this->line("Created directory: {$directory}");
                    } catch (\Exception $e) {
                        $this->warn("Could not create directory {$directory}: " . $e->getMessage());
                    }
                }
                continue;
            }

            $currentPerms = substr(sprintf('%o', fileperms($directory)), -4);
            $targetPerms = '0755';

            if ($currentPerms !== $targetPerms) {
                if ($dryRun) {
                    $this->line("Would change {$directory} from {$currentPerms} to {$targetPerms}");
                } else {
                    try {
                        chmod($directory, 0755);
                        $this->line("Changed {$directory} from {$currentPerms} to {$targetPerms}");
                    } catch (\Exception $e) {
                        $this->warn("Could not change permissions for {$directory}: " . $e->getMessage());
                    }
                }
            } else {
                $this->line("✓ {$directory} already has correct permissions ({$currentPerms})");
            }
        }
    }

    private function fixFilePermissions(array $directories, bool $recursive, bool $dryRun): void
    {
        $this->newLine();
        $this->info('📄 Fixing file permissions...');

        foreach ($directories as $directory) {
            if (!File::isDirectory($directory)) {
                continue;
            }

            $pattern = $recursive ? $directory . '/**/*.log' : $directory . '/*.log';
            $files = File::glob($pattern);

            foreach ($files as $file) {
                if (!File::isFile($file)) {
                    continue;
                }

                $currentPerms = substr(sprintf('%o', fileperms($file)), -4);
                $targetPerms = '0644';

                if ($currentPerms !== $targetPerms) {
                    if ($dryRun) {
                        $this->line("Would change " . basename($file) . " from {$currentPerms} to {$targetPerms}");
                    } else {
                        try {
                            chmod($file, 0644);
                            $this->line("Changed " . basename($file) . " from {$currentPerms} to {$targetPerms}");
                        } catch (\Exception $e) {
                            $this->warn("Could not change permissions for " . basename($file) . ": " . $e->getMessage());
                        }
                    }
                } else {
                    $this->line("✓ " . basename($file) . " already has correct permissions ({$currentPerms})");
                }
            }
        }
    }

    private function createLogrotateConfig(bool $dryRun): void
    {
        $this->newLine();
        $this->info('🔄 Creating logrotate configuration...');

        $logrotateConfig = $this->generateLogrotateConfig();
        $configPath = storage_path('logs/logrotate.conf');

        if ($dryRun) {
            $this->line("Would create logrotate config at: {$configPath}");
            $this->newLine();
            $this->line("Config content:");
            $this->line($logrotateConfig);
        } else {
            File::put($configPath, $logrotateConfig);
            chmod($configPath, 0644);
            $this->line("Created logrotate config at: {$configPath}");

            $this->newLine();
            $this->info('💡 To enable automatic log rotation, add this to your crontab:');
            $this->line('0 2 * * * /usr/sbin/logrotate ' . $configPath . ' --state ' . storage_path('logs/logrotate.state'));
        }
    }

    private function generateLogrotateConfig(): string
    {
        $logPath = storage_path('logs');

        return <<<EOL
# Laravel Application Log Rotation Configuration
# Generated by FixLogPermissions command

{$logPath}/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    postrotate
        # Reload application if needed
        # systemctl reload php8.2-fpm
    endscript
}

{$logPath}/query/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    size 100M
}

{$logPath}/telegram/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
}

{$logPath}/whatsapp/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
}

{$logPath}/sirup/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    size 500M
}

{$logPath}/performance/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
}
EOL;
    }
}
