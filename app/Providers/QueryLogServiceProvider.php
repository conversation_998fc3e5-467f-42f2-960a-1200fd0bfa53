<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class QueryLogServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only enable if query logging is enabled
        if (!env('ENABLE_QUERY_LOGGING', true)) {
            return;
        }

        // Enable query logging in both debug and production for monitoring
        DB::listen(function ($query) {
            $sql = $query->sql;
            $bindings = $query->bindings;
            $time = $query->time;

            // Get thresholds from environment
            $threshold = config('app.debug')
                ? env('SLOW_QUERY_THRESHOLD_DEBUG', 100)
                : env('SLOW_QUERY_THRESHOLD_PRODUCTION', 500);

            $criticalThreshold = env('CRITICAL_QUERY_THRESHOLD', 2000);

            if ($time > $threshold) {
                $this->logSlowQuery($sql, $bindings, $time);
            }

            // Log extremely slow queries with more details
            if ($time > $criticalThreshold) {
                $this->logCriticalSlowQuery($sql, $bindings, $time);
            }
        });

        // Ensure log directory has proper permissions
        $this->ensureLogPermissions();
    }

    /**
     * Log slow query with formatted output
     */
    private function logSlowQuery(string $sql, array $bindings, float $time): void
    {
        $bindingsString = $this->formatBindings($bindings);
        $memoryUsage = $this->formatBytes(memory_get_usage(true));
        $peakMemory = $this->formatBytes(memory_get_peak_usage(true));

        $context = [
            'execution_time_ms' => $time,
            'memory_usage' => $memoryUsage,
            'peak_memory' => $peakMemory,
            'sql' => $sql,
            'bindings' => $bindingsString,
            'request_uri' => request()->getRequestUri() ?? 'CLI',
            'user_agent' => request()->userAgent() ?? 'CLI',
            'timestamp' => now()->toISOString(),
        ];

        Log::channel('query')->warning("Slow Query Detected [{$time}ms]", $context);
    }

    /**
     * Log critical slow query with stack trace
     */
    private function logCriticalSlowQuery(string $sql, array $bindings, float $time): void
    {
        $bindingsString = $this->formatBindings($bindings);
        $stackTrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10);

        $context = [
            'execution_time_ms' => $time,
            'sql' => $sql,
            'bindings' => $bindingsString,
            'stack_trace' => $stackTrace,
            'request_uri' => request()->getRequestUri() ?? 'CLI',
            'memory_usage' => $this->formatBytes(memory_get_usage(true)),
            'peak_memory' => $this->formatBytes(memory_get_peak_usage(true)),
            'timestamp' => now()->toISOString(),
        ];

        Log::channel('query_critical')->error("Critical Slow Query [{$time}ms]", $context);

        // Also log to main log for critical queries
        Log::error("Critical Database Performance Issue: Query took {$time}ms", [
            'sql' => substr($sql, 0, 200) . (strlen($sql) > 200 ? '...' : ''),
            'execution_time' => $time,
        ]);
    }

    /**
     * Format query bindings for logging
     */
    private function formatBindings(array $bindings): string
    {
        return implode(', ', array_map(function ($binding) {
            if (is_null($binding)) {
                return 'NULL';
            }
            if (is_bool($binding)) {
                return $binding ? 'TRUE' : 'FALSE';
            }
            if (is_numeric($binding)) {
                return (string) $binding;
            }
            if (is_string($binding)) {
                // Truncate very long strings
                $truncated = strlen($binding) > 100 ? substr($binding, 0, 100) . '...' : $binding;
                return "'" . addslashes($truncated) . "'";
            }
            return "'" . addslashes((string) $binding) . "'";
        }, $bindings));
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Ensure log directories have proper permissions
     */
    private function ensureLogPermissions(): void
    {
        $logPaths = [
            storage_path('logs'),
            storage_path('logs/query'),
            storage_path('logs/telegram'),
            storage_path('logs/whatsapp'),
            storage_path('logs/sirup'),
            storage_path('logs/performance'),
        ];

        foreach ($logPaths as $path) {
            try {
                if (!is_dir($path)) {
                    mkdir($path, 0755, true);
                }

                // Try to set proper permissions for the directory
                // Suppress errors in container environments where this might fail
                @chmod($path, 0755);

                // Set permissions for existing log files
                $files = glob($path . '/*.log');
                foreach ($files as $file) {
                    @chmod($file, 0644);
                }
            } catch (\Exception $e) {
                // Silently continue if permission changes fail
                // This is common in Docker environments
                continue;
            }
        }
    }
}
