<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Models\SystemError;
use App\Policies\SystemErrorPolicy;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        SystemError::class => SystemErrorPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Register policies
        $this->registerPolicies();

        // Define gates for system error management
        Gate::define('view-system-errors', function ($user) {
            return $user->account_type === 'Super Admin';
        });

        Gate::define('resolve-system-errors', function ($user) {
            return $user->account_type === 'Super Admin';
        });

        Gate::define('delete-system-errors', function ($user) {
            return $user->account_type === 'Super Admin';
        });
    }
}
