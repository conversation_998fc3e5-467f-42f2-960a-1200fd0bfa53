<?php

namespace App\Providers;

use App\Actions\Fortify\CreateNewUser;
use App\Actions\Fortify\ResetUserPassword;
use App\Actions\Fortify\UpdateUserPassword;
use App\Actions\Fortify\UpdateUserProfileInformation;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Laravel\Fortify\Fortify;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class FortifyServiceProvider extends ServiceProvider
{

    public const HOME = '/email/verify';  // Ubah ini sesuai dengan tujuan redirect setelah registrasi

    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Registering services if needed
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register actions for creating, updating, and resetting users
        Fortify::createUsersUsing(CreateNewUser::class);
        Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
        Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
        Fortify::resetUserPasswordsUsing(ResetUserPassword::class);


        // Custom authentication logic
        Fortify::authenticateUsing(function (Request $request) {
            $user = User::where('email', $request->email)->first();

            if ($user && Hash::check($request->password, $user->password)) {
                if (!$user->hasVerifiedEmail()) {
                    $user->sendEmailVerificationNotification();
                   throw ValidationException::withMessages([
                        'email' => 'Silakan verifikasi email Anda untuk mengakses dashboard. Periksa kotak masuk email Anda.',
                    ]);
                }
                return $user; // Return the authenticated user
            }

            return null; // Return null if authentication fails
        });

        // Register custom views for Fortify
        Fortify::registerView(fn() => view('tenderid.auth.register'));
        Fortify::loginView(fn() => view('tenderid.auth.login'));
        Fortify::verifyEmailView(fn() => view('tenderid.auth.verify-email'));
        Fortify::requestPasswordResetLinkView(function () {
            return view('tenderid.auth.forgot-password');
        });

        Fortify::resetPasswordView(function ($request) {
            return view('tenderid.auth.reset-password', ['request' => $request]);
        });



        // Throttle login attempts
        RateLimiter::for('login', function (Request $request) {
            $throttleKey = Str::transliterate(Str::lower($request->input(Fortify::username())) . '|' . $request->ip());
            return Limit::perMinute(5)->by($throttleKey);
        });

        RateLimiter::for('two-factor', function (Request $request) {
            return Limit::perMinute(5)->by($request->session()->get('login.id'));
        });
    }
}
