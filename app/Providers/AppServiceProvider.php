<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\TenderLpse;
use App\Models\User;
use App\Observers\TenderLpseObserver;
use App\Observers\UserObserver;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Request;
use Illuminate\Cache\RateLimiting\Limit;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        \Illuminate\Support\Facades\App::setLocale('id');

        TenderLpse::observe(TenderLpseObserver::class);
        User::observe(UserObserver::class);

        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by(optional($request->user())->id ?: $request->ip());
        });
    }
}
