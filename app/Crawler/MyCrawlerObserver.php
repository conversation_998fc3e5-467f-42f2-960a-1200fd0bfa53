<?php

namespace App\Crawler;

use <PERSON><PERSON>\Crawler\CrawlObservers\CrawlObserver;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\UriInterface;
use GuzzleHttp\Exception\RequestException;
use App\Crawler\CustomClient;

class MyCrawlerObserver extends CrawlObserver
{
    protected $client;

    public function __construct()
    {
        // Inisialisasi CustomClient dengan header yang diperlukan
        $this->client = new CustomClient([
            'headers' => [
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language' => 'en-GB,en;q=0.5',
                'Cache-Control' => 'max-age=0',
                'Cookie' => 'SPSE_SESSION=310fbcf78dd5801e5ca169049bd175b786c7affe-___TS=1724755265810&___ID=24b844f6-09ae-4ab3-bd04-4c60415c8735; _cfuvid=6iTsfiNA_HbOovyLinb39jtjNpVKWW1_RS7y0EtNGNQ-1724753465919-*******-604800000',
                'Referer' => 'https://tenderid.mcmtestapp.com/',
                'Sec-CH-UA' => '"Chromium";v="128", "Not;A=Brand";v="24", "Brave";v="128"',
                'Sec-CH-UA-Mobile' => '?0',
                'Sec-CH-UA-Platform' => '"Windows"',
                'Sec-Fetch-Dest' => 'document',
                'Sec-Fetch-Mode' => 'navigate',
                'Sec-Fetch-Site' => 'cross-site',
                'Sec-Fetch-User' => '?1',
                'Sec-GPC' => '1',
                'Upgrade-Insecure-Requests' => '1',
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36',
            ],
        ]);
    }

    public function crawled(
        UriInterface $url,
        ResponseInterface $response,
        ?UriInterface $foundOnUrl = null,
        ?string $linkText = null
    ): void {
        // Proses respons di sini
        echo "Crawled: " . (string)$url . "\n";
        // Contoh: menampilkan konten halaman
        echo $response->getBody();
    }

    public function crawlFailed(
        UriInterface $url,
        RequestException $requestException,
        ?UriInterface $foundOnUrl = null,
        ?string $linkText = null
    ): void {
        // Tangani kesalahan di sini
        echo "Failed to crawl: " . (string)$url . "\n";
        echo "Error: " . $requestException->getMessage() . "\n";
    }

    public function startCrawling($url)
    {
        // Lakukan permintaan menggunakan Guzzle Client
        try {
            $response = $this->client->get($url);
            $this->crawled(new \GuzzleHttp\Psr7\Uri($url), $response);
        } catch (RequestException $e) {
            $this->crawlFailed(new \GuzzleHttp\Psr7\Uri($url), $e);
        }
    }
}