<?php

namespace App\Crawler;

use Illuminate\Support\Facades\Log;
use GuzzleHttp\Psr7\Response;

class CustomClientSquid
{
    protected $proxies = [];
    protected $options = [];

    public function __construct(array $options = [])
    {
        // Read proxies from environment variable PROXIES (comma separated)
        $proxiesEnv = env('PROXIES_SSH', '');

        // Parse proxies - split by comma and trim each proxy
        $this->proxies = array_filter(array_map('trim', explode(',', $proxiesEnv)));

        // Log the number of proxies found
       // Log::info("CustomClientSquid initialized with " . count($this->proxies) . " proxies");

        $this->options = $options;
    }

    /**
     * Make a GET request with optional random proxy usage.
     *
     * @param string $url
     * @param array $options
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function get(string $url, array $options = [])
    {
        return $this->request('GET', $url, $options);
    }

    /**
     * Make a POST request with optional random proxy usage.
     *
     * @param string $url
     * @param array $options
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function post(string $url, array $options = [])
    {
        return $this->request('POST', $url, $options);
    }

    /**
     * Make a HTTP request with optional random proxy usage.
     *
     * @param string $method
     * @param string $url
     * @param array $options
     * @return \Psr\Http\Message\ResponseInterface
     */
    private function request(string $method, string $url, array $options = [])
    {
        $ch = curl_init($url);

        // Default headers that work well with LPSE
        $headers = [
            'Referer: ' . $url,
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language: en-US,en;q=0.9,id;q=0.8,sv;q=0.7,fi;q=0.6',
            'Accept-Encoding: gzip, deflate, br, zstd',
            'Cache-Control: max-age=0',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1',
            'sec-ch-ua: "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "Windows"',
            'sec-fetch-dest: document',
            'sec-fetch-mode: navigate',
            'sec-fetch-site: same-origin',
            'sec-fetch-user: ?1',
            'Priority: u=0, i',
            'Cookie: SPSE_SESSION=05bd57cc0524b4a1549917abaf9af2ba965f06b2-___TS=1745685683226&___ID=eb4c9836-d69b-4c33-ba3d-553703c55610; _cfuvid=orLWOMF4wSzyYi5N.UA7hE8Q6HC7lhEuGSq7BLablEg-1745681373354-*******-604800000'
        ];

        // Add custom headers from options if provided
        if (isset($options['headers'])) {
            foreach ($options['headers'] as $key => $value) {
                $headers[] = $key . ': ' . $value;
            }
        }

        // Set request method
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);

            // Set post data if provided
            if (isset($options['body'])) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $options['body']);
                //Log::info("POST data: " . substr($options['body'], 0, 100) . (strlen($options['body']) > 100 ? '...' : ''));
            }
        }

        // Decide randomly whether to use proxy or not (50% chance)
        $useProxy = !empty($this->proxies) && (bool)random_int(0, 1);
        $useProxy = true; // Force using proxy for testing

        if ($useProxy && !empty($this->proxies)) {
            try {
                // Pilih satu proxy secara acak
                $proxy = $this->proxies[array_rand($this->proxies)];

                // Validasi format proxy
                if (strpos($proxy, ',') !== false) {
                    // Jika masih ada koma, ambil hanya proxy pertama
                    $proxy = trim(explode(',', $proxy)[0]);
                    Log::warning("Multiple proxies detected in single entry, using first one: $proxy");
                }

                // Set proxy untuk curl
                curl_setopt($ch, CURLOPT_PROXY, $proxy);

                // Set proxy auth jika ada
                if (strpos($proxy, '@') !== false) {
                    curl_setopt($ch, CURLOPT_PROXYUSERPWD, substr($proxy, strpos($proxy, '//') + 2, strpos($proxy, '@') - strpos($proxy, '//') - 2));
                }

                //Log::info("$method request to {$url} made using proxy: {$proxy}");
            } catch (\Exception $e) {
                //Log::error("Error setting proxy: " . $e->getMessage());
                // Jika gagal menggunakan proxy, lanjutkan tanpa proxy
                // Log::info("Falling back to direct connection due to proxy error");
            }
        } else {
            // Log::info("$method request to {$url} made without proxy");
        }

        // Set timeout dari options atau dari constructor options
        $timeout = $options['timeout'] ?? $this->options['timeout'] ?? 240.0;
        $connectTimeout = $options['connect_timeout'] ?? $this->options['connect_timeout'] ?? 30.0;

        // Log timeout yang digunakan
        // Log::info("Setting timeout to {$timeout} seconds for URL: {$url}");
        // Log::info("Setting connection timeout to {$connectTimeout} seconds for URL: {$url}");

        // Set timeout options
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $connectTimeout);

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_ENCODING, '');

        $response = curl_exec($ch);
        $info = curl_getinfo($ch);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            $errorCode = curl_errno($ch);

            // Get curl info before closing the handle
            $curlInfo = curl_getinfo($ch);
            curl_close($ch);

            // Log error dengan lebih detail
            // Log::error("Curl error {$errorCode} for URL {$url}: {$error}");
            // Log::error("Request options: " . json_encode($options));
            // Log::error("Curl info: " . json_encode($curlInfo));

            // Pesan khusus untuk timeout
            if ($errorCode == CURLE_OPERATION_TIMEDOUT) {
                throw new \Exception("Request timeout after {$timeout} seconds ($method request to $url): {$error}");
            }

            throw new \Exception("Curl error ($method request to $url): {$error}");
        }

        curl_close($ch);

        // Create a PSR-7 response to maintain compatibility with existing code
        $response = new Response(
            $info['http_code'],
            [
                'Content-Type' => $info['content_type'],
            ],
            $response
        );

        return $response;
    }
}
