<?php

namespace App\Crawler;

use Illuminate\Support\Facades\Log;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Cache;

class CustomClient
{
    protected $proxies = [];
    protected $options = [];

    public function __construct(array $options = [])
    {
        // Read proxies from environment variable PROXIES (comma separated)
        $proxiesEnv = env('PROXIES', '');

        // Add additional proxies for LPSE
        $additionalProxies = [ ];

        $envProxies = array_filter(array_map('trim', explode(',', $proxiesEnv)));
        $this->proxies = array_merge($envProxies, $additionalProxies);
        $this->options = $options;

        // Log available proxies
        // Log::info("CustomClient initialized with " . count($this->proxies) . " proxies");
    }

    /**
     * Make a GET request with optional random proxy usage.
     *
     * @param string $url
     * @param array $options
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function get(string $url, array $options = [])
    {
        $ch = curl_init($url);

        // Default headers that work well with LPSE
        $headers = [
            "Referer: {$url}",
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language: en-US,en;q=0.9,id;q=0.8,sv;q=0.7,fi;q=0.6',
            'Accept-Encoding: gzip, deflate, br, zstd',
            'Cache-Control: max-age=0',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1',
            'sec-ch-ua: "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "Windows"',
            'sec-fetch-dest: document',
            'sec-fetch-mode: navigate',
            'sec-fetch-site: same-origin',
            'sec-fetch-user: ?1',
            'Priority: u=0, i',
            'Cookie: SPSE_SESSION=05bd57cc0524b4a1549917abaf9af2ba965f06b2-___TS=1745685683226&___ID=eb4c9836-d69b-4c33-ba3d-553703c55610; _cfuvid=orLWOMF4wSzyYi5N.UA7hE8Q6HC7lhEuGSq7BLablEg-1745681373354-*******-604800000'
        ];

        // Add custom headers from options if provided
        if (isset($options['headers'])) {
            foreach ($options['headers'] as $key => $value) {
                $headers[] = "{$key}: {$value}";
            }
        }

        // Decide whether to use proxy based on options or random chance (75% chance by default)
        $useProxy = false;

        // If proxy is explicitly set in options, use it
        if (isset($options['proxy'])) {
            $useProxy = true;
            $proxy = $options['proxy'];
        }
        // Otherwise use random proxy with higher probability (75%)
        else if (!empty($this->proxies) && (random_int(0, 100) < 75)) {
            $useProxy = true;
            // Shuffle proxies and pick one
            $proxyKeys = array_keys($this->proxies);
            shuffle($proxyKeys);
            $proxy = $this->proxies[$proxyKeys[0]];
        }

        if ($useProxy) {
            curl_setopt($ch, CURLOPT_PROXY, $proxy);
            // Log::info("Request to {$url} made using proxy: {$proxy}");
        } else {
            // Log::info("Request to {$url} made without proxy");
        }

        // Set timeout (default 60 seconds or from options)
        $timeout = $options['timeout'] ?? 60;
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        // Log::info("Setting timeout to {$timeout} seconds for URL: {$url}");

        // Set connection timeout (default 30 seconds or from options)
        $connectTimeout = $options['connect_timeout'] ?? 30;
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $connectTimeout);
        // Log::info("Setting connection timeout to {$connectTimeout} seconds for URL: {$url}");

        // Set DNS resolver options
        curl_setopt($ch, CURLOPT_DNS_USE_GLOBAL_CACHE, false);
        curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120); // 2 minutes

        // Set connection options
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true); // Force new connection

        // Set standard options
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_ENCODING, '');
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5); // Maximum number of redirects

        // Set additional options from constructor
        if (isset($this->options['curl_options']) && is_array($this->options['curl_options'])) {
            foreach ($this->options['curl_options'] as $option => $value) {
                curl_setopt($ch, $option, $value);
            }
        }

        $response = curl_exec($ch);
        $info = curl_getinfo($ch);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            $errorCode = curl_errno($ch);

            // Get curl info before closing the handle
            $curlInfo = curl_getinfo($ch);
            curl_close($ch);

            // Log the error with more details
            // Log::error("Curl error {$errorCode} for URL {$url}: {$error}");
            // Log::error("Request options: " . json_encode($options));
            // Log::error("Curl info: " . json_encode($curlInfo));

            throw new \Exception("Curl error: {$error}");
        }

        curl_close($ch);

        // Create a PSR-7 response to maintain compatibility with existing code
        $response = new Response(
            $info['http_code'],
            [
                'Content-Type' => $info['content_type'],
            ],
            $response
        );

        return $response;
    }
}
