<?php

namespace App\Listeners;

use App\Services\EmailLoggerService;
use Illuminate\Mail\Events\MessageSent;
use Illuminate\Support\Facades\Log;

class LogSentEmail
{
    /**
     * @var EmailLoggerService
     */
    protected $emailLoggerService;

    /**
     * Create the event listener.
     */
    public function __construct(EmailLoggerService $emailLoggerService)
    {
        $this->emailLoggerService = $emailLoggerService;
    }

    /**
     * Handle the event.
     */
    public function handle(MessageSent $event): void
    {
        try {
            $message = $event->message;
            $data = $event->data;

            // Get recipients
            $to = $message->getTo();
            $recipientEmail = array_key_first($to);

            // Get subject
            $subject = $message->getSubject();

            // Get body (try to get HTML body first, then plain text)
            $body = null;
            if ($message->getHtmlBody()) {
                $body = $message->getHtmlBody();
            } elseif ($message->getTextBody()) {
                $body = $message->getTextBody();
            }

            // Get email type from data if available
            $emailType = $data['email_type'] ?? null;

            // Try to detect email type from subject and content if not explicitly set
            if (!$emailType) {
                // Check for verification emails
                if (
                    stripos($subject, 'verify') !== false ||
                    stripos($subject, 'verifikasi') !== false ||
                    (
                        $body && (
                            stripos($body, 'verify your email') !== false ||
                            stripos($body, 'verifikasi email') !== false ||
                            stripos($body, 'email/verify') !== false
                        )
                    )
                ) {
                    $emailType = 'verification';
                }
                // Check for password reset emails
                elseif (
                    stripos($subject, 'reset password') !== false ||
                    stripos($subject, 'reset kata sandi') !== false ||
                    (
                        $body && (
                            stripos($body, 'reset your password') !== false ||
                            stripos($body, 'reset kata sandi') !== false ||
                            stripos($body, 'password/reset') !== false
                        )
                    )
                ) {
                    $emailType = 'password_reset';
                }
                // Check for welcome emails
                elseif (
                    stripos($subject, 'welcome') !== false ||
                    stripos($subject, 'selamat datang') !== false
                ) {
                    $emailType = 'welcome';
                }
                // Check for notification emails
                elseif (
                    stripos($subject, 'notification') !== false ||
                    stripos($subject, 'notifikasi') !== false
                ) {
                    $emailType = 'notification';
                }
            }

            // Get metadata
            $metadata = [
                'headers' => $message->getHeaders()->toArray(),
                'has_attachments' => count($message->getAttachments()) > 0,
                'cc' => $message->getCc(),
                'bcc' => $message->getBcc(),
            ];

            // Log the email
            $this->emailLoggerService->logEmail(
                $recipientEmail,
                $subject,
                $body,
                'sent',
                null,
                $emailType,
                $metadata
            );
        } catch (\Exception $e) {
            Log::error('Failed to log sent email: ' . $e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
