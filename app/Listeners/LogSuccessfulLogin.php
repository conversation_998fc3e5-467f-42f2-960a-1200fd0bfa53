<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Login;
use App\Models\LoginLog;
use App\Helpers\DeviceDetector;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class LogSuccessfulLogin
{
    public function handle(Login $event)
    {
        try {
            // Menggunakan DeviceDetector untuk mendapatkan informasi detail
            $detector = new DeviceDetector();
            $deviceInfo = $detector->detect();

            $userAgent = request()->header('User-Agent');
            $ipAddress = $detector->ipAddress; // Gunakan IP yang sudah dideteksi oleh DeviceDetector

            // Tambahkan informasi header Cloudflare
            $request = request();

            // Informasi browser
            $browser = $deviceInfo['browser']['name'];
            $browserVersion = $deviceInfo['browser']['version'];

            // Informasi sistem operasi
            $os = $deviceInfo['os']['name'];
            $osVersion = $deviceInfo['os']['version'];

            // Informasi perangkat
            $deviceType = $deviceInfo['device']['type'];
            $deviceBrand = $deviceInfo['device']['brand'];
            $deviceModel = $deviceInfo['device']['model'];

            // Informasi lokasi
            $location = $deviceInfo['location'];
            $country = $location['country'];
            $region = $location['region'];
            $city = $location['city'];
            $latitude = $location['latitude'];
            $longitude = $location['longitude'];

            // Informasi tambahan
            $additionalInfo = [
                'isp' => $location['isp'] ?? null,
                'timezone' => $location['timezone'] ?? null,
                'referrer' => request()->header('referer'),
                'session_id' => Session::getId(),
                'cf_ray' => $request->header('CF-RAY'),
                'cf_connecting_ip' => $request->header('CF-Connecting-IP'),
                'cf_ipcountry' => $request->header('CF-IPCountry'),
                'cf_visitor' => $request->header('CF-Visitor'),
                'cdn_loop' => $request->header('CDN-Loop'),
            ];

            // Cek apakah ada sesi login yang belum logout
            $existingActiveLogin = LoginLog::where('user_id', $event->user->id)
                ->whereNull('logout_at')
                ->latest()
                ->first();

            // Jika ada, update waktu logout-nya
            if ($existingActiveLogin) {
                $existingActiveLogin->update(['logout_at' => now()]);
                Log::info('Menutup otomatis sesi login sebelumnya saat login baru', [
                    'user_id' => $event->user->id,
                    'login_log_id' => $existingActiveLogin->id
                ]);
            }

            // Buat catatan login baru
            $loginLog = LoginLog::create([
                'user_id' => $event->user->id,
                'login_at' => now(),
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'browser' => $browser,
                'os' => $os,
                'device_type' => $deviceType,
                'device_brand' => $deviceBrand,
                'device_model' => $deviceModel,
                'country' => $country,
                'region' => $region,
                'city' => $city,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'additional_info' => json_encode($additionalInfo),
            ]);

            Log::info('Login pengguna tercatat', [
                'user_id' => $event->user->id,
                'login_log_id' => $loginLog->id,
                'ip_address' => $ipAddress,
                'browser' => $browser,
                'os' => $os
            ]);

            // Invalidate other sessions for the user except current session
            try {
                $currentSessionId = Session::getId();

                $deletedSessions = DB::table('sessions')
                    ->where('user_id', $event->user->id)
                    ->where('id', '!=', $currentSessionId)
                    ->delete();

                if ($deletedSessions > 0) {
                    Log::info('Menginvalidasi sesi lain untuk pengguna', [
                        'user_id' => $event->user->id,
                        'sessions_deleted' => $deletedSessions
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Error saat menginvalidasi sesi lain', [
                    'user_id' => $event->user->id,
                    'error' => $e->getMessage()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error saat mencatat login pengguna', [
                'user_id' => $event->user->id ?? 'tidak diketahui',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
