<?php

namespace App\Listeners;

use App\Helpers\WhatsAppHelper;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendWhatsAppWelcomeMessage implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Jumlah percobaan maksimal untuk mengirim pesan
     */
    public $tries = 3;

    /**
     * Waktu tunggu antara percobaan dalam detik
     */
    public $backoff = 5;

    /**
     * Handle the event.
     *
     * @param  Registered  $event
     * @return void
     */
    public function handle(Registered $event)
    {
        $user = $event->user;

        Log::info('SendWhatsAppWelcomeMessage triggered', [
            'user_id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone ?? 'not set'
        ]);

        if (!$user->phone) {
            Log::warning('User has no phone number, skipping WhatsApp welcome message', ['user_id' => $user->id]);
            return;
        }

        // Format nomor telepon jika perlu
        $phone = $this->formatPhoneNumber($user->phone);

        // Log nomor telepon sebelum dan sesudah format
        Log::debug('Phone number before and after formatting', [
            'user_id' => $user->id,
            'before' => $user->phone,
            'after' => $phone
        ]);

        // Prepare complete user data
        $userData = [
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $phone,
        ];

        Log::info('Sending WhatsApp welcome message', [
            'user_id' => $user->id,
            'phone' => $phone,
            'userData' => $userData
        ]);

        // Use the helper to send welcome message with retry logic
        $success = WhatsAppHelper::sendWelcomeMessage($phone, $userData);

        if ($success) {
            Log::info('WhatsApp welcome message sent successfully', [
                'user_id' => $user->id,
                'phone' => $phone
            ]);
            return;
        } else {
            Log::error('Failed to send WhatsApp welcome message', [
                'user_id' => $user->id,
                'phone' => $phone
            ]);

            // Throw exception to trigger retry
            if ($this->attempts() < $this->tries) {
                throw new \Exception('Failed to send WhatsApp welcome message. Will retry.');
            }
        }
    }

    /**
     * Format nomor telepon untuk memastikan format yang benar
     *
     * @param string $phone
     * @return string
     */
    private function formatPhoneNumber($phone)
    {
        // Log nomor telepon sebelum format
        Log::debug('Formatting phone number', [
            'original' => $phone
        ]);

        // Hapus semua karakter non-digit
        $phone = preg_replace('/\D/', '', $phone);

        // Pastikan nomor dimulai dengan 62 (kode negara Indonesia)
        if (substr($phone, 0, 2) !== '62') {
            // Jika dimulai dengan 0, ganti dengan 62
            if (substr($phone, 0, 1) === '0') {
                $phone = '62' . substr($phone, 1);
                Log::debug('Phone number starts with 0, replacing with 62', [
                    'formatted' => $phone
                ]);
            } else {
                // Jika tidak dimulai dengan 0, tambahkan 62 di depan
                $phone = '62' . $phone;
                Log::debug('Phone number does not start with 0 or 62, adding 62', [
                    'formatted' => $phone
                ]);
            }
        } else {
            Log::debug('Phone number already starts with 62', [
                'formatted' => $phone
            ]);
        }

        // Log nomor telepon setelah format
        Log::debug('Phone number formatted', [
            'formatted' => $phone
        ]);

        return $phone;
    }
}
