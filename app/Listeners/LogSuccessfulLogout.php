<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Logout;
use App\Models\LoginLog;
use Illuminate\Support\Facades\Log;

class LogSuccessfulLogout
{
    public function handle(Logout $event)
    {
        try {
            // Perbarui data logout di database
            $loginLog = LoginLog::where('user_id', $event->user->id)
                ->whereNull('logout_at') // Cari log login yang belum diisi logout
                ->latest()
                ->first();

            if ($loginLog) {
                $loginLog->update(['logout_at' => now()]);
                Log::info('User logout recorded', [
                    'user_id' => $event->user->id,
                    'login_log_id' => $loginLog->id,
                    'login_at' => $loginLog->login_at,
                    'logout_at' => now()
                ]);
            } else {
                Log::warning('No active login session found for user during logout', [
                    'user_id' => $event->user->id
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error recording user logout', [
                'user_id' => $event->user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
