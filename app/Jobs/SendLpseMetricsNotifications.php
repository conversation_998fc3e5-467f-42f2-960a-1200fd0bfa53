<?php

namespace App\Jobs;

use App\Models\Lpse;
use App\Models\User;
use App\Notifications\LpseMetricsNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendLpseMetricsNotifications implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    public $tries = 3; // Coba ulang hingga 3 kali
    public $retryAfter = 60; // Tunggu 60 detik sebelum mencoba lagi

    protected $lpse;
    protected $metrics;

    /**
     * Create a new job instance.
     *
     * @param Lpse $lpse
     * @param array $metrics
     * @return void
     */
    public function __construct(Lpse $lpse, array $metrics)
    {
        $this->lpse = $lpse;
        $this->metrics = $metrics;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        User::whereNotNull('lpse_followed')
            ->where(function ($query) {
                $query->whereJsonContains('lpse_followed', $this->lpse->kd_lpse)
                    ->orWhereJsonContains('lpse_followed', strval($this->lpse->kd_lpse));
            })
            ->chunk(100, function ($users) {
                foreach ($users as $user) {
                    try {
                        $user->notify(new LpseMetricsNotification($this->lpse, $this->metrics));
                    } catch (\Exception $e) {
                        Log::error("Failed to send LPSE metrics notification to user {$user->id}", [
                            'error' => $e->getMessage(),
                            'lpse_id' => $this->lpse->kd_lpse,
                        ]);
                    }
                }
            });
    }
}
