<?php

namespace App\Jobs;

use App\Models\DataSirup;
use App\Models\User;
use App\Notifications\SirupNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendSirupNotifications implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $sirup;

    public function __construct(DataSirup $sirup)
    {
        $this->sirup = $sirup;
    }

    public function handle()
    {
        User::whereNotNull('sirup_followed')
            ->where(function ($query) {
                $query->whereJsonContains('sirup_followed', $this->sirup->kode_rup)
                      ->orWhereJsonContains('sirup_followed', strval($this->sirup->kode_rup));
            })
            ->chunk(100, function ($users) {
                foreach ($users as $user) {
                    try {
                        $user->notify(new SirupNotification($this->sirup));
                    } catch (\Exception $e) {
                        \Log::error("Failed to send Sirup notification to user {$user->id}: " . $e->getMessage());
                    }
                }
            });
    }
}
