<?php

namespace App\Jobs;

use App\Models\TenderLpse;
use App\Models\User;
use App\Notifications\TenderLpseStatusNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendTenderLpseNotifications implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    public $tries = 3; // Coba ulang hingga 3 kali
    public $retryAfter = 60; // Tunggu 60 detik sebelum mencoba lagi

    protected $tender;

    public function __construct(TenderLpse $tender)
    {
        $this->tender = $tender;
    }

    public function handle()
    {
        User::whereNotNull('tender_followed')
            ->where(function ($query) {
                $query->whereJsonContains('tender_followed', $this->tender->kode_tender)
                    ->orWhereJsonContains('tender_followed', strval($this->tender->kode_tender));
            })
            ->chunk(100, function ($users) {
                foreach ($users as $user) {
                    try {
                        $user->notify(new TenderLpseStatusNotification($this->tender));
                    } catch (\Exception $e) {
                        \Log::error("Failed to send Tender LPSE notification to user {$user->id}: " . $e->getMessage());
                    }
                }
            });
    }
}