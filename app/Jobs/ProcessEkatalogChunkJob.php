<?php
namespace App\Jobs;

use App\Models\ListPerusahaanTender;
use App\Models\PaketPengadaan;
use App\Models\Produk;
use App\Models\TransaksiPaket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Output\ConsoleOutput;

class ProcessEkatalogChunkJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $chunkData;

    public function __construct($chunkData)
    {
        $this->chunkData = $chunkData;
    }

    public function handle()
    {
        $totalItems = count($this->chunkData);

        // Inisialisasi progress bar
        $output      = new ConsoleOutput();
        $progressBar = new ProgressBar($output, $totalItems);
        $progressBar->start();

        // Variabel untuk profiling
        $totalTime = 0;
        $timePerusahaan = 0;
        $timePaket = 0;
        $timeProduk = 0;
        $timeTransaksi = 0;
        $itemCount = 0;
        $slowItems = [];

        foreach ($this->chunkData as $item) {
            $startTime = microtime(true);
            $itemCount++;

            try {
                DB::transaction(function () use ($item, &$timePerusahaan, &$timePaket, &$timeProduk, &$timeTransaksi) {
                    // Cek terlebih dahulu apakah paket sudah ada
                    $startPaketCheck = microtime(true);
                    $existingPaket = PaketPengadaan::where('nomor_paket', $item['Nomor Paket'])->first();
                    $checkTime = microtime(true) - $startPaketCheck;

                    // Jika paket sudah ada, gunakan paket yang ada
                    if ($existingPaket) {
                        $paket = $existingPaket;
                        $timePaket += $checkTime;

                        // Log untuk debugging
                        \Log::debug("Paket dengan nomor {$item['Nomor Paket']} sudah ada, skip proses perusahaan");

                        // Langsung proses produk jika paket sudah ada
                        if ($paket) {
                            // Profiling untuk produk
                            $startProduk = microtime(true);
                            $produk = $this->saveOrGetProduk($item);
                            $timeProduk += microtime(true) - $startProduk;

                            if ($produk) {
                                // Profiling untuk transaksi
                                $startTransaksi = microtime(true);
                                $this->saveTransaksiPaket($item, $paket->id_paket, $produk->id_produk);
                                $timeTransaksi += microtime(true) - $startTransaksi;
                            }
                        }
                    } else {
                        // Jika paket belum ada, proses perusahaan dan buat paket baru

                        // Profiling untuk perusahaan
                        $startPerusahaan = microtime(true);
                        $penyedia = $this->saveOrGetPerusahaan($item['Nama Penyedia']);
                        $pelaksana = $this->saveOrGetPerusahaan($item['Nama Pelaksana Pekerjaan']);
                        $timePerusahaan += microtime(true) - $startPerusahaan;

                        // Profiling untuk paket pengadaan
                        $startPaket = microtime(true);
                        $paket = $this->saveOrUpdatePaketPengadaan($item, $penyedia->id, $pelaksana->id);
                        $timePaket += microtime(true) - $startPaket + $checkTime;

                        if ($paket) {
                            // Profiling untuk produk
                            $startProduk = microtime(true);
                            $produk = $this->saveOrGetProduk($item);
                            $timeProduk += microtime(true) - $startProduk;

                            if ($produk) {
                                // Profiling untuk transaksi
                                $startTransaksi = microtime(true);
                                $this->saveTransaksiPaket($item, $paket->id_paket, $produk->id_produk);
                                $timeTransaksi += microtime(true) - $startTransaksi;
                            }
                        }
                    }
                });

                // Update progress bar
                $progressBar->advance();
            } catch (\Exception $e) {
                \Log::error("Error memproses item: " . json_encode($item) . ", Error: " . $e->getMessage());
            }

            $endTime = microtime(true);
            $itemTime = $endTime - $startTime;
            $totalTime += $itemTime;

            // Log jika item memakan waktu lebih dari 500ms
            if ($itemTime > 0.5) {
                $slowItems[] = [
                    'nomor_paket' => $item['Nomor Paket'] ?? 'N/A',
                    'nama_paket' => $item['Nama Paket'] ?? 'N/A',
                    'nama_penyedia' => $item['Nama Penyedia'] ?? 'N/A',
                    'nama_pelaksana' => $item['Nama Pelaksana Pekerjaan'] ?? 'N/A',
                    'time' => round($itemTime, 3) . 's'
                ];

                \Log::channel('query')->info("Item lambat [{$itemTime}s]: " . json_encode([
                    'nomor_paket' => $item['Nomor Paket'] ?? 'N/A',
                    'nama_paket' => $item['Nama Paket'] ?? 'N/A',
                    'nama_penyedia' => $item['Nama Penyedia'] ?? 'N/A',
                    'nama_pelaksana' => $item['Nama Pelaksana Pekerjaan'] ?? 'N/A',
                ]));
            }
        }

        // Selesaikan progress bar
        $progressBar->finish();

        // Log hasil profiling
        $avgTime = $itemCount > 0 ? $totalTime / $itemCount : 0;
        $output->writeln("\nProses selesai dalam " . round($totalTime, 2) . " detik.");
        $output->writeln("Rata-rata waktu per item: " . round($avgTime, 4) . " detik.");

        if (count($slowItems) > 0) {
            $output->writeln("Jumlah item lambat (>0.5s): " . count($slowItems));
        }

        \Log::channel('query')->info("ProcessEkatalogChunkJob profiling: " . json_encode([
            'total_items' => $itemCount,
            'total_time' => round($totalTime, 2) . 's',
            'avg_time_per_item' => round($avgTime, 4) . 's',
            'time_perusahaan' => round($timePerusahaan, 2) . 's (' . round(($timePerusahaan / $totalTime) * 100, 2) . '%)',
            'time_paket' => round($timePaket, 2) . 's (' . round(($timePaket / $totalTime) * 100, 2) . '%)',
            'time_produk' => round($timeProduk, 2) . 's (' . round(($timeProduk / $totalTime) * 100, 2) . '%)',
            'time_transaksi' => round($timeTransaksi, 2) . 's (' . round(($timeTransaksi / $totalTime) * 100, 2) . '%)',
            'slow_items_count' => count($slowItems),
            'slow_items' => array_slice($slowItems, 0, 10) // Tampilkan 10 item terlambat saja
        ]));
    }

    private function saveOrGetPerusahaan(string $namaPerusahaan)
    {
        try {
            // Gunakan metode createOrUpdatePerusahaan yang baru
            return ListPerusahaanTender::createOrUpdatePerusahaan(
                $namaPerusahaan,
                null, // NPWP tidak tersedia dari Ekatalog
                null  // Alamat tidak tersedia dari Ekatalog
            );
        } catch (\Exception $e) {
            // Log error
            \Log::error("Error in createOrUpdatePerusahaan: " . $e->getMessage());

            // Fallback ke metode lama jika terjadi error
            $normalizedName = ListPerusahaanTender::normalizeName($namaPerusahaan);
            $normalizedNoSpace = str_replace(' ', '', $normalizedName);
            $pembeda = $normalizedNoSpace;

            try {
                // Coba sekali lagi dengan firstOrCreate
                return ListPerusahaanTender::firstOrCreate(
                    ['pembeda' => $pembeda],
                    [
                        'npwp' => null,
                        'npwp_blur' => null,
                        'nama_peserta' => $namaPerusahaan,
                        'normalized_name' => $normalizedName,
                        'partial_npwp' => null,
                        'alamat' => null,
                    ]
                );
            } catch (\Illuminate\Database\QueryException $e) {
                // Jika masih terjadi error, coba dengan pembeda yang unik
                if ($e->getCode() == 23000) {
                    $uniquePembeda = $normalizedNoSpace . '_' . substr(md5(uniqid()), 0, 8);

                    return ListPerusahaanTender::create([
                        'npwp' => null,
                        'npwp_blur' => null,
                        'nama_peserta' => $namaPerusahaan,
                        'normalized_name' => $normalizedName,
                        'partial_npwp' => null,
                        'pembeda' => $uniquePembeda,
                        'alamat' => null,
                    ]);
                } else {
                    throw $e;
                }
            }
        }
    }

    private function maskNpwp($npwp)
    {
        return preg_replace_callback(
            '/^(\d)\d\.(\d)\d\d\.(\d)\d\d\.(\d)-(\d)(\d)(\d)\.(\d)(\d)(\d)$/',
            function ($matches) {
                return "{$matches[1]}*.{$matches[2]}**.{$matches[3]}**.*-*{$matches[6]}{$matches[7]}.**{$matches[10]}";
            },
            $npwp
        );
    }

    private function saveOrUpdatePaketPengadaan(array $item, int $idPenyedia, int $idPelaksana)
    {
        $paket = PaketPengadaan::updateOrCreate(
            ['nomor_paket' => $item['Nomor Paket']],
            [
                'nama_paket' => $item['Nama Paket'] ?? null,
                'pengelola' => $item['Pengelola'] ?? null,
                'satuan_kerja' => $item['Satuan Kerja'] ?? null,
                'instansi_pembeli' => $item['Instansi Pembeli'] ?? null,
                'jenis_katalog' => $item['Jenis Katalog'] ?? null,
                'etalase' => $item['Etalase'] ?? null,
                'tanggal_paket' => $item['Tanggal Paket'] ?? now(),
                'status_paket' => $item['Status Paket'] ?? null,
                'rup_id' => $item['rup_id'] ?? null,
                'status_umkm' => $item['Status UMKM'] ?? null,
                'id_penyedia' => $idPenyedia,
                'id_pelaksana' => $idPelaksana,
            ]
        );

        // Dispatch Sirup notification job if new record
        if ($paket->wasRecentlyCreated) {
            $dataSirup = \App\Models\DataSirup::where('kode_rup', $paket->rup_id)->first();
            if ($dataSirup) {
                \App\Jobs\SendSirupNotifications::dispatch($dataSirup);
            } else {
                \Log::info("DataSirup not found for kode_rup: {$paket->nomor_paket}, skipping notification.");
            }
        }

        return $paket;
    }

    private function saveOrGetProduk(array $item)
    {
        return Produk::updateOrCreate(
            [
                'nama_produk' => $item['Nama Produk'],
                'kategori_lv1' => $item['Kategori lv1'] ?? null,
                'kategori_lv2' => $item['Kategori lv2'] ?? null,
                'nama_manufaktur' => $item['Nama Manufaktur'] ?? null,
                'jenis_produk' => $item['Jenis Produk'] ?? null,

            ],
            [
                'kategori_lv1' => $item['Kategori lv1'] ?? null,
                'kategori_lv2' => $item['Kategori lv2'] ?? null,
                'nama_manufaktur' => $item['Nama Manufaktur'] ?? null,
                'jenis_produk' => $item['Jenis Produk'] ?? null,
            ]
        );
    }

    private function saveTransaksiPaket(array $item, int $idPaket, int $idProduk)
    {
        return TransaksiPaket::updateOrCreate(
            [
                'id_paket' => $idPaket,
                'id_produk' => $idProduk,
                'kuantitas_produk' => $item['Kuantitas Produk'] ?? 0,
                'harga_satuan' => $item['Harga Satuan Produk'] ?? 0,
                'harga_ongkos_kirim' => $item['Harga Ongkos Kirim'] ?? 0,
                'total_harga' => $item['Total Harga Produk'] ?? 0,
            ],
            [
                'kuantitas_produk' => $item['Kuantitas Produk'] ?? 0,
                'harga_satuan' => $item['Harga Satuan Produk'] ?? 0,
                'harga_ongkos_kirim' => $item['Harga Ongkos Kirim'] ?? 0,
                'total_harga' => $item['Total Harga Produk'] ?? 0,
            ]
        );
    }
}