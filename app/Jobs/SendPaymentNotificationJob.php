<?php

namespace App\Jobs;

use App\Models\UserSubscription;
use App\Mail\InvoiceDetailsMail;
use App\Http\Controllers\WhatsAppIntegrationController;
use App\Http\Controllers\TelegramController;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendPaymentNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userSubscription;

    /**
     * Create a new job instance.
     *
     * @param UserSubscription $userSubscription
     * @return void
     */
    public function __construct(UserSubscription $userSubscription)
    {
        $this->userSubscription = $userSubscription;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $userSubscription = $this->userSubscription;
        $user = $userSubscription->user;

        try {
            // Send invoice email
            Mail::to($user->email)->send(new InvoiceDetailsMail($userSubscription));
        } catch (\Exception $e) {
            Log::error('Failed to send invoice email', ['error' => $e->getMessage(), 'user_id' => $user->id]);
        }

        try {
            // Send WhatsApp invoice notification
            $whatsappController = app(WhatsAppIntegrationController::class);
            $whatsappController->sendMessage(new \Illuminate\Http\Request([
                'phone' => $user->phone,
                'templateId' => 'paymentSuccess',
                'data' => [
                    'date' => now()->format('d M Y'),
                    'amount' => number_format($userSubscription->total_amount, 0, ',', '.'),
                    'description' => 'Subscription payment',
                ],
            ]));
        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp notification', ['error' => $e->getMessage(), 'user_id' => $user->id]);
        }

        try {
            // Send Telegram notification
            if ($user && $user->telegram_bot) {
                $telegramController = app(TelegramController::class);
                $message = "Pembayaran paket langganan berhasil:\n";
                $message .= "Nama: {$user->name}\n";
                $message .= "Paket: {$userSubscription->subscription->name}\n";
                $message .= "Tanggal: " . now()->format('d M Y') . "\n";
                $message .= "Jumlah: Rp " . number_format($userSubscription->total_amount, 0, ',', '.') . "\n";
                $telegramController->sendTelegramMessage($user->telegram_bot, $message);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send Telegram notification', ['error' => $e->getMessage(), 'user_id' => $user->id]);
        }
    }
}
