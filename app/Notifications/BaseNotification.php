<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Telegram\TelegramMessage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

abstract class BaseNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        $channels = ['database'];

        if ($notifiable->email) {
            $channels[] = 'mail';
        }

        if ($notifiable->telegram_bot) {
            $channels[] = 'telegram';
        }

        return $channels;
    }

    /**
     * Send WhatsApp notification
     *
     * @param mixed $notifiable
     * @param string $templateId
     * @param array $data
     * @return void
     */
    protected function sendWhatsApp($notifiable, $templateId, $data)
    {
        // Log awal pengiriman WhatsApp
        Log::info("Attempting to send WhatsApp notification", [
            'user_id' => $notifiable->id ?? 'unknown',
            'phone' => $notifiable->phone ?? 'not set',
            'template' => $templateId,
            'data' => $data
        ]);

        if (!$notifiable->phone) {
            Log::warning("WhatsApp notification skipped - no phone number", [
                'user_id' => $notifiable->id ?? 'unknown'
            ]);
            return;
        }

        try {
            $whatsappServiceUrl = config('services.whatsapp.url');
            $apiKey = config('services.whatsapp.api_key');

            // Log konfigurasi WhatsApp
            Log::info("WhatsApp service configuration", [
                'service_url' => $whatsappServiceUrl,
                'api_key_set' => !empty($apiKey)
            ]);

            // Pastikan URL dan API key terkonfigurasi
            if (empty($whatsappServiceUrl) || empty($apiKey)) {
                Log::error("WhatsApp service not properly configured", [
                    'service_url_set' => !empty($whatsappServiceUrl),
                    'api_key_set' => !empty($apiKey)
                ]);
                return;
            }

            // Kirim request ke WhatsApp service
            $response = Http::withHeaders([
                'X-API-KEY' => $apiKey,
                'Accept' => 'application/json',
            ])->post("{$whatsappServiceUrl}/message/send", [
                'phone' => $notifiable->phone,
                'templateId' => $templateId,
                'data' => $data,
            ]);

            // Log response dari WhatsApp service
            if ($response->successful()) {
                Log::info('WhatsApp notification sent successfully', [
                    'user_id' => $notifiable->id,
                    'phone' => $notifiable->phone,
                    'template' => $templateId,
                    'response' => $response->json()
                ]);
            } else {
                Log::error('WhatsApp notification failed', [
                    'user_id' => $notifiable->id,
                    'phone' => $notifiable->phone,
                    'template' => $templateId,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('WhatsApp notification exception', [
                'user_id' => $notifiable->id,
                'phone' => $notifiable->phone,
                'template' => $templateId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
