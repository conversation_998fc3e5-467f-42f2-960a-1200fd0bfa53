<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use NotificationChannels\Telegram\TelegramMessage;
use App\Models\UserSubscription;

class SubscriptionPackageNotification extends BaseNotification
{
    protected $userSubscription;

    /**
     * Create a new notification instance.
     *
     * @param UserSubscription $userSubscription
     * @return void
     */
    public function __construct(UserSubscription $userSubscription)
    {
        $this->userSubscription = $userSubscription;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $subscription = $this->userSubscription->subscription;
        $bankAccounts = $this->getBankAccounts();

        return (new MailMessage)
            ->subject('Detail Paket Langganan TenderID')
            ->greeting('Halo ' . $notifiable->name . '!')
            ->line('Terima kasih telah memilih paket langganan di TenderID.')
            ->line('Be<PERSON>ut adalah detail paket langganan Anda:')
            ->line('Paket: ' . $subscription->name)
            ->line('Durasi: ' . $subscription->duration . ' hari')
            ->line('Total Pembayaran: Rp ' . number_format($this->userSubscription->total_amount, 0, ',', '.'))
            ->line('Silakan lakukan pembayaran ke salah satu rekening berikut:')
            ->line($bankAccounts)
            ->line('Setelah melakukan pembayaran, silakan konfirmasi melalui halaman konfirmasi pembayaran.')
            ->action('Konfirmasi Pembayaran', url('/subscriptions/' . $this->userSubscription->id . '/confirm-payment'))
            ->line('Terima kasih telah menggunakan aplikasi kami!');
    }

    /**
     * Get the Telegram representation of the notification.
     *
     * @param mixed $notifiable
     * @return NotificationChannels\Telegram\TelegramMessage
     */
    public function toTelegram($notifiable)
    {
        $subscription = $this->userSubscription->subscription;
        $bankAccounts = $this->getBankAccounts(true);

        $message = "💼 *Detail Paket Langganan TenderID*\n\n";
        $message .= "Halo {$notifiable->name},\n\n";
        $message .= "Terima kasih telah memilih paket langganan di TenderID.\n\n";
        $message .= "*Detail Paket:*\n";
        $message .= "- Paket: {$subscription->name}\n";
        $message .= "- Durasi: {$subscription->duration} hari\n";
        $message .= "- Total Pembayaran: Rp " . number_format($this->userSubscription->total_amount, 0, ',', '.') . "\n\n";
        $message .= "*Silakan lakukan pembayaran ke:*\n";
        $message .= $bankAccounts . "\n\n";
        $message .= "Setelah melakukan pembayaran, silakan konfirmasi melalui halaman konfirmasi pembayaran.\n";
        $message .= "[Konfirmasi Pembayaran](" . url('/subscriptions/' . $this->userSubscription->id . '/confirm-payment') . ")";

        return TelegramMessage::create()
            ->content($message)
            ->options(['parse_mode' => 'Markdown']);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $subscription = $this->userSubscription->subscription;
        
        return [
            'user_subscription_id' => $this->userSubscription->id,
            'subscription_name' => $subscription->name,
            'duration' => $subscription->duration,
            'total_amount' => $this->userSubscription->total_amount,
            'type' => 'subscription_selected',
        ];
    }

    /**
     * Send WhatsApp notification after the notification is sent.
     *
     * @param mixed $notifiable
     * @param string $channel
     * @param mixed $response
     * @return void
     */
    public function afterSend($notifiable, $channel, $response)
    {
        if ($channel !== 'database') {
            return;
        }

        $subscription = $this->userSubscription->subscription;
        $bankAccounts = $this->getBankAccounts(false, true);

        $this->sendWhatsApp($notifiable, 'subscriptionPackage', [
            'name' => $notifiable->name,
            'package' => $subscription->name,
            'duration' => $subscription->duration,
            'amount' => number_format($this->userSubscription->total_amount, 0, ',', '.'),
            'bank_accounts' => $bankAccounts,
            'invoice_url' => url('/subscriptions/invoice/' . $this->userSubscription->id),
        ]);
    }

    /**
     * Get bank accounts information
     *
     * @param bool $forTelegram
     * @param bool $forWhatsApp
     * @return string
     */
    protected function getBankAccounts($forTelegram = false, $forWhatsApp = false)
    {
        $accounts = [
            [
                'bank' => 'BCA',
                'account_number' => '**********',
                'account_name' => 'Van Ray Hosea Gultom',
            ],

        ];

        if ($forWhatsApp) {
            $result = '';
            foreach ($accounts as $account) {
                $result .= "{$account['bank']} - {$account['account_number']} a.n {$account['account_name']}\n";
            }
            return $result;
        }

        if ($forTelegram) {
            $result = '';
            foreach ($accounts as $account) {
                $result .= "- *{$account['bank']}*: {$account['account_number']} a.n {$account['account_name']}\n";
            }
            return $result;
        }

        $result = '';
        foreach ($accounts as $account) {
            $result .= "- {$account['bank']}: {$account['account_number']} a.n {$account['account_name']}\n";
        }
        return $result;
    }
}
