<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use NotificationChannels\Telegram\TelegramMessage;
use App\Models\Lpse;

class LpseMetricsNotification extends BaseNotification
{
    protected $lpse;
    protected $metrics;

    /**
     * Create a new notification instance.
     *
     * @param Lpse $lpse
     * @param array $metrics
     * @return void
     */
    public function __construct(Lpse $lpse, array $metrics)
    {
        $this->lpse = $lpse;
        $this->metrics = $metrics;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Laporan Metrik LPSE: ' . $this->lpse->nama_lpse)
            ->greeting('Halo ' . $notifiable->name . '!')
            ->line('Berikut adalah laporan metrik terbaru untuk LPSE ' . $this->lpse->nama_lpse . ':')
            ->line('Tender Baru: ' . $this->metrics['new_tenders'])
            ->line('Tender Diperbarui: ' . $this->metrics['updated_tenders'])
            ->line('Total Tender: ' . $this->metrics['total_tenders'])
            ->line('Periode: ' . $this->metrics['period'])
            ->action('Lihat Detail LPSE', url('/lpse/' . $this->lpse->kd_lpse . '/dashboard'))
            ->line('Terima kasih telah menggunakan aplikasi kami!');
    }

    /**
     * Get the Telegram representation of the notification.
     *
     * @param mixed $notifiable
     * @return NotificationChannels\Telegram\TelegramMessage
     */
    public function toTelegram($notifiable)
    {
        $message = "📊 *Laporan Metrik LPSE*\n\n";
        $message .= "*LPSE:* " . $this->lpse->nama_lpse . "\n\n";
        $message .= "*Metrik:*\n";
        $message .= "- Tender Baru: " . $this->metrics['new_tenders'] . "\n";
        $message .= "- Tender Diperbarui: " . $this->metrics['updated_tenders'] . "\n";
        $message .= "- Total Tender: " . $this->metrics['total_tenders'] . "\n";
        $message .= "- Periode: " . $this->metrics['period'] . "\n\n";
        $message .= "[Lihat Detail LPSE](" . url('/lpse/' . $this->lpse->kd_lpse . '/dashboard') . ")";

        return TelegramMessage::create()
            ->content($message)
            ->options(['parse_mode' => 'Markdown']);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'lpse_id' => $this->lpse->kd_lpse,
            'lpse_name' => $this->lpse->nama_lpse,
            'new_tenders' => $this->metrics['new_tenders'],
            'updated_tenders' => $this->metrics['updated_tenders'],
            'total_tenders' => $this->metrics['total_tenders'],
            'period' => $this->metrics['period'],
            'type' => 'lpse_metrics',
        ];
    }

    /**
     * Send WhatsApp notification after the notification is sent.
     *
     * @param mixed $notifiable
     * @param string $channel
     * @param mixed $response
     * @return void
     */
    public function afterSend($notifiable, $channel, $response)
    {
        if ($channel !== 'database') {
            return;
        }
        
        $this->sendWhatsApp($notifiable, 'lpseMetrics', [
            'name' => $notifiable->name,
            'lpse_name' => $this->lpse->nama_lpse,
            'new_tenders' => $this->metrics['new_tenders'],
            'updated_tenders' => $this->metrics['updated_tenders'],
            'total_tenders' => $this->metrics['total_tenders'],
            'period' => $this->metrics['period'],
            'url' => url('/lpse/' . $this->lpse->kd_lpse . '/dashboard'),
        ]);
    }
}
