<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use NotificationChannels\Telegram\TelegramMessage;
use App\Models\DataSirup;

class SirupNotification extends BaseNotification
{
    protected $sirup;

    public function __construct(DataSirup $sirup)
    {
        $this->sirup = $sirup;
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Notifikasi SIRUP: ' . $this->sirup->nama_paket)
            ->line('Ada tender atau paket pengadaan baru dengan kode SIRUP: ' . $this->sirup->kode_rup)
            ->line('Nama Paket: ' . $this->sirup->nama_paket)
            ->action('Lihat Detail SIRUP', url('/sirup/' . $this->sirup->id))
            ->line('Terima kasih telah menggunakan aplikasi kami!');
    }

    public function toTelegram($notifiable)
    {
        $message = "📢 *Notifikasi SIRUP*\n";
        $message .= "*Kode RUP:* " . $this->sirup->kode_rup . "\n";
        $message .= "*Nama Paket:* " . $this->sirup->nama_paket . "\n";
        $message .= "[Lihat Detail SIRUP](" . url('/sirup/' . $this->sirup->id) . ")\n";

        return TelegramMessage::create()
            ->content($message)
            ->options(['parse_mode' => 'Markdown']);
    }

    public function toArray($notifiable)
    {
        return [
            'sirup_id' => $this->sirup->id,
            'kode_rup' => $this->sirup->kode_rup,
            'nama_paket' => $this->sirup->nama_paket,
            'type' => 'sirup_notification',
        ];
    }

    /**
     * Send WhatsApp notification after the notification is sent.
     *
     * @param mixed $notifiable
     * @param string $channel
     * @param mixed $response
     * @return void
     */
    public function afterSend($notifiable, $channel, $response)
    {
        if ($channel !== 'database') {
            return;
        }

        $this->sendWhatsApp($notifiable, 'sirupNotification', [
            'name' => $notifiable->name,
            'kode_rup' => $this->sirup->kode_rup,
            'nama_paket' => $this->sirup->nama_paket,
            'url' => url('/sirup/' . $this->sirup->id),
        ]);
    }
}
