<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use NotificationChannels\Telegram\TelegramMessage;
use App\Models\TenderLpse;

class TenderLpseStatusNotification extends BaseNotification
{
    protected $tender;

    public function __construct(TenderLpse $tender)
    {
        $this->tender = $tender;
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Update Tender ' . $this->tender->nama_paket.'')
            ->line('Status tender ' . $this->tender->nama_paket . ' telah berubah.')
            ->line('Status saat ini: ' . $this->tender->status_tender)
            ->line('Tahap tender: ' . $this->tender->tahap_tender)
            ->action('Lihat Detail Tender', url('/tender/' . $this->tender->kode_tender))
            ->line('Terima kasih telah menggunakan aplikasi kami!');
    }

    public function toTelegram($notifiable)
    {
        $message = "📢 *Update Status Tender LPSE*\n";
        $message .= "*Nama Paket:* " . $this->tender->nama_paket . "\n";
        $message .= "*Status:* " . $this->tender->status_tender . "\n";
        $message .= "*Tahap:* " . $this->tender->tahap_tender . "\n";

        // Add jadwal details if available
        $jadwalText = "";
        if ($this->tender->jadwal && $this->tender->jadwal->count() > 0) {
            $jadwalText .= "\n*Jadwal Tender:*\n";
            foreach ($this->tender->jadwal as $jadwal) {
                $jadwalText .= "- " . $jadwal->tahap . ": " .
                    $jadwal->tanggal_mulai->format('d M Y H:i') . " s/d " .
                    $jadwal->tanggal_selesai->format('d M Y H:i') . "\n";
            }
        }
        $message .= $jadwalText;

        $message .= "[Lihat Detail Tender](" . url('/tender/' . $this->tender->kode_tender) . ")\n";

        return TelegramMessage::create()
            ->content($message)
            ->options(['parse_mode' => 'Markdown']);
    }

    public function toArray($notifiable)
    {
        return [
            'tender_id' => $this->tender->kode_tender,
            'nama_paket' => $this->tender->nama_paket,
            'status_tender' => $this->tender->status_tender,
            'tahap_tender' => $this->tender->tahap_tender,
            'type' => 'tender_status_update',
        ];
    }

    /**
     * Send WhatsApp notification after the notification is sent.
     *
     * @param mixed $notifiable
     * @param string $channel
     * @param mixed $response
     * @return void
     */
    public function afterSend($notifiable, $channel, $response)
    {
        if ($channel !== 'database') {
            return;
        }

        // Prepare jadwal text for WhatsApp
        $jadwalText = "";
        if ($this->tender->jadwal && $this->tender->jadwal->count() > 0) {
            $jadwalText = "\nJadwal Tender:\n";
            foreach ($this->tender->jadwal as $jadwal) {
                $jadwalText .= "- " . $jadwal->tahap . ": " .
                    $jadwal->tanggal_mulai->format('d M Y H:i') . " s/d " .
                    $jadwal->tanggal_selesai->format('d M Y H:i') . "\n";
            }
        }

        $this->sendWhatsApp($notifiable, 'tenderUpdate', [
            'name' => $notifiable->name,
            'tender_name' => $this->tender->nama_paket,
            'status' => $this->tender->status_tender,
            'tahap' => $this->tender->tahap_tender,
            'jadwal' => $jadwalText,
            'url' => url('/tender/' . $this->tender->kode_tender),
        ]);
    }
}