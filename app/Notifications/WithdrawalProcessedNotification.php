<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use NotificationChannels\Telegram\TelegramMessage;
use App\Models\Withdrawal;

class WithdrawalProcessedNotification extends BaseNotification
{
    protected $withdrawal;

    /**
     * Create a new notification instance.
     *
     * @param Withdrawal $withdrawal
     * @return void
     */
    public function __construct(Withdrawal $withdrawal)
    {
        $this->withdrawal = $withdrawal;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $status = $this->withdrawal->status;
        $mailMessage = (new MailMessage)
            ->subject('Update Status Penarikan Dana');

        if ($status === 'approved') {
            $mailMessage
                ->greeting('Halo ' . $notifiable->name . '!')
                ->line('Permintaan penarikan dana Anda telah disetujui dan diproses.')
                ->line('Detail penarikan:')
                ->line('Jumlah: Rp ' . number_format($this->withdrawal->amount, 0, ',', '.'))
                ->line('Bank: ' . $this->withdrawal->bank_name)
                ->line('Nomor Rekening: ' . $this->withdrawal->account_number)
                ->line('Nama Pemilik Rekening: ' . $this->withdrawal->account_name)
                ->line('Status: Disetujui')
                ->line('Tanggal Diproses: ' . $this->withdrawal->updated_at->format('d M Y H:i'))
                ->line('Dana telah ditransfer ke rekening Anda. Mohon periksa rekening Anda dalam 1-2 hari kerja.')
                ->action('Lihat Riwayat Penarikan', url('/withdrawals/history'))
                ->line('Terima kasih telah menggunakan aplikasi kami!');
        } else {
            $mailMessage
                ->greeting('Halo ' . $notifiable->name . '!')
                ->line('Mohon maaf, permintaan penarikan dana Anda tidak dapat disetujui.')
                ->line('Detail penarikan:')
                ->line('Jumlah: Rp ' . number_format($this->withdrawal->amount, 0, ',', '.'))
                ->line('Bank: ' . $this->withdrawal->bank_name)
                ->line('Nomor Rekening: ' . $this->withdrawal->account_number)
                ->line('Nama Pemilik Rekening: ' . $this->withdrawal->account_name)
                ->line('Status: Ditolak')
                ->line('Tanggal Diproses: ' . $this->withdrawal->updated_at->format('d M Y H:i'))
                ->line('Alasan: ' . ($this->withdrawal->notes ?? 'Tidak ada alasan yang diberikan.'))
                ->line('Jika Anda memiliki pertanyaan, silakan hubungi tim dukungan kami.')
                ->action('Lihat Riwayat Penarikan', url('/withdrawals/history'))
                ->line('Terima kasih telah menggunakan aplikasi kami!');
        }

        return $mailMessage;
    }

    /**
     * Get the Telegram representation of the notification.
     *
     * @param mixed $notifiable
     * @return NotificationChannels\Telegram\TelegramMessage
     */
    public function toTelegram($notifiable)
    {
        $status = $this->withdrawal->status;
        
        if ($status === 'approved') {
            $message = "✅ *Penarikan Dana Disetujui*\n\n";
            $message .= "Halo {$notifiable->name},\n\n";
            $message .= "Permintaan penarikan dana Anda telah disetujui dan diproses.\n\n";
            $message .= "*Detail Penarikan:*\n";
            $message .= "- Jumlah: Rp " . number_format($this->withdrawal->amount, 0, ',', '.') . "\n";
            $message .= "- Bank: {$this->withdrawal->bank_name}\n";
            $message .= "- Nomor Rekening: {$this->withdrawal->account_number}\n";
            $message .= "- Nama Pemilik: {$this->withdrawal->account_name}\n";
            $message .= "- Status: Disetujui\n";
            $message .= "- Tanggal Diproses: " . $this->withdrawal->updated_at->format('d M Y H:i') . "\n\n";
            $message .= "Dana telah ditransfer ke rekening Anda. Mohon periksa rekening Anda dalam 1-2 hari kerja.\n";
            $message .= "[Lihat Riwayat Penarikan](" . url('/withdrawals/history') . ")";
        } else {
            $message = "❌ *Penarikan Dana Ditolak*\n\n";
            $message .= "Halo {$notifiable->name},\n\n";
            $message .= "Mohon maaf, permintaan penarikan dana Anda tidak dapat disetujui.\n\n";
            $message .= "*Detail Penarikan:*\n";
            $message .= "- Jumlah: Rp " . number_format($this->withdrawal->amount, 0, ',', '.') . "\n";
            $message .= "- Bank: {$this->withdrawal->bank_name}\n";
            $message .= "- Nomor Rekening: {$this->withdrawal->account_number}\n";
            $message .= "- Nama Pemilik: {$this->withdrawal->account_name}\n";
            $message .= "- Status: Ditolak\n";
            $message .= "- Tanggal Diproses: " . $this->withdrawal->updated_at->format('d M Y H:i') . "\n";
            $message .= "- Alasan: " . ($this->withdrawal->notes ?? 'Tidak ada alasan yang diberikan.') . "\n\n";
            $message .= "Jika Anda memiliki pertanyaan, silakan hubungi tim dukungan kami.\n";
            $message .= "[Lihat Riwayat Penarikan](" . url('/withdrawals/history') . ")";
        }

        return TelegramMessage::create()
            ->content($message)
            ->options(['parse_mode' => 'Markdown']);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'withdrawal_id' => $this->withdrawal->id,
            'amount' => $this->withdrawal->amount,
            'bank_name' => $this->withdrawal->bank_name,
            'account_number' => $this->withdrawal->account_number,
            'account_name' => $this->withdrawal->account_name,
            'status' => $this->withdrawal->status,
            'notes' => $this->withdrawal->notes,
            'type' => 'withdrawal_processed',
        ];
    }

    /**
     * Send WhatsApp notification after the notification is sent.
     *
     * @param mixed $notifiable
     * @param string $channel
     * @param mixed $response
     * @return void
     */
    public function afterSend($notifiable, $channel, $response)
    {
        if ($channel !== 'database') {
            return;
        }

        $status = $this->withdrawal->status;
        $templateId = $status === 'approved' ? 'withdrawalSuccess' : 'withdrawalFailed';
        
        $this->sendWhatsApp($notifiable, $templateId, [
            'name' => $notifiable->name,
            'amount' => number_format($this->withdrawal->amount, 0, ',', '.'),
            'bank' => $this->withdrawal->bank_name,
            'account' => $this->withdrawal->account_number,
            'date' => $this->withdrawal->updated_at->format('d M Y H:i'),
            'reason' => $status === 'approved' ? '' : ($this->withdrawal->notes ?? 'Tidak ada alasan yang diberikan.'),
        ]);
    }
}
