<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use NotificationChannels\Telegram\TelegramMessage;
use App\Models\Withdrawal;

class WithdrawalRequestNotification extends BaseNotification
{
    protected $withdrawal;
    protected $isForAdmin;

    /**
     * Create a new notification instance.
     *
     * @param Withdrawal $withdrawal
     * @param bool $isForAdmin
     * @return void
     */
    public function __construct(Withdrawal $withdrawal, bool $isForAdmin = false)
    {
        $this->withdrawal = $withdrawal;
        $this->isForAdmin = $isForAdmin;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        if ($this->isForAdmin) {
            return $this->toAdminMail($notifiable);
        }

        return (new MailMessage)
            ->subject('Permintaan Penarikan Dana')
            ->greeting('Halo ' . $notifiable->name . '!')
            ->line('Permintaan penarikan dana Anda telah berhasil diajukan.')
            ->line('Detail penarikan:')
            ->line('Jumlah: Rp ' . number_format($this->withdrawal->amount, 0, ',', '.'))
            ->line('Bank: ' . $this->withdrawal->bank_name)
            ->line('Nomor Rekening: ' . $this->withdrawal->account_number)
            ->line('Nama Pemilik Rekening: ' . $this->withdrawal->account_name)
            ->line('Status: Menunggu Persetujuan')
            ->line('Tanggal Pengajuan: ' . $this->withdrawal->created_at->format('d M Y H:i'))
            ->line('Kami akan memproses permintaan Anda segera.')
            ->action('Lihat Riwayat Penarikan', url('/withdrawals/history'))
            ->line('Terima kasih telah menggunakan aplikasi kami!');
    }

    /**
     * Get admin mail notification
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    protected function toAdminMail($notifiable)
    {
        $user = $this->withdrawal->user;

        return (new MailMessage)
            ->subject('Permintaan Penarikan Dana Baru')
            ->greeting('Halo Admin!')
            ->line('Ada permintaan penarikan dana baru yang perlu diproses.')
            ->line('Detail penarikan:')
            ->line('User: ' . $user->name . ' (' . $user->email . ')')
            ->line('Jumlah: Rp ' . number_format($this->withdrawal->amount, 0, ',', '.'))
            ->line('Bank: ' . $this->withdrawal->bank_name)
            ->line('Nomor Rekening: ' . $this->withdrawal->account_number)
            ->line('Nama Pemilik Rekening: ' . $this->withdrawal->account_name)
            ->line('Tanggal Pengajuan: ' . $this->withdrawal->created_at->format('d M Y H:i'))
            ->line('Silakan proses permintaan ini melalui panel admin.')
            ->action('Proses Penarikan', url('/admin/withdrawals'))
            ->line('Terima kasih!');
    }

    /**
     * Get the Telegram representation of the notification.
     *
     * @param mixed $notifiable
     * @return NotificationChannels\Telegram\TelegramMessage
     */
    public function toTelegram($notifiable)
    {
        if ($this->isForAdmin) {
            return $this->toAdminTelegram($notifiable);
        }

        $message = "💸 *Permintaan Penarikan Dana*\n\n";
        $message .= "Halo {$notifiable->name},\n\n";
        $message .= "Permintaan penarikan dana Anda telah berhasil diajukan.\n\n";
        $message .= "*Detail Penarikan:*\n";
        $message .= "- Jumlah: Rp " . number_format($this->withdrawal->amount, 0, ',', '.') . "\n";
        $message .= "- Bank: {$this->withdrawal->bank_name}\n";
        $message .= "- Nomor Rekening: {$this->withdrawal->account_number}\n";
        $message .= "- Nama Pemilik: {$this->withdrawal->account_name}\n";
        $message .= "- Status: Menunggu Persetujuan\n";
        $message .= "- Tanggal Pengajuan: " . $this->withdrawal->created_at->format('d M Y H:i') . "\n\n";
        $message .= "Kami akan memproses permintaan Anda segera.\n";
        $message .= "[Lihat Riwayat Penarikan](" . url('/withdrawals/history') . ")";

        return TelegramMessage::create()
            ->content($message)
            ->options(['parse_mode' => 'Markdown']);
    }

    /**
     * Get admin Telegram notification
     *
     * @param mixed $notifiable
     * @return NotificationChannels\Telegram\TelegramMessage
     */
    protected function toAdminTelegram($notifiable)
    {
        $user = $this->withdrawal->user;

        $message = "💸 *Permintaan Penarikan Dana Baru*\n\n";
        $message .= "*Detail Penarikan:*\n";
        $message .= "- User: {$user->name} ({$user->email})\n";
        $message .= "- Jumlah: Rp " . number_format($this->withdrawal->amount, 0, ',', '.') . "\n";
        $message .= "- Bank: {$this->withdrawal->bank_name}\n";
        $message .= "- Nomor Rekening: {$this->withdrawal->account_number}\n";
        $message .= "- Nama Pemilik: {$this->withdrawal->account_name}\n";
        $message .= "- Tanggal Pengajuan: " . $this->withdrawal->created_at->format('d M Y H:i') . "\n\n";
        $message .= "Silakan proses permintaan ini melalui panel admin.\n";
        $message .= "[Proses Penarikan](" . url('/admin/withdrawals') . ")";

        return TelegramMessage::create()
            ->content($message)
            ->options(['parse_mode' => 'Markdown']);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'withdrawal_id' => $this->withdrawal->id,
            'amount' => $this->withdrawal->amount,
            'bank_name' => $this->withdrawal->bank_name,
            'account_number' => $this->withdrawal->account_number,
            'account_name' => $this->withdrawal->account_name,
            'type' => $this->isForAdmin ? 'admin_withdrawal_request' : 'withdrawal_request',
        ];
    }

    /**
     * Send WhatsApp notification after the notification is sent.
     *
     * @param mixed $notifiable
     * @param string $channel
     * @param mixed $response
     * @return void
     */
    public function afterSend($notifiable, $channel, $response)
    {
        if ($channel !== 'database') {
            return;
        }

        if ($this->isForAdmin) {
            $user = $this->withdrawal->user;
            
            $this->sendWhatsApp($notifiable, 'adminWithdrawalRequest', [
                'user_name' => $user->name,
                'user_email' => $user->email,
                'amount' => number_format($this->withdrawal->amount, 0, ',', '.'),
                'bank' => $this->withdrawal->bank_name,
                'account' => $this->withdrawal->account_number,
                'account_name' => $this->withdrawal->account_name,
                'date' => $this->withdrawal->created_at->format('d M Y H:i'),
            ]);
        } else {
            $this->sendWhatsApp($notifiable, 'withdrawalRequest', [
                'name' => $notifiable->name,
                'amount' => number_format($this->withdrawal->amount, 0, ',', '.'),
                'bank' => $this->withdrawal->bank_name,
                'account' => $this->withdrawal->account_number,
                'date' => $this->withdrawal->created_at->format('d M Y H:i'),
            ]);
        }
    }
}
