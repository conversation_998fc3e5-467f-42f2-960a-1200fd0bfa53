<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use NotificationChannels\Telegram\TelegramMessage;
use App\Models\UserSubscription;

class PaymentConfirmationNotification extends BaseNotification
{
    protected $userSubscription;

    /**
     * Create a new notification instance.
     *
     * @param UserSubscription $userSubscription
     * @return void
     */
    public function __construct(UserSubscription $userSubscription)
    {
        $this->userSubscription = $userSubscription;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $subscription = $this->userSubscription->subscription;
        $user = $this->userSubscription->user;

        return (new MailMessage)
            ->subject('Konfirmasi Pembayaran Paket Langganan')
            ->greeting('Halo Admin!')
            ->line('User telah melakukan konfirmasi pembayaran untuk paket langganan.')
            ->line('Detail pembayaran:')
            ->line('User: ' . $user->name . ' (' . $user->email . ')')
            ->line('Paket: ' . $subscription->name)
            ->line('Durasi: ' . $subscription->duration . ' hari')
            ->line('Total Pembayaran: Rp ' . number_format($this->userSubscription->total_amount, 0, ',', '.'))
            ->line('Tanggal Konfirmasi: ' . now()->format('d M Y H:i'))
            ->line('Metode Pembayaran: ' . $this->userSubscription->payment_method)
            ->line('Silakan verifikasi pembayaran ini melalui panel admin.')
            ->action('Verifikasi Pembayaran', url('/admin/user-subscriptions'))
            ->line('Terima kasih!');
    }

    /**
     * Get the Telegram representation of the notification.
     *
     * @param mixed $notifiable
     * @return NotificationChannels\Telegram\TelegramMessage
     */
    public function toTelegram($notifiable)
    {
        $subscription = $this->userSubscription->subscription;
        $user = $this->userSubscription->user;

        $message = "💰 *Konfirmasi Pembayaran Paket Langganan*\n\n";
        $message .= "*Detail Pembayaran:*\n";
        $message .= "- User: {$user->name} ({$user->email})\n";
        $message .= "- Paket: {$subscription->name}\n";
        $message .= "- Durasi: {$subscription->duration} hari\n";
        $message .= "- Total: Rp " . number_format($this->userSubscription->total_amount, 0, ',', '.') . "\n";
        $message .= "- Tanggal Konfirmasi: " . now()->format('d M Y H:i') . "\n";
        $message .= "- Metode Pembayaran: {$this->userSubscription->payment_method}\n\n";
        $message .= "Silakan verifikasi pembayaran ini melalui panel admin.\n";
        $message .= "[Verifikasi Pembayaran](" . url('/admin/user-subscriptions') . ")";

        return TelegramMessage::create()
            ->content($message)
            ->options(['parse_mode' => 'Markdown']);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $subscription = $this->userSubscription->subscription;
        $user = $this->userSubscription->user;

        return [
            'user_subscription_id' => $this->userSubscription->id,
            'user_id' => $user->id,
            'user_name' => $user->name,
            'user_email' => $user->email,
            'subscription_name' => $subscription->name,
            'duration' => $subscription->duration,
            'total_amount' => $this->userSubscription->total_amount,
            'payment_method' => $this->userSubscription->payment_method,
            'type' => 'payment_confirmation',
        ];
    }

    /**
     * Send WhatsApp notification after the notification is sent.
     *
     * @param mixed $notifiable
     * @param string $channel
     * @param mixed $response
     * @return void
     */
    public function afterSend($notifiable, $channel, $response)
    {
        // Kirim notifikasi WhatsApp terlepas dari channel yang digunakan
        // Ini memastikan WhatsApp dikirim bahkan jika channel lain gagal

        $subscription = $this->userSubscription->subscription;
        $user = $this->userSubscription->user;

        // Kirim ke admin (notifiable)
        $this->sendWhatsApp($notifiable, 'paymentConfirmation', [
            'user_name' => $user->name,
            'user_email' => $user->email,
            'package' => $subscription->name,
            'amount' => number_format($this->userSubscription->total_amount, 0, ',', '.'),
            'date' => now()->format('d M Y H:i'),
            'payment_method' => $this->userSubscription->payment_method,
        ]);

        // Log pengiriman notifikasi WhatsApp
        \Illuminate\Support\Facades\Log::info("WhatsApp notification attempted for payment confirmation", [
            'admin_id' => $notifiable->id,
            'admin_phone' => $notifiable->phone,
            'subscription_id' => $this->userSubscription->id,
            'user_id' => $user->id
        ]);
    }
}
