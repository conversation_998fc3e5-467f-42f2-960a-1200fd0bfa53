<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use NotificationChannels\Telegram\TelegramMessage;
use App\Models\User;

class UserRegistrationNotification extends BaseNotification
{
    protected $user;
    protected $isForAdmin;

    /**
     * Create a new notification instance.
     *
     * @param User $user
     * @param bool $isForAdmin
     * @return void
     */
    public function __construct(User $user, bool $isForAdmin = false)
    {
        $this->user = $user;
        $this->isForAdmin = $isForAdmin;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        if ($this->isForAdmin) {
            return $this->toAdminMail($notifiable);
        }

        $mailMessage = (new MailMessage)
            ->subject('Selamat Datang di TenderID')
            ->greeting('Halo ' . $this->user->name . '!')
            ->line('<PERSON><PERSON> kasih telah mendaftar di TenderID.')
            ->line('Akun Anda telah berhasil dibuat dan siap digunakan.')
            ->line('<PERSON>lakan verifikasi email Anda untuk mengakses semua fitur aplikasi.')
            ->action('Verifikasi Email', url('/email/verify/' . $this->user->id . '/' . sha1($this->user->email)))
            ->line('Jika Anda memiliki pertanyaan, jangan ragu untuk menghubungi tim dukungan kami.');

        return $mailMessage;
    }

    /**
     * Get admin mail notification
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    protected function toAdminMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Pendaftaran User Baru di TenderID')
            ->greeting('Halo Admin!')
            ->line('User baru telah mendaftar di TenderID.')
            ->line('Detail user:')
            ->line('Nama: ' . $this->user->name)
            ->line('Email: ' . $this->user->email)
            ->line('Perusahaan: ' . ($this->user->company_name ?? '-'))
            ->line('NPWP: ' . ($this->user->npwp ?? '-'))
            ->action('Lihat Detail User', url('/admin/users/' . $this->user->id))
            ->line('Terima kasih telah menggunakan aplikasi kami!');
    }

    /**
     * Get the Telegram representation of the notification.
     *
     * @param mixed $notifiable
     * @return NotificationChannels\Telegram\TelegramMessage
     */
    public function toTelegram($notifiable)
    {
        if ($this->isForAdmin) {
            return $this->toAdminTelegram($notifiable);
        }

        $message = "👋 *Selamat Datang di TenderID!*\n\n";
        $message .= "Halo {$this->user->name},\n\n";
        $message .= "Terima kasih telah mendaftar di TenderID. Akun Anda telah berhasil dibuat dan siap digunakan.\n\n";
        $message .= "Silakan verifikasi email Anda untuk mengakses semua fitur aplikasi.\n\n";
        $message .= "Jika Anda memiliki pertanyaan, jangan ragu untuk menghubungi tim dukungan kami.";

        return TelegramMessage::create()
            ->content($message)
            ->options(['parse_mode' => 'Markdown']);
    }

    /**
     * Get admin Telegram notification
     *
     * @param mixed $notifiable
     * @return NotificationChannels\Telegram\TelegramMessage
     */
    protected function toAdminTelegram($notifiable)
    {
        $message = "🔔 *User Baru Terdaftar*\n\n";
        $message .= "*Nama:* {$this->user->name}\n";
        $message .= "*Email:* {$this->user->email}\n";
        $message .= "*Perusahaan:* " . ($this->user->company_name ?? '-') . "\n";
        $message .= "*NPWP:* " . ($this->user->npwp ?? '-') . "\n";
        $message .= "*Tanggal Daftar:* " . $this->user->created_at->format('d M Y H:i') . "\n";
        $message .= "[Lihat Detail User](" . url('/admin/users/' . $this->user->id) . ")";

        return TelegramMessage::create()
            ->content($message)
            ->options(['parse_mode' => 'Markdown']);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'user_id' => $this->user->id,
            'name' => $this->user->name,
            'email' => $this->user->email,
            'type' => $this->isForAdmin ? 'admin_notification' : 'welcome',
        ];
    }

    /**
     * Send WhatsApp notification after the notification is sent.
     *
     * @param mixed $notifiable
     * @param string $channel
     * @param mixed $response
     * @return void
     */
    public function afterSend($notifiable, $channel, $response)
    {
        if ($channel !== 'database') {
            return;
        }

        if ($this->isForAdmin) {
            $this->sendWhatsApp($notifiable, 'adminNewUser', [
                'name' => $this->user->name,
                'email' => $this->user->email,
                'company' => $this->user->company_name ?? '-',
                'date' => $this->user->created_at->format('d M Y H:i'),
            ]);
        } else {
            $this->sendWhatsApp($notifiable, 'registration', [
                'name' => $this->user->name,
            ]);
        }
    }
}
