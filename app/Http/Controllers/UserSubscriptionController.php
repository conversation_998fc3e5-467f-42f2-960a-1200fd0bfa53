<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Models\UserSubscription;
use App\Models\Payment;
use App\Models\Referral;
use App\Models\Commission;
use App\Models\Withdrawal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use App\Helpers\NotificationHelper;

class UserSubscriptionController extends Controller
{
    use AuthorizesRequests;
    public function __construct()
    {
        $this->middleware('auth');
    }

    // Show available subscription plans
    public function index()
    {
        $subscriptions = Subscription::all();
        return view('subscriptions.index', compact('subscriptions'));
    }

    // Show user's current subscriptions and referral info
    public function mySubscriptions()
    {
        $user = Auth::user();
        $userSubscriptions = UserSubscription::with('subscription')->where('user_id', $user->id)->get();
        $referrals = Referral::where('referrer_id', $user->id)->with('referred')->get();
        $commissionBalance = $user->commission_balance;

        return view('subscriptions.my_subscriptions', compact('userSubscriptions', 'referrals', 'commissionBalance'));
    }

    // Show form to confirm payment for a subscription
    public function confirmPaymentForm($userSubscriptionId)
    {
        $userSubscription = UserSubscription::findOrFail($userSubscriptionId);
        // $this->authorize('update', $userSubscription);

        $now = \Carbon\Carbon::now();
        $paymentOpenDate = \Carbon\Carbon::parse($userSubscription->end_date)->subWeeks(2);

        if ($now->lt($paymentOpenDate)) {
            return redirect()->route('subscriptions.select')->with('error', 'Payment page will be available 2 weeks before subscription expires.');
        }

        return view('payments.confirm_payment', compact('userSubscription'));
    }

    // Handle payment confirmation submission
    public function confirmPayment(Request $request, $userSubscriptionId)
    {
        $userSubscription = UserSubscription::findOrFail($userSubscriptionId);
        // $this->authorize('update', $userSubscription);

        $request->validate([
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|string|max:255',
            'confirmation_proof' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:2048',
        ]);

        $paymentData = $request->only(['amount', 'payment_method']);
        $paymentData['user_id'] = Auth::id();
        $paymentData['user_subscription_id'] = $userSubscription->id;
        $paymentData['status'] = 'pending';
        $paymentData['confirmation_status'] = 'unconfirmed';

        if ($request->hasFile('confirmation_proof')) {
            $path = $request->file('confirmation_proof')->store('payment_proofs', 'public');
            $paymentData['confirmation_proof'] = $path;
        }

        Payment::create($paymentData);

        // Send notification to admin about payment confirmation
        try {
            // Reload the user subscription to ensure we have the latest data
            $userSubscription = UserSubscription::with('user', 'subscription')->findOrFail($userSubscriptionId);

            // Send notification
            NotificationHelper::sendPaymentConfirmationNotifications($userSubscription);

            Log::info("Payment confirmation notification sent for subscription ID: {$userSubscription->id}");
        } catch (\Exception $e) {
            Log::error("Failed to send payment confirmation notification: {$e->getMessage()}", [
                'subscription_id' => $userSubscription->id,
                'user_id' => Auth::id()
            ]);
        }

        return redirect()->route('payments.confirm.form', $userSubscription->id)->with('success', 'Payment confirmation submitted successfully.');
    }

    // Show withdrawal request form
    public function withdrawalForm()
    {
        $user = Auth::user();
        $commissionBalance = $user->commission_balance;
        return view('withdrawals.request', compact('commissionBalance'));
    }

    public function withdrawalHistory()
    {
        $user = Auth::user();
        $withdrawals = $user->withdrawals()
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('withdrawals.history', compact('withdrawals'));
    }

    public function commissionHistory(Request $request)
    {
        $user = Auth::user();
        $query = $user->commissions()
            ->with(['referred', 'payment.userSubscription.subscription'])
            ->orderBy('created_at', 'desc');

        // Filter by search (name or email of referred user)
        if ($request->has('search') && !empty($request->search)) {
            $query->whereHas('referred', function($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                  ->orWhere('email', 'like', "%{$request->search}%");
            });
        }

        // Filter by period
        if ($request->has('period') && !empty($request->period)) {
            switch ($request->period) {
                case 'this_month':
                    $query->whereMonth('created_at', now()->month)
                          ->whereYear('created_at', now()->year);
                    break;
                case 'last_month':
                    $lastMonth = now()->subMonth();
                    $query->whereMonth('created_at', $lastMonth->month)
                          ->whereYear('created_at', $lastMonth->year);
                    break;
                case 'this_year':
                    $query->whereYear('created_at', now()->year);
                    break;
            }
        }

        // Filter by status
        if ($request->has('status') && !empty($request->status)) {
            if ($request->status === 'confirmed') {
                $query->whereHas('payment', function($q) {
                    $q->where('status', 'confirmed');
                });
            } elseif ($request->status === 'pending') {
                $query->whereHas('payment', function($q) {
                    $q->where('status', 'pending');
                });
            }
        }

        $commissions = $query->paginate(10);

        // Append query parameters to pagination links
        $commissions->appends($request->all());

        return view('commissions.history', compact('commissions'));
    }

    // Show subscription package selection page or active subscription details
    public function selectSubscription()
    {
        $user = auth()->user();

        // Check if user has a pending subscription
        $pendingSubscription = UserSubscription::where('user_id', $user->id)
            ->where('status', 'pending')
            ->first();

        if ($pendingSubscription) {
            // Redirect to invoice page for the pending subscription
            return redirect()->route('subscriptions.invoice.show', ['userSubscription' => $pendingSubscription->id]);
        }

        $activeSubscription = UserSubscription::where('user_id', $user->id)
            ->where('status', 'active')
            ->where('end_date', '>=', now())
            ->first();

        if ($activeSubscription) {
            return view('subscriptions.select', [
                'activeSubscription' => $activeSubscription,
                'subscriptions' => null,
            ]);
        } else {
            $subscriptions = Subscription::where('is_active', true)->get();
            return view('subscriptions.select', [
                'activeSubscription' => null,
                'subscriptions' => $subscriptions,
            ]);
        }
    }

    // Generate invoice with unique code and show invoice page
    public function generateInvoice(Request $request)
    {
        $request->validate([
            'subscription_id' => 'required|exists:subscriptions,id',
        ]);

        $subscription = Subscription::findOrFail($request->subscription_id);

        // Generate unique random code between 100 and 1000
        $uniqueCode = rand(100, 1000);

        // Calculate total amount with unique code added
        $totalAmount = $subscription->price + $uniqueCode;

        // Create a new UserSubscription record with status pending
        $userSubscription = new UserSubscription();
        $userSubscription->user_id = Auth::id();
        $userSubscription->subscription_id = $subscription->id;
        $userSubscription->start_date = now();
        $userSubscription->end_date = now()->addDays($subscription->duration_days);
        $userSubscription->status = 'pending';
        $userSubscription->unique_code = $uniqueCode;
        $userSubscription->total_amount = $totalAmount;
        $userSubscription->save();

        return redirect()->route('subscriptions.invoice.show', $userSubscription->id);
    }

    // Show invoice page for existing user subscription (GET)
    public function showInvoice(UserSubscription $userSubscription)
    {
        // Use policy check if exists, otherwise remove or replace with manual check
        // For now, remove authorize call to avoid error
        // $this->authorize('view', $userSubscription);
        return view('subscriptions.invoice', compact('userSubscription'));
    }

    // Export invoice to PDF
    public function exportInvoicePdf(UserSubscription $userSubscription)
    {
        $pdf = Pdf::loadView('subscriptions.print_invoice', compact('userSubscription'));
        return $pdf->download("invoice_{$userSubscription->id}.pdf");
    }

    // Show print-friendly invoice page
    public function showPrintInvoice(UserSubscription $userSubscription)
    {
        return view('subscriptions.print_invoice', compact('userSubscription'));
    }

    // Show all user subscriptions for Super Admin
    public function adminIndex(Request $request)
    {
        $query = UserSubscription::with('user', 'subscription', 'payments');

        // Filter by user (name or email)
        if ($request->has('user') && !empty($request->user)) {
            $query->whereHas('user', function($q) use ($request) {
                $q->where('name', 'like', "%{$request->user}%")
                  ->orWhere('email', 'like', "%{$request->user}%");
            });
        }

        // Filter by status
        if ($request->has('status') && !empty($request->status)) {
            if ($request->status === 'expired') {
                $query->where('status', 'active')
                      ->where('end_date', '<', now());
            } else {
                $query->where('status', $request->status);
            }
        }

        // Filter by package (subscription_id)
        if ($request->has('package') && !empty($request->package)) {
            $query->where('subscription_id', $request->package);
        }

        // Filter by date range
        if ($request->has('date_range') && !empty($request->date_range)) {
            switch ($request->date_range) {
                case 'today':
                    $query->whereDate('created_at', now());
                    break;
                case 'yesterday':
                    $query->whereDate('created_at', now()->subDay());
                    break;
                case 'this_week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'last_week':
                    $query->whereBetween('created_at', [
                        now()->subWeek()->startOfWeek(),
                        now()->subWeek()->endOfWeek()
                    ]);
                    break;
                case 'this_month':
                    $query->whereMonth('created_at', now()->month)
                          ->whereYear('created_at', now()->year);
                    break;
                case 'last_month':
                    $lastMonth = now()->subMonth();
                    $query->whereMonth('created_at', $lastMonth->month)
                          ->whereYear('created_at', $lastMonth->year);
                    break;
            }
        }

        $userSubscriptions = $query->orderBy('created_at', 'desc')->paginate(15);

        // Append query parameters to pagination links
        $userSubscriptions->appends($request->all());

        return view('admin.user_subscriptions.index', compact('userSubscriptions'));
    }

    // Approve payment confirmation
    public function approvePayment($id)
    {
        $userSubscription = UserSubscription::with('subscription', 'payments', 'user')->findOrFail($id);

        // Check if already active to prevent duplicate processing
        if ($userSubscription->status === 'active' && $userSubscription->is_active) {
            return redirect()->back()->with('info', 'Payment was already approved.');
        }

        $userSubscription->status = 'active';
        $userSubscription->is_active = true;

        \DB::beginTransaction();
        try {
            $userSubscription->save();

            // Update related payments status to confirmed
            foreach ($userSubscription->payments as $payment) {
                $payment->status = 'confirmed';
                $payment->confirmation_status = 'confirmed';
                $payment->save();

                // Check if the user has a referrer
                $referral = Referral::where('referred_id', $userSubscription->user_id)->first();
                if ($referral) {
                    // Get the base subscription price (excluding unique code)
                    $subscription = $userSubscription->subscription;
                    $basePrice = $subscription->price;

                    // Calculate 10% commission
                    $commissionAmount = $basePrice * 0.10;

                    // Create commission record
                    Commission::create([
                        'referrer_id' => $referral->referrer_id,
                        'referred_id' => $userSubscription->user_id,
                        'payment_id' => $payment->id,
                        'amount' => $commissionAmount,
                    ]);
                }
            }

            \DB::commit();

            // Dispatch notification job outside of transaction to prevent lock issues
            try {
                // Use a shorter timeout for the job dispatch
                \App\Jobs\SendPaymentNotificationJob::dispatch($userSubscription)->onQueue('notifications');
                return redirect()->back()->with('success', 'Payment approved successfully and notification sent.');
            } catch (\Exception $e) {
                \Log::error('Failed to dispatch notification job: ' . $e->getMessage());
                return redirect()->back()->with('success', 'Payment approved successfully, but notification could not be sent.');
            }
        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Failed to approve payment: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to approve payment. Please try again.');
        }
    }

    // Reject payment confirmation
    public function rejectPayment($id)
    {
        $userSubscription = UserSubscription::findOrFail($id);
        $userSubscription->status = 'rejected';
        $userSubscription->is_active = false;
        $userSubscription->save();

        // Update related payments status to rejected
        foreach ($userSubscription->payments as $payment) {
            $payment->status = 'rejected';
            $payment->confirmation_status = 'rejected';
            $payment->save();
        }

        return redirect()->back()->with('success', 'Payment rejected successfully.');
    }

    // Delete subscription (for rejected or expired subscriptions)
    public function deleteSubscription($id)
    {
        $userSubscription = UserSubscription::findOrFail($id);

        // Check if subscription is rejected or expired
        $isRejected = $userSubscription->status === 'rejected';
        $isExpired = $userSubscription->status === 'active' && \Carbon\Carbon::parse($userSubscription->end_date)->isPast();

        if (!$isRejected && !$isExpired) {
            return redirect()->back()->with('error', 'Only rejected or expired subscriptions can be deleted.');
        }

        // Begin transaction to ensure all related records are deleted properly
        \DB::beginTransaction();

        try {
            // Delete related payments first
            foreach ($userSubscription->payments as $payment) {
                // Delete related commissions if any
                $payment->commissions()->delete();
                $payment->delete();
            }

            // Delete the subscription
            $userSubscription->delete();

            \DB::commit();
            return redirect()->back()->with('success', 'Subscription deleted successfully.');
        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Failed to delete subscription: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to delete subscription. Please try again.');
        }
    }

    // Bulk delete all rejected and expired subscriptions
    public function bulkDeleteSubscriptions()
    {
        // Get all rejected subscriptions
        $rejectedSubscriptions = UserSubscription::where('status', 'rejected')->get();

        // Get all expired subscriptions (active but past end date)
        $expiredSubscriptions = UserSubscription::where('status', 'active')
            ->where('end_date', '<', now())
            ->get();

        // Combine both collections
        $subscriptionsToDelete = $rejectedSubscriptions->merge($expiredSubscriptions);

        if ($subscriptionsToDelete->isEmpty()) {
            return redirect()->back()->with('error', 'No rejected or expired subscriptions found to delete.');
        }

        $totalDeleted = 0;

        // Begin transaction
        \DB::beginTransaction();

        try {
            foreach ($subscriptionsToDelete as $subscription) {
                // Delete related payments and commissions
                foreach ($subscription->payments as $payment) {
                    $payment->commissions()->delete();
                    $payment->delete();
                }

                // Delete the subscription
                $subscription->delete();
                $totalDeleted++;
            }

            \DB::commit();
            return redirect()->back()->with('success', "Successfully deleted {$totalDeleted} expired/rejected subscriptions.");
        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Failed to bulk delete subscriptions: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to delete subscriptions. Please try again.');
        }
    }

    // Handle withdrawal request submission
    public function requestWithdrawal(Request $request)
    {
        $user = Auth::user();
        $minAmount = 50000;

        if ($user->commission_balance < $minAmount) {
            return redirect()->route('withdrawals.request')
                ->with('error', 'Saldo komisi Anda harus minimal Rp ' . number_format($minAmount, 0, ',', '.') . ' untuk mengajukan penarikan.');
        }

        $request->validate([
            'amount' => [
                'required',
                'numeric',
                "min:{$minAmount}",
                "max:{$user->commission_balance}"
            ],
            'bank_name' => 'required|string|max:255',
            'account_number' => 'required|string|max:255',
            'account_name' => 'required|string|max:255',
            'note' => 'nullable|string|max:1000',
        ], [
            'amount.min' => 'Jumlah penarikan minimum adalah Rp ' . number_format($minAmount, 0, ',', '.'),
            'amount.max' => 'Jumlah penarikan tidak boleh melebihi saldo komisi Anda Rp ' . number_format($user->commission_balance, 0, ',', '.'),
            'bank_name.required' => 'Nama bank harus diisi',
            'account_number.required' => 'Nomor rekening harus diisi',
            'account_name.required' => 'Nama pemilik rekening harus diisi',
        ]);

        Withdrawal::create([
            'user_id' => $user->id,
            'amount' => $request->amount,
            'status' => 'pending',
            'bank_name' => $request->bank_name,
            'account_number' => $request->account_number,
            'account_name' => $request->account_name,
            'note' => $request->note,
        ]);

        return redirect()->route('withdrawals.request')->with('success', 'Permintaan penarikan berhasil diajukan. Kami akan memproses permintaan Anda dalam 1-3 hari kerja.');
    }
}
