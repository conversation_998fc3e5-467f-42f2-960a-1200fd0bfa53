<?php

namespace App\Http\Controllers;

use App\Models\CommandProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CommandMonitoringController extends Controller
{
    /**
     * Display the command monitoring dashboard
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $commands = CommandProgress::orderBy('updated_at', 'desc')->get();
        
        return view('admin.command_monitoring.index', compact('commands'));
    }
    
    /**
     * Get the progress of a specific command
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProgress(Request $request)
    {
        $commandName = $request->input('command_name');
        
        if (!$commandName) {
            return response()->json(['error' => 'Command name is required'], 400);
        }
        
        $progress = CommandProgress::where('command_name', $commandName)->first();
        
        if (!$progress) {
            return response()->json(['error' => 'Command not found'], 404);
        }
        
        return response()->json([
            'command_name' => $progress->command_name,
            'current_step' => $progress->current_step,
            'total_steps' => $progress->total_steps,
            'percentage' => $progress->percentage,
            'status' => $progress->status,
            'message' => $progress->message,
            'started_at' => $progress->started_at ? $progress->started_at->format('Y-m-d H:i:s') : null,
            'finished_at' => $progress->finished_at ? $progress->finished_at->format('Y-m-d H:i:s') : null,
            'additional_data' => $progress->additional_data,
            'updated_at' => $progress->updated_at->format('Y-m-d H:i:s'),
        ]);
    }
    
    /**
     * Get the status of all commands
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllProgress()
    {
        $commands = CommandProgress::orderBy('updated_at', 'desc')->get();
        
        $result = [];
        foreach ($commands as $command) {
            $result[] = [
                'command_name' => $command->command_name,
                'current_step' => $command->current_step,
                'total_steps' => $command->total_steps,
                'percentage' => $command->percentage,
                'status' => $command->status,
                'message' => $command->message,
                'started_at' => $command->started_at ? $command->started_at->format('Y-m-d H:i:s') : null,
                'finished_at' => $command->finished_at ? $command->finished_at->format('Y-m-d H:i:s') : null,
                'additional_data' => $command->additional_data,
                'updated_at' => $command->updated_at->format('Y-m-d H:i:s'),
            ];
        }
        
        return response()->json($result);
    }
    
    /**
     * Get the status of the running commands
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRunningCommands()
    {
        $commands = CommandProgress::where('status', CommandProgress::STATUS_RUNNING)
            ->orderBy('updated_at', 'desc')
            ->get();
        
        $result = [];
        foreach ($commands as $command) {
            $result[] = [
                'command_name' => $command->command_name,
                'current_step' => $command->current_step,
                'total_steps' => $command->total_steps,
                'percentage' => $command->percentage,
                'status' => $command->status,
                'message' => $command->message,
                'started_at' => $command->started_at ? $command->started_at->format('Y-m-d H:i:s') : null,
                'updated_at' => $command->updated_at->format('Y-m-d H:i:s'),
            ];
        }
        
        return response()->json($result);
    }
}
