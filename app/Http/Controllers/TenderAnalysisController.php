<?php

namespace App\Http\Controllers;

use App\Models\TenderLpse;
use App\Models\PesertaTender;
use App\Models\PemenangLpse;
use App\Models\Jadwal;
use App\Models\Anggaran;
use App\Models\LokasiPaket;
use App\Models\InstansiSatker;
use App\Models\Lpse;
use App\Models\ListPerusahaanTender;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TenderAnalysisController extends Controller
{
    /**
     * Display tender analysis dashboard
     */
    public function index()
    {
        return view('tenderid.tender_analysis.index');
    }

    /**
     * Get tender statistics for AJAX
     */
    public function getStats(Request $request)
    {
        $period = $request->get('period', '30'); // Default 30 days
        $startDate = Carbon::now()->subDays($period);

        $stats = [
            'total_tenders' => TenderLpse::whereNotNull('tanggal_paket_tayang')
                ->where('tanggal_paket_tayang', '>=', $startDate)->count(),
            'active_tenders' => TenderLpse::where('status_tender', 'Aktif')
                ->whereNotNull('tanggal_paket_tayang')
                ->where('tanggal_paket_tayang', '>=', $startDate)->count(),
            'completed_tenders' => TenderLpse::where('status_tender', 'Selesai')
                ->whereNotNull('tanggal_paket_tayang')
                ->where('tanggal_paket_tayang', '>=', $startDate)->count(),
            'failed_tenders' => TenderLpse::where('status_tender', 'Gagal')
                ->whereNotNull('tanggal_paket_tayang')
                ->where('tanggal_paket_tayang', '>=', $startDate)->count(),
            'total_value' => TenderLpse::whereNotNull('tanggal_paket_tayang')
                ->where('tanggal_paket_tayang', '>=', $startDate)
                ->sum('pagu') ?: 0,
            'avg_participants' => round(TenderLpse::whereNotNull('tanggal_paket_tayang')
                ->where('tanggal_paket_tayang', '>=', $startDate)
                ->avg('jumlah_pendaftar') ?: 0, 1),
            'avg_bidders' => round(TenderLpse::whereNotNull('tanggal_paket_tayang')
                ->where('tanggal_paket_tayang', '>=', $startDate)
                ->avg('jumlah_penawar') ?: 0, 1),
            'competition_ratio' => $this->getCompetitionRatio($startDate),
        ];

        return response()->json($stats);
    }

    /**
     * Get tender trends by month
     */
    public function getTenderTrends(Request $request)
    {
        $months = $request->get('months', 12);
        $startDate = Carbon::now()->subMonths($months)->startOfMonth();

        $trends = TenderLpse::selectRaw('
                YEAR(tanggal_paket_tayang) as year,
                MONTH(tanggal_paket_tayang) as month,
                COUNT(*) as total_tenders,
                SUM(COALESCE(pagu, 0)) as total_value,
                AVG(COALESCE(jumlah_pendaftar, 0)) as avg_participants,
                AVG(COALESCE(jumlah_penawar, 0)) as avg_bidders
            ')
            ->whereNotNull('tanggal_paket_tayang')
            ->where('tanggal_paket_tayang', '>=', $startDate)
            ->groupBy('year', 'month')
            ->orderBy('year', 'asc')
            ->orderBy('month', 'asc')
            ->get()
            ->map(function ($item) {
                $item->month_name = Carbon::createFromDate($item->year, $item->month, 1)->format('M Y');
                return $item;
            });

        return response()->json($trends);
    }

    /**
     * Get tender analysis by category
     */
    public function getTenderByCategory(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period);

        $categories = TenderLpse::selectRaw('
                kategori_pekerjaan,
                COUNT(*) as total_tenders,
                SUM(COALESCE(pagu, 0)) as total_value,
                AVG(COALESCE(pagu, 0)) as avg_value,
                AVG(COALESCE(jumlah_pendaftar, 0)) as avg_participants
            ')
            ->whereNotNull('tanggal_paket_tayang')
            ->where('tanggal_paket_tayang', '>=', $startDate)
            ->whereNotNull('kategori_pekerjaan')
            ->groupBy('kategori_pekerjaan')
            ->orderBy('total_tenders', 'desc')
            ->limit(10)
            ->get();

        return response()->json($categories);
    }

    /**
     * Get tender analysis by method
     */
    public function getTenderByMethod(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period);

        $methods = TenderLpse::selectRaw('
                metode_pemilihan,
                COUNT(*) as total_tenders,
                SUM(COALESCE(pagu, 0)) as total_value,
                AVG(COALESCE(pagu, 0)) as avg_value,
                AVG(COALESCE(jumlah_pendaftar, 0)) as avg_participants,
                AVG(COALESCE(jumlah_penawar, 0)) as avg_bidders
            ')
            ->whereNotNull('tanggal_paket_tayang')
            ->where('tanggal_paket_tayang', '>=', $startDate)
            ->whereNotNull('metode_pemilihan')
            ->groupBy('metode_pemilihan')
            ->orderBy('total_tenders', 'desc')
            ->get();

        return response()->json($methods);
    }

    /**
     * Get top LPSE by tender volume
     */
    public function getTopLpse(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period);

        $topLpse = TenderLpse::selectRaw('
                lpse.kd_lpse,
                lpse.nama_lpse,
                lpse.provinsi,
                lpse.logo_path,
                COUNT(tender_umum_publik.kode_tender) as total_tenders,
                SUM(COALESCE(tender_umum_publik.pagu, 0)) as total_value,
                AVG(COALESCE(tender_umum_publik.pagu, 0)) as avg_value,
                AVG(COALESCE(tender_umum_publik.jumlah_pendaftar, 0)) as avg_participants
            ')
            ->join('lpse', 'lpse.kd_lpse', '=', 'tender_umum_publik.kd_lpse')
            ->whereNotNull('tender_umum_publik.tanggal_paket_tayang')
            ->where('tender_umum_publik.tanggal_paket_tayang', '>=', $startDate)
            ->groupBy('lpse.kd_lpse', 'lpse.nama_lpse', 'lpse.provinsi', 'lpse.logo_path')
            ->orderBy('total_tenders', 'desc')
            ->limit(15)
            ->get();

        return response()->json($topLpse);
    }

    /**
     * Get most active companies
     */
    public function getMostActiveCompanies(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period)->format('Y-m-d H:i:s');

        try {
            // Check if we have any tender data in the period
            $tendersInPeriod = DB::table('tender_umum_publik')
                ->where('tanggal_paket_tayang', '>=', $startDate)
                ->count();

            // If no data in period, extend the period to get some results
            if ($tendersInPeriod == 0) {
                $startDate = Carbon::now()->subDays(365)->format('Y-m-d H:i:s'); // Try 1 year
            }

            // Simplified query using DB facade for better debugging
            $companies = DB::table('peserta_tender')
                ->select([
                    'list_perusahaan_tender.id',
                    'list_perusahaan_tender.nama_peserta',
                    'list_perusahaan_tender.npwp',
                    'list_perusahaan_tender.npwp_blur',
                    'list_perusahaan_tender.pembeda',
                    DB::raw('COUNT(DISTINCT peserta_tender.kode_tender) as total_participation'),
                    DB::raw('COUNT(DISTINCT pemenang_lpse.kode_tender) as total_wins'),
                    DB::raw('CASE WHEN COUNT(DISTINCT peserta_tender.kode_tender) > 0 THEN ROUND(COUNT(DISTINCT pemenang_lpse.kode_tender) * 100.0 / COUNT(DISTINCT peserta_tender.kode_tender), 2) ELSE 0 END as win_rate')
                ])
                ->join('list_perusahaan_tender', 'list_perusahaan_tender.id', '=', 'peserta_tender.id_peserta')
                ->join('tender_umum_publik', 'tender_umum_publik.kode_tender', '=', 'peserta_tender.kode_tender')
                ->leftJoin('pemenang_lpse', function($join) {
                    $join->on('pemenang_lpse.id_peserta', '=', 'peserta_tender.id_peserta')
                         ->on('pemenang_lpse.kode_tender', '=', 'peserta_tender.kode_tender');
                })
                ->where('tender_umum_publik.tanggal_paket_tayang', '>=', $startDate)
                ->whereNotNull('list_perusahaan_tender.pembeda')
                ->whereNotNull('list_perusahaan_tender.nama_peserta')
                ->groupBy([
                    'list_perusahaan_tender.id',
                    'list_perusahaan_tender.nama_peserta',
                    'list_perusahaan_tender.npwp',
                    'list_perusahaan_tender.npwp_blur',
                    'list_perusahaan_tender.pembeda'
                ])
                ->havingRaw('COUNT(DISTINCT peserta_tender.kode_tender) >= 1') // Minimal 1 partisipasi
                ->orderByDesc('total_participation')
                ->limit(15)
                ->get();

            return response()->json($companies);

        } catch (\Exception $e) {
            \Log::error('Active Companies Error: ' . $e->getMessage());
            \Log::error('Active Companies Stack: ' . $e->getTraceAsString());

            // Return some debug info
            return response()->json([
                'error' => $e->getMessage(),
                'debug' => [
                    'period' => $period,
                    'start_date' => $startDate
                ]
            ]);
        }
    }

    /**
     * Get tender duration analysis
     */
    public function getTenderDurationAnalysis(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period);

        $durations = TenderLpse::selectRaw('
                CASE
                    WHEN durasi_tender <= 7 THEN "1-7 hari"
                    WHEN durasi_tender <= 14 THEN "8-14 hari"
                    WHEN durasi_tender <= 30 THEN "15-30 hari"
                    WHEN durasi_tender <= 60 THEN "31-60 hari"
                    ELSE ">60 hari"
                END as duration_range,
                COUNT(*) as total_tenders,
                AVG(jumlah_pendaftar) as avg_participants,
                AVG(jumlah_penawar) as avg_bidders
            ')
            ->where('tanggal_paket_tayang', '>=', $startDate)
            ->whereNotNull('durasi_tender')
            ->groupBy('duration_range')
            ->orderByRaw('MIN(durasi_tender)')
            ->get();

        return response()->json($durations);
    }

    /**
     * Get tender value distribution
     */
    public function getTenderValueDistribution(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period);

        $distribution = TenderLpse::selectRaw('
                CASE
                    WHEN pagu < 50000000 THEN "< 50 Juta"
                    WHEN pagu < 200000000 THEN "50-200 Juta"
                    WHEN pagu < 1000000000 THEN "200 Juta - 1 Miliar"
                    WHEN pagu < 5000000000 THEN "1-5 Miliar"
                    WHEN pagu < 25000000000 THEN "5-25 Miliar"
                    ELSE "> 25 Miliar"
                END as value_range,
                COUNT(*) as total_tenders,
                SUM(pagu) as total_value,
                AVG(jumlah_pendaftar) as avg_participants
            ')
            ->where('tanggal_paket_tayang', '>=', $startDate)
            ->whereNotNull('pagu')
            ->where('pagu', '>', 0)
            ->groupBy('value_range')
            ->orderByRaw('MIN(pagu)')
            ->get();

        return response()->json($distribution);
    }

    /**
     * Get tender schedule analysis
     */
    public function getTenderScheduleAnalysis(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period);

        $scheduleAnalysis = Jadwal::selectRaw('
                tahap,
                COUNT(DISTINCT kode_tender) as total_tenders,
                AVG(TIMESTAMPDIFF(DAY, tanggal_mulai, tanggal_selesai)) as avg_duration_days
            ')
            ->whereHas('tender', function($query) use ($startDate) {
                $query->where('tanggal_paket_tayang', '>=', $startDate);
            })
            ->whereNotNull('tanggal_selesai')
            ->groupBy('tahap')
            ->orderBy('total_tenders', 'desc')
            ->get();

        return response()->json($scheduleAnalysis);
    }

    /**
     * Get regional analysis
     */
    public function getRegionalAnalysis(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period);

        $regional = TenderLpse::selectRaw('
                lpse.provinsi,
                COUNT(tender_umum_publik.kode_tender) as total_tenders,
                SUM(tender_umum_publik.pagu) as total_value,
                AVG(tender_umum_publik.pagu) as avg_value,
                AVG(tender_umum_publik.jumlah_pendaftar) as avg_participants
            ')
            ->join('lpse', 'lpse.kd_lpse', '=', 'tender_umum_publik.kd_lpse')
            ->where('tender_umum_publik.tanggal_paket_tayang', '>=', $startDate)
            ->whereNotNull('lpse.provinsi')
            ->groupBy('lpse.provinsi')
            ->orderBy('total_tenders', 'desc')
            ->get();

        return response()->json($regional);
    }

    /**
     * Get top winning companies
     */
    public function getTopWinners(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period)->format('Y-m-d H:i:s');

        try {
            $topWinners = PemenangLpse::selectRaw('
                    list_perusahaan_tender.id,
                    list_perusahaan_tender.nama_peserta,
                    list_perusahaan_tender.npwp,
                    list_perusahaan_tender.npwp_blur,
                    list_perusahaan_tender.pembeda,
                    COUNT(pemenang_lpse.kode_tender) as total_wins,
                    SUM(COALESCE(tender_umum_publik.pagu, 0)) as total_value,
                    AVG(COALESCE(tender_umum_publik.pagu, 0)) as avg_value
                ')
                ->join('list_perusahaan_tender', 'list_perusahaan_tender.id', '=', 'pemenang_lpse.id_peserta')
                ->join('tender_umum_publik', 'tender_umum_publik.kode_tender', '=', 'pemenang_lpse.kode_tender')
                ->where('tender_umum_publik.tanggal_paket_tayang', '>=', $startDate)
                ->whereNotNull('list_perusahaan_tender.pembeda')
                ->groupBy('list_perusahaan_tender.id', 'list_perusahaan_tender.nama_peserta', 'list_perusahaan_tender.npwp', 'list_perusahaan_tender.npwp_blur', 'list_perusahaan_tender.pembeda')
                ->having('total_wins', '>=', 1) // Minimal 1 kemenangan
                ->orderBy('total_wins', 'desc')
                ->limit(15)
                ->get();

            return response()->json($topWinners);

        } catch (\Exception $e) {
            \Log::error('Top Winners Error: ' . $e->getMessage());
            return response()->json([]);
        }
    }

    /**
     * Get fraud analysis - detect potential rigged tenders
     */
    public function getFraudAnalysis(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period)->format('Y-m-d H:i:s');

        try {
            \Log::info('Fraud Analysis - Start', ['period' => $period, 'startDate' => $startDate]);

            // First, let's check if we have any data at all
            $basicCount = DB::table('tender_umum_publik')
                ->where('tanggal_paket_tayang', '>=', $startDate)
                ->count();

            \Log::info('Fraud Analysis - Basic tender count', ['count' => $basicCount]);

            if ($basicCount == 0) {
                \Log::info('Fraud Analysis - No tenders found in period');
                return response()->json([]);
            }

            // Simplified query for better performance
            $suspiciousTenders = DB::table('tender_umum_publik')
                ->select([
                    'tender_umum_publik.kode_tender',
                    'tender_umum_publik.kd_lpse',
                    'tender_umum_publik.nama_paket',
                    'instansi_satker.nama_instansi as nama_klpd',
                    'tender_umum_publik.jumlah_pendaftar',
                    'tender_umum_publik.jumlah_penawar',
                    'tender_umum_publik.pagu',
                    'list_perusahaan_tender.nama_peserta as pemenang',
                    'list_perusahaan_tender.npwp',
                    'list_perusahaan_tender.npwp_blur',
                    'list_perusahaan_tender.pembeda',
                    'lpse.nama_lpse',
                    'lpse.logo_path'
                ])
                ->join('pemenang_lpse', 'pemenang_lpse.kode_tender', '=', 'tender_umum_publik.kode_tender')
                ->join('list_perusahaan_tender', 'list_perusahaan_tender.id', '=', 'pemenang_lpse.id_peserta')
                ->leftJoin('instansi_satker', 'instansi_satker.kode_tender', '=', 'tender_umum_publik.kode_tender')
                ->leftJoin('lpse', 'lpse.kd_lpse', '=', 'tender_umum_publik.kd_lpse')
                ->where('tender_umum_publik.tanggal_paket_tayang', '>=', $startDate)
                ->whereNotNull('tender_umum_publik.jumlah_pendaftar')
                ->whereNotNull('tender_umum_publik.jumlah_penawar')
                ->where('tender_umum_publik.jumlah_pendaftar', '>', 0)
                ->where('tender_umum_publik.jumlah_penawar', '>', 0)
                ->limit(50) // Reduced limit for better performance
                ->get();

            \Log::info('Fraud Analysis - Raw data count', ['count' => $suspiciousTenders->count()]);

            $processedTenders = $suspiciousTenders->map(function ($tender) {
                $fraudIndicators = [];
                $riskScore = 0;

                    // Indicator 1: Only 1 bidder despite multiple registrants
                    if ($tender->jumlah_pendaftar > 1 && $tender->jumlah_penawar == 1) {
                        $fraudIndicators[] = 'Hanya 1 penawar dari ' . $tender->jumlah_pendaftar . ' pendaftar';
                        $riskScore += 30;
                    }

                    // Indicator 2: Very low participation rate
                    if ($tender->jumlah_pendaftar > 0) {
                        $participationRate = ($tender->jumlah_penawar / $tender->jumlah_pendaftar) * 100;
                        if ($participationRate < 20) {
                            $fraudIndicators[] = 'Tingkat partisipasi rendah (' . round($participationRate, 1) . '%)';
                            $riskScore += 25;
                        }
                    }

                    // Indicator 3: High value tender with low competition
                    if ($tender->pagu > 1000000000 && $tender->jumlah_penawar <= 2) {
                        $fraudIndicators[] = 'Tender bernilai tinggi dengan kompetisi rendah';
                        $riskScore += 35;
                    }

                    // Skip win rate check for now to improve performance
                    // TODO: Implement efficient win rate calculation

                    // Indicator 5: No competition (only 1 participant and 1 bidder)
                    if ($tender->jumlah_pendaftar == 1 && $tender->jumlah_penawar == 1) {
                        $fraudIndicators[] = 'Tidak ada kompetisi (1 pendaftar, 1 penawar)';
                        $riskScore += 40;
                    }

                    $tender->fraud_indicators = $fraudIndicators;
                    $tender->risk_score = min($riskScore, 100); // Cap at 100%

                    return $tender;
                });

            \Log::info('Fraud Analysis - After processing', ['count' => $processedTenders->count()]);

            // Filter tenders with risk score >= 20 (lowered threshold)
            $filteredTenders = $processedTenders->filter(function ($tender) {
                return $tender->risk_score >= 20;
            })->sortByDesc('risk_score')->take(15)->values();

            \Log::info('Fraud Analysis - Final filtered', ['count' => $filteredTenders->count()]);

            return response()->json($filteredTenders);

        } catch (\Exception $e) {
            \Log::error('Fraud Analysis Error: ' . $e->getMessage());

            // Return empty array instead of sample data
            return response()->json([]);
        }
    }

    /**
     * Get winner's win rate for fraud analysis (simplified for performance)
     */
    private function getWinnerWinRate($companyName)
    {
        try {
            // Use cache to avoid repeated queries
            $cacheKey = 'win_rate_' . md5($companyName);

            return \Cache::remember($cacheKey, 3600, function () use ($companyName) {
                $totalParticipation = PesertaTender::join('list_perusahaan_tender', 'list_perusahaan_tender.id', '=', 'peserta_tender.id_peserta')
                    ->where('list_perusahaan_tender.nama_peserta', $companyName)
                    ->limit(1000) // Limit untuk performance
                    ->count();

                $totalWins = PemenangLpse::join('list_perusahaan_tender', 'list_perusahaan_tender.id', '=', 'pemenang_lpse.id_peserta')
                    ->where('list_perusahaan_tender.nama_peserta', $companyName)
                    ->limit(1000) // Limit untuk performance
                    ->count();

                return $totalParticipation > 0 ? round(($totalWins / $totalParticipation) * 100, 1) : 0;
            });
        } catch (\Exception $e) {
            \Log::warning('Win rate calculation failed for: ' . $companyName . ' - ' . $e->getMessage());
            return 0; // Return 0 if calculation fails
        }
    }

    /**
     * Calculate competition ratio
     */
    private function getCompetitionRatio($startDate)
    {
        $totalTenders = TenderLpse::whereNotNull('tanggal_paket_tayang')
            ->where('tanggal_paket_tayang', '>=', $startDate)->count();
        $competitiveTenders = TenderLpse::whereNotNull('tanggal_paket_tayang')
            ->where('tanggal_paket_tayang', '>=', $startDate)
            ->where('jumlah_penawar', '>', 1)->count();

        return $totalTenders > 0 ? round(($competitiveTenders / $totalTenders) * 100, 2) : 0;
    }
}
