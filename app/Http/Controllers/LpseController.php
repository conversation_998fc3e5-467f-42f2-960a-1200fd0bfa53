<?php
namespace App\Http\Controllers;

use App\Models\Lpse;
use App\Models\PesertaTender;
use App\Models\PemenangLpse;
use Akaunting\Apexcharts\Chart;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;

class LpseController extends Controller
{
    public function index(Request $request)
    {
        // Mendapatkan parameter filter
        $search = $request->input('search');
        $provinsi = $request->input('provinsi');
        $perPage = 20; // Jumlah data per halaman

        // Memulai query
        $query = Lpse::withCount('tenders');

        // Menerapkan filter
        if ($search) {
            // Gunakan where like untuk pencarian
            $query->where(function($q) use ($search) {
                $q->where('nama_lpse', 'like', "%{$search}%")
                  ->orWhere('url', 'like', "%{$search}%");
            });
        }

        if ($provinsi) {
            $query->where('provinsi', $provinsi);
        }

        // Urutkan data
        $query->orderBy('nama_lpse');

        // Ambil data dengan pagination
        $dataLpse = $query->paginate($perPage)->withQueryString();

        return view('tenderid.lpse.index', [
            'dataLpse' => $dataLpse,
        ]);
    }

    public function show($id)
    {
        $lpse = Lpse::findOrFail($id);

        return view('tenderid.lpse.show', [
            'lpse' => $lpse,
        ]);
    }

    public function create()
    {
        return view('tenderid.lpse.create');
    }

    public function store(Request $request)
    {
        // Validasi input
        $request->validate([
            'kd_lpse' => 'required|string|unique:lpse,kd_lpse',
            'nama_lpse' => 'required|string|max:255',
            'provinsi' => 'nullable|string|max:255',
            'alamat' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'helpdesk' => 'nullable|string|max:255',
            'versi_lpse' => 'nullable|string|max:50',
            'url' => 'nullable|url|max:255',
            'logo_file' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        try {
            // Buat instance LPSE baru
            $lpse = new Lpse();
            $lpse->kd_lpse = $request->kd_lpse;
            $lpse->nama_lpse = $request->nama_lpse;
            $lpse->provinsi = $request->provinsi;
            $lpse->alamat = $request->alamat;
            $lpse->email = $request->email;
            $lpse->helpdesk = $request->helpdesk;
            $lpse->versi_lpse = $request->versi_lpse;
            $lpse->url = $request->url;

            // Proses unggah file logo jika ada
            if ($request->hasFile('logo_file')) {
                $file = $request->file('logo_file');
                $filename = uniqid() . '.' . $file->getClientOriginalExtension();

                // Menyimpan logo baru
                $path = $file->storeAs('logos', $filename, 'public');
                if (!$path) {
                    return redirect()->back()->with('error', 'Gagal menyimpan file logo.');
                }

                $lpse->logo_path = asset("storage/{$path}");
            }

            // Simpan LPSE
            $lpse->save();

            return redirect()->route('lpse.index')->with('success', 'Data LPSE berhasil ditambahkan.');
        } catch (\Exception $e) {
            \Log::error('Error creating LPSE: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan saat menambahkan data: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        $lpse = Lpse::findOrFail($id);

        return view('tenderid.lpse.edit', [
            'lpse' => $lpse,
        ]);
    }

    public function update(Request $request, $id)
{
    // Validasi input
    $request->validate([
        'nama_lpse' => 'required|string|max:255',
        'logo_file' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        'provinsi' => 'nullable|string|max:255',
        'alamat' => 'nullable|string|max:255',
        'email' => 'nullable|email|max:255',
        'helpdesk' => 'nullable|string|max:255',
        'versi_lpse' => 'nullable|string|max:255',
        'url' => 'nullable|url|max:255',
    ]);

    try {
        $lpse = Lpse::findOrFail($id);

        // Proses unggah file logo jika ada
        if ($request->hasFile('logo_file')) {
            $file = $request->file('logo_file');
            $filename = uniqid() . '.' . $file->getClientOriginalExtension();

            // Menghapus logo lama jika ada
            if ($lpse->logo_path) {
                $oldLogoPath = str_replace(asset(''), '', $lpse->logo_path);
                if (\Storage::disk('public')->exists($oldLogoPath)) {
                    \Storage::disk('public')->delete($oldLogoPath);
                }
            }

            // Menyimpan logo baru
            $path = $file->storeAs('logos', $filename, 'public');
            if (!$path) {
                return redirect()->back()->with('error', 'Gagal menyimpan file.');
            }

            $lpse->logo_path = asset("storage/{$path}");
        }

        // Update field lainnya
        $lpse->nama_lpse = $request->nama_lpse;
        $lpse->provinsi = $request->provinsi;
        $lpse->alamat = $request->alamat;
        $lpse->email = $request->email;
        $lpse->helpdesk = $request->helpdesk;
        $lpse->versi_lpse = $request->versi_lpse;
        $lpse->url = $request->url;
        $lpse->save();

        return redirect()->route('lpse.index')->with('success', 'Data LPSE berhasil diperbarui.');
    } catch (\Exception $e) {
        \Log::error('Error updating LPSE: ' . $e->getMessage());
        return redirect()->back()->with('error', 'Terjadi kesalahan saat memperbarui data: ' . $e->getMessage());
    }
}

    public function destroy($id)
    {
        $lpse = Lpse::findOrFail($id);
        $lpse->delete();

        return redirect()->route('lpse.index')->with('success', 'Data LPSE berhasil dihapus.');
    }

    /**
     * Update logo for LPSE from URL.
     */
    public function updateLogo(Request $request, $id)
    {
        try {
            // Validate request
            $request->validate([
                'image_url' => 'required|url',
            ]);

            // Find LPSE
            $lpse = Lpse::findOrFail($id);

            // Get image content
            $imageContent = file_get_contents($request->image_url);
            if (!$imageContent) {
                return response()->json(['success' => false, 'message' => 'Gagal mengunduh gambar dari URL.']);
            }

            // Generate filename
            $extension = pathinfo(parse_url($request->image_url, PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'png';
            $filename = 'lpse_' . $id . '_' . time() . '.' . $extension;

            // Save image to storage
            $path = 'logos/' . $filename;
            if (!Storage::disk('public')->put($path, $imageContent)) {
                return response()->json(['success' => false, 'message' => 'Gagal menyimpan gambar.']);
            }

            // Delete old logo if exists
            if ($lpse->logo_path) {
                $oldLogoPath = str_replace(asset(''), '', $lpse->logo_path);
                if (Storage::disk('public')->exists($oldLogoPath)) {
                    Storage::disk('public')->delete($oldLogoPath);
                }
            }

            // Update LPSE with new logo path
            $lpse->logo_path = asset('storage/' . $path);
            $lpse->save();

            return response()->json(['success' => true, 'message' => 'Logo berhasil diperbarui.']);
        } catch (\Exception $e) {
            \Log::error('Error updating LPSE logo: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
        }
    }

    public function dashboard($id)
    {
        $lpse = Lpse::findOrFail($id);
        $tenders = $lpse->tenders;

        $totalTenders = $tenders->count();
        $totalPagu = $tenders->sum('pagu');
        $totalHPS = $tenders->sum('hps');

        // Ambil 15 tender terbaru berdasarkan tanggal paket tayang
        $recentTenders = $lpse->tenders()
            ->orderByDesc('tanggal_paket_tayang')
            ->limit(15)
            ->get();

        // Refactor fungsi untuk menghitung tender per bulan
        $monthlyTenderData = $this->getMonthlyTenderData($tenders);

        $chart = (new Chart)
            ->setType('line')
            ->setWidth(960)
            ->setHeight(280)
            ->setLabels($monthlyTenderData['labels'])
            ->setDataset('Jumlah Tender', 'line', $monthlyTenderData['counts'])
            ->setOptions([
                'chart' => [
                    'foreColor' => '#9ba7b2',
                    'type' => 'line',
                    'toolbar' => [
                        'show' => true
                    ],
                    'sparkline' => [
                        'enabled' => false
                    ],
                    'zoom' => [
                        'enabled' => false
                    ],
                    'width' => '100%',  // Membuat chart fleksibel
                    'maxWidth' => '100%',  // Menyesuaikan maksimal dengan ukuran parent
                ],
                'dataLabels' => [
                    'enabled' => true
                ],
                'stroke' => [
                    'width' => 4,
                    'curve' => 'smooth',
                    'colors' => ['transparent']
                ],
                'fill' => [
                    'type' => 'gradient',
                    'gradient' => [
                        'shade' => 'dark',
                        'gradientToColors' => ['#0d6efd', '#6f42c1'],
                        'shadeIntensity' => 1,
                        'type' => 'vertical',
                        'stops' => [0, 100, 100, 100]
                    ]
                ],
                'colors' => ['#0d6efd', '#6f42c1'],
                'plotOptions' => [
                    'bar' => [
                        'horizontal' => false,
                        'borderRadius' => 4,
                        'borderRadiusApplication' => 'around',
                        'borderRadiusWhenStacked' => 'last',
                        'columnWidth' => '55%',
                    ]
                ],
                'grid' => [
                    'show' => false,
                    'borderColor' => 'rgba(0, 0, 0, 0.15)',
                    'strokeDashArray' => 4
                ],
                'tooltip' => [
                    'theme' => 'dark',
                    'fixed' => [
                        'enabled' => true
                    ],
                    'x' => [
                        'show' => true
                    ],
                    'y' => [
                        'title' => [
                            'formatter' => function ($e) {
                                return "";
                            }
                        ]
                    ],
                    'marker' => [
                        'show' => false
                    ]
                ],
                'xaxis' => [
                    'categories' => $monthlyTenderData['labels'],
                    'type' => 'datetime'
                ],
                'yaxis' => [
                    'title' => [
                        'text' => 'Jumlah Tender'
                    ]
                ],
                'subtitle' => [
                    'text' => '' // Mengatur subtitle menjadi string kosong
                ],
                'responsive' => [
                    [
                        'breakpoint' => 768, // Tablet
                        'options' => [
                            'chart' => [
                                'width' => '100%' // Ubah width menjadi 100%
                            ]
                        ]
                    ],
                    [
                        'breakpoint' => 480, // Smartphone
                        'options' => [
                            'chart' => [
                                'width' => '100%' // Ubah width menjadi 100%
                            ]
                        ]
                    ]
                ]
            ]);


        $categories = $tenders->groupBy('kategori_pekerjaan')->map(function ($items, $key) {
            return [
                'kategori' => $key,
                'jumlah' => $items->count()
            ];
        });

        $labels = $categories->pluck('kategori')->toArray();  // Mengambil kategori sebagai label
        $counts = $categories->pluck('jumlah')->toArray();    // Mengambil jumlah sebagai data

        $kategoriChart = (new Chart)
            ->setType('bar') // Mengubah menjadi 'bar' untuk chart bar
            ->setWidth(960)
            ->setHeight(280)
            ->setLabels($categories->pluck('kategori')->toArray()) // Label berdasarkan kategori pekerjaan
            ->setDataset('Jumlah Tender', 'bar', $categories->pluck('jumlah')->toArray()) // Data jumlah tender
            ->setOptions([
                'chart' => [
                    'foreColor' => '#9ba7b2',
                    'type' => 'bar',
                    'toolbar' => [
                        'show' => true
                    ],
                    'sparkline' => [
                        'enabled' => false
                    ],
                    'zoom' => [
                        'enabled' => false
                    ],
                    'width' => '100%',  // Membuat chart fleksibel
                    'maxWidth' => '100%',  // Menyesuaikan maksimal dengan ukuran parent
                ],
                'dataLabels' => [
                    'enabled' => true
                ],
                'stroke' => [
                    'width' => 4,
                    'curve' => 'smooth',
                    'colors' => ['transparent']
                ],
                'fill' => [
                    'type' => 'gradient',
                    'gradient' => [
                        'shade' => 'dark',
                        'gradientToColors' => ['#0d6efd', '#6f42c1'],
                        'shadeIntensity' => 1,
                        'type' => 'vertical',
                        'stops' => [0, 100, 100, 100]
                    ]
                ],
                'colors' => ['#0d6efd', '#6f42c1'],
                'plotOptions' => [
                    'bar' => [
                        'horizontal' => false,
                        'borderRadius' => 4,
                        'borderRadiusApplication' => 'around',
                        'borderRadiusWhenStacked' => 'last',
                        'columnWidth' => '55%',
                    ]
                ],
                'grid' => [
                    'show' => false,
                    'borderColor' => 'rgba(0, 0, 0, 0.15)',
                    'strokeDashArray' => 4
                ],
                'tooltip' => [
                    'theme' => 'dark',
                    'fixed' => [
                        'enabled' => true
                    ],
                    'x' => [
                        'show' => true
                    ],
                    'y' => [
                        'title' => [
                            'formatter' => function ($e) {
                                return "";
                            }
                        ]
                    ],
                    'marker' => [
                        'show' => false
                    ]
                ],
                'xaxis' => [
                    'categories' => $categories->pluck('kategori')->toArray(), // Kategori pekerjaan sebagai label di sumbu X
                ],
                'yaxis' => [
                    'title' => [
                        'text' => 'Jumlah Tender'
                    ]
                ],
                'subtitle' => [
                    'text' => '' // Mengatur subtitle menjadi string kosong
                ]
            ]);

        // 15 Perusahaan paling sering menang di LPSE terkait
        $topWinningCompanies = PemenangLpse::select('id_peserta', \DB::raw('COUNT(*) as jumlah_kemenangan'), \DB::raw('SUM(harga_penawaran) as total_nilai_kemenangan'))
            ->whereHas('tenderUmumPublik', function ($query) use ($id) {
                $query->where('kd_lpse', $id);
            })
            ->groupBy('id_peserta')
            ->orderByDesc('total_nilai_kemenangan')
            ->limit(15)
            ->with('peserta') // Mengambil data perusahaan
            ->get();

        // 15 Perusahaan paling sering menang di LPSE terkait
        $topWinningCompanies2 = PemenangLpse::select('id_peserta', \DB::raw('COUNT(*) as jumlah_kemenangan'), \DB::raw('SUM(harga_penawaran) as total_nilai_kemenangan'))
            ->whereHas('tenderUmumPublik', function ($query) use ($id) {
                $query->where('kd_lpse', $id);
            })
            ->groupBy('id_peserta')
            ->orderByDesc('jumlah_kemenangan')
            ->limit(15)
            ->with('peserta') // Mengambil data perusahaan
            ->get();

        // 15 Perusahaan paling sering ikut tender di LPSE terkait
        $topParticipatingCompanies = PesertaTender::select('id_peserta', \DB::raw('count(*) as total_ikut'))
            ->whereHas('tender', function ($query) use ($id) {
                $query->where('kd_lpse', $id);
            })
            ->groupBy('id_peserta')
            ->orderByDesc('total_ikut')
            ->limit(15)
            ->with('perusahaan') // Include informasi perusahaan
            ->get();


        return view('tenderid.lpse.dashboard', compact('lpse', 'kategoriChart', 'totalTenders', 'totalPagu', 'totalHPS', 'chart', 'recentTenders', 'topWinningCompanies', 'topParticipatingCompanies', 'topWinningCompanies2'));
    }


    private function getMonthlyTenderData($tenders)
    {
        $currentYear = now()->year;

        // Filter tender yang hanya tayang pada tahun saat ini
        $filteredTenders = $tenders->filter(function ($tender) use ($currentYear) {
            return Carbon::parse($tender->tanggal_paket_tayang)->year == $currentYear;
        });

        // Membuat array untuk semua bulan dalam tahun saat ini
        $allMonths = collect(range(1, 12))->mapWithKeys(function ($month) use ($currentYear) {
            return [Carbon::create($currentYear, $month, 1)->format('Y-m') => 0];
        });

        // Mengelompokkan data tender berdasarkan bulan dan menghitung jumlah tender
        $monthlyData = $filteredTenders->groupBy(function ($tender) {
            return Carbon::parse($tender->tanggal_paket_tayang)->format('Y-m');
        })->map->count();

        // Menggabungkan data yang ada dengan array semua bulan
        $completeMonthlyData = $allMonths->merge($monthlyData);

        // Mendapatkan label bulan yang sudah diurutkan
        $labels = $completeMonthlyData->keys()->toArray();

        // Mendapatkan jumlah tender per bulan
        $counts = $completeMonthlyData->values()->toArray();

        return [
            'labels' => $labels,
            'counts' => $counts,
        ];
    }

}
