<?php
namespace App\Http\Controllers;

use App\Models\PaketPengadaan;
use Illuminate\Http\Request;

class EkatalogController extends Controller
{
    /**
     * Menampilkan daftar Paket Pengadaan dengan relasi terkait dan mendukung pencarian.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Mendapatkan parameter pencarian dan filter dari request
        $search = $request->input('search');
        $instansiPembeli = $request->input('instansi');
        $tanggalMulai = $request->input('tanggal_mulai');
        $tanggalAkhir = $request->input('tanggal_akhir');

        // Memulai query builder dengan eager loading relasi
        $query = PaketPengadaan::with([
            'transaksiPaket.produk',
            'penyedia',
            'pelaksana',
        ]);

        // Penerapan pencarian berdasarkan beberapa kolom
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('nama_paket', 'LIKE', '%' . $search . '%')
                    ->orWhere('nomor_paket', 'LIKE', '%' . $search . '%')
                    ->orWhere('satuan_kerja', 'LIKE', '%' . $search . '%')
                    ->orWhere('instansi_pembeli', 'LIKE', '%' . $search . '%')
                    ->orWhereHas('penyedia', function ($q2) use ($search) {
                        $q2->where('nama_peserta', 'LIKE', '%' . $search . '%');
                    })
                    ->orWhereHas('pelaksana', function ($q2) use ($search) {
                        $q2->where('nama_peserta', 'LIKE', '%' . $search . '%');
                    });
            });
        }


        // Filter berdasarkan instansi pembeli
        if ($instansiPembeli) {
            $query->where('instansi_pembeli', $instansiPembeli);
        }

        // Filter berdasarkan range tanggal
        if ($tanggalMulai && $tanggalAkhir) {
            $query->whereBetween('tanggal', [$tanggalMulai, $tanggalAkhir]);
        }

        // Urutkan hasil berdasarkan tanggal_paket_diumumkan
        $query->orderBy('tanggal_paket', 'desc')->withNilaiKontrak();

        // Eksekusi query dengan pagination
        $paketPengadaan = $query->simplePaginate(25)->appends([
            'search' => $search,
            'instansi' => $instansiPembeli,
            'tanggal_mulai' => $tanggalMulai,
            'tanggal_akhir' => $tanggalAkhir,
        ]);

        // Mendapatkan daftar status paket untuk dropdown filter
        $statusPaketList = PaketPengadaan::select('status_paket')
            ->distinct()
            ->orderBy('status_paket', 'asc')
            ->pluck('status_paket');

        // Mendapatkan daftar instansi_pembeli untuk dropdown filter
        $instansiPembeliList = PaketPengadaan::select('instansi_pembeli')
            ->distinct()
            ->orderBy('instansi_pembeli', 'asc')
            ->pluck('instansi_pembeli');

        return view('tenderid.ekatalog.index', [
            'paketPengadaan' => $paketPengadaan,
            'instansi_pembeli_list' => $instansiPembeliList,
            'search' => $search,
            'instansiPembeliSelected' => $instansiPembeli,
            'tanggalMulai' => $tanggalMulai,
            'tanggalAkhir' => $tanggalAkhir,
        ]);
    }

    /**
     * Menampilkan detail Paket Pengadaan tertentu.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        if (!ctype_digit($id)) {
            abort(404, 'ID tidak valid');
        }
        $paketPengadaan = PaketPengadaan::with([
            'transaksiPaket.produk',
            'penyedia',
            'pelaksana',
        ])->where('id_paket', $id)
            ->firstOrFail();

        if (!$paketPengadaan) {
            // Tampilkan pesan error jika data Paket Pengadaan tidak ditemukan
            return view('tenderid.ekatalog.error', ['message' => 'Data Paket Pengadaan tidak ditemukan']);
        }

        $totalNilaiKontrak = $paketPengadaan->transaksiPaket->sum('total_harga');

        return view('tenderid.ekatalog.detail', compact('paketPengadaan', 'totalNilaiKontrak'));
    }

    /**
     * Menampilkan form untuk mengedit Paket Pengadaan tertentu.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $paketPengadaan = PaketPengadaan::findOrFail($id);
        return view('ekatalog.edit', compact('paketPengadaan'));
    }

    /**
     * Memperbarui Paket Pengadaan tertentu di database.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        // Validasi input
        $validated = $request->validate([
            'nama_paket' => 'required|string|max:255',
            'nomor_paket' => 'required|string|max:100',
            'pengelola' => 'required|string|max:255',
            'satuan_kerja' => 'required|string|max:255',
            'instansi_pembeli' => 'required|string|max:255',
            'jenis_katalog' => 'required|string|max:255',
            'etalase' => 'nullable|string|max:255',
            'tanggal_paket' => 'required|date',
            'status_paket' => 'required|string|max:50',
            'rup_id' => 'required|integer',
            'status_umkm' => 'nullable|string|max:50',
            'id_penyedia' => 'required|integer',
            'id_pelaksana' => 'required|integer',
        ]);

        // Cari dan perbarui data
        $paketPengadaan = PaketPengadaan::findOrFail($id);
        $paketPengadaan->update($validated);

        return redirect()->route('ekatalog.index')->with('success', 'Paket Pengadaan berhasil diperbarui.');
    }

    /**
     * Menghapus Paket Pengadaan tertentu dari database.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $paketPengadaan = PaketPengadaan::findOrFail($id);
        $paketPengadaan->delete();

        return redirect()->route('ekatalog.index')->with('success', 'Paket Pengadaan berhasil dihapus.');
    }

    public function getPerubahanData()
    {
        $paketPengadaan = new PaketPengadaan();
        $dataPerTanggal = $paketPengadaan->getDataPerTanggal();

        $hasil = [];
        $sebelumnya = null;

        foreach ($dataPerTanggal as $data) {
            $tanggal = $data->tanggal;
            $jumlah = $data->jumlah;

            // Bandingkan dengan tanggal sebelumnya
            if ($sebelumnya !== null) {
                $perubahan = $jumlah - $sebelumnya['jumlah'];
                $status = $perubahan > 0 ? 'bertambah' : ($perubahan < 0 ? 'berkurang' : 'sama');
            } else {
                $perubahan = 0;
                $status = 'data awal';
            }

            $hasil[] = [
                'tanggal' => $tanggal,
                'jumlah' => $jumlah,
                'perubahan' => $perubahan,
                'status' => $status,
            ];

            // Simpan data sebelumnya
            $sebelumnya = [
                'tanggal' => $tanggal,
                'jumlah' => $jumlah,
            ];
        }

        return response()->json($hasil);
    }

    public function paketTanpaNilai()
    {
        $paketPengadaan = PaketPengadaan::with([
            'transaksiPaket',
            'pelaksana',
            'penyedia',
        ])
            ->withNilaiKontrak()
            ->having('nilai_kontrak', '=', 0) // Filter hanya yang memiliki nilai_kontrak = 0
            ->orderBy('tanggal_paket', 'desc')
            ->simplePaginate(25);

        return view('tenderid.ekatalog.nilai_kosong', compact('paketPengadaan'));
    }

    public function paketTanpaNilai2()
    {
        $paketPengadaan = PaketPengadaan::with([
            'pelaksana',
            'penyedia',
        ])
            ->withNilaiKontrak()
            ->leftJoin('ekatalog_transaksi_paket', 'ekatalog_paket_pengadaan.id_paket', '=', 'ekatalog_transaksi_paket.id_paket')
            ->whereNull('ekatalog_transaksi_paket.id_paket') // Menampilkan yang tidak memiliki transaksi
            ->orderBy('tanggal_paket', 'desc')
            ->select('ekatalog_paket_pengadaan.*') // Hindari kolom ganda karena join
            ->simplePaginate(25);

        return view('tenderid.ekatalog.nilai_kosong', compact('paketPengadaan'));
    }

}
