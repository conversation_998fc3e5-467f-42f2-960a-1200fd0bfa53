<?php

namespace App\Http\Controllers;

use App\Helpers\WhatsAppHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;

class WhatsAppMonitoringController extends Controller
{
    protected $whatsappServiceUrl;
    protected $apiKey;
    protected $logPath;

    public function __construct()
    {
        $this->whatsappServiceUrl = config('services.whatsapp.url');
        $this->apiKey = config('services.whatsapp.api_key');
        $this->logPath = base_path('waservices/logs/combined.log');
    }

    /**
     * Display WhatsApp monitoring dashboard
     */
    public function dashboard()
    {
        // Get WhatsApp status
        $status = $this->getWhatsAppStatus();

        // Get recent errors (last 24 hours)
        $recentErrors = $this->getRecentErrors();

        // Get message stats
        $messageStats = $this->getMessageStats();

        return view('admin.whatsapp.monitoring', compact('status', 'recentErrors', 'messageStats'));
    }

    /**
     * Get WhatsApp connection status
     */
    public function getWhatsAppStatus()
    {
        try {
            // Try to get from cache first (cache for 1 minute)
            if (Cache::has('whatsapp_status')) {
                return Cache::get('whatsapp_status');
            }

            $response = Http::withHeaders([
                'X-API-KEY' => $this->apiKey,
                'Accept' => 'application/json',
            ])->get("{$this->whatsappServiceUrl}/auth/status");

            if ($response->successful()) {
                $status = $response->json();
                // Cache for 1 minute
                Cache::put('whatsapp_status', $status, 60);
                return $status;
            }

            return ['success' => false, 'error' => 'Failed to get status'];
        } catch (\Exception $e) {
            Log::error('Error getting WhatsApp status', ['exception' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get recent errors from log file
     */
    public function getRecentErrors()
    {
        try {
            if (!File::exists($this->logPath)) {
                return ['success' => false, 'error' => 'Log file not found'];
            }

            // Get last 100 lines from log file
            $logContent = $this->getTailOfFile($this->logPath, 100);

            // Parse log content to find errors
            $errors = [];
            $lines = explode("\n", $logContent);

            foreach ($lines as $line) {
                if (empty($line)) continue;

                $logEntry = json_decode($line, true);

                if (!$logEntry) continue;

                // Check if it's an error log
                if (isset($logEntry['level']) && $logEntry['level'] === 'error') {
                    // Check if it's recent (last 24 hours)
                    if (isset($logEntry['timestamp'])) {
                        $logTime = strtotime($logEntry['timestamp']);
                        $yesterday = strtotime('-24 hours');

                        if ($logTime >= $yesterday) {
                            $errors[] = $logEntry;
                        }
                    }
                }
            }

            // Sort errors by timestamp, newest first
            usort($errors, function($a, $b) {
                $timestampA = isset($a['timestamp']) ? strtotime($a['timestamp']) : 0;
                $timestampB = isset($b['timestamp']) ? strtotime($b['timestamp']) : 0;
                return $timestampB - $timestampA; // Descending order (newest first)
            });

            return $errors;
        } catch (\Exception $e) {
            Log::error('Error getting recent errors', ['exception' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get message statistics
     */
    public function getMessageStats()
    {
        try {
            if (!File::exists($this->logPath)) {
                return ['success' => false, 'error' => 'Log file not found'];
            }

            // Get last 500 lines from log file
            $logContent = $this->getTailOfFile($this->logPath, 500);

            // Parse log content to find message stats
            $stats = [
                'total_sent' => 0,
                'success_count' => 0,
                'error_count' => 0,
                'templates' => [],
            ];

            $lines = explode("\n", $logContent);

            foreach ($lines as $line) {
                if (empty($line)) continue;

                $logEntry = json_decode($line, true);

                if (!$logEntry) continue;

                // Check if it's a message sent log
                if (isset($logEntry['message']) && $logEntry['message'] === 'Message sent successfully') {
                    $stats['total_sent']++;
                    $stats['success_count']++;

                    // Count by template
                    if (isset($logEntry['templateId'])) {
                        $templateId = $logEntry['templateId'];
                        if (!isset($stats['templates'][$templateId])) {
                            $stats['templates'][$templateId] = 0;
                        }
                        $stats['templates'][$templateId]++;
                    }
                }

                // Check if it's an error sending message
                if (isset($logEntry['message']) && $logEntry['message'] === 'Error sending message') {
                    $stats['total_sent']++;
                    $stats['error_count']++;
                }
            }

            return $stats;
        } catch (\Exception $e) {
            Log::error('Error getting message stats', ['exception' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * View recent logs
     */
    public function viewLogs(Request $request)
    {
        $lines = $request->input('lines', 100);
        $filter = $request->input('filter', '');

        try {
            if (!File::exists($this->logPath)) {
                return response()->json(['success' => false, 'error' => 'Log file not found']);
            }

            // Get last X lines from log file
            $logContent = $this->getTailOfFile($this->logPath, $lines);

            // Parse log content
            $logs = [];
            $logLines = explode("\n", $logContent);

            foreach ($logLines as $line) {
                if (empty($line)) continue;

                // Apply filter if provided
                if (!empty($filter) && strpos($line, $filter) === false) {
                    continue;
                }

                $logEntry = json_decode($line, true);

                if ($logEntry) {
                    $logs[] = $logEntry;
                } else {
                    $logs[] = ['raw' => $line, 'timestamp' => date('Y-m-d H:i:s')];
                }
            }

            // Sort logs by timestamp, newest first
            usort($logs, function($a, $b) {
                $timestampA = isset($a['timestamp']) ? strtotime($a['timestamp']) : 0;
                $timestampB = isset($b['timestamp']) ? strtotime($b['timestamp']) : 0;
                return $timestampB - $timestampA; // Descending order (newest first)
            });

            if ($request->expectsJson()) {
                return response()->json(['success' => true, 'logs' => $logs]);
            }

            return view('admin.whatsapp.logs', compact('logs', 'lines', 'filter'));
        } catch (\Exception $e) {
            Log::error('Error viewing logs', ['exception' => $e->getMessage()]);

            if ($request->expectsJson()) {
                return response()->json(['success' => false, 'error' => $e->getMessage()]);
            }

            return back()->with('error', 'Error viewing logs: ' . $e->getMessage());
        }
    }

    /**
     * Send test notification
     */
    public function sendTestNotification()
    {
        try {
            $adminPhone = '6281343596588'; // Admin phone number
            $message = 'Ini adalah pesan test dari sistem monitoring WhatsApp. Waktu: ' . now()->format('Y-m-d H:i:s');

            $response = WhatsAppHelper::sendSystemNotification($adminPhone, $message);

            if ($response && $response->successful()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Test notification sent successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'error' => 'Failed to send test notification',
                'response' => $response ? $response->body() : 'No response'
            ], 500);
        } catch (\Exception $e) {
            Log::error('Error sending test notification', ['exception' => $e->getMessage()]);
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get the last X lines of a file
     */
    private function getTailOfFile($filePath, $lines = 100)
    {
        // Use the tail command to get the last X lines
        $output = shell_exec("tail -n {$lines} {$filePath}");
        return $output ?: '';
    }
}
