<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\PageAccessLog;
use App\Models\LoginLog;

class UserController extends Controller
{
    // 1. Menampilkan aktivitas halaman yang diakses
    public function activityLogs(Request $request)
    {
        // Fetch all users for the dropdown
        $users = User::orderBy('name')->get();

        // Start building the query for logs
        $query = PageAccessLog::with('user');

        // If a user_id is provided in the request, filter logs by that user
        if ($request->has('user_id') && $request->user_id != '') {
            $query->where('user_id', $request->user_id);
        }

        // If a method is provided in the request, filter logs by that method
        if ($request->has('method') && $request->method != '') {
            $query->where('method', $request->method);
        }

        // If a URL search term is provided, filter logs by URL containing that term
        if ($request->has('url') && $request->url != '') {
            $query->where('url', 'like', "%{$request->url}%");
        }

        // If a date is provided, filter logs by that date
        if ($request->has('date') && $request->date != '') {
            $query->whereDate('created_at', $request->date);
        }

        // Fetch paginated logs
        $logs = $query->latest()->paginate(50);

        // Append query parameters to pagination links to persist the filters
        $logs->appends($request->only(['user_id', 'method', 'url', 'date']));

        return view('tenderid.user.activity_logs', compact('logs', 'users'));
    }

    // 2. Menampilkan user yang sedang login
    public function onlineUsers()
    {
        $onlineUsers = User::where('last_seen', '>', now()->subMinutes(60))
            ->orderByRaw('last_seen DESC')
            ->paginate(50);

        return view('tenderid.user.online_users', compact('onlineUsers'));
    }

    // 3. Menampilkan durasi login user
    public function loginDurations()
    {
        $logs = LoginLog::with('user')
            ->orderBy('login_at', 'desc')
            ->paginate(50);

        return view('tenderid.user.login_durations', compact('logs'));
    }
}