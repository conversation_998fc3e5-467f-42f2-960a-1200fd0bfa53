<?php

namespace App\Http\Controllers;

use App\Models\PaketPengadaan;
use App\Models\TransaksiPaket;
use App\Models\Produk;
use App\Models\Lpse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EkatalogAnalyticsAjaxController extends Controller
{
    /**
     * Menampilkan dashboard analisis tren pembelian produk dan layanan
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Filter tahun
        $tahun = $request->input('tahun', date('Y'));
        $tahunList = PaketPengadaan::selectRaw('YEAR(tanggal_paket) as tahun')
            ->distinct()
            ->orderBy('tahun', 'desc')
            ->pluck('tahun');

        // Hanya tampilkan view dasar, data akan dimuat dengan AJAX
        return view('tenderid.ekatalog.analytics_ajax', compact(
            'tahun',
            'tahunList'
        ));
    }

    /**
     * Mendapatkan statistik umum untuk dashboard analisis
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Statistik umum
            $totalPaket = PaketPengadaan::whereYear('tanggal_paket', $tahun)->count();
            $totalNilai = TransaksiPaket::join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->sum('ekatalog_transaksi_paket.total_harga');

            return response()->json([
                'totalPaket' => $totalPaket,
                'totalNilai' => $totalNilai
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mendapatkan data tren bulanan
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTrenBulanan(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Tren pembelian per bulan
            $trenPembelianBulanan = PaketPengadaan::selectRaw('MONTH(tanggal_paket) as bulan, COUNT(*) as jumlah_paket')
                ->whereYear('tanggal_paket', $tahun)
                ->groupBy(DB::raw('MONTH(tanggal_paket)'))
                ->orderBy('bulan')
                ->get();

            // Tren nilai kontrak per bulan
            $trenNilaiBulanan = TransaksiPaket::join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('MONTH(ekatalog_paket_pengadaan.tanggal_paket) as bulan, SUM(ekatalog_transaksi_paket.total_harga) as total_nilai')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->groupBy(DB::raw('MONTH(ekatalog_paket_pengadaan.tanggal_paket)'))
                ->orderBy('bulan')
                ->get();

            return response()->json([
                'trenPembelianBulanan' => $trenPembelianBulanan,
                'trenNilaiBulanan' => $trenNilaiBulanan
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mendapatkan data top produk dan kategori
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopProdukKategori(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Top 10 produk terlaris
            $topProduk = TransaksiPaket::join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
                ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('ekatalog_produk.nama_produk, SUM(ekatalog_transaksi_paket.kuantitas_produk) as total_kuantitas, SUM(ekatalog_transaksi_paket.total_harga) as total_nilai')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->groupBy('ekatalog_produk.nama_produk')
                ->orderBy('total_kuantitas', 'desc')
                ->limit(10)
                ->get();

            // Top 10 kategori produk
            $topKategori = TransaksiPaket::join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
                ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('ekatalog_produk.kategori_lv1, COUNT(*) as jumlah_transaksi, SUM(ekatalog_transaksi_paket.total_harga) as total_nilai')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->groupBy('ekatalog_produk.kategori_lv1')
                ->orderBy('total_nilai', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'topProduk' => $topProduk,
                'topKategori' => $topKategori
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mendapatkan data top instansi dan penyedia
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopInstansiPenyedia(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Top 10 instansi pembeli
            $topInstansi = PaketPengadaan::selectRaw('instansi_pembeli, COUNT(*) as jumlah_paket')
                ->whereYear('tanggal_paket', $tahun)
                ->groupBy('instansi_pembeli')
                ->orderBy('jumlah_paket', 'desc')
                ->limit(10)
                ->get();

            // Top 10 penyedia
            $topPenyedia = PaketPengadaan::join('list_perusahaan_tender', 'ekatalog_paket_pengadaan.id_penyedia', '=', 'list_perusahaan_tender.id')
                ->selectRaw('list_perusahaan_tender.nama_peserta, COUNT(*) as jumlah_paket')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->groupBy('list_perusahaan_tender.nama_peserta')
                ->orderBy('jumlah_paket', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'topInstansi' => $topInstansi,
                'topPenyedia' => $topPenyedia
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mendapatkan data distribusi jenis katalog dan status paket
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDistribusi(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Distribusi jenis katalog
            $jenisKatalog = PaketPengadaan::selectRaw('jenis_katalog, COUNT(*) as jumlah_paket')
                ->whereYear('tanggal_paket', $tahun)
                ->groupBy('jenis_katalog')
                ->orderBy('jumlah_paket', 'desc')
                ->get();

            // Perbandingan status paket
            $statusPaket = PaketPengadaan::selectRaw('status_paket, COUNT(*) as jumlah_paket')
                ->whereYear('tanggal_paket', $tahun)
                ->groupBy('status_paket')
                ->orderBy('jumlah_paket', 'desc')
                ->get();

            return response()->json([
                'jenisKatalog' => $jenisKatalog,
                'statusPaket' => $statusPaket
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Menampilkan analisis detail untuk kategori produk tertentu
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $kategori
     * @return \Illuminate\View\View
     */
    public function kategoriDetail(Request $request, $kategori)
    {
        // Filter tahun
        $tahun = $request->input('tahun', date('Y'));

        // Dapatkan data kategori
        $kategoriData = Produk::where('kategori_lv1', $kategori)->first();

        if (!$kategoriData) {
            return redirect()->route('ekatalog.analytics.ajax')->with('error', 'Kategori tidak ditemukan');
        }

        return view('tenderid.ekatalog.kategori_detail_ajax', compact(
            'kategori',
            'tahun'
        ));
    }

    /**
     * Mendapatkan data detail kategori
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getKategoriDetail(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));
            $kategori = $request->input('kategori');

            if (!$kategori) {
                return response()->json(['error' => 'Parameter kategori diperlukan'], 400);
            }

            // Tren pembelian kategori per bulan
            $trenBulanan = TransaksiPaket::join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
                ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('MONTH(ekatalog_paket_pengadaan.tanggal_paket) as bulan, SUM(ekatalog_transaksi_paket.total_harga) as total_nilai')
                ->where('ekatalog_produk.kategori_lv1', $kategori)
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->groupBy(DB::raw('MONTH(ekatalog_paket_pengadaan.tanggal_paket)'))
                ->orderBy('bulan')
                ->get();

            // Top produk dalam kategori
            $topProdukKategori = TransaksiPaket::join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
                ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('ekatalog_produk.nama_produk, SUM(ekatalog_transaksi_paket.kuantitas_produk) as total_kuantitas, SUM(ekatalog_transaksi_paket.total_harga) as total_nilai')
                ->where('ekatalog_produk.kategori_lv1', $kategori)
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->groupBy('ekatalog_produk.nama_produk')
                ->orderBy('total_kuantitas', 'desc')
                ->limit(10)
                ->get();

            // Top instansi pembeli untuk kategori ini
            $topInstansiKategori = TransaksiPaket::join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
                ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('ekatalog_paket_pengadaan.instansi_pembeli, COUNT(*) as jumlah_paket, SUM(ekatalog_transaksi_paket.total_harga) as total_nilai')
                ->where('ekatalog_produk.kategori_lv1', $kategori)
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->groupBy('ekatalog_paket_pengadaan.instansi_pembeli')
                ->orderBy('total_nilai', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'trenBulanan' => $trenBulanan,
                'topProdukKategori' => $topProdukKategori,
                'topInstansiKategori' => $topInstansiKategori
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
