<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ProfileController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        return view('tenderid.profile.index', compact('user'));
    }

    public function edit()
    {
        $user = auth()->user();
        return view('tenderid.profile.edit', compact('user'));
    }

    public function update(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|regex:/^[1-9][0-9]{9,15}$/|min:10|max:15',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048'
        ], [
            'phone.regex' => 'Format nomor WhatsApp tidak valid. Gunakan format 628xxx (tanpa tanda + atau awalan 0)',
            'phone.min' => 'Nomor WhatsApp minimal 10 digit',
            'phone.max' => 'Nomor WhatsApp maksimal 15 digit'
        ]);

        $data = $request->only(['name', 'email', 'phone']);

        if ($request->hasFile('photo')) {
            // Delete old photo if exists
            if ($user->photo && \Storage::disk('public')->exists($user->photo)) {
                \Storage::disk('public')->delete($user->photo);
            }

            // Store new photo
            $data['photo'] = $request->file('photo')->store('photos', 'public');
        }

        $user->update($data);

        return redirect()->route('tenderid.profile.index')->with('success', 'Profile updated successfully.');
    }

    public function showChangePasswordForm()
    {
        return view('profile.change-password');
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        auth()->user()->update([
            'password' => Hash::make($request->password)
        ]);

        return redirect()
            ->route('profile.change-password')
            ->with('success', 'Password has been updated successfully.');
    }
}
