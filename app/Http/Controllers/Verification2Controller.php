<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class Verification2Controller extends Controller
{
    public function showResendForm(Request $request)
    {
        // Verify that the user is authenticated
        $user = Auth::user();

        // Check if the user has already verified their email
        if ($user->hasVerifiedEmail()) {
            return redirect()->route('dashboard')->with('message', 'Your email is already verified.');
        }

        // Perform the POST request to resend the email
        // This uses Laravel's HTTP client to make a POST request to the route Fortify would normally handle
        $response = Http::withHeaders([
            'X-CSRF-TOKEN' => csrf_token(),
        ])->post(route('verification.resend'));

        // Redirect back with a success message
        return redirect()->back()->with('status', 'Verification email has been resent. Please check your inbox.');
    }
}
