<?php

namespace App\Http\Controllers;

use App\Helpers\WhatsAppHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class WhatsAppIntegrationController extends Controller
{
    protected $whatsappServiceUrl;
    protected $apiKey;

    public function __construct()
    {
        $this->whatsappServiceUrl = config('services.whatsapp.url'); // e.g. https://whatsapp-service.example.com/api

        // Pastikan URL menggunakan HTTPS
        if (strpos($this->whatsappServiceUrl, 'http:') === 0) {
            $this->whatsappServiceUrl = str_replace('http:', 'https:', $this->whatsappServiceUrl);
        }

        $this->apiKey = config('services.whatsapp.api_key');
    }

    protected function headers()
    {
        return [
            'X-API-KEY' => $this->apiKey,
            'Accept' => 'application/json',
            'Authorization' => "Bearer {$this->apiKey}", // Tambahkan header Authorization
        ];
    }

    public function getQR()
    {
        // Hapus cache QR code yang ada
        if (\Cache::has('whatsapp_qr_code')) {
            \Cache::forget('whatsapp_qr_code');
            \Log::info('Deleted existing QR code cache');
        }

        try {
            // Set timeout yang lebih pendek untuk menghindari blocking
            $response = Http::timeout(5)->withHeaders($this->headers())->get("{$this->whatsappServiceUrl}/auth/qr");

            // Log response untuk debugging
            \Log::info('QR API Response:', [
                'status' => $response->status(),
                'body_preview' => substr($response->body(), 0, 100) . '...',
                'url' => "{$this->whatsappServiceUrl}/auth/qr",
                'headers' => $this->headers()
            ]);

            if ($response->successful()) {
                $data = $response->json();

                // Pastikan QR code ada dan valid
                if (isset($data['qrCode'])) {
                    // Pastikan QR code memiliki prefix yang benar
                    $qrCode = $data['qrCode'];
                    if (strpos($qrCode, 'data:image/png;base64,') !== 0) {
                        $qrCode = "data:image/png;base64,{$qrCode}";
                    }

                    // Simpan ke cache selama 2 menit
                    \Cache::put('whatsapp_qr_code', [
                        'qrCode' => $qrCode,
                        'timestamp' => now()->toDateTimeString()
                    ], now()->addMinutes(2));

                    return response()->json([
                        'success' => true,
                        'qrCode' => $qrCode
                    ]);
                } else {
                    \Log::warning('QR code tidak valid atau tidak ada dalam response', [
                        'data_keys' => array_keys($data)
                    ]);
                    return response()->json(['error' => 'QR code tidak valid atau tidak ada'], 400);
                }
            }

            \Log::error('Failed to get QR code', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            return response()->json(['error' => 'Failed to get QR code: ' . $response->body()], 500);
        } catch (\Exception $e) {
            \Log::error('Exception when getting QR code', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Jika terjadi error, coba gunakan cache jika ada
            if (\Cache::has('whatsapp_qr_code')) {
                $cachedData = \Cache::get('whatsapp_qr_code');
                \Log::info('Returning cached QR code after error', ['cache_time' => $cachedData['timestamp']]);
                return response()->json([
                    'success' => true,
                    'qrCode' => $cachedData['qrCode'],
                    'cached' => true,
                    'cache_time' => $cachedData['timestamp']
                ]);
            }

            return response()->json(['error' => 'Exception: ' . $e->getMessage()], 500);
        }
    }

    public function getStatus()
    {
        // Cek cache terlebih dahulu (cache selama 10 detik)
        if (\Cache::has('whatsapp_status')) {
            $cachedData = \Cache::get('whatsapp_status');
            \Log::info('Returning cached WhatsApp status', ['cache_time' => $cachedData['timestamp']]);
            return response()->json([
                'status' => $cachedData['status'],
                'cached' => true,
                'cache_time' => $cachedData['timestamp']
            ]);
        }

        try {
            // Set timeout yang lebih pendek untuk menghindari blocking
            $response = Http::timeout(3)->withHeaders($this->headers())->get("{$this->whatsappServiceUrl}/auth/status");

            if ($response->successful()) {
                $data = $response->json();

                // Simpan ke cache selama 10 detik
                \Cache::put('whatsapp_status', [
                    'status' => $data,
                    'timestamp' => now()->toDateTimeString()
                ], now()->addSeconds(10));

                return response()->json($data);
            }

            // Jika gagal, coba gunakan cache jika ada
            if (\Cache::has('whatsapp_status')) {
                $cachedData = \Cache::get('whatsapp_status');
                \Log::info('Returning cached WhatsApp status after failed request', ['cache_time' => $cachedData['timestamp']]);
                return response()->json([
                    'status' => $cachedData['status'],
                    'cached' => true,
                    'cache_time' => $cachedData['timestamp']
                ]);
            }

            return response()->json(['error' => 'Failed to get status'], 500);
        } catch (\Exception $e) {
            \Log::error('Exception when getting WhatsApp status', [
                'message' => $e->getMessage()
            ]);

            // Jika terjadi error, coba gunakan cache jika ada
            if (\Cache::has('whatsapp_status')) {
                $cachedData = \Cache::get('whatsapp_status');
                \Log::info('Returning cached WhatsApp status after error', ['cache_time' => $cachedData['timestamp']]);
                return response()->json([
                    'status' => $cachedData['status'],
                    'cached' => true,
                    'cache_time' => $cachedData['timestamp']
                ]);
            }

            return response()->json(['error' => 'Exception: ' . $e->getMessage()], 500);
        }
    }

    public function sendMessage(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'templateId' => 'required|string',
            'data' => 'nullable|array',
        ]);

        $payload = [
            'phone' => $request->phone,
            'templateId' => $request->templateId,
            'data' => $request->data ?? [],
        ];

        try {
            // Set timeout yang lebih pendek untuk menghindari blocking
            $response = Http::timeout(5)->withHeaders($this->headers())
                ->post("{$this->whatsappServiceUrl}/message/send", $payload);

            if ($response->successful()) {
                return response()->json($response->json());
            }

            \Log::error('Failed to send WhatsApp message', [
                'status' => $response->status(),
                'body' => $response->body(),
                'payload' => $payload
            ]);

            return response()->json(['error' => 'Failed to send message: ' . $response->body()], 500);
        } catch (\Exception $e) {
            \Log::error('Exception when sending WhatsApp message', [
                'message' => $e->getMessage(),
                'payload' => $payload
            ]);

            return response()->json(['error' => 'Exception: ' . $e->getMessage()], 500);
        }
    }

    public function sendWelcomeMessage(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'userData' => 'nullable|array',
        ]);

        // Ensure userData has required fields
        $userData = $request->userData ?? [];

        // Make sure name is present
        if (!isset($userData['name']) || empty($userData['name'])) {
            return response()->json(['error' => 'User name is required in userData'], 400);
        }

        // Use helper with retry logic
        $response = WhatsAppHelper::sendWelcomeMessage($request->phone, $userData);

        if ($response && $response->successful()) {
            return response()->json($response->json());
        }

        return response()->json([
            'error' => 'Failed to send welcome message',
            'details' => $response ? $response->body() : 'No response from service'
        ], 500);
    }

    // Method to send a test message with complete payload
    public function sendTestMessage()
    {
        // Test welcome message
        $phone = '6281343596588'; // Admin phone number

        // Complete userData for welcome message
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '6281234567890'
        ];

        // Use helper with retry logic
        $response = WhatsAppHelper::sendWelcomeMessage($phone, $userData);

        $responseBody = $response ? $response->body() : 'No response';
        $statusCode = $response ? $response->status() : 500;

        if ($response && $response->successful()) {
            return response()->json([
                'success' => true,
                'message' => 'Test welcome message sent successfully',
                'response' => $response->json()
            ]);
        }

        // Test payment notification
        $paymentData = [
            'date' => date('Y-m-d'),
            'amount' => '300000',
            'description' => 'Pembayaran Langganan Bulanan'
        ];

        $paymentResponse = WhatsAppHelper::sendPaymentNotification($phone, 'success', $paymentData);

        if ($paymentResponse && $paymentResponse->successful()) {
            return response()->json([
                'success' => true,
                'message' => 'Test payment notification sent successfully',
                'response' => $paymentResponse->json()
            ]);
        }

        return response()->json([
            'success' => false,
            'error' => 'Failed to send test messages',
            'welcome_status_code' => $statusCode,
            'welcome_response' => $responseBody,
            'payment_response' => $paymentResponse ? $paymentResponse->body() : 'No response'
        ], 500);
    }
}
