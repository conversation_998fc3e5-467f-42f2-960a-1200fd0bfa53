<?php
namespace App\Http\Controllers;

use App\Models\Ekatalog6;
use Illuminate\Http\Request;

class Ekatalog6Controller extends Controller
{
    /**
     * Menampilkan daftar Ekatalog6 dengan filter dan pagination.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $search = $request->input('search');
        $instansi = $request->input('instansi');
        $tanggalMulai = $request->input('tanggal_mulai');
        $tanggalAkhir = $request->input('tanggal_akhir');

        $query = Ekatalog6::with(['penyedia', 'klpd']);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('nama_paket', 'LIKE', '%' . $search . '%')
                  ->orWhere('kode', 'LIKE', '%' . $search . '%')
                  ->orWhere('nama_satker', 'LIKE', '%' . $search . '%');
            });
        }

        if ($instansi) {
            $query->whereHas('klpd', function ($q) use ($instansi) {
                $q->where('nama_klpd', $instansi);
            });
        }

        if ($tanggalMulai && $tanggalAkhir) {
            // Validate if inputs are numeric years
            if (is_numeric($tanggalMulai) && is_numeric($tanggalAkhir)) {
                $query->whereBetween('tahun_anggaran', [(int)$tanggalMulai, (int)$tanggalAkhir]);
            }
        }

        $query->orderBy('kode_rup', 'desc');

        $ekatalog6List = $query->simplePaginate(25)->appends([
            'search' => $search,
            'instansi' => $instansi,
            'tanggal_mulai' => $tanggalMulai,
            'tanggal_akhir' => $tanggalAkhir,
        ]);

        $instansiList = \App\Models\Klpd::select('nama_klpd')
            ->distinct()
            ->orderBy('nama_klpd', 'asc')
            ->pluck('nama_klpd');

        return view('tenderid.ekatalog6.index', [
            'ekatalog6List' => $ekatalog6List,
            'instansi_list' => $instansiList,
            'search' => $search,
            'instansiSelected' => $instansi,
            'tanggalMulai' => $tanggalMulai,
            'tanggalAkhir' => $tanggalAkhir,
        ]);
    }
}
