<?php

namespace App\Http\Controllers;

use App\Models\PaketPengadaan;
use App\Models\TransaksiPaket;
use App\Models\Produk;
use App\Models\ListPerusahaanTender;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EkatalogAnalysisController extends Controller
{
    /**
     * Menampilkan halaman analisis ekatalog
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Hanya mengirimkan periode yang dipilih ke view
        $period = $request->input('period', 'all');

        return view('tenderid.ekatalog.analysis', compact('period'));
    }

    /**
     * Mendapatkan statistik umum untuk API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStatistics(Request $request)
    {
        // Filter berdasarkan periode
        list($startDate, $endDate) = $this->getDateRange($request->input('period', 'all'));

        $statistics = $this->fetchStatistics($startDate, $endDate);

        return response()->json($statistics);
    }

    /**
     * Mendapatkan tren bulanan untuk API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMonthlyTrend(Request $request)
    {
        $monthlyTrend = $this->fetchMonthlyTrend();

        return response()->json($monthlyTrend);
    }

    /**
     * Mendapatkan produk termahal dan paling banyak dibeli untuk API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopProducts(Request $request)
    {
        // Filter berdasarkan periode
        list($startDate, $endDate) = $this->getDateRange($request->input('period', 'all'));

        $topProducts = $this->fetchTopProducts($startDate, $endDate);

        return response()->json($topProducts);
    }

    /**
     * Mendapatkan penyedia paling banyak untuk API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopVendors(Request $request)
    {
        // Filter berdasarkan periode
        list($startDate, $endDate) = $this->getDateRange($request->input('period', 'all'));

        $topVendors = $this->fetchTopVendors($startDate, $endDate);

        return response()->json($topVendors);
    }

    /**
     * Mendapatkan pelaksana paling banyak untuk API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopExecutors(Request $request)
    {
        // Filter berdasarkan periode
        list($startDate, $endDate) = $this->getDateRange($request->input('period', 'all'));

        $topExecutors = $this->fetchTopExecutors($startDate, $endDate);

        return response()->json($topExecutors);
    }

    /**
     * Mendapatkan merek paling laris untuk API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopBrands(Request $request)
    {
        // Filter berdasarkan periode
        list($startDate, $endDate) = $this->getDateRange($request->input('period', 'all'));

        $topBrands = $this->fetchTopBrands($startDate, $endDate);

        return response()->json($topBrands);
    }

    /**
     * Mendapatkan kategori paling populer untuk API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopCategories(Request $request)
    {
        // Filter berdasarkan periode
        list($startDate, $endDate) = $this->getDateRange($request->input('period', 'all'));

        $topCategories = $this->fetchTopCategories($startDate, $endDate);

        return response()->json($topCategories);
    }

    /**
     * Mendapatkan instansi pembeli paling aktif untuk API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopBuyers(Request $request)
    {
        // Filter berdasarkan periode
        list($startDate, $endDate) = $this->getDateRange($request->input('period', 'all'));

        $topBuyers = $this->fetchTopBuyers($startDate, $endDate);

        return response()->json($topBuyers);
    }

    /**
     * Helper untuk mendapatkan range tanggal berdasarkan periode
     *
     * @param string $period
     * @return array
     */
    private function getDateRange($period)
    {
        $startDate = null;
        $endDate = Carbon::now();

        switch ($period) {
            case 'today':
                $startDate = Carbon::today();
                break;
            case 'week':
                $startDate = Carbon::now()->subWeek();
                break;
            case 'month':
                $startDate = Carbon::now()->subMonth();
                break;
            case 'year':
                $startDate = Carbon::now()->subYear();
                break;
            default:
                $startDate = null;
                break;
        }

        return [$startDate, $endDate];
    }

    /**
     * Mendapatkan produk termahal dan paling banyak dibeli
     *
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    private function fetchTopProducts($startDate = null, $endDate = null)
    {
        // Query untuk produk termahal
        $mostExpensiveQuery = TransaksiPaket::select(
                'ekatalog_produk.id_produk',
                'ekatalog_produk.nama_produk',
                'ekatalog_produk.kategori_lv1',
                'ekatalog_produk.kategori_lv2',
                'ekatalog_produk.nama_manufaktur',
                DB::raw('MAX(ekatalog_transaksi_paket.harga_satuan) as max_price'),
                DB::raw('SUM(ekatalog_transaksi_paket.kuantitas_produk) as total_quantity'),
                DB::raw('COUNT(DISTINCT ekatalog_transaksi_paket.id_paket) as total_packages')
            )
            ->join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
            ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket');

        // Query untuk produk paling banyak dibeli
        $mostPurchasedQuery = TransaksiPaket::select(
                'ekatalog_produk.id_produk',
                'ekatalog_produk.nama_produk',
                'ekatalog_produk.kategori_lv1',
                'ekatalog_produk.kategori_lv2',
                'ekatalog_produk.nama_manufaktur',
                DB::raw('SUM(ekatalog_transaksi_paket.kuantitas_produk) as total_quantity'),
                DB::raw('AVG(ekatalog_transaksi_paket.harga_satuan) as avg_price'),
                DB::raw('COUNT(DISTINCT ekatalog_transaksi_paket.id_paket) as total_packages')
            )
            ->join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
            ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket');

        // Query untuk produk dengan nilai transaksi tertinggi
        $highestValueQuery = TransaksiPaket::select(
                'ekatalog_produk.id_produk',
                'ekatalog_produk.nama_produk',
                'ekatalog_produk.kategori_lv1',
                'ekatalog_produk.kategori_lv2',
                'ekatalog_produk.nama_manufaktur',
                DB::raw('SUM(ekatalog_transaksi_paket.total_harga) as total_value'),
                DB::raw('SUM(ekatalog_transaksi_paket.kuantitas_produk) as total_quantity'),
                DB::raw('COUNT(DISTINCT ekatalog_transaksi_paket.id_paket) as total_packages')
            )
            ->join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
            ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket');

        // Menerapkan filter tanggal jika ada
        if ($startDate && $endDate) {
            $mostExpensiveQuery->whereBetween('ekatalog_paket_pengadaan.tanggal_paket', [$startDate, $endDate]);
            $mostPurchasedQuery->whereBetween('ekatalog_paket_pengadaan.tanggal_paket', [$startDate, $endDate]);
            $highestValueQuery->whereBetween('ekatalog_paket_pengadaan.tanggal_paket', [$startDate, $endDate]);
        }

        // Menyelesaikan query
        $mostExpensive = $mostExpensiveQuery
            ->groupBy('ekatalog_produk.id_produk', 'ekatalog_produk.nama_produk', 'ekatalog_produk.kategori_lv1', 'ekatalog_produk.kategori_lv2', 'ekatalog_produk.nama_manufaktur')
            ->orderBy('max_price', 'desc')
            ->limit(10)
            ->get();

        $mostPurchased = $mostPurchasedQuery
            ->groupBy('ekatalog_produk.id_produk', 'ekatalog_produk.nama_produk', 'ekatalog_produk.kategori_lv1', 'ekatalog_produk.kategori_lv2', 'ekatalog_produk.nama_manufaktur')
            ->orderBy('total_quantity', 'desc')
            ->limit(10)
            ->get();

        $highestValue = $highestValueQuery
            ->groupBy('ekatalog_produk.id_produk', 'ekatalog_produk.nama_produk', 'ekatalog_produk.kategori_lv1', 'ekatalog_produk.kategori_lv2', 'ekatalog_produk.nama_manufaktur')
            ->orderBy('total_value', 'desc')
            ->limit(10)
            ->get();

        return [
            'most_expensive' => $mostExpensive,
            'most_purchased' => $mostPurchased,
            'highest_value' => $highestValue
        ];
    }

    /**
     * Mendapatkan penyedia paling banyak
     *
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return \Illuminate\Support\Collection
     */
    private function fetchTopVendors($startDate = null, $endDate = null)
    {
        $query = PaketPengadaan::select(
                'list_perusahaan_tender.id',
                'list_perusahaan_tender.nama_peserta',
                'list_perusahaan_tender.npwp',
                'list_perusahaan_tender.npwp_blur',
                'list_perusahaan_tender.pembeda',
                DB::raw('COUNT(DISTINCT ekatalog_paket_pengadaan.id_paket) as total_packages'),
                DB::raw('SUM(ekatalog_transaksi_paket.total_harga) as total_value')
            )
            ->join('list_perusahaan_tender', 'ekatalog_paket_pengadaan.id_penyedia', '=', 'list_perusahaan_tender.id')
            ->leftJoin('ekatalog_transaksi_paket', 'ekatalog_paket_pengadaan.id_paket', '=', 'ekatalog_transaksi_paket.id_paket');

        // Menerapkan filter tanggal jika ada
        if ($startDate && $endDate) {
            $query->whereBetween('ekatalog_paket_pengadaan.tanggal_paket', [$startDate, $endDate]);
        }

        return $query->groupBy('list_perusahaan_tender.id', 'list_perusahaan_tender.nama_peserta', 'list_perusahaan_tender.npwp', 'list_perusahaan_tender.npwp_blur', 'list_perusahaan_tender.pembeda')
            ->orderBy('total_packages', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Mendapatkan pelaksana paling banyak
     *
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return \Illuminate\Support\Collection
     */
    private function fetchTopExecutors($startDate = null, $endDate = null)
    {
        $query = PaketPengadaan::select(
                'list_perusahaan_tender.id',
                'list_perusahaan_tender.nama_peserta',
                'list_perusahaan_tender.npwp',
                'list_perusahaan_tender.npwp_blur',
                'list_perusahaan_tender.pembeda',
                DB::raw('COUNT(DISTINCT ekatalog_paket_pengadaan.id_paket) as total_packages'),
                DB::raw('SUM(ekatalog_transaksi_paket.total_harga) as total_value')
            )
            ->join('list_perusahaan_tender', 'ekatalog_paket_pengadaan.id_pelaksana', '=', 'list_perusahaan_tender.id')
            ->leftJoin('ekatalog_transaksi_paket', 'ekatalog_paket_pengadaan.id_paket', '=', 'ekatalog_transaksi_paket.id_paket');

        // Menerapkan filter tanggal jika ada
        if ($startDate && $endDate) {
            $query->whereBetween('ekatalog_paket_pengadaan.tanggal_paket', [$startDate, $endDate]);
        }

        return $query->groupBy('list_perusahaan_tender.id', 'list_perusahaan_tender.nama_peserta', 'list_perusahaan_tender.npwp', 'list_perusahaan_tender.npwp_blur', 'list_perusahaan_tender.pembeda')
            ->orderBy('total_packages', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Mendapatkan merek paling laris
     *
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return \Illuminate\Support\Collection
     */
    private function fetchTopBrands($startDate = null, $endDate = null)
    {
        $query = TransaksiPaket::select(
                'ekatalog_produk.nama_manufaktur',
                DB::raw('COUNT(DISTINCT ekatalog_transaksi_paket.id_paket) as total_packages'),
                DB::raw('COUNT(DISTINCT ekatalog_produk.id_produk) as total_products'),
                DB::raw('SUM(ekatalog_transaksi_paket.total_harga) as total_value')
            )
            ->join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
            ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
            ->whereNotNull('ekatalog_produk.nama_manufaktur');

        // Menerapkan filter tanggal jika ada
        if ($startDate && $endDate) {
            $query->whereBetween('ekatalog_paket_pengadaan.tanggal_paket', [$startDate, $endDate]);
        }

        return $query->groupBy('ekatalog_produk.nama_manufaktur')
            ->orderBy('total_packages', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Mendapatkan kategori paling populer
     *
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return \Illuminate\Support\Collection
     */
    private function fetchTopCategories($startDate = null, $endDate = null)
    {
        $query = TransaksiPaket::select(
                'ekatalog_produk.kategori_lv1',
                DB::raw('COUNT(DISTINCT ekatalog_transaksi_paket.id_paket) as total_packages'),
                DB::raw('COUNT(DISTINCT ekatalog_produk.id_produk) as total_products'),
                DB::raw('SUM(ekatalog_transaksi_paket.total_harga) as total_value')
            )
            ->join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
            ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
            ->whereNotNull('ekatalog_produk.kategori_lv1');

        // Menerapkan filter tanggal jika ada
        if ($startDate && $endDate) {
            $query->whereBetween('ekatalog_paket_pengadaan.tanggal_paket', [$startDate, $endDate]);
        }

        return $query->groupBy('ekatalog_produk.kategori_lv1')
            ->orderBy('total_packages', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Mendapatkan instansi pembeli paling aktif
     *
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return \Illuminate\Support\Collection
     */
    private function fetchTopBuyers($startDate = null, $endDate = null)
    {
        // Query untuk instansi pembeli dengan jumlah paket terbanyak
        $topByPackages = PaketPengadaan::select(
                'instansi_pembeli',
                'satuan_kerja',
                DB::raw('COUNT(DISTINCT id_paket) as total_packages'),
                DB::raw('SUM(COALESCE((SELECT SUM(total_harga) FROM ekatalog_transaksi_paket WHERE id_paket = ekatalog_paket_pengadaan.id_paket), 0)) as total_value')
            )
            ->whereNotNull('instansi_pembeli')
            ->where('instansi_pembeli', '!=', '');

        // Query untuk instansi pembeli dengan nilai transaksi tertinggi
        $topByValue = PaketPengadaan::select(
                'instansi_pembeli',
                'satuan_kerja',
                DB::raw('COUNT(DISTINCT id_paket) as total_packages'),
                DB::raw('SUM(COALESCE((SELECT SUM(total_harga) FROM ekatalog_transaksi_paket WHERE id_paket = ekatalog_paket_pengadaan.id_paket), 0)) as total_value')
            )
            ->whereNotNull('instansi_pembeli')
            ->where('instansi_pembeli', '!=', '');

        // Menerapkan filter tanggal jika ada
        if ($startDate && $endDate) {
            $topByPackages->whereBetween('tanggal_paket', [$startDate, $endDate]);
            $topByValue->whereBetween('tanggal_paket', [$startDate, $endDate]);
        }

        // Menyelesaikan query
        $topByPackages = $topByPackages
            ->groupBy('instansi_pembeli', 'satuan_kerja')
            ->orderBy('total_packages', 'desc')
            ->limit(10)
            ->get();

        $topByValue = $topByValue
            ->groupBy('instansi_pembeli', 'satuan_kerja')
            ->orderBy('total_value', 'desc')
            ->limit(10)
            ->get();

        return [
            'top_by_packages' => $topByPackages,
            'top_by_value' => $topByValue
        ];
    }

    /**
     * Mendapatkan tren bulanan
     *
     * @return \Illuminate\Support\Collection
     */
    private function fetchMonthlyTrend()
    {
        // Mendapatkan data 12 bulan terakhir
        $startDate = Carbon::now()->subMonths(11)->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        $monthlyData = PaketPengadaan::select(
                DB::raw('DATE_FORMAT(tanggal_paket, "%Y-%m") as month'),
                DB::raw('COUNT(DISTINCT id_paket) as total_packages'),
                DB::raw('SUM(COALESCE((SELECT SUM(total_harga) FROM ekatalog_transaksi_paket WHERE id_paket = ekatalog_paket_pengadaan.id_paket), 0)) as total_value')
            )
            ->whereBetween('tanggal_paket', [$startDate, $endDate])
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Mengisi bulan yang kosong
        $result = [];
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $yearMonth = $currentDate->format('Y-m');
            $found = false;

            foreach ($monthlyData as $item) {
                if ($item->month === $yearMonth) {
                    $result[] = $item;
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $result[] = (object) [
                    'month' => $yearMonth,
                    'total_packages' => 0,
                    'total_value' => 0
                ];
            }

            $currentDate->addMonth();
        }

        return collect($result);
    }

    /**
     * Mendapatkan statistik umum
     *
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return object
     */
    private function fetchStatistics($startDate = null, $endDate = null)
    {
        $query = PaketPengadaan::select(
            DB::raw('COUNT(DISTINCT id_paket) as total_packages'),
            DB::raw('COUNT(DISTINCT id_penyedia) as total_vendors'),
            DB::raw('COUNT(DISTINCT id_pelaksana) as total_executors'),
            DB::raw('SUM(COALESCE((SELECT SUM(total_harga) FROM ekatalog_transaksi_paket WHERE id_paket = ekatalog_paket_pengadaan.id_paket), 0)) as total_value'),
            DB::raw('COUNT(DISTINCT (SELECT id_produk FROM ekatalog_transaksi_paket WHERE id_paket = ekatalog_paket_pengadaan.id_paket LIMIT 1)) as total_products')
        );

        // Menerapkan filter tanggal jika ada
        if ($startDate && $endDate) {
            $query->whereBetween('tanggal_paket', [$startDate, $endDate]);
        }

        return $query->first();
    }
}
