<?php
namespace App\Http\Controllers;

use App\Models\DataSirup;
use App\Models\ListPerusahaanTender; // Model untuk data tender
use App\Models\Lpse;                 // Model untuk data SIRUP
use App\Models\PaketPengadaan;       // Model untuk data perusahaan
use App\Models\TenderLpse;           // Model untuk data e-Katalog
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Menampilkan halaman dashboard utama.
     */
    public function index()
    {
        $user = auth()->user();

        $loginLogs = \App\Models\LoginLog::where('user_id', $user->id)
            ->orderBy('login_at', 'desc')
            ->take(8)
            ->get(['ip_address', 'login_at', 'user_agent', 'browser', 'os']);

        return view('tenderid.dashboard.index', compact('loginLogs'));
    }

    /**
     * Mengembalikan data harian untuk jenis tertentu melalui API.
     */

    public function getData(Request $request)
    {
        $type = $request->get('type');

        switch ($type) {
            case 'tender':
                $data     = TenderLpse::getTenderCountPerDay();
                $totalSum = TenderLpse::whereHas('anggaran', function ($query) {
                    $query->where('ang_tahun', date('Y'));
                })->count();
                break;
            case 'sirup':
                $data     = DataSirup::getSirupCountPerDay();
                $totalSum = DataSirup::where('tahun_anggaran', date('Y'))->count();
                break;
            case 'perusahaan':
                $data     = ListPerusahaanTender::getPerusahaanCountPerDay();
                $totalSum = ListPerusahaanTender::count();
                break;
            case 'ekatalog':
                $data     = PaketPengadaan::getEKatalogCountPerDay();
                $totalSum = PaketPengadaan::count();
                break;
            default:
                return response()->json(['error' => 'Invalid type'], 400);
        }

        // Format tanggal
        $data = collect($data)->map(function ($item) {
            $item['tanggal_string'] = $this->formatDateIndonesian($item['tanggal']);
            return $item;
        });
        $data     = $data->toArray();
        $totalSum = (int) $totalSum;
        return response()->json(['data' => $data, 'totalSum' => $totalSum]);
    }

    /**
     * Fungsi untuk format tanggal ke Bahasa Indonesia.
     */
    private function formatDateIndonesian($date)
    {
        $months = [
            1 => 'Januari',
            'Februari',
            'Maret',
            'April',
            'Mei',
            'Juni',
            'Juli',
            'Agustus',
            'September',
            'Oktober',
            'November',
            'Desember',
        ];

        $dateParts = explode('-', $date);
        $day       = $dateParts[2];
        $month     = $months[(int) $dateParts[1]];
        $year      = $dateParts[0];

        return "{$day} {$month} {$year}";
    }

    public function getChartTenderData()
    {
        $data = DB::table('tender_umum_publik')
            ->selectRaw("YEAR(tanggal_paket_tayang_date) as tahun, MONTH(tanggal_paket_tayang_date) as bulan, COUNT(*) as total")
            ->whereYear('tanggal_paket_tayang_date', '>=', 2024)
            ->whereYear('tanggal_paket_tayang_date', '<=', 2025)
            ->groupBy('tahun', 'bulan')
            ->orderBy('tahun')
            ->orderBy('bulan')
            ->get();

        // Format data untuk ApexCharts
        $formattedData = [
            "2024" => array_fill(1, 12, 0), // Inisialisasi data dari Januari-Desember
            "2025" => array_fill(1, 12, 0),
        ];

        foreach ($data as $row) {
            $formattedData[$row->tahun][$row->bulan] = $row->total;
        }

        return response()->json($formattedData);
    }

    public function getChartEkatalogData()
    {
        $data = PaketPengadaan::selectRaw('tahun, bulan, COUNT(*) as jumlah')
            ->whereBetween('tanggal', ['2024-01-01', '2025-12-31'])
            ->groupBy('tahun', 'bulan')
            ->orderBy('tahun')
            ->orderBy('bulan')
            ->get();

        $formattedData = [
            '2024' => array_fill(1, 12, 0), // Isi default 0 untuk 12 bulan
            '2025' => array_fill(1, 12, 0),
        ];

        foreach ($data as $row) {
            $formattedData[$row->tahun][$row->bulan] = $row->jumlah;
        }

        return response()->json($formattedData);
    }

    public function getChartKategoriPekerjaan()
    {
        $data = TenderLpse::selectRaw('kategori_pekerjaan, COUNT(*) as jumlah')
            ->whereNotNull('kategori_pekerjaan')
        //->where('status_tender', 'Aktif')
            ->groupBy('kategori_pekerjaan')
            ->orderBy('jumlah', 'desc')
            ->get();

        return response()->json([
            'categories' => $data->pluck('kategori_pekerjaan'),
            'values'     => $data->pluck('jumlah'),
        ]);
    }

    public function getChartStatusUMKMEkatalog()
    {
        $data = PaketPengadaan::selectRaw('status_umkm, COUNT(*) as jumlah')
            ->whereNotNull('status_umkm')
            ->groupBy('status_umkm')
            ->get();

        return response()->json([
            'categories' => $data->pluck('status_umkm'),
            'values'     => $data->pluck('jumlah'),
        ]);
    }

    public function getTopTenders(Request $request)
    {
        return $this->getTopTendersByPeriod('1 year');
    }

    public function getTopTendersByPeriod($period)
    {
        $cutoffTime = Carbon::now()->sub($period);

        $tenders = TenderLpse::select(
            'tender_umum_publik.kode_tender',
            'tender_umum_publik.nama_paket',
            'tender_umum_publik.hps',
            'lpse.nama_lpse',
            'lpse.logo_path'
        )
            ->join('lpse', 'lpse.kd_lpse', '=', 'tender_umum_publik.kd_lpse')
            ->where('tender_umum_publik.tanggal_paket_tayang', '>=', $cutoffTime)
            ->where('tender_umum_publik.status_tender', 'Aktif')
            ->orderBy('tender_umum_publik.hps', 'desc')
            ->take(5)
            ->get();

        return response()->json([
            'status' => 'success',
            'data'   => $tenders,
        ]);
    }

    public function getTopEkatalogByPeriod($period)
    {
        $cutoffTime = Carbon::now()->sub($period)->toDateString();

        $paketPengadaan = PaketPengadaan::select(
            'nomor_paket as noPaket',
            'instansi_pembeli as instansi',
            'nama_paket as judul',
            'nilai_kontrakz as kontrak',
            'id_paket as id'
        )
            ->where('tanggal', '>=', $cutoffTime)
            ->orderBy('kontrak', 'desc')
            ->take(5)
            ->get();

        foreach ($paketPengadaan as $paket) {
            $nama_klpd = $paket->instansi;
            if (strpos($nama_klpd, "Kab.") !== false) {
                $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
            }
            $lpse = Lpse::where('nama_lpse', 'LIKE', "%{$nama_klpd}%")->first();

            $paket->logo_path = $lpse ? ($lpse->logo_path ?? '/build/images/logo-default.png') : '/build/images/logo-default.png';
        }

        return response()->json([
            'status' => 'success',
            'data'   => $paketPengadaan,
        ]);
    }

    public function getTopEkatalog()
    {
        return $this->getTopEkatalogByPeriod('1 year');
    }

    public function getTopEkatalog6ByPeriod($period)
    {
        $cutoffDate = Carbon::now()->sub($period);

        $ekatalog6 = \App\Models\Ekatalog6::select(
            'ekatalog6.kode as id',
            'ekatalog6.nama_paket',
            'ekatalog6.total_pelaksanaan as nilai_kontrak',
            'klpd.nama_klpd as instansi_pembeli',
            'ekatalog6.tahun_anggaran'
        )
            ->join('klpd', 'klpd.kd_klpd', '=', 'ekatalog6.kode_klpd')
            ->where('ekatalog6.created_at', '>=', $cutoffDate)
            ->orderBy('nilai_kontrak', 'desc')
            ->take(5)
            ->get();

        foreach ($ekatalog6 as $paket) {
            $nama_klpd = $paket->instansi_pembeli;
            if (strpos($nama_klpd, "Kab.") !== false) {
                $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
            }
            $lpse = Lpse::where('nama_lpse', 'LIKE', "%{$nama_klpd}%")->first();

            $paket->logo_path = $lpse ? ($lpse->logo_path ?? '/build/images/logo-default.png') : '/build/images/logo-default.png';
        }

        return response()->json([
            'status' => 'success',
            'data'   => $ekatalog6,
        ]);
    }

    public function topPaketPengadaan(Request $request)
    {
        $paketPengadaan = PaketPengadaan::select(
            'nomor_paket as noPaket',
            'instansi_pembeli as instansi',
            'nama_paket as judul',
            'nilai_kontrakz as kontrak',
            'id_paket as id' // Sesuaikan dengan primary key
        )
            ->where('tahun', 2025)
            ->orderBy('kontrak', 'desc') // Menggunakan alias yang baru
            ->take(5)
            ->get();

        foreach ($paketPengadaan as $paket) {
            $nama_klpd = $paket->instansi;
            if (strpos($nama_klpd, "Kab.") !== false) {
                $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
            }
            $lpse = Lpse::where('nama_lpse', 'LIKE', "%{$nama_klpd}%")->first();

            // Pastikan $lpse tidak null sebelum mengakses logo_path
            $paket->logo_path = $lpse ? ($lpse->logo_path ?? '/build/images/logo-default.png') : '/build/images/logo-default.png';
        }

        return response()->json([
            'status' => 'success',
            'data'   => $paketPengadaan,
        ]);
    }

    public static function processNpwp(string $rawNpwp): array
    {
        // Hapus semua karakter kecuali angka, titik (.), dan tanda hubung (-)
        $cleanNpwp = preg_replace('/[^\d.\-]/', '', $rawNpwp);
        
        // Cek apakah input sudah dalam format npwp_blur (mengandung '*')
        $isBlurred = strpos($rawNpwp, '*') !== false;
    
        // Jika input hanya angka (16 digit) → ubah ke format NPWP
        if (!$isBlurred) {
            $digitsOnly = preg_replace('/\D/', '', $rawNpwp);
            if (strlen($digitsOnly) === 15) {
                $cleanNpwp = sprintf(
                    "%s.%s.%s.%s-%s.%s",
                    substr($digitsOnly, 0, 2),
                    substr($digitsOnly, 2, 3),
                    substr($digitsOnly, 5, 3),
                    substr($digitsOnly, 8, 1),
                    substr($digitsOnly, 9, 3),
                    substr($digitsOnly, 12, 3)
                );
            }
        }
    
        // Jika bukan npwp_blur, set npwp hasil formatting
        $npwp = $isBlurred ? null : $cleanNpwp;
    
        // Ekstrak partial NPWP
        if ($npwp === null) {
            $partialNpwp = ListPerusahaanTender::extractPartialNpwp($rawNpwp);
        } else {
            $partialNpwp = ListPerusahaanTender::extractPartialNpwp($npwp);
        }
        
    
        // Buat npwp_blur berdasarkan partial_npwp
        if (strlen($partialNpwp) === 6) {
            $npwpBlur = sprintf(
                "%s*.%s**.%s**.*-*%s%s.**%s",
                $partialNpwp[0],  // X1
                $partialNpwp[1],  // X2
                $partialNpwp[2],  // X3
                $partialNpwp[3],  // X4
                $partialNpwp[4],  // X5
                $partialNpwp[5]   // X6
            );
        } else {
            $npwpBlur = str_repeat('*', strlen($npwp ?? '')); // Jika partial_npwp tidak valid, blur semua
        }
    
        return [
            'npwp' => $npwp,
            'npwp_blur' => $npwpBlur,
            'partial_npwp' => $partialNpwp,
        ];
    }
    


    public function testNpwp(Request $request)
    {
        $npwp   = $request->query('npwp', ''); // Ambil query string npwp
        $result = $this->processNpwp($npwp);   // Panggil fungsi yang sudah dibuat

        return response()->json($result);
    }

}
