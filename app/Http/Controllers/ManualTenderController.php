<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\TenderLpse;
use App\Models\InstansiSatker;
use App\Models\Anggaran;
use App\Models\LokasiPaket;

class ManualTenderController extends Controller
{
    private $logger;

    public function __construct()
    {
        $this->logger = Log::channel('scrapeTender');
    }

    public function showForm()
    {
        return view('manual_tender');
    }

    public function showAutoFetchForm()
    {
        return view('manual_tender_auto');
    }

    public function processData(Request $request)
    {
        $jsonData = $request->input('tender_json');

        if (empty($jsonData)) {
            return back()->withErrors(['tender_json' => 'JSON data is required']);
        }

        $tenderDataList = json_decode($jsonData, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return back()->withErrors(['tender_json' => 'Invalid JSON data: ' . json_last_error_msg()]);
        }

        if (!is_array($tenderDataList)) {
            return back()->withErrors(['tender_json' => 'JSON data must be an array of tender objects']);
        }

        $newCount = 0;
        $existingCount = 0;
        $processedLpseCodes = [];

        DB::transaction(function () use ($tenderDataList, &$newCount, &$existingCount, &$processedLpseCodes) {
            foreach ($tenderDataList as $index => $tenderData) {
                if (!isset($tenderData['Kode Tender'])) {
                    $this->logger->warning("Invalid data structure for tender index {$index}. 'Kode Tender' is missing.");
                    continue;
                }

                $result = $this->processTender($tenderData);

                if ($result === 'created') {
                    $newCount++;
                } elseif ($result === 'updated') {
                    $existingCount++;
                }

                $kdLpse = $tenderData['Repo id LPSE'] ?? null;
                if ($kdLpse && !in_array($kdLpse, $processedLpseCodes)) {
                    $processedLpseCodes[] = $kdLpse;
                }
            }
        });

        $lpseCodesString = implode(', ', $processedLpseCodes);

        return back()->with('status', "Processing complete. New records: {$newCount}, Existing records updated: {$existingCount}. LPSE codes processed: {$lpseCodesString}");
    }

    private function processTender(array $tenderData): string
    {
        try {
            $newData = $this->mapTenderData($tenderData);

            $tender = TenderLpse::updateOrCreate(
                ['kode_tender' => $newData['kode_tender']],
                $newData
            );

            $this->processAdditionalData($tenderData, $newData['kode_tender']);

            return $tender->wasRecentlyCreated ? 'created' : 'updated';
        } catch (\Exception $e) {
            $this->logger->error("Error processing tender Kode Tender = {$tenderData['Kode Tender']}. Error: " . $e->getMessage());
            return 'error';
        }
    }

    private function mapTenderData(array $tenderData): array
    {
        if (isset($tenderData['Status_Tender'])) {
            if ($tenderData['Status_Tender'] === 'Tender Sedang Berjalan') {
                $tenderData['Status_Tender'] = 'Aktif';
            } elseif ($tenderData['Status_Tender'] === 'Tender Ditutup') {
                $tenderData['Status_Tender'] = 'Ditutup';
            }
        }

        return [
            'kode_tender' => (string) $tenderData['Kode Tender'],
            'kd_lpse' => $tenderData['Repo id LPSE'] ?? null,
            'status_tender' => $tenderData['Status_Tender'] ?? null,
            'nama_paket' => $tenderData['Nama Paket'] ?? null,
            'pagu' => $tenderData['Pagu'] ?? null,
            'hps' => $this->formatDecimal($tenderData['HPS'] ?? null),
            'tanggal_paket_dibuat' => $tenderData['tanggal paket dibuat'] ?? null,
            'tanggal_paket_tayang' => $tenderData['tanggal paket tayang'] ?? null,
            'kategori_pekerjaan' => $tenderData['Kategori Pekerjaan'] ?? null,
            'metode_pemilihan' => $tenderData['Metode Pemilihan'] ?? null,
            'metode_pengadaan' => $tenderData['Metode Pengadaan'] ?? null,
            'metode_evaluasi' => $tenderData['Metode Evaluasi'] ?? null,
            'cara_pembayaran' => $tenderData['Cara Pembayaran'] ?? null,
            'jenis_penetapan_pemenang' => $tenderData['Jenis Penetapan Pemenang'] ?? null,
            'apakah_paket_konsolidasi' => $tenderData['Apakah paket konsolidasi'] ?? null,
            'jumlah_pendaftar' => $tenderData['Jumlah Pendaftar'] ?? null,
            'jumlah_penawar' => $tenderData['Jumlah Penawar'] ?? null,
            'jumlah_kirim_kualifikasi' => $tenderData['jumlah_kirim_kualifikasi'] ?? null,
            'durasi_tender' => $tenderData['Durasi Tender'] ?? null,
            'versi_paket_spse' => $tenderData['Versi_spse_paket'] ?? null,
        ];
    }

    private function processAdditionalData(array $tenderData, string $kode_tender): void
    {
        if (isset($tenderData['Instansi dan Satker'])) {
            $this->processInstansiSatker($tenderData['Instansi dan Satker'], $kode_tender);
        }

        if (isset($tenderData['anggaran'])) {
            $this->processAnggaran($tenderData['anggaran'], $kode_tender);
        }

        if (isset($tenderData['lokasi_paket'])) {
            $this->processLokasiPaket($tenderData['lokasi_paket'], $kode_tender);
        }
    }

    private function processInstansiSatker(array $instansiSatkerData, string $kode_tender): void
    {
        foreach ($instansiSatkerData as $data) {
            InstansiSatker::updateOrCreate(
                [
                    'rup_id' => $data['rup_id'] ?? 0,
                    'kode_tender' => $kode_tender
                ],
                [
                    'nama_satker' => $this->mapNullToZero($data['stk_nama'] ?? null, true),
                    'nama_instansi' => $this->mapNullToZero($data['nama_instansi'] ?? null, true),
                    'jenis_instansi' => $this->mapNullToZero($data['jenis_instansi'] ?? null, true)
                ]
            );
        }
    }

    private function processAnggaran(array $anggaranData, string $kode_tender): void
    {
        foreach ($anggaranData as $data) {
            Anggaran::updateOrCreate(
                [
                    'rup_id' => $data['rup_id'] ?? 0,
                    'kode_tender' => $kode_tender,
                    'ang_tahun' => $this->mapNullToZero($data['ang_tahun'] ?? null),
                    'ang_nilai' => $this->mapNullToZero($data['ang_nilai'] ?? null),
                    'sbd_id' => $this->mapNullToZero($data['sbd_id'] ?? null),
                    'ang_koderekening' => $this->mapNullToZero($data['ang_koderekening'] ?? null, true)
                ],
                [
                    // Add any additional fields that need to be updated here
                ]
            );
        }
    }

    private function processLokasiPaket(array $lokasiPaketData, string $kode_tender): void
    {
        foreach ($lokasiPaketData as $data) {
            LokasiPaket::updateOrCreate(
                [
                    'kode_tender' => $kode_tender,
                    'kbp_nama' => $this->mapNullToZero($data['lokasi']['kbp_nama'] ?? '', true),
                    'prp_nama' => $this->mapNullToZero($data['lokasi']['prp_nama'] ?? '', true),
                    'pkt_lokasi' => $this->mapNullToZero($data['lokasi']['pkt_lokasi'] ?? '', true),
                ]
            );
        }
    }

    private function formatDecimal($value): ?float
    {
        if (is_numeric($value)) {
            return (float) number_format((float) $value, 2, '.', '');
        }
        return null;
    }

    private function mapNullToZero($value, $isString = false)
    {
        if (is_null($value)) {
            return $isString ? '' : 0;
        }
        return $value;
    }
}
