<?php

namespace App\Http\Controllers;

use App\Models\ListPerusahaanTender;
use App\Models\PesertaTender;
use App\Models\TenderLpse;
use App\Models\Jadwal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CompanyAnalysisController extends Controller
{
    /**
     * Menampilkan halaman analisis perusahaan
     *
     * @param string $pembeda
     * @return \Illuminate\View\View
     */
    public function show($pembeda)
    {
        $pembeda = decrypt($pembeda);
        $perusahaan = ListPerusahaanTender::where('pembeda', $pembeda)->firstOrFail();

        // Mendapatkan data analisis
        $competitors = $this->getCompetitors($perusahaan->id);
        $projectTypes = $this->getProjectTypes($perusahaan->id);
        $participationTrend = $this->getParticipationTrend($perusahaan->id);
        $successRate = $this->getSuccessRate($perusahaan->id);
        $tenderParticipation = $this->getTenderParticipation($perusahaan->id);

        return view('tenderid.perusahaan.analysis', compact(
            'perusahaan',
            'competitors',
            'projectTypes',
            'participationTrend',
            'successRate',
            'tenderParticipation'
        ));
    }

    /**
     * Mendapatkan data kompetitor perusahaan
     *
     * @param int $perusahaanId
     * @return \Illuminate\Support\Collection
     */
    private function getCompetitors($perusahaanId)
    {
        // Mendapatkan semua tender yang diikuti oleh perusahaan
        $tenderIds = PesertaTender::where('id_peserta', $perusahaanId)
            ->pluck('kode_tender');

        // Mendapatkan semua perusahaan yang ikut tender yang sama
        $competitors = DB::table('peserta_tender as pt1')
            ->select(
                'lpt.id',
                'lpt.nama_peserta',
                'lpt.npwp',
                'lpt.npwp_blur',
                'lpt.pembeda',
                DB::raw('COUNT(DISTINCT pt1.kode_tender) as jumlah_tender_sama'),
                DB::raw('(SELECT COUNT(*) FROM pemenang_lpse WHERE id_peserta = lpt.id) as jumlah_menang')
            )
            ->join('list_perusahaan_tender as lpt', 'pt1.id_peserta', '=', 'lpt.id')
            ->whereIn('pt1.kode_tender', $tenderIds)
            ->where('pt1.id_peserta', '!=', $perusahaanId)
            ->groupBy('lpt.id', 'lpt.nama_peserta', 'lpt.npwp', 'lpt.npwp_blur', 'lpt.pembeda')
            ->orderByDesc('jumlah_tender_sama')
            ->limit(10)
            ->get();

        return $competitors;
    }

    /**
     * Mendapatkan jenis proyek yang paling sering diikuti
     *
     * @param int $perusahaanId
     * @return \Illuminate\Support\Collection
     */
    private function getProjectTypes($perusahaanId)
    {
        // Mendapatkan kategori pekerjaan yang paling sering diikuti
        $projectTypes = DB::table('peserta_tender as pt')
            ->select(
                'tup.kategori_pekerjaan',
                DB::raw('COUNT(*) as jumlah'),
                DB::raw('SUM(CASE WHEN pl.kode_tender IS NOT NULL THEN 1 ELSE 0 END) as jumlah_menang'),
                DB::raw('AVG(tup.pagu) as rata_rata_pagu')
            )
            ->join('tender_umum_publik as tup', 'pt.kode_tender', '=', 'tup.kode_tender')
            ->leftJoin('pemenang_lpse as pl', function ($join) use ($perusahaanId) {
                $join->on('pt.kode_tender', '=', 'pl.kode_tender')
                    ->where('pl.id_peserta', '=', $perusahaanId);
            })
            ->where('pt.id_peserta', $perusahaanId)
            ->whereNotNull('tup.kategori_pekerjaan')
            ->groupBy('tup.kategori_pekerjaan')
            ->orderByDesc('jumlah')
            ->get();

        return $projectTypes;
    }

    /**
     * Mendapatkan tren partisipasi tender per bulan
     *
     * @param int $perusahaanId
     * @return \Illuminate\Support\Collection
     */
    private function getParticipationTrend($perusahaanId)
    {
        // Mendapatkan data partisipasi per bulan dalam 12 bulan terakhir
        $startDate = Carbon::now()->subMonths(11)->startOfMonth();

        $participationTrend = DB::table('peserta_tender as pt')
            ->select(
                DB::raw('DATE_FORMAT(tup.tanggal_paket_tayang, "%Y-%m") as bulan'),
                DB::raw('COUNT(*) as jumlah_tender'),
                DB::raw('SUM(CASE WHEN pl.kode_tender IS NOT NULL THEN 1 ELSE 0 END) as jumlah_menang')
            )
            ->join('tender_umum_publik as tup', 'pt.kode_tender', '=', 'tup.kode_tender')
            ->leftJoin('pemenang_lpse as pl', function ($join) use ($perusahaanId) {
                $join->on('pt.kode_tender', '=', 'pl.kode_tender')
                    ->where('pl.id_peserta', '=', $perusahaanId);
            })
            ->where('pt.id_peserta', $perusahaanId)
            ->where('tup.tanggal_paket_tayang', '>=', $startDate)
            ->groupBy('bulan')
            ->orderBy('bulan')
            ->get();

        // Mengisi bulan yang kosong
        $result = [];
        $currentDate = $startDate->copy();
        $endDate = Carbon::now()->endOfMonth();

        while ($currentDate->lte($endDate)) {
            $yearMonth = $currentDate->format('Y-m');
            $found = false;

            foreach ($participationTrend as $item) {
                if ($item->bulan === $yearMonth) {
                    $result[] = $item;
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $result[] = (object) [
                    'bulan' => $yearMonth,
                    'jumlah_tender' => 0,
                    'jumlah_menang' => 0
                ];
            }

            $currentDate->addMonth();
        }

        return collect($result);
    }

    /**
     * Mendapatkan tingkat keberhasilan tender
     *
     * @param int $perusahaanId
     * @return object
     */
    private function getSuccessRate($perusahaanId)
    {
        // Mendapatkan jumlah tender yang diikuti dan dimenangkan
        $stats = DB::table('peserta_tender as pt')
            ->select(
                DB::raw('COUNT(DISTINCT pt.kode_tender) as total_tender'),
                DB::raw('COUNT(DISTINCT pl.kode_tender) as total_menang'),
                DB::raw('SUM(tup.pagu) as total_pagu'),
                DB::raw('SUM(CASE WHEN pl.kode_tender IS NOT NULL THEN COALESCE(pl.harga_penawaran, pl.harga_terkoreksi) ELSE 0 END) as total_nilai_kontrak')
            )
            ->join('tender_umum_publik as tup', 'pt.kode_tender', '=', 'tup.kode_tender')
            ->leftJoin('pemenang_lpse as pl', function ($join) use ($perusahaanId) {
                $join->on('pt.kode_tender', '=', 'pl.kode_tender')
                    ->where('pl.id_peserta', '=', $perusahaanId);
            })
            ->where('pt.id_peserta', $perusahaanId)
            ->first();

        // Jika tidak ada data, buat objek default
        if (!$stats) {
            return (object) [
                'total_tender' => 0,
                'total_menang' => 0,
                'total_pagu' => 0,
                'total_nilai_kontrak' => 0,
                'success_rate' => 0
            ];
        }

        // Menghitung persentase keberhasilan
        $successRate = $stats->total_tender > 0 ? ($stats->total_menang / $stats->total_tender) * 100 : 0;
        $stats->success_rate = round($successRate, 2);

        // Pastikan semua properti memiliki nilai default jika null
        $stats->total_tender = $stats->total_tender ?? 0;
        $stats->total_menang = $stats->total_menang ?? 0;
        $stats->total_pagu = $stats->total_pagu ?? 0;
        $stats->total_nilai_kontrak = $stats->total_nilai_kontrak ?? 0;

        return $stats;
    }

    /**
     * Mendapatkan data tender yang diikuti bersama kompetitor
     *
     * @param int $perusahaanId
     * @return \Illuminate\Support\Collection
     */
    private function getTenderParticipation($perusahaanId)
    {
        // Mendapatkan 10 tender terakhir yang diikuti
        $tenderParticipation = DB::table('peserta_tender as pt')
            ->select(
                'tup.kode_tender',
                'tup.nama_paket',
                'tup.pagu',
                'tup.hps',
                'tup.kategori_pekerjaan',
                'tup.tanggal_paket_tayang',
                'tup.kd_lpse',
                'l.nama_lpse',
                'l.url as url_tender',
                'l.logo_path as logo_lpse',
                DB::raw('COUNT(DISTINCT pt2.id_peserta) as jumlah_peserta'),
                DB::raw('CASE WHEN pl.kode_tender IS NOT NULL THEN 1 ELSE 0 END as is_winner')
            )
            ->join('tender_umum_publik as tup', 'pt.kode_tender', '=', 'tup.kode_tender')
            ->join('peserta_tender as pt2', 'pt.kode_tender', '=', 'pt2.kode_tender')
            ->leftJoin('lpse as l', 'tup.kd_lpse', '=', 'l.kd_lpse')
            ->leftJoin('pemenang_lpse as pl', function ($join) use ($perusahaanId) {
                $join->on('pt.kode_tender', '=', 'pl.kode_tender')
                    ->where('pl.id_peserta', '=', $perusahaanId);
            })
            ->where('pt.id_peserta', $perusahaanId)
            ->groupBy('tup.kode_tender', 'tup.nama_paket', 'tup.pagu', 'tup.hps', 'tup.kategori_pekerjaan',
                     'tup.tanggal_paket_tayang', 'tup.kd_lpse', 'l.nama_lpse', 'l.url', 'l.logo_path', 'is_winner')
            ->orderByDesc('tup.tanggal_paket_tayang')
            ->limit(10)
            ->get();

        return $tenderParticipation;
    }
}
