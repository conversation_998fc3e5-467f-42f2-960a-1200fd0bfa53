<?php

namespace App\Http\Controllers;

use App\Models\SystemError;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class SystemErrorController extends Controller
{
    /**
     * Display a listing of the system errors.
     */
    public function index(Request $request)
    {
        // Authorize access
        Gate::authorize('view-system-errors');

        // Get filter parameters
        $status = $request->input('status', 'all');
        $type = $request->input('type');
        $search = $request->input('search');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');

        // Build the query
        $query = SystemError::query();

        // Apply status filter
        if ($status === 'resolved') {
            $query->resolved();
        } elseif ($status === 'unresolved') {
            $query->unresolved();
        }

        // Apply type filter
        if ($type) {
            $query->ofType($type);
        }

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('message', 'like', "%{$search}%")
                  ->orWhere('file', 'like', "%{$search}%")
                  ->orWhere('user_email', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%");
            });
        }

        // Apply date range filter
        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }
        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        // Apply sorting
        $query->orderBy($sortBy, $sortOrder);

        // Get error types for filter dropdown
        $errorTypes = SystemError::select('type')
            ->distinct()
            ->orderBy('type')
            ->pluck('type');

        // Get error statistics
        $statistics = [
            'total' => SystemError::count(),
            'resolved' => SystemError::resolved()->count(),
            'unresolved' => SystemError::unresolved()->count(),
            'today' => SystemError::whereDate('created_at', today())->count(),
            'this_week' => SystemError::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month' => SystemError::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];

        // Get errors with pagination
        $errors = $query->with(['user', 'resolvedByUser'])->paginate(20)->withQueryString();

        // Get counts for unresolved and resolved errors
        $unresolvedCount = SystemError::unresolved()->count();
        $resolvedCount = SystemError::resolved()->count();

        // Get unresolved error IDs for bulk resolve
        $unresolvedIds = SystemError::unresolved()->pluck('id')->toArray();

        return view('admin.system-errors.index', compact(
            'errors',
            'errorTypes',
            'statistics',
            'status',
            'type',
            'search',
            'startDate',
            'endDate',
            'sortBy',
            'sortOrder',
            'unresolvedCount',
            'resolvedCount',
            'unresolvedIds'
        ));
    }

    /**
     * Display the specified system error.
     */
    public function show(SystemError $systemError)
    {
        // Authorize access
        Gate::authorize('view-system-errors');

        return view('admin.system-errors.show', compact('systemError'));
    }

    /**
     * Mark a system error as resolved.
     */
    public function resolve(Request $request, SystemError $systemError)
    {
        // Authorize access
        Gate::authorize('resolve-system-errors');

        // Validate the request
        $validated = $request->validate([
            'notes' => 'nullable|string|max:1000',
        ]);

        // Mark the error as resolved
        $systemError->markAsResolved(
            auth()->id(),
            $validated['notes'] ?? null
        );

        return redirect()
            ->route('admin.system-errors.show', $systemError)
            ->with('success', 'Error telah ditandai sebagai terselesaikan.');
    }

    /**
     * Mark multiple system errors as resolved.
     */
    public function bulkResolve(Request $request)
    {
        // Authorize access
        Gate::authorize('resolve-system-errors');

        // Validate the request
        $validated = $request->validate([
            'error_ids' => 'required|array',
            'error_ids.*' => 'exists:system_errors,id',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Mark the errors as resolved
        SystemError::whereIn('id', $validated['error_ids'])
            ->update([
                'resolved' => true,
                'resolved_at' => now(),
                'resolved_by' => auth()->id(),
                'notes' => $validated['notes'] ?? null,
            ]);

        return redirect()
            ->route('admin.system-errors.index')
            ->with('success', count($validated['error_ids']) . ' error telah ditandai sebagai terselesaikan.');
    }

    /**
     * Delete a system error.
     */
    public function destroy(SystemError $systemError)
    {
        // Authorize access
        Gate::authorize('delete-system-errors');

        // Delete the error
        $systemError->delete();

        return redirect()
            ->route('admin.system-errors.index')
            ->with('success', 'Error telah dihapus.');
    }

    /**
     * Delete all resolved system errors.
     */
    public function destroyResolved()
    {
        // Authorize access
        Gate::authorize('delete-system-errors');

        // Delete all resolved errors
        $count = SystemError::resolved()->count();
        SystemError::resolved()->delete();

        return redirect()
            ->route('admin.system-errors.index')
            ->with('success', $count . ' error terselesaikan telah dihapus.');
    }
}
