<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TenderLpse;
use App\Models\DataSirup;
use App\Models\PaketPengadaan;
use App\Models\Lpse;
use App\Models\Subscription;

class LandingController extends Controller
{
    /**
     * Display the landing page
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        // Return the basic view without loading any data
        // Data will be loaded asynchronously via AJAX
        return view('landing.index');
    }

    /**
     * Get statistics data for the landing page
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        try {
            // Use caching for statistics which can be heavy to calculate
            $stats = cache()->remember('landing_stats', 60 * 60, function () {
                return [
                    'tender_count' => TenderLpse::count(),
                    'sirup_count' => DataSirup::count(),
                    'ekatalog_count' => PaketPengadaan::count(),
                    'lpse_count' => Lpse::count(),
                ];
            });

            return response()->json($stats)
                ->header('Content-Type', 'application/json')
                ->header('X-Content-Type-Options', 'nosniff');
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Landing stats error: ' . $e->getMessage());

            // Return error response
            return response()->json(['error' => true], 500)
                ->header('Content-Type', 'application/json')
                ->header('X-Content-Type-Options', 'nosniff');
        }
    }

    /**
     * Get latest tenders data for the landing page
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLatestTenders()
    {
        try {
            // Use caching to reduce database load
            $latestTenders = cache()->remember('landing_latest_tenders', 60 * 30, function () {
                return TenderLpse::with('lpse')
                    ->orderBy('created_at', 'desc')
                    ->take(6)
                    ->get();
            });

            return response()->json($latestTenders)
                ->header('Content-Type', 'application/json')
                ->header('X-Content-Type-Options', 'nosniff');
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Landing latest tenders error: ' . $e->getMessage());

            // Return error response
            return response()->json(['error' => true], 500)
                ->header('Content-Type', 'application/json')
                ->header('X-Content-Type-Options', 'nosniff');
        }
    }

    /**
     * Get latest SIRUP data for the landing page
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLatestSirup()
    {
        try {
            // Use caching to reduce database load
            $latestSirup = cache()->remember('landing_latest_sirup', 60 * 30, function () {
                return DataSirup::whereNotNull('tahun_anggaran')
                    ->orderBy('created_at', 'desc')
                    ->take(6)
                    ->get();
            });

            return response()->json($latestSirup)
                ->header('Content-Type', 'application/json')
                ->header('X-Content-Type-Options', 'nosniff');
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Landing latest SIRUP error: ' . $e->getMessage());

            // Return error response
            return response()->json(['error' => true], 500)
                ->header('Content-Type', 'application/json')
                ->header('X-Content-Type-Options', 'nosniff');
        }

    }

    /**
     * Display the features page
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function features()
    {
        return view('landing.features');
    }

    /**
     * Display the pricing page
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function pricing()
    {
        // Define subscription packages
        $subscriptionPackages = [
            [
                'name' => 'Bulanan',
                'price' => 250000.00,
                'duration_days' => 30,
            ],
            [
                'name' => 'Kuartalan',
                'price' => 650000.00,
                'duration_days' => 90,
            ],
            [
                'name' => 'Semesteran',
                'price' => 1200000.00,
                'duration_days' => 180,
            ],
            [
                'name' => 'Tahunan',
                'price' => 2000000.00,
                'duration_days' => 360,
            ],
        ];

        // Calculate discounts based on normal monthly price of 300,000
        $normalMonthlyPrice = 300000.00;

        foreach ($subscriptionPackages as &$package) {
            $normalPrice = $normalMonthlyPrice * ($package['duration_days'] / 30);
            $discountAmount = $normalPrice - $package['price'];
            $discountPercentage = ($discountAmount / $normalPrice) * 100;

            $package['normal_price'] = $normalPrice;
            $package['discount_amount'] = $discountAmount;
            $package['discount_percentage'] = round($discountPercentage, 1);
        }

        return view('landing.pricing', compact('subscriptionPackages'));
    }



    /**
     * Display public tender detail page
     *
     * @param  string  $id
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\RedirectResponse
     */
    public function tenderDetail($id)
    {
        try {
            // Validasi ID
            if (empty($id) || $id === 'undefined') {
                return redirect()->route('landing.index')
                    ->with('error', 'ID tender tidak valid');
            }

            $tender = TenderLpse::with(['lpse', 'jadwal'])->where('kode_tender', $id)->first();

            if (!$tender) {
                return redirect()->route('landing.index')
                    ->with('error', 'Tender tidak ditemukan');
            }

            return view('landing.tender_detail', compact('tender'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Tender detail error: ' . $e->getMessage());
            return redirect()->route('landing.index')
                ->with('error', 'Tender tidak ditemukan');
        }
    }

    /**
     * Display public SIRUP detail page
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\RedirectResponse
     */
    public function sirupDetail($id)
    {
        try {
            // Validasi ID
            if (empty($id) || $id === 'undefined') {
                return redirect()->route('landing.index')
                    ->with('error', 'ID SIRUP tidak valid');
            }

            $sirup = DataSirup::where('kode_rup', $id)->first();

            if (!$sirup) {
                return redirect()->route('landing.index')
                    ->with('error', 'SIRUP tidak ditemukan');
            }

            // Cari LPSE berdasarkan nama_klpd
            $lpse = Lpse::where('nama_lpse', 'LIKE', "%{$sirup->nama_klpd}%")->first();
            $sirup->lpse = $lpse;

            return view('landing.sirup_detail', compact('sirup'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('SIRUP detail error: ' . $e->getMessage());
            return redirect()->route('landing.index')
                ->with('error', 'SIRUP tidak ditemukan');
        }
    }
}
