<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;

class UrlHelperController extends Controller
{
    /**
     * Mendapatkan URL terenkripsi untuk perusahaan
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCompanyUrls(Request $request)
    {
        $pembeda = $request->input('pembeda');
        
        if (!$pembeda) {
            return response()->json([
                'error' => 'Parameter pembeda diperlukan'
            ], 400);
        }
        
        $encryptedPembeda = encrypt($pembeda);
        
        return response()->json([
            'detail_url' => route('perusahaan.show', ['pembeda' => $encryptedPembeda]),
            'analysis_url' => route('perusahaan.analysis', ['pembeda' => $encryptedPembeda])
        ]);
    }
}
