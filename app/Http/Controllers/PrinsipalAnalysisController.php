<?php

namespace App\Http\Controllers;

use App\Models\PaketPengadaan;
use App\Models\TransaksiPaket;
use App\Models\Produk;
use App\Models\ListPerusahaanTender;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PrinsipalAnalysisController extends Controller
{
    /**
     * Menampilkan halaman analisis prinsipal (penyedia e-katalog)
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Filter tahun
        $tahun = $request->input('tahun', date('Y'));
        $tahunList = PaketPengadaan::selectRaw('YEAR(tanggal_paket) as tahun')
            ->distinct()
            ->orderBy('tahun', 'desc')
            ->pluck('tahun');

        // Filter prinsipal (penyedia) - menggunakan parameter terenkripsi
        $id_penyedia = null;
        $kode_prinsipal = $request->input('kode_prinsipal');

        if ($kode_prinsipal) {
            try {
                $id_penyedia = decrypt($kode_prinsipal);
            } catch (\Exception $e) {
                // Jika decrypt gagal, abaikan parameter
                $id_penyedia = null;
            }
        }

        // Dapatkan daftar prinsipal (penyedia)
        $prinsipalList = PaketPengadaan::join('list_perusahaan_tender', 'ekatalog_paket_pengadaan.id_penyedia', '=', 'list_perusahaan_tender.id')
            ->select('list_perusahaan_tender.id', 'list_perusahaan_tender.nama_peserta')
            ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
            ->distinct()
            ->orderBy('list_perusahaan_tender.nama_peserta')
            ->get();

        // Enkripsi ID untuk setiap prinsipal
        foreach ($prinsipalList as $prinsipal) {
            $prinsipal->kode_prinsipal = encrypt($prinsipal->id);
        }

        // Hanya tampilkan view dasar, data akan dimuat dengan AJAX
        return view('tenderid.ekatalog.prinsipal_analysis', compact(
            'tahun',
            'tahunList',
            'id_penyedia',
            'kode_prinsipal',
            'prinsipalList'
        ));
    }

    /**
     * Mendapatkan daftar prinsipal (penyedia) untuk dropdown
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPrinsipalList(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Dapatkan daftar prinsipal (penyedia)
            $prinsipalList = PaketPengadaan::join('list_perusahaan_tender', 'ekatalog_paket_pengadaan.id_penyedia', '=', 'list_perusahaan_tender.id')
                ->select('list_perusahaan_tender.id', 'list_perusahaan_tender.nama_peserta')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->distinct()
                ->orderBy('list_perusahaan_tender.nama_peserta')
                ->get();

            // Enkripsi ID untuk setiap prinsipal
            $prinsipalListWithEncryption = $prinsipalList->map(function($prinsipal) {
                return [
                    'id' => $prinsipal->id, // Tetap sertakan ID asli untuk debugging
                    'nama_peserta' => $prinsipal->nama_peserta,
                    'kode_prinsipal' => encrypt($prinsipal->id)
                ];
            });

            return response()->json([
                'prinsipalList' => $prinsipalListWithEncryption
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mendapatkan statistik umum untuk dashboard analisis
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Dekripsi kode_prinsipal menjadi id_penyedia
            $id_penyedia = null;
            $kode_prinsipal = $request->input('kode_prinsipal');

            if ($kode_prinsipal) {
                try {
                    $id_penyedia = decrypt($kode_prinsipal);
                } catch (\Exception $e) {
                    // Jika decrypt gagal, abaikan parameter
                    $id_penyedia = null;
                }
            }

            // Base query
            $paketQuery = PaketPengadaan::whereYear('tanggal_paket', $tahun);
            $transaksiQuery = TransaksiPaket::join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun);

            // Filter berdasarkan prinsipal jika ada
            if ($id_penyedia) {
                $paketQuery->where('id_penyedia', $id_penyedia);
                $transaksiQuery->where('ekatalog_paket_pengadaan.id_penyedia', $id_penyedia);
            }

            // Statistik umum
            $totalPenyedia = $id_penyedia ? 1 : $paketQuery->distinct('id_penyedia')->count('id_penyedia');

            $totalPelaksana = $paketQuery->distinct('id_pelaksana')->count('id_pelaksana');

            $totalProduk = $transaksiQuery->distinct('id_produk')->count('id_produk');

            $totalNilai = $transaksiQuery->sum('ekatalog_transaksi_paket.total_harga');

            // Jika ada filter prinsipal, dapatkan nama prinsipal
            $namaPrinsipal = null;
            if ($id_penyedia) {
                $prinsipal = ListPerusahaanTender::find($id_penyedia);
                $namaPrinsipal = $prinsipal ? $prinsipal->nama_peserta : null;
            }

            return response()->json([
                'totalPenyedia' => $totalPenyedia,
                'totalPelaksana' => $totalPelaksana,
                'totalProduk' => $totalProduk,
                'totalNilai' => $totalNilai,
                'namaPrinsipal' => $namaPrinsipal,
                'kode_prinsipal' => $kode_prinsipal // Kembalikan kode_prinsipal untuk konsistensi
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mendapatkan data produk paling laku
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopProducts(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Dekripsi kode_prinsipal menjadi id_penyedia
            $id_penyedia = null;
            $kode_prinsipal = $request->input('kode_prinsipal');

            if ($kode_prinsipal) {
                try {
                    $id_penyedia = decrypt($kode_prinsipal);
                } catch (\Exception $e) {
                    // Jika decrypt gagal, abaikan parameter
                    $id_penyedia = null;
                }
            }

            // Base query untuk produk terlaris berdasarkan kuantitas
            $quantityQuery = TransaksiPaket::join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
                ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('ekatalog_produk.nama_produk, ekatalog_produk.nama_manufaktur, SUM(ekatalog_transaksi_paket.kuantitas_produk) as total_kuantitas, COUNT(DISTINCT ekatalog_paket_pengadaan.id_paket) as jumlah_paket')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun);

            // Base query untuk produk terlaris berdasarkan nilai
            $valueQuery = TransaksiPaket::join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
                ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('ekatalog_produk.nama_produk, ekatalog_produk.nama_manufaktur, SUM(ekatalog_transaksi_paket.total_harga) as total_nilai, COUNT(DISTINCT ekatalog_paket_pengadaan.id_paket) as jumlah_paket')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun);

            // Base query untuk produk paling mahal
            $expensiveQuery = TransaksiPaket::join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
                ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('ekatalog_produk.nama_produk, ekatalog_produk.nama_manufaktur, MAX(ekatalog_transaksi_paket.harga_satuan) as harga_tertinggi')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun);

            // Filter berdasarkan prinsipal jika ada
            if ($id_penyedia) {
                $quantityQuery->where('ekatalog_paket_pengadaan.id_penyedia', $id_penyedia);
                $valueQuery->where('ekatalog_paket_pengadaan.id_penyedia', $id_penyedia);
                $expensiveQuery->where('ekatalog_paket_pengadaan.id_penyedia', $id_penyedia);
            }

            // Top 10 produk terlaris berdasarkan kuantitas
            $topProdukByQuantity = $quantityQuery
                ->groupBy('ekatalog_produk.nama_produk', 'ekatalog_produk.nama_manufaktur')
                ->orderBy('total_kuantitas', 'desc')
                ->limit(10)
                ->get();

            // Top 10 produk terlaris berdasarkan nilai
            $topProdukByValue = $valueQuery
                ->groupBy('ekatalog_produk.nama_produk', 'ekatalog_produk.nama_manufaktur')
                ->orderBy('total_nilai', 'desc')
                ->limit(10)
                ->get();

            // Produk paling mahal berdasarkan harga satuan
            $produkTermahal = $expensiveQuery
                ->groupBy('ekatalog_produk.nama_produk', 'ekatalog_produk.nama_manufaktur')
                ->orderBy('harga_tertinggi', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'topProdukByQuantity' => $topProdukByQuantity,
                'topProdukByValue' => $topProdukByValue,
                'produkTermahal' => $produkTermahal
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mendapatkan data instansi yang paling banyak belanja
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopBuyers(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Dekripsi kode_prinsipal menjadi id_penyedia
            $id_penyedia = null;
            $kode_prinsipal = $request->input('kode_prinsipal');

            if ($kode_prinsipal) {
                try {
                    $id_penyedia = decrypt($kode_prinsipal);
                } catch (\Exception $e) {
                    // Jika decrypt gagal, abaikan parameter
                    $id_penyedia = null;
                }
            }

            // Base query untuk instansi berdasarkan jumlah paket
            $packageQuery = PaketPengadaan::selectRaw('instansi_pembeli, COUNT(*) as jumlah_paket')
                ->whereYear('tanggal_paket', $tahun);

            // Base query untuk instansi berdasarkan nilai belanja
            $valueQuery = PaketPengadaan::join('ekatalog_transaksi_paket', 'ekatalog_paket_pengadaan.id_paket', '=', 'ekatalog_transaksi_paket.id_paket')
                ->selectRaw('instansi_pembeli, SUM(ekatalog_transaksi_paket.total_harga) as total_nilai')
                ->whereYear('tanggal_paket', $tahun);

            // Filter berdasarkan prinsipal jika ada
            if ($id_penyedia) {
                $packageQuery->where('id_penyedia', $id_penyedia);
                $valueQuery->where('ekatalog_paket_pengadaan.id_penyedia', $id_penyedia);
            }

            // Top 10 instansi berdasarkan jumlah paket
            $topInstansiByPackage = $packageQuery
                ->groupBy('instansi_pembeli')
                ->orderBy('jumlah_paket', 'desc')
                ->limit(10)
                ->get();

            // Top 10 instansi berdasarkan nilai belanja
            $topInstansiByValue = $valueQuery
                ->groupBy('instansi_pembeli')
                ->orderBy('total_nilai', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'topInstansiByPackage' => $topInstansiByPackage,
                'topInstansiByValue' => $topInstansiByValue
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mendapatkan data distributor paling aktif (pelaksana)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopDistributors(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Dekripsi kode_prinsipal menjadi id_penyedia
            $id_penyedia = null;
            $kode_prinsipal = $request->input('kode_prinsipal');

            if ($kode_prinsipal) {
                try {
                    $id_penyedia = decrypt($kode_prinsipal);
                } catch (\Exception $e) {
                    // Jika decrypt gagal, abaikan parameter
                    $id_penyedia = null;
                }
            }

            // Base query untuk distributor berdasarkan jumlah paket
            $packageQuery = PaketPengadaan::join('list_perusahaan_tender', 'ekatalog_paket_pengadaan.id_pelaksana', '=', 'list_perusahaan_tender.id')
                ->selectRaw('list_perusahaan_tender.nama_peserta, list_perusahaan_tender.pembeda, COUNT(*) as jumlah_paket')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun);

            // Base query untuk distributor berdasarkan nilai transaksi
            $valueQuery = PaketPengadaan::join('list_perusahaan_tender', 'ekatalog_paket_pengadaan.id_pelaksana', '=', 'list_perusahaan_tender.id')
                ->join('ekatalog_transaksi_paket', 'ekatalog_paket_pengadaan.id_paket', '=', 'ekatalog_transaksi_paket.id_paket')
                ->selectRaw('list_perusahaan_tender.nama_peserta, list_perusahaan_tender.pembeda, SUM(ekatalog_transaksi_paket.total_harga) as total_nilai')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun);

            // Filter berdasarkan prinsipal jika ada
            if ($id_penyedia) {
                $packageQuery->where('ekatalog_paket_pengadaan.id_penyedia', $id_penyedia);
                $valueQuery->where('ekatalog_paket_pengadaan.id_penyedia', $id_penyedia);
            }

            // Top 10 distributor (pelaksana) berdasarkan jumlah paket
            $topDistributorsByPackage = $packageQuery
                ->groupBy('list_perusahaan_tender.nama_peserta', 'list_perusahaan_tender.pembeda')
                ->orderBy('jumlah_paket', 'desc')
                ->limit(10)
                ->get();

            // Top 10 distributor (pelaksana) berdasarkan nilai transaksi
            $topDistributorsByValue = $valueQuery
                ->groupBy('list_perusahaan_tender.nama_peserta', 'list_perusahaan_tender.pembeda')
                ->orderBy('total_nilai', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'topDistributorsByPackage' => $topDistributorsByPackage,
                'topDistributorsByValue' => $topDistributorsByValue
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mendapatkan data merek paling populer
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTopBrands(Request $request)
    {
        try {
            $tahun = $request->input('tahun', date('Y'));

            // Dekripsi kode_prinsipal menjadi id_penyedia
            $id_penyedia = null;
            $kode_prinsipal = $request->input('kode_prinsipal');

            if ($kode_prinsipal) {
                try {
                    $id_penyedia = decrypt($kode_prinsipal);
                } catch (\Exception $e) {
                    // Jika decrypt gagal, abaikan parameter
                    $id_penyedia = null;
                }
            }

            // Base query untuk merek berdasarkan jumlah produk
            $productQuery = TransaksiPaket::join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
                ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('ekatalog_produk.nama_manufaktur, COUNT(DISTINCT ekatalog_produk.id_produk) as jumlah_produk')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->whereNotNull('ekatalog_produk.nama_manufaktur')
                ->where('ekatalog_produk.nama_manufaktur', '!=', '');

            // Base query untuk merek berdasarkan nilai transaksi
            $valueQuery = TransaksiPaket::join('ekatalog_produk', 'ekatalog_transaksi_paket.id_produk', '=', 'ekatalog_produk.id_produk')
                ->join('ekatalog_paket_pengadaan', 'ekatalog_transaksi_paket.id_paket', '=', 'ekatalog_paket_pengadaan.id_paket')
                ->selectRaw('ekatalog_produk.nama_manufaktur, SUM(ekatalog_transaksi_paket.total_harga) as total_nilai')
                ->whereYear('ekatalog_paket_pengadaan.tanggal_paket', $tahun)
                ->whereNotNull('ekatalog_produk.nama_manufaktur')
                ->where('ekatalog_produk.nama_manufaktur', '!=', '');

            // Filter berdasarkan prinsipal jika ada
            if ($id_penyedia) {
                $productQuery->where('ekatalog_paket_pengadaan.id_penyedia', $id_penyedia);
                $valueQuery->where('ekatalog_paket_pengadaan.id_penyedia', $id_penyedia);
            }

            // Top 10 merek berdasarkan jumlah produk
            $topBrandsByProduct = $productQuery
                ->groupBy('ekatalog_produk.nama_manufaktur')
                ->orderBy('jumlah_produk', 'desc')
                ->limit(10)
                ->get();

            // Top 10 merek berdasarkan nilai transaksi
            $topBrandsByValue = $valueQuery
                ->groupBy('ekatalog_produk.nama_manufaktur')
                ->orderBy('total_nilai', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'topBrandsByProduct' => $topBrandsByProduct,
                'topBrandsByValue' => $topBrandsByValue
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
