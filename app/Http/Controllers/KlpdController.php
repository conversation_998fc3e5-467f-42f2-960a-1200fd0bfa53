<?php

namespace App\Http\Controllers;

use App\Models\Klpd;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class KlpdController extends Controller
{
    public function index(Request $request)
    {
        // Mendapatkan parameter filter
        $search = $request->input('search');
        $jenisKlpd = $request->input('jenis_klpd');
        $provinsi = $request->input('provinsi');
        $perPage = 20; // Jumlah data per halaman

        // Memulai query
        $query = Klpd::query();

        // Menerapkan filter
        if ($search) {
            // Gunakan where like untuk pencarian
            $query->where(function($q) use ($search) {
                $q->where('kd_klpd', 'like', "%{$search}%")
                  ->orWhere('nama_klpd', 'like', "%{$search}%");
            });
        }

        if ($jenisKlpd) {
            $query->where('jenis_klpd', $jenisKlpd);
        }

        if ($provinsi) {
            $query->where('kd_provinsi', $provinsi);
        }

        // Urutkan data
        $query->orderBy('nama_klpd');

        // Ambil data dengan pagination
        $dataKlpd = $query->paginate($perPage)->withQueryString();

        // Mengirim data ke view
        return view('tenderid.klpd.index', [
            'dataKlpd' => $dataKlpd,
        ]);
    }

    /**
     * Show the form for creating a new KLPD.
     */
    public function create()
    {
        // Get list of provinces from KLPD with jenis_klpd "PROVINSI"
        $provinces = $this->getProvincesFromDatabase();

        // Get distinct jenis_klpd from database
        $jenisKlpd = $this->getDistinctJenisKlpd();

        return view('tenderid.klpd.create', compact('provinces', 'jenisKlpd'));
    }

    /**
     * Store a newly created KLPD in storage.
     */
    public function store(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'kd_klpd' => 'required|string|max:10|unique:klpd,kd_klpd',
            'nama_klpd' => 'required|string|max:255',
            'jenis_klpd' => 'required|string|max:100',
            'kd_provinsi' => 'required|string|max:10',
            'kd_kabupaten' => 'nullable|string|max:10',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle logo upload if provided
        $logoPath = null;
        if ($request->hasFile('logo')) {
            $logo = $request->file('logo');
            $filename = 'klpd_' . Str::slug($request->nama_klpd) . '_' . time() . '.' . $logo->getClientOriginalExtension();
            $path = $logo->storeAs('public/logos/klpd', $filename);
            $logoPath = 'storage/logos/klpd/' . $filename;
        }

        // Create the KLPD
        $klpd = new Klpd();
        $klpd->kd_klpd = $request->kd_klpd;
        $klpd->nama_klpd = $request->nama_klpd;
        $klpd->jenis_klpd = $request->jenis_klpd;
        $klpd->kd_provinsi = $request->kd_provinsi;
        $klpd->kd_kabupaten = $request->kd_kabupaten;
        $klpd->logo_path = $logoPath;
        $klpd->save();

        return redirect()->route('klpd.index')
            ->with('success', 'KLPD berhasil ditambahkan!');
    }

    /**
     * Show the form for editing the specified KLPD.
     */
    public function edit($id)
    {
        $klpd = Klpd::findOrFail($id);

        // Ensure kd_provinsi is cast to string for comparison in the view
        if ($klpd->kd_provinsi !== null) {
            $klpd->kd_provinsi = (string) $klpd->kd_provinsi;
        }

        $provinces = $this->getProvincesFromDatabase();
        $jenisKlpd = $this->getDistinctJenisKlpd();

        // Debug information
        \Log::info('KLPD Edit Data', [
            'kd_klpd' => $klpd->kd_klpd,
            'kd_provinsi' => $klpd->kd_provinsi,
            'kd_provinsi_type' => gettype($klpd->kd_provinsi),
            'provinces_keys' => array_keys($provinces),
            'provinces_keys_types' => array_map('gettype', array_keys($provinces))
        ]);

        return view('tenderid.klpd.edit', compact('klpd', 'provinces', 'jenisKlpd'));
    }

    /**
     * Update the specified KLPD in storage.
     */
    public function update(Request $request, $id)
    {
        // Log input data for debugging
        \Log::info('KLPD Update Request', [
            'id' => $id,
            'request_data' => $request->all()
        ]);

        // Find the KLPD by primary key
        $klpd = Klpd::where('kd_klpd', $id)->first();

        if (!$klpd) {
            return redirect()->route('klpd.index')
                ->with('error', 'KLPD tidak ditemukan!');
        }

        // Log KLPD data before update
        \Log::info('KLPD Before Update', [
            'kd_klpd' => $klpd->kd_klpd,
            'nama_klpd' => $klpd->nama_klpd,
            'jenis_klpd' => $klpd->jenis_klpd,
            'kd_provinsi' => $klpd->kd_provinsi,
            'kd_kabupaten' => $klpd->kd_kabupaten
        ]);

        // Validate the request
        $validator = Validator::make($request->all(), [
            'nama_klpd' => 'required|string|max:255',
            'jenis_klpd' => 'required|string|max:100',
            'kd_provinsi' => 'required|string|max:10',
            'kd_kabupaten' => 'nullable|string|max:10',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle logo upload if provided
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($klpd->logo_path && Storage::exists('public/' . str_replace('storage/', '', $klpd->logo_path))) {
                Storage::delete('public/' . str_replace('storage/', '', $klpd->logo_path));
            }

            $logo = $request->file('logo');
            $filename = 'klpd_' . Str::slug($request->nama_klpd) . '_' . time() . '.' . $logo->getClientOriginalExtension();
            $path = $logo->storeAs('public/logos/klpd', $filename);
            $klpd->logo_path = 'storage/logos/klpd/' . $filename;
        }

        // Update the KLPD
        $klpd->nama_klpd = $request->nama_klpd;
        $klpd->jenis_klpd = $request->jenis_klpd;
        $klpd->kd_provinsi = $request->kd_provinsi;
        $klpd->kd_kabupaten = $request->kd_kabupaten;

        // Use updateOrFail to ensure only one record is updated
        try {
            $updated = Klpd::where('kd_klpd', $id)->update([
                'nama_klpd' => $request->nama_klpd,
                'jenis_klpd' => $request->jenis_klpd,
                'kd_provinsi' => $request->kd_provinsi,
                'kd_kabupaten' => $request->kd_kabupaten,
                'logo_path' => $klpd->logo_path
            ]);

            // Log update result
            \Log::info('KLPD Update Result', [
                'updated' => $updated,
                'kd_klpd' => $id
            ]);

            if ($updated) {
                return redirect()->route('klpd.index')
                    ->with('success', 'KLPD berhasil diperbarui!');
            } else {
                return redirect()->route('klpd.index')
                    ->with('error', 'Gagal memperbarui KLPD!');
            }
        } catch (\Exception $e) {
            \Log::error('KLPD Update Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('klpd.index')
                ->with('error', 'Terjadi kesalahan saat memperbarui KLPD: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified KLPD from storage.
     */
    public function destroy($id)
    {
        $klpd = Klpd::findOrFail($id);

        // Delete logo if exists
        if ($klpd->logo_path && Storage::exists('public/' . str_replace('storage/', '', $klpd->logo_path))) {
            Storage::delete('public/' . str_replace('storage/', '', $klpd->logo_path));
        }

        $klpd->delete();

        return redirect()->route('klpd.index')
            ->with('success', 'KLPD berhasil dihapus!');
    }

    /**
     * Get list of provinces from database where jenis_klpd is "PROVINSI".
     */
    private function getProvincesFromDatabase()
    {
        // Get provinces from KLPD table where jenis_klpd is "PROVINSI"
        $provinces = Klpd::where(function($query) {
                $query->where('jenis_klpd', 'PROVINSI')
                      ->orWhere('jenis_klpd', 'Provinsi')
                      ->orWhere('jenis_klpd', 'PROVINSI');
            })
            ->orderBy('nama_klpd')
            ->get();



        // If no provinces found in database, use fallback
        if ($provinces->isEmpty()) {
            \Log::info('No provinces found in database, using fallback');
            return $this->getProvincesFallback();
        }

        // Convert to array with kd_provinsi as key and nama_klpd as value
        $result = [];
        foreach ($provinces as $province) {
            // For provinces, use kd_provinsi if available, otherwise use kd_klpd
            // Always convert to string to ensure consistent comparison in the view
            if ($province->kd_provinsi) {
                $key = (string) $province->kd_provinsi;
            } else {
                $key = (string) $province->kd_klpd;
            }


            $result[$key] = $province->nama_klpd;
        }


        return $result;
    }

    /**
     * Get distinct jenis_klpd from database.
     */
    private function getDistinctJenisKlpd()
    {
        // Get distinct jenis_klpd from KLPD table
        $jenisKlpd = Klpd::whereNotNull('jenis_klpd')
            ->distinct()
            ->orderBy('jenis_klpd')
            ->pluck('jenis_klpd')
            ->toArray();

        // If no jenis_klpd found in database, use fallback
        if (empty($jenisKlpd)) {
            return [
                'Kementerian',
                'Lembaga',
                'Provinsi',
                'Kabupaten',
                'Kota',
            ];
        }

        return $jenisKlpd;
    }
}
