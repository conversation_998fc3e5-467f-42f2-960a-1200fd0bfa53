<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserManagementController extends Controller
{
    public function __construct()
    {
        // Middleware to restrict access to authenticated users with role 'Super Admin'
        $this->middleware(function ($request, $next) {
            if (Auth::check() && Auth::user()->account_type === 'Super Admin') {
                return $next($request);
            }
            abort(403, 'Unauthorized');
        });
    }

    /**
     * Display a listing of the users.
     */
    public function index(Request $request)
    {
        $query = User::query();

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%");
            });
        }

        // No role filter needed as we use account_type instead

        // Apply account type filter
        if ($request->has('account_type') && !empty($request->account_type)) {
            $query->where('account_type', $request->account_type);
        }

        // Apply subscription status filter
        if ($request->has('subscription_status') && !empty($request->subscription_status)) {
            if ($request->subscription_status === 'active') {
                $query->whereHas('userSubscriptions', function($q) {
                    $q->where('status', 'active')
                      ->where('is_active', 1)
                      ->where('end_date', '>=', now());
                });
            } else if ($request->subscription_status === 'inactive') {
                $query->whereDoesntHave('userSubscriptions', function($q) {
                    $q->where('status', 'active')
                      ->where('is_active', 1)
                      ->where('end_date', '>=', now());
                });
            }
        }

        // Apply user status filter
        if ($request->has('status') && !empty($request->status)) {
            if ($request->status === 'online') {
                $query->online(); // Using the scope defined in the User model
            } else if ($request->status === 'offline') {
                $query->where(function($q) {
                    $q->whereNull('last_seen')
                      ->orWhere('last_seen', '<=', now()->subMinutes(5));
                });
            }
        }

        $users = $query->paginate(15)->withQueryString();
        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit($id)
    {
        $user = User::findOrFail($id);
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => "required|email|max:255|unique:users,email,{$user->id}",
            'account_type' => 'required|string|in:Super Admin,Individu,Perusahaan,Bank & Asuransi',
            'npwp' => 'nullable|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'add_subscription_days' => 'nullable|integer|min:1',
            'subscription_id' => 'nullable|exists:user_subscriptions,id',
            'verify_subscription' => 'nullable|in:1',
            'pending_subscription_id' => 'nullable|exists:user_subscriptions,id',
            'verify_email' => 'nullable|in:1',
        ]);

        // Update user information
        $user->update([
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'account_type' => $validatedData['account_type'],
            'npwp' => $validatedData['npwp'],
            'company_name' => $validatedData['company_name'],
            'phone' => $validatedData['phone'],
        ]);

        // Handle adding days to existing subscription
        if (!empty($request->add_subscription_days) && !empty($request->subscription_id)) {
            $userSubscription = \App\Models\UserSubscription::findOrFail($request->subscription_id);

            // Make sure this subscription belongs to the user
            if ($userSubscription->user_id == $user->id) {
                // Add days to the end date - convert to integer to avoid type error
                $daysToAdd = (int)$request->add_subscription_days;
                $newEndDate = \Carbon\Carbon::parse($userSubscription->end_date)->addDays($daysToAdd);
                $userSubscription->end_date = $newEndDate;
                $userSubscription->save();

                return redirect()->route('admin.users.show', $user->id)
                    ->with('success', "User updated and {$request->add_subscription_days} days added to subscription successfully.");
            }
        }

        // Handle manual verification of pending subscription
        if ($request->verify_subscription == '1' && !empty($request->pending_subscription_id)) {
            $pendingSubscription = \App\Models\UserSubscription::findOrFail($request->pending_subscription_id);

            // Make sure this subscription belongs to the user and is pending
            if ($pendingSubscription->user_id == $user->id && $pendingSubscription->status == 'pending') {
                $pendingSubscription->status = 'active';
                $pendingSubscription->is_active = true;
                $pendingSubscription->save();

                // Update related payments status to confirmed if any
                foreach ($pendingSubscription->payments as $payment) {
                    $payment->status = 'confirmed';
                    $payment->confirmation_status = 'confirmed';
                    $payment->save();
                }

                return redirect()->route('admin.users.show', $user->id)
                    ->with('success', 'User updated and subscription verified successfully.');
            }
        }

        // Handle manual email verification
        if ($request->verify_email == '1' && !$user->email_verified_at) {
            $user->email_verified_at = now();
            $user->save();

            return redirect()->route('admin.users.show', $user->id)
                ->with('success', 'User updated and email verified successfully.');
        }

        return redirect()->route('admin.users.show', $user->id)->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy($id)
    {
        $user = User::findOrFail($id);
        // Prevent deleting self
        if (Auth::id() == $user->id) {
            return redirect()->route('admin.users.index')->with('error', 'You cannot delete your own account.');
        }
        $user->delete();

        return redirect()->route('admin.users.index')->with('success', 'User deleted successfully.');
    }

    /**
     * Display the specified user's profile.
     */
    public function show($id)
    {
        $user = User::findOrFail($id);
        return view('admin.users.show', compact('user'));
    }
}
