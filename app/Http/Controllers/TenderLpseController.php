<?php
namespace App\Http\Controllers;

use App\Models\TenderLpse; // Pastikan namespace model sudah sesuai
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Symfony\Component\DomCrawler\Crawler;

class TenderLpseController extends Controller
{
    // Tampilkan semua data TenderLpse
    public function index(Request $request)
    {
        // Ambil query pencarian dan filter dari request
        $search = $request->input('search');
        $kategori = $request->input('kategori');
        $status = $request->input('status');

        // Ambil data tender dengan join ke tabel LPSE, pagination, dan pencarian
        $tenders = TenderLpse::with('lpse')
            ->when($search, function ($query, $search) {
                return $query->where('nama_paket', 'like', '%' . $search . '%')
                    ->orWhere('kode_tender', 'like', '%' . $search . '%')
                    ->orWhereHas('lpse', function ($q) use ($search) {
                        $q->where('nama_lpse', 'like', '%' . $search . '%');
                    })
                    ->orWhere('metode_pemilihan', 'like', '%' . $search . '%')
                    ->orWhere('tahap_tender', 'like', '%' . $search . '%');
            })
            ->when($kategori, function ($query, $kategori) {
                return $query->where('kategori_pekerjaan', $kategori);
            })
            ->when($status, function ($query, $status) {
                return $query->where('status_tender', $status);
            })
            ->orderBy('tanggal_paket_tayang', 'desc')
            ->paginate(25);

        // Ambil daftar kategori pekerjaan yang unik dari database
        $kategori_pekerjaan = TenderLpse::select('kategori_pekerjaan')
            ->whereNotNull('kategori_pekerjaan')
            ->distinct()
            ->orderBy('kategori_pekerjaan')
            ->pluck('kategori_pekerjaan')
            ->toArray();

        $tenders->appends([
            'search' => $search,
            'kategori' => $kategori,
            'status' => $status
        ]);

        // Kembalikan view dengan data tender dan kategori pekerjaan
        return view('tenderid.tender_lpse.index', compact('tenders', 'kategori_pekerjaan'));
    }

    // Tampilkan form untuk membuat TenderLpse baru
    public function create()
    {
        return view('tender.create');
    }

    public function count()
    {
        $tenderCounts = TenderLpse::getTenderCounts();

        return response()->json($tenderCounts);
    }

    // Simpan TenderLpse baru
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'field1' => 'required',
            'field2' => 'required',
            // Tambahkan validasi untuk field lain yang ada di model
        ]);

        TenderLpse::create($validatedData);

        return redirect()->route('tender.index')->with('success', 'Tender created successfully.');
    }

    // Tampilkan detail TenderLpse
    public function show($kode_tender)
    {
        // Mengambil data tender berdasarkan kode_tender
        $tender = TenderLpse::with([
                'lpse',
                'peserta' => function($query) {
                    $query->orderBy('created_at');
                },
                'pemenang',
                'instansiSatker',
                'jadwal',
                'anggaran',
                'lokasiPaket'
            ])
            ->where('kode_tender', $kode_tender)
            ->firstOrFail();
        $hpsDecreasePercentage = $tender->getHpsDecreasePercentage();

        return view('tenderid.tender_lpse.detail', compact('tender', 'hpsDecreasePercentage'));
    }

    // Tampilkan form untuk mengedit TenderLpse
    public function edit(TenderLpse $tender)
    {
        return view('tender.edit', compact('tender'));
    }

    // Update TenderLpse
    public function update(Request $request, TenderLpse $tender)
    {
        $validatedData = $request->validate([
            'field1' => 'required',
            'field2' => 'required',
            // Tambahkan validasi untuk field lain yang ada di model
        ]);

        $tender->update($validatedData);

        return redirect()->route('tender.index')->with('success', 'Tender updated successfully.');
    }

    // Hapus TenderLpse
    public function destroy(TenderLpse $tender)
    {
        $tender->delete();

        return redirect()->route('tender.index')->with('success', 'Tender deleted successfully.');
    }

    // Fungsi baru untuk menampilkan tender yang tayang dalam 36 jam terakhir
    public function tenderHariIni(Request $request)
    {
        // Ambil waktu cutoff 36 jam yang lalu
        $cutoffTime = Carbon::now()->subHours(36);

        // Ambil query pencarian dan filter dari request
        $search = $request->input('search');
        $kategori = $request->input('kategori');
        $status = $request->input('status');

        // Ambil tender yang tayang setelah waktu cutoff
        $tenders = TenderLpse::with('lpse')
            ->where('tanggal_paket_tayang', '>=', $cutoffTime)
            ->when($search, function ($query, $search) {
                return $query->where('nama_paket', 'like', '%' . $search . '%')
                    ->orWhere('kode_tender', 'like', '%' . $search . '%')
                    ->orWhereHas('lpse', function ($q) use ($search) {
                        $q->where('nama_lpse', 'like', '%' . $search . '%');
                    })
                    ->orWhere('metode_pemilihan', 'like', '%' . $search . '%')
                    ->orWhere('tahap_tender', 'like', '%' . $search . '%');
            })
            ->when($kategori, function ($query, $kategori) {
                return $query->where('kategori_pekerjaan', $kategori);
            })
            ->when($status, function ($query, $status) {
                return $query->where('status_tender', $status);
            })
            ->orderBy('tanggal_paket_tayang', 'desc')
            ->paginate(25);

        // Ambil daftar kategori pekerjaan yang unik dari database
        $kategori_pekerjaan = TenderLpse::select('kategori_pekerjaan')
            ->whereNotNull('kategori_pekerjaan')
            ->distinct()
            ->orderBy('kategori_pekerjaan')
            ->pluck('kategori_pekerjaan')
            ->toArray();

        $tenders->appends([
            'search' => $search,
            'kategori' => $kategori,
            'status' => $status
        ]);

        // Kembalikan view dengan data tender yang baru tayang dan kategori pekerjaan
        return view('tenderid.tender_lpse.index', compact('tenders', 'kategori_pekerjaan'));
    }

    public function TenderSelesai(Request $request)
    {
        // Ambil query pencarian dan filter dari request
        $search = $request->input('search');
        $kategori = $request->input('kategori');
        $status = $request->input('status');

        // Ambil data tender yang statusnya aktif dan sudah selesai dengan join ke tabel LPSE, pagination, dan pencarian
        $tenders = TenderLpse::with('lpse')
            ->whereIn('status_tender', ['Aktif', 'Tender Sedang Berjalan'])              // Filter untuk tender dengan status Aktif
            ->where('tahap_tender', 'Tender Sudah Selesai') // Filter untuk tender yang tahapnya sudah selesai
            ->when($search, function ($query, $search) {
                return $query->where('nama_paket', 'like', '%' . $search . '%')
                    ->orWhere('kode_tender', 'like', '%' . $search . '%')
                    ->orWhereHas('lpse', function ($q) use ($search) {
                        $q->where('nama_lpse', 'like', '%' . $search . '%');
                    })
                    ->orWhere('metode_pemilihan', 'like', '%' . $search . '%');
            })
            ->when($kategori, function ($query, $kategori) {
                return $query->where('kategori_pekerjaan', $kategori);
            })
            ->when($status, function ($query, $status) {
                return $query->where('status_tender', $status);
            })
            ->orderBy('updated_at', 'desc')
            ->paginate(25);

        // Ambil daftar kategori pekerjaan yang unik dari database
        $kategori_pekerjaan = TenderLpse::select('kategori_pekerjaan')
            ->whereNotNull('kategori_pekerjaan')
            ->distinct()
            ->orderBy('kategori_pekerjaan')
            ->pluck('kategori_pekerjaan')
            ->toArray();

        $tenders->appends([
            'search' => $search,
            'kategori' => $kategori,
            'status' => $status
        ]);

        // Kembalikan view dengan data tender yang aktif dan sudah selesai
        return view('tenderid.tender_lpse.index', compact('tenders', 'kategori_pekerjaan'));
    }

    public function TenderGagal(Request $request)
    {
        // Ambil query pencarian dan filter dari request
        $search = $request->input('search');
        $kategori = $request->input('kategori');
        $status = $request->input('status');

        // Ambil data tender yang statusnya aktif dan sudah selesai dengan join ke tabel LPSE, pagination, dan pencarian
        $tenders = TenderLpse::with('lpse')
            ->where('status_tender', 'Ditutup')
            ->when($search, function ($query, $search) {
                return $query->where('nama_paket', 'like', '%' . $search . '%')
                    ->orWhere('kode_tender', 'like', '%' . $search . '%')
                    ->orWhereHas('lpse', function ($q) use ($search) {
                        $q->where('nama_lpse', 'like', '%' . $search . '%');
                    })
                    ->orWhere('metode_pemilihan', 'like', '%' . $search . '%');
            })
            ->when($kategori, function ($query, $kategori) {
                return $query->where('kategori_pekerjaan', $kategori);
            })
            ->when($status, function ($query, $status) {
                return $query->where('status_tender', $status);
            })
            ->orderBy('updated_at', 'desc')
            ->paginate(25);

        // Ambil daftar kategori pekerjaan yang unik dari database
        $kategori_pekerjaan = TenderLpse::select('kategori_pekerjaan')
            ->whereNotNull('kategori_pekerjaan')
            ->distinct()
            ->orderBy('kategori_pekerjaan')
            ->pluck('kategori_pekerjaan')
            ->toArray();

        $tenders->appends([
            'search' => $search,
            'kategori' => $kategori,
            'status' => $status
        ]);

        // Kembalikan view dengan data tender yang aktif dan sudah selesai
        return view('tenderid.tender_lpse.index', compact('tenders', 'kategori_pekerjaan'));
    }

    public function TenderOnProcess(Request $request)
    {
        // Ambil query pencarian dan filter dari request
        $search = $request->input('search');
        $kategori = $request->input('kategori');
        $status = $request->input('status');

        // Ambil data tender yang statusnya aktif dan sudah selesai dengan join ke tabel LPSE, pagination, dan pencarian
        $tenders = TenderLpse::with('lpse')
            ->where('status_tender', 'Aktif') // Filter untuk tender dengan status Aktif
            ->where('tahap_tender', '!=', 'Tender Sudah Selesai')
            ->when($search, function ($query, $search) {
                return $query->where('nama_paket', 'like', '%' . $search . '%')
                    ->orWhere('kode_tender', 'like', '%' . $search . '%')
                    ->orWhereHas('lpse', function ($q) use ($search) {
                        $q->where('nama_lpse', 'like', '%' . $search . '%');
                    })
                    ->orWhere('metode_pemilihan', 'like', '%' . $search . '%');
            })
            ->when($kategori, function ($query, $kategori) {
                return $query->where('kategori_pekerjaan', $kategori);
            })
            ->when($status, function ($query, $status) {
                return $query->where('status_tender', $status);
            })
            ->orderBy('updated_at', 'desc')
            ->paginate(25);

        // Ambil daftar kategori pekerjaan yang unik dari database
        $kategori_pekerjaan = TenderLpse::select('kategori_pekerjaan')
            ->whereNotNull('kategori_pekerjaan')
            ->distinct()
            ->orderBy('kategori_pekerjaan')
            ->pluck('kategori_pekerjaan')
            ->toArray();

        $tenders->appends([
            'search' => $search,
            'kategori' => $kategori,
            'status' => $status
        ]);

        // Kembalikan view dengan data tender yang aktif dan sudah selesai
        return view('tenderid.tender_lpse.index', compact('tenders', 'kategori_pekerjaan'));
    }

    public function getUraianLink(Request $request)
    {
        $link_tender = $request->get('url');
        $url         = $link_tender;

        // Decode URL jika diperlukan
        $decodedUrl = urldecode($link_tender);

        // Parse URL untuk mendapatkan komponen-komponen dari URL
        $parsedUrl = parse_url($decodedUrl);

        // Gabungkan scheme (protokol) dan host (domain utama)
        $fullUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];

        // Pilihan proxy, bisa diisi array proxy atau kosong
        $proxies = [
            'http://**************:3128',
            // Tambahkan proxy lain jika ada
        ];

        // Tentukan apakah akan menggunakan proxy secara random
        $useProxy = (bool)random_int(0, 1);

        // Inisialisasi Guzzle Client dengan opsi proxy jika dipilih
        $clientOptions = [];
        if ($useProxy && count($proxies) > 0) {
            $randomProxy = $proxies[array_rand($proxies)];
            $clientOptions['proxy'] = $randomProxy;
        }

        // Lakukan request menggunakan Guzzle untuk mendapatkan HTML dari URL dengan opsi proxy
        $headers = [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Connection' => 'keep-alive',
            'Upgrade-Insecure-Requests' => '1',
        ];

        $clientOptions['headers'] = $headers;

        // Lakukan request menggunakan cURL dengan proxy dan header di getHtmlContentFromUrl
        $htmlContent = $this->getHtmlContentFromUrl($url);

        // Parse HTML menggunakan Symfony DomCrawler
        $crawler = new Crawler($htmlContent);

        // Cari link yang mengandung teks 'Uraian Singkat Pekerjaan'
        $uraianLink  = '';
        $fileName    = '';
        $refresh     = 'No';
        $crawler->filter('tr')->each(function (Crawler $node) use (&$uraianLink, &$fileName) {
            // Periksa apakah <th> di dalam baris ini berisi 'Uraian Singkat Pekerjaan'
            $thNode = $node->filter('th')->first();
            if ($thNode->count() > 0 && strpos($thNode->text(), 'Uraian Singkat Pekerjaan') !== false) {
                // Ambil <a> yang ada di dalam <td> setelah <th> pada baris yang sama
                $tdNode = $node->filter('td')->first(); // Ambil elemen <td> setelah <th>
                $tdNode->filter('a')->each(function (Crawler $aNode) use (&$uraianLink, &$fileName) {
                    $uraianLink = $aNode->attr('href');
                    $fileName   = trim($aNode->text());
                });
            }
        });

        // Untuk Kode Tender
        $kodeTender = '';
        if ($crawler->filter('th:contains("Kode Tender")')->count() > 0) {
            $kodeTender = $crawler->filter('th:contains("Kode Tender")')->siblings()->text();
        }

        // Untuk Alasan
        $alasan = '';
        if ($crawler->filter('th:contains("Alasan")')->count() > 0) {
            $alasan = $crawler->filter('th:contains("Alasan")')->siblings()->text();
        }

        // Untuk syarat kualifikasi
        $syaratKualifikasi = '';
        if ($crawler->filter('th:contains("Syarat Kualifikasi")')->count() > 0) {
            $syaratKualifikasi = $crawler->filter('th:contains("Syarat Kualifikasi")')->siblings()->html();
        }

        // untuk Kualifikasi Usaha
        $kualifikasiUsaha = '';
        if ($crawler->filter('th:contains("Kualifikasi Usaha")')->count() > 0) {
            $kualifikasiUsaha = $crawler->filter('th:contains("Kualifikasi Usaha")')->siblings()->text();
        }

        // Untuk Tahap Tender
        $tahapTender = '';
        if ($crawler->filter('th:contains("Tahap Tender Saat Ini")')->count() > 0) {
            $tahapTender = rapikan_str_tahap_tender(
                $crawler->filter('th:contains("Tahap Tender Saat Ini")')->siblings()->text()
            );
        }

        if (! empty($kodeTender)) {
            $kodeTender = trim($kodeTender);
            if (! empty($tahapTender)) {
                $tahapTender = rapikan_str_tahap_tender(trim($tahapTender));
                $tender      = TenderLpse::where('kode_tender', $kodeTender)->first();
                if ($tender) {
                    $batalStages = ['Tender Batal', 'Seleksi Batal', 'Seleksi Gagal'];

                    if (in_array($tahapTender, $batalStages)) {
                        if ($tender->status_tender === 'Ditutup') {
                            $refresh = 'No';
                        } else {
                            $tender->status_tender = 'Ditutup';
                            $tender->save();
                            $refresh = 'Yes';
                        }
                    }

                    if($tender->syarat_kualifikasi == null && $syaratKualifikasi != '' || $tender->syarat_kualifikasi != $syaratKualifikasi) {
                        $tender->syarat_kualifikasi = $syaratKualifikasi;
                        $tender->save();
                        $refresh = 'Yes';
                    }

                    if ($tender->kualifikasi_usaha == null && $kualifikasiUsaha != '') {
                        $tender->kualifikasi_usaha = $kualifikasiUsaha;
                        $tender->save();
                        $refresh = 'Yes';
                    }

                    if ($tender->keterangan == null && $alasan != '') {
                        $tender->keterangan = $alasan;
                        $tender->save();
                        $refresh = 'Yes';
                    }

                    if ($tender->tahap_tender != $tahapTender) {
                        $tender->tahap_tender = $tahapTender;
                        $tender->save();
                        $refresh = 'Yes';
                    }
                }
            }
        }

        // Jika ditemukan URL, kembalikan sebagai JSON
        if ($uraianLink) {
            return response()->json([
                'success'     => true,
                'uraian_link' => $fullUrl . $uraianLink,
                'filename'    => $fileName,
                'refresh'     => $refresh,
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Link Uraian Singkat Pekerjaan tidak ditemukan.',
            ]);
        }
    }

    /**
     * Fetch URL with exponential backoff retry
     *
     * @param string $url
     * @param int $maxRetries
     * @return string
     * @throws \Exception
     */
    private function getHtmlContentFromUrl($url, $maxRetries = 5)
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $maxRetries) {
            try {
                $attempt++;

                // Exponential backoff: 2^attempt * 1000ms with some jitter
                if ($attempt > 1) {
                    $backoff = (2 ** ($attempt - 1)) * 1000 + rand(100, 1000);
                    $backoffSeconds = round($backoff / 1000, 2);
                    \Log::warning("Retry attempt {$attempt}/{$maxRetries} after {$backoffSeconds}s backoff for URL: {$url}");
                    usleep($backoff * 1000); // Convert to microseconds
                }

                $ch = curl_init($url);

                // Use different user agents for each retry
                $userAgents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
                    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36'
                ];

                $userAgent = $userAgents[array_rand($userAgents)];

                $headers = [
                    "Referer: {$url}",
                    "User-Agent: {$userAgent}",
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language: en-US,en;q=0.9,id;q=0.8,sv;q=0.7,fi;q=0.6',
                    'Cache-Control: no-cache',
                    'Pragma: no-cache',
                    'Connection: keep-alive',
                    'Upgrade-Insecure-Requests: 1',
                ];

                // Ambil proxy dari environment variable PROXIES (comma separated)
                $proxiesEnv = env('PROXIES', '');
                $proxies = array_filter(array_map('trim', explode(',', $proxiesEnv)));

                // Tentukan apakah akan menggunakan proxy (75% chance)
                if (rand(0, 100) < 75 && !empty($proxies)) {
                    // Shuffle proxies dan pilih satu
                    $proxyKeys = array_keys($proxies);
                    shuffle($proxyKeys);
                    $randomProxy = $proxies[$proxyKeys[0]];

                    curl_setopt($ch, CURLOPT_PROXY, $randomProxy);
                    \Log::info("Request to {$url} made using proxy: {$randomProxy}");
                } else {
                    \Log::info("Request to {$url} made without proxy");
                }

                // Set DNS resolver options
                curl_setopt($ch, CURLOPT_DNS_USE_GLOBAL_CACHE, false);
                curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120); // 2 minutes

                // Set connection options
                curl_setopt($ch, CURLOPT_FRESH_CONNECT, true); // Force new connection
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15); // Connection timeout

                // Set timeout (random between 30-60 seconds)
                $timeout = rand(30, 60);
                curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);

                // Set standard options
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_ENCODING, '');
                curl_setopt($ch, CURLOPT_MAXREDIRS, 5); // Maximum number of redirects

                $response = curl_exec($ch);

                // Get status code and other info for logging
                $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                // Log non-200 status codes
                if ($statusCode != 200) {
                    \Log::warning("HTTP status {$statusCode} for URL {$url} on attempt {$attempt}");
                }

                if (curl_errno($ch)) {
                    $error = curl_error($ch);
                    $errorCode = curl_errno($ch);
                    curl_close($ch);

                    // Log the error with more details
                    \Log::error("Curl error {$errorCode} for URL {$url}: {$error}");

                    if ($attempt < $maxRetries) {
                        $lastException = new \Exception("Curl error: {$error}");
                        continue;
                    } else {
                        throw new \Exception("Curl error: {$error}");
                    }
                }

                curl_close($ch);

                // Check for empty or invalid response
                if (empty($response) || !preg_match('/<[^>]*>/', $response)) {
                    if ($attempt < $maxRetries) {
                        \Log::warning("Empty or invalid HTML response for URL: {$url}. Will retry...");
                        $lastException = new \Exception("Empty or invalid HTML response");
                        continue;
                    } else {
                        throw new \Exception("Empty or invalid HTML response after {$maxRetries} attempts");
                    }
                }

                // Check if redirected to login page
                if (strpos($response, 'login') !== false && strpos($response, 'password') !== false) {
                    if ($attempt < $maxRetries) {
                        \Log::warning("Redirected to login page for URL: {$url}. Will retry...");
                        $lastException = new \Exception("Redirected to login page");
                        continue;
                    } else {
                        throw new \Exception("Redirected to login page after {$maxRetries} attempts");
                    }
                }

                return $response;

            } catch (\Exception $e) {
                $lastException = $e;
                \Log::warning("Error on attempt {$attempt}/{$maxRetries} for URL {$url}: " . $e->getMessage());

                // If this is the last attempt, rethrow
                if ($attempt >= $maxRetries) {
                    throw new \Exception("Failed after {$maxRetries} attempts: " . $e->getMessage(), 0, $e);
                }
            }
        }

        // If we get here, all retries failed
        throw $lastException ?: new \Exception("Failed after {$maxRetries} attempts");
    }

    // ================================
    // KHUSUS WHATSAPP BOT INTEGRATION
    // ================================

    /**
     * API untuk pencarian tender (format sederhana untuk WhatsApp)
     */
    public function searchForWhatsApp(Request $request)
    {
        $keyword = $request->input('keyword');

        $tenders = TenderLpse::with('lpse') // Mengambil relasi LPSE
            ->when($keyword, function ($query, $keyword) {
                return $query->where('nama_paket', 'like', "%$keyword%")
                    ->orWhere('kode_tender', 'like', "%$keyword%")
                    ->orWhereHas('lpse', function ($q) use ($keyword) {
                        $q->where('nama_lpse', 'like', "%$keyword%");
                    })
                    ->orWhere('metode_pemilihan', 'like', "%$keyword%")
                    ->orWhere('tahap_tender', 'like', "%$keyword%");
            })
            ->orderBy('tanggal_paket_tayang', 'desc') // Urutkan berdasarkan tanggal tayang terbaru
            ->limit(10)
            ->get([
                'kode_tender',
                'nama_paket',
                'metode_pemilihan',
                'tahap_tender',
                'hps',
                'pagu',
                'tanggal_paket_tayang',
            ])
            ->map(function ($tender) {
                return [
                    'kode_tender'      => $tender->kode_tender,
                    'nama_paket'       => $tender->nama_paket,
                    'metode_pemilihan' => $tender->metode_pemilihan,
                    'tahap_tender'     => $tender->tahap_tender,
                    'hps'              => convert_to_rupiah($tender->hps),  // Format ke Rupiah
                    'pagu'             => convert_to_rupiah($tender->pagu), // Format Pagu
                    'tanggal_tayang'   => $tender->tanggal_paket_tayang,
                    'lpse'             => $tender->lpse ? $tender->lpse->nama_lpse : 'LPSE tidak tersedia',
                    'detail'           => "Ketik */tender {$tender->kode_tender}*", // Link ke detail tender
                ];
            });

        return response()->json($tenders);

    }

    /**
     * API untuk detail tender (format sederhana untuk WhatsApp)
     */
    public function showForWhatsApp($id)
    {
        $tender = TenderLpse::with(['lpse', 'peserta', 'pemenang', 'instansiSatker', 'jadwal', 'anggaran', 'lokasiPaket'])
            ->where('kode_tender', $id)
            ->firstOrFail();

        // Ambil data LPSE
        $lpse = $tender->lpse ? $tender->lpse->nama_lpse : 'LPSE tidak tersedia';

        // Ambil lokasi pertama jika hanya ingin satu lokasi
        $lokasiPertama = $tender->lokasiPaket->first();

        // Gabungkan semua lokasi jika ada lebih dari satu
        $lokasiSemua = $tender->lokasiPaket->map(function ($lokasi) {
            return "$lokasi->kbp_nama, $lokasi->prp_nama, $lokasi->pkt_lokasi";
        })->implode(' | ');

        return response()->json([
            'kode'    => $tender->kode_tender,
            'nama'    => $tender->nama_paket,
            'hps'     => convert_to_rupiah($tender->hps),
            'pagu'    => convert_to_rupiah($tender->pagu),
            'lpse'    => $lpse,
            'lokasi'  => $lokasiSemua ?: ($lokasiPertama ? "$lokasiPertama->kbp_nama, $lokasiPertama->prp_nama, $lokasiPertama->pkt_lokasi" : 'Tidak ada lokasi'),
            'tahap'   => $tender->tahap_tender,
            'metode'  => $tender->metode_pemilihan,
            'tanggal' => $tender->tanggal_paket_tayang,
            'link'    => url("/tender/{$tender->kode_tender}"),
        ]);

    }

    public function syncPeserta($kode_tender)
    {
        try {
            // Kita tidak perlu memeriksa proses yang sedang berjalan
            // Kita akan selalu menjalankan proses baru
            $isRunning = false;

            // Kita akan menggunakan nama lock khusus untuk kode tender ini

            // Jalankan command baru di background dengan parameter kode_tender
            // Gunakan nama lock file khusus untuk kode tender ini
            // Tambahkan log untuk debugging
            $logFile = storage_path("logs/sync-peserta-{$kode_tender}.log");
            $command = 'nohup php ' . base_path('artisan') . ' sync:peserta-tender ' . $kode_tender . ' --lock-name=sync-peserta-' . $kode_tender . ' --force > ' . $logFile . ' 2>&1 &';
            exec($command);

            // Tunggu sebentar untuk memastikan proses dimulai
            sleep(1);

            // Jalankan command secara sinkron untuk mendapatkan output awal
            Artisan::call('sync:peserta-tender', ['kode_tender' => $kode_tender, '--preview' => true]);
            $output = Artisan::output();

            $message = "{$output}\n\nProses telah dijalankan di background dan data akan diperbarui secara otomatis.";

            // Jika sudah ada proses yang berjalan, tambahkan informasi
            if ($isRunning) {
                $message = "⚠️ CATATAN: Sudah ada proses sync peserta untuk tender {$kode_tender} yang sedang berjalan.\n\n" .
                           "Permintaan baru telah dijalankan dan akan berjalan secara paralel.\n\n" . $message;
            }

            return response()->json([
                'success' => true,
                'output' => $message,
                'isRunning' => $isRunning
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    public function syncJadwal($kode_tender)
    {
        try {
            // Kita tidak perlu memeriksa proses yang sedang berjalan
            // Kita akan selalu menjalankan proses baru
            $isRunning = false;

            // Kita akan menggunakan nama lock khusus untuk kode tender ini

            // Jalankan command baru di background dengan parameter kode_tender
            // Gunakan nama lock file khusus untuk kode tender ini
            // Tambahkan log untuk debugging
            $logFile = storage_path("logs/scrape-tender-{$kode_tender}.log");
            $command = 'nohup php ' . base_path('artisan') . ' scrape:tender-details ' . $kode_tender . ' --lock-name=scrape-tender-' . $kode_tender . ' --force > ' . $logFile . ' 2>&1 &';
            exec($command);

            // Tunggu sebentar untuk memastikan proses dimulai
            sleep(1);

            // Jalankan command secara sinkron untuk mendapatkan output awal
            Artisan::call('scrape:tender-details', ['kodeTender' => $kode_tender, '--preview' => true]);
            $output = Artisan::output();

            $message = "{$output}\n\nProses telah dijalankan di background dan data akan diperbarui secara otomatis.";

            // Jika sudah ada proses yang berjalan, tambahkan informasi
            if ($isRunning) {
                $message = "⚠️ CATATAN: Sudah ada proses sync jadwal untuk tender {$kode_tender} yang sedang berjalan.\n\n" .
                           "Permintaan baru telah dijalankan dan akan berjalan secara paralel.\n\n" . $message;
            }

            return response()->json([
                'success' => true,
                'output' => $message,
                'isRunning' => $isRunning
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }
}
