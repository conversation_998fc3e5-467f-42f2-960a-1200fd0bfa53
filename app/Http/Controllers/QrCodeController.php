<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class QrCodeController extends Controller
{
    /**
     * Generate QR code image for a given URL using Google Charts API
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function generate(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'data' => 'required|string',
                'size' => 'nullable|integer|min:100|max:1000',
            ]);

            // Get the data to encode
            $data = $request->input('data');
            $size = $request->input('size', 200);

            // Use Google Charts API to generate QR code
            // This is more reliable than the previous service
            // Set error correction level to H (high) and margin to 2
            // High error correction allows the QR code to be readable even with the logo
            $googleChartsUrl = "https://chart.googleapis.com/chart?cht=qr&chs={$size}x{$size}&chl=" . urlencode($data) . "&chld=H|2";

            // Fetch the QR code image
            $response = Http::get($googleChartsUrl);

            if ($response->successful()) {
                // Log success
                Log::info('QR code generated successfully', [
                    'data_length' => strlen($data),
                    'size' => $size,
                    'service' => 'Google Charts API'
                ]);

                // Return the QR code as an image
                return response($response->body())
                    ->header('Content-Type', 'image/png')
                    ->header('Cache-Control', 'public, max-age=86400'); // Cache for 1 day
            } else {
                throw new \Exception("Failed to generate QR code: " . $response->status());
            }
        } catch (\Exception $e) {
            // Log error
            Log::error('Error generating QR code', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback to QR Server API if Google Charts fails
            $qrServerUrl = "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data=" . urlencode($data) . "&margin=10&format=png";

            try {
                $fallbackResponse = Http::get($qrServerUrl);

                if ($fallbackResponse->successful()) {
                    Log::info('QR code generated using fallback service', [
                        'service' => 'QR Server API'
                    ]);

                    return response($fallbackResponse->body())
                        ->header('Content-Type', 'image/png')
                        ->header('Cache-Control', 'public, max-age=86400');
                }
            } catch (\Exception $fallbackError) {
                Log::error('Fallback QR code generation also failed', [
                    'error' => $fallbackError->getMessage()
                ]);
            }

            // Return a placeholder image or error response if all methods fail
            return response()->json(['error' => 'Failed to generate QR code'], 500);
        }
    }
}
