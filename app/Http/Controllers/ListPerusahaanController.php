<?php
namespace App\Http\Controllers;

use App\Models\ListPerusahaanTender;
use App\Models\PaketPengadaan;
use App\Models\TenderLpse;
use App\Models\Ekatalog6;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ListPerusahaanController extends Controller
{
    public function index(Request $request)
    {
        $search = $request->input('search');
        $filter = $request->input('filter');      // Parameter filter, misalnya: name, npwp, tender, dll.
        $sort   = $request->input('sort', 'asc'); // Default sort asc jika tidak diberikan

        $query = DB::table('list_perusahaan_tender')
            ->select(
                'list_perusahaan_tender.id',
                'list_perusahaan_tender.nama_peserta',
                'list_perusahaan_tender.npwp',
                'list_perusahaan_tender.npwp_blur',
                'list_perusahaan_tender.alamat',
                'list_perusahaan_tender.pembeda',
                'perusahaan_statistik.jumlah_tender_dimenangkan',
                'perusahaan_statistik.jumlah_tender_diikuti',
                'perusahaan_statistik.nilai_tender_dimenangkan',
                'perusahaan_statistik.jumlah_ekatalog_didapatkan',
                'perusahaan_statistik.nilai_ekatalog_didapatkan',
                'perusahaan_statistik.total_tender_ekatalog'
            )
            ->leftJoin('perusahaan_statistik', 'list_perusahaan_tender.id', '=', 'perusahaan_statistik.id_perusahaan');

        // Pencarian berdasarkan nama, NPWP, atau alamat
        if (! empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('list_perusahaan_tender.nama_peserta', 'like', '%' . $search . '%')
                    ->orWhere('list_perusahaan_tender.npwp', 'like', '%' . $search . '%')
                    ->orWhere('list_perusahaan_tender.alamat', 'like', '%' . $search . '%');
            });
        }

        // Mapping parameter filter ke nama kolom yang relevan
        $filterMapping = [
            'name'           => 'list_perusahaan_tender.nama_peserta',
            'npwp'           => 'list_perusahaan_tender.npwp',
            'tender'         => 'perusahaan_statistik.jumlah_tender_dimenangkan',
            'nilai_tender'   => 'perusahaan_statistik.nilai_tender_dimenangkan',
            'paket_ekatalog' => 'perusahaan_statistik.jumlah_ekatalog_didapatkan',
            'nilai_ekatalog' => 'perusahaan_statistik.nilai_ekatalog_didapatkan',
            'total'          => 'perusahaan_statistik.total_tender_ekatalog',
        ];

        // Jika parameter filter ada dan valid, gunakan untuk pengurutan
        if ($filter && array_key_exists($filter, $filterMapping)) {
            $query->orderBy($filterMapping[$filter], $sort);
        } else {
            // Default: urutkan berdasarkan total tender ekatalog (descending)
            $query->orderByDesc('perusahaan_statistik.total_tender_ekatalog');
        }

        // Ambil data dengan paginasi dan sertakan parameter pencarian & filter pada link paginasi
        $perusahaan = $query->simplePaginate(25);
        $perusahaan->appends($request->all());

        return view('tenderid.perusahaan.index', compact('perusahaan'));
    }

    public function show($pembeda)
    {
        $pembeda = decrypt($pembeda);
        $perusahaan = ListPerusahaanTender::where('pembeda', $pembeda)->firstOrFail();

        // Helper function to normalize tender status
        $normalizeStatus = function($tender) {
            if ($tender->status_tender === 'Tender Sedang Berjalan') {
                $tender->status_tender = 'Aktif';
            } elseif ($tender->status_tender === 'Tender Ditutup') {
                $tender->status_tender = 'Tidak Aktif';
            }
            return $tender;
        };

        // Get E-Katalog 5 data with eager loading
        $paketPengadaan5 = PaketPengadaan::with(['transaksiPaket.produk', 'penyedia'])
            ->where('id_pelaksana', $perusahaan->id)
            ->orderBy('tanggal_paket', 'desc')
            ->get()
            ->map(function ($item) {
                $item->katalog_version = 'E-Katalog 5';
                return $item;
            });

        // Get E-Katalog 6 data with eager loading
        $paketPengadaan6 = Ekatalog6::with(['penyedia', 'klpd'])
            ->where('id_penyedia', $perusahaan->id)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($item) {
                return (object) [
                    'katalog_version' => 'E-Katalog 6',
                    'id_paket' => $item->kode,
                    'nama_paket' => $item->nama_paket,
                    'instansi_pembeli' => optional($item->klpd)->nama_klpd ?? 'N/A',
                    'nilai_kontrak' => $item->total_pelaksanaan,
                    'tanggal_paket' => $item->created_at,
                    'pengelola' => 'E-Katalog 6.0'
                ];
            });

        // Merge both E-Katalog data and convert to collection
        $allPaketPengadaan = $paketPengadaan5->concat($paketPengadaan6)
            ->sortByDesc('tanggal_paket');

        // Get total counts and values before pagination
        $totalPaketPengadaan = $allPaketPengadaan->count();
        $totalNilaiPaketPengadaan = $allPaketPengadaan->sum('nilai_kontrak');

        // Paginate the merged collection
        $page = request()->input('page_paket_pengadaan', 1);
        $perPage = 25;
        $paketPengadaan = new \Illuminate\Pagination\LengthAwarePaginator(
            $allPaketPengadaan->forPage($page, $perPage),
            $allPaketPengadaan->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'pageName' => 'page_paket_pengadaan']
        );

        // Get tender data with eager loading
        $tenderDiikuti = TenderLpse::with(['lpse', 'peserta', 'pemenang'])
            ->whereHas('peserta', function ($query) use ($perusahaan) {
                $query->where('id_peserta', $perusahaan->id);
            })
            ->orderBy('tanggal_paket_tayang', 'desc')
            ->paginate(25);

        // Normalize status for paginated items
        $tenderDiikuti->through($normalizeStatus);

        $tenderDimenangkanQuery = TenderLpse::with(['lpse', 'pemenang'])
            ->whereHas('pemenang', function ($query) use ($perusahaan) {
                $query->where('id_peserta', $perusahaan->id);
            })
            ->orderBy('tanggal_paket_tayang', 'desc');

        // Get total count before pagination
        $totalTenderDimenangkan = $tenderDimenangkanQuery->count();

        // Paginate the results
        $tenderDimenangkan = $tenderDimenangkanQuery->paginate(25, ['*'], 'page_tender_dimenangkan');

        // Normalize status for paginated items
        $tenderDimenangkan->through($normalizeStatus);

        $totalTenderDiikuti = $tenderDiikuti->total();

        // Calculate total HPS and contract value from all winning tenders (not just paginated ones)
        $allTenderDimenangkan = TenderLpse::with(['lpse', 'pemenang'])
            ->whereHas('pemenang', function ($query) use ($perusahaan) {
                $query->where('id_peserta', $perusahaan->id);
            })
            ->get();

        $totalHps = $allTenderDimenangkan->sum('hps');
        $totalNilaiKontrak = $allTenderDimenangkan->sum(function ($tender) {
            $nilaiKontrak = $tender->pemenang->first()->harga_penawaran;
            if ($nilaiKontrak < 1) {
                $nilaiKontrak = $tender->pemenang->first()->harga_terkoreksi;
            }
            return $nilaiKontrak;
        });

        return view('tenderid.perusahaan.show', compact(
            'perusahaan',
            'paketPengadaan',
            'totalPaketPengadaan',
            'totalNilaiPaketPengadaan',
            'tenderDiikuti',
            'tenderDimenangkan',
            'totalTenderDiikuti',
            'totalTenderDimenangkan',
            'totalHps',
            'totalNilaiKontrak'
        ));
    }

}
