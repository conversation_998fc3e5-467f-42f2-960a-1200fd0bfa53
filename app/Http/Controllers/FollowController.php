<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\TenderLpse;
use App\Models\Lpse;

class FollowController extends Controller
{
    public function followTender(Request $request, $kodeTender)
    {
        $user = auth()->user();
        $followed = json_decode($user->tender_followed) ?? [];
        
        if (!in_array($kodeTender, $followed)) {
            $followed[] = $kodeTender;
            $user->tender_followed = json_encode($followed);
            $user->save();
            return response()->json(['status' => 'followed']);
        }
        
        return response()->json(['status' => 'already_followed']);
    }

    public function unfollowTender(Request $request, $kodeTender)
    {
        $user = auth()->user();
        $followed = json_decode($user->tender_followed) ?? [];
        
        $followed = array_values(array_diff($followed, [$kodeTender]));
        $user->tender_followed = json_encode($followed);
        $user->save();
        
        return response()->json(['status' => 'unfollowed']);
    }

    public function followLpse(Request $request, $kdLpse)
    {
        $user = auth()->user();
        $followed = json_decode($user->lpse_followed) ?? [];
        
        if (!in_array($kdLpse, $followed)) {
            $followed[] = $kdLpse;
            $user->lpse_followed = json_encode($followed);
            $user->save();
            return response()->json(['status' => 'followed']);
        }
        
        return response()->json(['status' => 'already_followed']);
    }

    public function unfollowLpse(Request $request, $kdLpse)
    {
        $user = auth()->user();
        $followed = json_decode($user->lpse_followed) ?? [];
        
        $followed = array_values(array_diff($followed, [$kdLpse]));
        $user->lpse_followed = json_encode($followed);
        $user->save();
        
        return response()->json(['status' => 'unfollowed']);
    }

    public function followSirup(Request $request, $kodeRup)
    {
        $user = auth()->user();
        $followed = json_decode($user->sirup_followed) ?? [];
        
        if (!in_array($kodeRup, $followed)) {
            $followed[] = $kodeRup;
            $user->sirup_followed = json_encode($followed);
            $user->save();
            return response()->json(['status' => 'followed']);
        }
        
        return response()->json(['status' => 'already_followed']);
    }

    public function unfollowSirup(Request $request, $kodeRup)
    {
        $user = auth()->user();
        $followed = json_decode($user->sirup_followed) ?? [];
        
        $followed = array_values(array_diff($followed, [$kodeRup]));
        $user->sirup_followed = json_encode($followed);
        $user->save();
        
        return response()->json(['status' => 'unfollowed']);
    }
}