<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EmailLogController extends Controller
{
    /**
     * Display a listing of email logs.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $query = EmailLog::with('user')->orderBy('sent_at', 'desc');

        // Filter by email type
        if ($request->has('email_type') && !empty($request->email_type)) {
            $query->where('email_type', $request->email_type);
        }

        // Filter by status
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Filter by recipient
        if ($request->has('recipient') && !empty($request->recipient)) {
            $query->where('recipient_email', 'like', '%' . $request->recipient . '%');
        }

        // Filter by subject
        if ($request->has('subject') && !empty($request->subject)) {
            $query->where('subject', 'like', '%' . $request->subject . '%');
        }

        // Filter by date range
        if ($request->has('date_range') && !empty($request->date_range)) {
            $dates = explode(' - ', $request->date_range);
            if (count($dates) == 2) {
                $startDate = date('Y-m-d', strtotime($dates[0])) . ' 00:00:00';
                $endDate = date('Y-m-d', strtotime($dates[1])) . ' 23:59:59';
                $query->whereBetween('sent_at', [$startDate, $endDate]);
            }
        }

        // Get email logs with pagination
        $emailLogs = $query->paginate(20);

        // Get email types for filter dropdown
        $emailTypes = EmailLog::select('email_type')
            ->distinct()
            ->whereNotNull('email_type')
            ->pluck('email_type');

        // Get statistics
        $stats = [
            'total' => EmailLog::count(),
            'sent' => EmailLog::where('status', 'sent')->count(),
            'failed' => EmailLog::where('status', 'failed')->count(),
            'delivered' => EmailLog::where('status', 'delivered')->count(),
            'opened' => EmailLog::where('status', 'opened')->count(),
            'today' => EmailLog::whereDate('sent_at', today())->count(),
            'this_week' => EmailLog::whereBetween('sent_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month' => EmailLog::whereMonth('sent_at', now()->month)
                ->whereYear('sent_at', now()->year)
                ->count(),
        ];

        // Get daily email count for the last 30 days
        $dailyStats = EmailLog::select(
            DB::raw('DATE(sent_at) as date'),
            DB::raw('COUNT(*) as count')
        )
            ->whereDate('sent_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('admin.email_logs.index', compact('emailLogs', 'emailTypes', 'stats', 'dailyStats'));
    }

    /**
     * Display the specified email log.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $emailLog = EmailLog::with('user')->findOrFail($id);
        return view('admin.email_logs.show', compact('emailLog'));
    }

    /**
     * Resend the specified email.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resend($id)
    {
        $emailLog = EmailLog::with('user')->findOrFail($id);
        $redirectRoute = request()->has('from_detail') ?
            route('admin.email_logs.show', $id) :
            route('admin.email_logs.index');

        try {
            // Check if this is a verification email
            if ($emailLog->email_type === 'verification' && $emailLog->user) {
                // Check if user's email is already verified
                if ($emailLog->user->hasVerifiedEmail()) {
                    return redirect($redirectRoute)
                        ->with('error', 'Email sudah diverifikasi oleh pengguna.');
                }

                // Resend verification email
                $emailLog->user->sendEmailVerificationNotification();

                return redirect($redirectRoute)
                    ->with('success', 'Email verifikasi berhasil dikirim ulang ke ' . $emailLog->recipient_email);
            }

            // For password reset emails
            if ($emailLog->email_type === 'password_reset' && $emailLog->user) {
                // Create a new password reset token
                $token = \Illuminate\Support\Str::random(64);

                // Store the token in the password_resets table
                \DB::table('password_reset_tokens')->updateOrInsert(
                    ['email' => $emailLog->recipient_email],
                    [
                        'token' => \Illuminate\Support\Facades\Hash::make($token),
                        'created_at' => now()
                    ]
                );

                // Send the password reset email
                $emailLog->user->notify(new \Illuminate\Auth\Notifications\ResetPassword($token));

                return redirect($redirectRoute)
                    ->with('success', 'Email reset password berhasil dikirim ulang ke ' . $emailLog->recipient_email);
            }

            // For other types of emails, we need to implement specific logic based on email_type
            // This is a more complex implementation that would require knowledge of all email types
            // and how to regenerate them

            return redirect($redirectRoute)
                ->with('error', 'Pengiriman ulang untuk tipe email "' . ($emailLog->email_type ?: 'tidak diketahui') . '" belum diimplementasikan.');

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to resend email', [
                'email_log_id' => $emailLog->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect($redirectRoute)
                ->with('error', 'Gagal mengirim ulang email: ' . $e->getMessage());
        }
    }

    /**
     * Delete the specified email log.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $emailLog = EmailLog::findOrFail($id);
        $emailLog->delete();

        return redirect()->route('admin.email_logs.index')
            ->with('success', 'Email log deleted successfully.');
    }
}
