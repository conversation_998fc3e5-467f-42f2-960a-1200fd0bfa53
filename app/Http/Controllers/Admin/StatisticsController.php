<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PageAccessLog;
use App\Models\LoginLog;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StatisticsController extends Controller
{
    public function index(Request $request)
    {
        $period = $request->input('period', 'daily');
        $days = (int)$request->input('days', 30);
        $startDate = Carbon::now()->subDays($days)->startOfDay();

        // Statistik akses halaman
        $pageStats = $this->getPageAccessStats($startDate, $period);

        // Statistik login
        $loginStats = $this->getLoginStats($startDate, $period);

        // Statistik perangkat
        $deviceStats = $this->getDeviceStats($startDate);

        // Statistik lokasi
        $locationStats = $this->getLocationStats($startDate);

        return view('admin.statistics.index', compact(
            'pageStats',
            'loginStats',
            'deviceStats',
            'locationStats',
            'period',
            'days'
        ));
    }

    protected function getPageAccessStats($startDate, $period)
    {
        // Halaman yang paling banyak dikunjungi
        $topPages = PageAccessLog::select('url', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->groupBy('url')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        // Kunjungan per periode
        $groupFormat = $this->getGroupFormat($period);

        $visitsByPeriod = PageAccessLog::select(
                DB::raw("DATE_FORMAT(created_at, '{$groupFormat}') as period"),
                DB::raw('count(*) as total')
            )
            ->where('created_at', '>=', $startDate)
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        // Rata-rata waktu muat
        $avgLoadTime = PageAccessLog::where('created_at', '>=', $startDate)
            ->whereNotNull('load_time_ms')
            ->avg('load_time_ms');

        // Waktu muat per periode
        $loadTimeByPeriod = PageAccessLog::select(
                DB::raw("DATE_FORMAT(created_at, '{$groupFormat}') as period"),
                DB::raw('AVG(load_time_ms) as avg_load_time')
            )
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('load_time_ms')
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        return [
            'topPages' => $topPages,
            'visitsByPeriod' => $visitsByPeriod,
            'avgLoadTime' => round($avgLoadTime, 2),
            'loadTimeByPeriod' => $loadTimeByPeriod,
            'totalVisits' => PageAccessLog::where('created_at', '>=', $startDate)->count(),
            'uniqueUrls' => PageAccessLog::where('created_at', '>=', $startDate)->distinct('url')->count('url'),
        ];
    }

    protected function getLoginStats($startDate, $period)
    {
        // Login per periode
        $groupFormat = $this->getGroupFormat($period);

        $loginsByPeriod = LoginLog::select(
                DB::raw("DATE_FORMAT(login_at, '{$groupFormat}') as period"),
                DB::raw('count(*) as total')
            )
            ->where('login_at', '>=', $startDate)
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        // Pengguna paling aktif
        $activeUsers = LoginLog::select('user_id', DB::raw('count(*) as total'))
            ->with('user:id,name,email')
            ->where('login_at', '>=', $startDate)
            ->groupBy('user_id')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        return [
            'loginsByPeriod' => $loginsByPeriod,
            'activeUsers' => $activeUsers,
            'totalLogins' => LoginLog::where('login_at', '>=', $startDate)->count(),
            'uniqueUsers' => LoginLog::where('login_at', '>=', $startDate)->distinct('user_id')->count('user_id'),
        ];
    }

    protected function getDeviceStats($startDate)
    {
        // Browser yang paling banyak digunakan
        $browsers = PageAccessLog::select('browser', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('browser')
            ->groupBy('browser')
            ->orderBy('total', 'desc')
            ->get();

        // Sistem operasi yang paling banyak digunakan
        $operatingSystems = PageAccessLog::select('os', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('os')
            ->groupBy('os')
            ->orderBy('total', 'desc')
            ->get();

        // Jenis perangkat
        $deviceTypes = PageAccessLog::select('device_type', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('device_type')
            ->groupBy('device_type')
            ->orderBy('total', 'desc')
            ->get();

        // Merek perangkat
        $deviceBrands = PageAccessLog::select('device_brand', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('device_brand')
            ->where('device_brand', '!=', 'Unknown')
            ->groupBy('device_brand')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        return [
            'browsers' => $browsers,
            'operatingSystems' => $operatingSystems,
            'deviceTypes' => $deviceTypes,
            'deviceBrands' => $deviceBrands,
        ];
    }

    protected function getLocationStats($startDate)
    {
        // Negara paling aktif
        $countries = PageAccessLog::select('country', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('country')
            ->where('country', '!=', '')
            ->groupBy('country')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        // Kota paling aktif
        $cities = PageAccessLog::select('country', 'city', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('city')
            ->where('city', '!=', '')
            ->groupBy('country', 'city')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        // Data untuk peta
        $mapData = PageAccessLog::select('country', 'latitude', 'longitude', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->where('latitude', '!=', '')
            ->where('longitude', '!=', '')
            ->groupBy('country', 'latitude', 'longitude')
            ->orderBy('total', 'desc')
            ->limit(100)
            ->get();

        // Distribusi geografis per region
        $regions = PageAccessLog::select('region', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('region')
            ->where('region', '!=', '')
            ->groupBy('region')
            ->orderBy('total', 'desc')
            ->get();

        return [
            'countries' => $countries,
            'cities' => $cities,
            'mapData' => $mapData,
            'regions' => $regions,
        ];
    }

    protected function getGroupFormat($period)
    {
        switch ($period) {
            case 'daily':
                return '%Y-%m-%d'; // Format: Tahun-Bulan-Hari
            case 'weekly':
                return '%x-W%v'; // Format: Tahun-Minggu
            case 'monthly':
                return '%Y-%m'; // Format: Tahun-Bulan
            default:
                return '%Y-%m-%d';
        }
    }

    public function exportCsv(Request $request)
    {
        $type = $request->input('type', 'page_access');
        $days = (int)$request->input('days', 30);
        $startDate = Carbon::now()->subDays($days)->startOfDay();

        switch ($type) {
            case 'page_access':
                $data = PageAccessLog::with('user:id,name,email')
                    ->where('created_at', '>=', $startDate)
                    ->orderBy('created_at', 'desc')
                    ->get();
                $filename = 'log_akses_halaman_' . date('Y-m-d') . '.csv';
                $headers = ['ID', 'Pengguna', 'URL', 'Metode', 'IP', 'Browser', 'OS', 'Perangkat', 'Negara', 'Kota', 'Tanggal'];
                break;

            case 'login':
                $data = LoginLog::with('user:id,name,email')
                    ->where('login_at', '>=', $startDate)
                    ->orderBy('login_at', 'desc')
                    ->get();
                $filename = 'log_login_' . date('Y-m-d') . '.csv';
                $headers = ['ID', 'Pengguna', 'Login Pada', 'Logout Pada', 'IP', 'Browser', 'OS', 'Perangkat', 'Negara', 'Kota'];
                break;

            default:
                return redirect()->back()->with('error', 'Tipe ekspor tidak valid');
        }

        $callback = function() use ($data, $headers, $type) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $headers);

            foreach ($data as $row) {
                if ($type === 'page_access') {
                    fputcsv($file, [
                        $row->id,
                        $row->user->name ?? 'Tidak diketahui',
                        $row->url,
                        $row->method,
                        $row->ip_address,
                        $row->browser,
                        $row->os,
                        $row->device_type,
                        $row->country,
                        $row->city,
                        $row->created_at,
                    ]);
                } else {
                    fputcsv($file, [
                        $row->id,
                        $row->user->name ?? 'Unknown',
                        $row->login_at,
                        $row->logout_at,
                        $row->ip_address,
                        $row->browser,
                        $row->os,
                        $row->device_type,
                        $row->country,
                        $row->city,
                    ]);
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
}
