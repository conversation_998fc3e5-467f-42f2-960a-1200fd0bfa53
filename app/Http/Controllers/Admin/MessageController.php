<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\MessageLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class MessageController extends Controller
{
    /**
     * Display the message form
     */
    public function index()
    {
        // Get all users for the dropdown
        $users = User::orderBy('name')->get();

        // Get message logs for history
        $messageLogs = MessageLog::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.messages.index', compact('users', 'messageLogs'));
    }

    /**
     * Send a message to a user or multiple users
     */
    public function send(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'message_type' => 'required|in:whatsapp,telegram,both',
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $userIds = $request->input('user_ids');
        $messageType = $request->input('message_type');
        $messageContent = $request->input('message');
        $successCount = 0;
        $failedCount = 0;

        // Get users
        $users = User::whereIn('id', $userIds)->get();

        foreach ($users as $user) {
            $success = false;
            $errorMessage = null;

            try {
                // Send message based on type
                if ($messageType === 'whatsapp' || $messageType === 'both') {
                    if ($user->phone) {
                        $whatsappSuccess = $this->sendWhatsAppMessage($user, $messageContent);
                        if ($whatsappSuccess) {
                            $success = true;
                        } else {
                            $errorMessage = 'Failed to send WhatsApp message';
                        }
                    } else {
                        $errorMessage = 'User has no phone number';
                    }
                }

                if ($messageType === 'telegram' || $messageType === 'both') {
                    if ($user->telegram_bot) {
                        $telegramSuccess = $this->sendTelegramMessage($user, $messageContent);
                        if ($telegramSuccess) {
                            $success = true;
                        } else {
                            $errorMessage = $errorMessage ? $errorMessage . ' and Telegram' : 'Failed to send Telegram message';
                        }
                    } else {
                        $errorMessage = $errorMessage ? $errorMessage . ' and no Telegram ID' : 'User has no Telegram ID';
                    }
                }

                // Log the message
                $messageLog = MessageLog::create([
                    'user_id' => $user->id,
                    'message_type' => $messageType,
                    'message' => $messageContent,
                    'status' => $success ? 'sent' : 'failed',
                    'error_message' => $errorMessage,
                ]);

                // Debug log to verify message is saved correctly
                Log::debug('Message log created', [
                    'id' => $messageLog->id,
                    'message_length' => strlen($messageLog->message),
                    'message_preview' => substr($messageLog->message, 0, 50) . (strlen($messageLog->message) > 50 ? '...' : '')
                ]);

                if ($success) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
            } catch (\Exception $e) {
                Log::error('Error sending message: ' . $e->getMessage(), [
                    'user_id' => $user->id,
                    'message_type' => $messageType,
                ]);

                $messageLog = MessageLog::create([
                    'user_id' => $user->id,
                    'message_type' => $messageType,
                    'message' => $messageContent,
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                ]);

                // Debug log to verify message is saved correctly
                Log::debug('Error message log created', [
                    'id' => $messageLog->id,
                    'message_length' => strlen($messageLog->message),
                    'message_preview' => substr($messageLog->message, 0, 50) . (strlen($messageLog->message) > 50 ? '...' : '')
                ]);

                $failedCount++;
            }
        }

        if ($successCount > 0 && $failedCount === 0) {
            return redirect()->route('admin.messages.index')
                ->with('success', "Messages sent successfully to {$successCount} users.");
        } elseif ($successCount > 0 && $failedCount > 0) {
            return redirect()->route('admin.messages.index')
                ->with('warning', "Messages sent to {$successCount} users, but failed for {$failedCount} users.");
        } else {
            return redirect()->route('admin.messages.index')
                ->with('error', "Failed to send messages to all {$failedCount} users.");
        }
    }

    /**
     * Send a WhatsApp message to a user
     */
    private function sendWhatsAppMessage($user, $message)
    {
        try {
            // Format phone number
            $phone = $this->formatPhoneNumber($user->phone);

            // Get WhatsApp service URL from config
            $whatsappServiceUrl = config('services.whatsapp.url');

            // Ensure URL is properly formatted
            if (empty($whatsappServiceUrl)) {
                $whatsappServiceUrl = env('WHATSAPP_SERVICE_URL', 'http://localhost:3000');
            }

            // Ensure URL doesn't end with slash
            $whatsappServiceUrl = rtrim($whatsappServiceUrl, '/');

            // Get API key
            $apiKey = config('services.whatsapp.api_key', env('WHATSAPP_API_KEY', 'tenderid2023'));

            // Log request details for debugging
            Log::info('Sending WhatsApp message', [
                'user_id' => $user->id,
                'phone' => $phone,
                'service_url' => $whatsappServiceUrl,
            ]);

            // Use the system notification endpoint which accepts custom messages
            $response = Http::timeout(10)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'x-api-key' => $apiKey,
                    'Accept' => 'application/json',
                ])
                ->post("{$whatsappServiceUrl}/notification/system", [
                    'phone' => $phone,
                    'message' => $message
                ]);

            // Log the response for debugging
            Log::info('WhatsApp API response', [
                'user_id' => $user->id,
                'phone' => $phone,
                'status' => $response->status(),
                'body' => $response->body(),
            ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Error sending WhatsApp message: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'phone' => $user->phone,
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Send a Telegram message to a user
     */
    private function sendTelegramMessage($user, $message)
    {
        try {
            // Get Telegram bot token from config
            $botToken = config('services.telegram-bot-api.token', env('TELEGRAM_BOT_TOKEN'));

            if (empty($botToken)) {
                Log::error('Telegram bot token is not configured');
                return false;
            }

            // Send message via Telegram API
            $response = Http::timeout(10)
                ->post("https://api.telegram.org/bot{$botToken}/sendMessage", [
                    'chat_id' => $user->telegram_bot,
                    'text' => $message,
                    'parse_mode' => 'HTML',
                ]);

            // Log the response for debugging
            Log::info('Telegram API response', [
                'user_id' => $user->id,
                'telegram_id' => $user->telegram_bot,
                'status' => $response->status(),
                'body' => $response->body(),
            ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Error sending Telegram message: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'telegram_id' => $user->telegram_bot,
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Get message content for a specific message log
     */
    public function getMessageContent($id)
    {
        try {
            $messageLog = MessageLog::findOrFail($id);

            // Log for debugging
            Log::debug('Retrieved message content', [
                'id' => $id,
                'message_length' => strlen($messageLog->message),
                'message_preview' => substr($messageLog->message, 0, 50) . (strlen($messageLog->message) > 50 ? '...' : '')
            ]);

            return response()->json([
                'success' => true,
                'message' => $messageLog->message
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrieving message content: ' . $e->getMessage(), [
                'id' => $id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Gagal mengambil pesan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a specific message log
     */
    public function delete($id)
    {
        try {
            $messageLog = MessageLog::findOrFail($id);
            $messageLog->delete();

            return redirect()->route('admin.messages.index')
                ->with('success', 'Pesan berhasil dihapus.');
        } catch (\Exception $e) {
            Log::error('Error deleting message log: ' . $e->getMessage(), [
                'id' => $id,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('admin.messages.index')
                ->with('error', 'Gagal menghapus pesan: ' . $e->getMessage());
        }
    }

    /**
     * Delete all message logs
     */
    public function deleteAll()
    {
        try {
            $count = MessageLog::count();
            MessageLog::truncate();

            return redirect()->route('admin.messages.index')
                ->with('success', "Berhasil menghapus {$count} riwayat pesan.");
        } catch (\Exception $e) {
            Log::error('Error deleting all message logs: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('admin.messages.index')
                ->with('error', 'Gagal menghapus semua pesan: ' . $e->getMessage());
        }
    }

    /**
     * Format phone number for WhatsApp
     */
    private function formatPhoneNumber($phone)
    {
        // Remove all non-digit characters
        $phone = preg_replace('/\D/', '', $phone);

        // Ensure number starts with 62 (Indonesia country code)
        if (substr($phone, 0, 2) !== '62') {
            // If starts with 0, replace with 62
            if (substr($phone, 0, 1) === '0') {
                $phone = '62' . substr($phone, 1);
            } else {
                // If doesn't start with 0, add 62 prefix
                $phone = '62' . $phone;
            }
        }

        return $phone;
    }
}
