<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Withdrawal;
use App\Models\Expense;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class FinancialJournalController extends Controller
{
    public function index(Request $request)
    {
        // Get date range from request or default to current month
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));
        $type = $request->get('type');

        // Get confirmed payments (money in)
        $payments = Payment::with(['user', 'userSubscription.subscription'])
            ->where('status', 'confirmed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->when($type === 'in', function ($query) {
                return $query;
            })
            ->when($type && $type !== 'in', function ($query) {
                return $query->whereNull('id'); // Return empty if filtering for other types
            })
            ->orderBy('created_at', 'desc')
            ->get();

        // Get approved withdrawals (money out)
        $withdrawals = Withdrawal::with('user')
            ->where('status', 'approved')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->when($type === 'out', function ($query) {
                return $query;
            })
            ->when($type && $type !== 'out', function ($query) {
                return $query->whereNull('id'); // Return empty if filtering for other types
            })
            ->orderBy('created_at', 'desc')
            ->get();

        // Get manual expenses (money out)
        $expenses = Expense::with('createdBy')
            ->whereBetween('date', [$startDate, $endDate])
            ->when($type === 'out', function ($query) {
                return $query;
            })
            ->when($type && $type !== 'out', function ($query) {
                return $query->whereNull('id'); // Return empty if filtering for other types
            })
            ->orderBy('date', 'desc')
            ->get();

        // Calculate totals
        $totalIn = $payments->sum('amount');
        $totalOut = $withdrawals->sum('amount') + $expenses->sum('amount');
        $balance = $totalIn - $totalOut;

        // Combine payments, withdrawals, and expenses into a single collection for the journal
        $journal = collect();

        foreach ($payments as $payment) {
            $journal->push([
                'date' => $payment->created_at,
                'type' => 'in',
                'description' => "Subscription Payment: {$payment->userSubscription->subscription->name}",
                'user' => $payment->user->name,
                'amount' => $payment->amount,
                'reference' => "Payment #{$payment->id}"
            ]);
        }

        foreach ($withdrawals as $withdrawal) {
            $journal->push([
                'date' => $withdrawal->created_at,
                'type' => 'out',
                'description' => 'Withdrawal Payment',
                'user' => $withdrawal->user->name,
                'amount' => $withdrawal->amount,
                'reference' => "Withdrawal #{$withdrawal->id}"
            ]);
        }

        foreach ($expenses as $expense) {
            $journal->push([
                'date' => $expense->date,
                'type' => 'out',
                'description' => $expense->description,
                'user' => $expense->createdBy->name,
                'amount' => $expense->amount,
                'reference' => $expense->reference ? $expense->reference : "Expense #{$expense->id}",
                'category' => $expense->category,
                'notes' => $expense->notes,
                'expense_id' => $expense->id
            ]);
        }

        // Sort journal entries by date
        $journal = $journal->sortByDesc('date');

        // Get expense categories for dropdown
        $categories = Expense::select('category')
            ->whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->toArray();

        return view('admin.financial_journal.index', compact(
            'journal',
            'totalIn',
            'totalOut',
            'balance',
            'startDate',
            'endDate',
            'type',
            'categories'
        ));
    }

    /**
     * Show the form for creating a new expense.
     */
    public function createExpense()
    {
        // Get expense categories for dropdown
        $categories = Expense::select('category')
            ->whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->toArray();

        return view('admin.financial_journal.create_expense', compact('categories'));
    }

    /**
     * Store a newly created expense in storage.
     */
    public function storeExpense(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0',
            'description' => 'required|string|max:255',
            'category' => 'nullable|string|max:100',
            'date' => 'required|date',
            'reference' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Create the expense
        $expense = new Expense();
        $expense->amount = $request->amount;
        $expense->description = $request->description;
        $expense->category = $request->category;
        $expense->date = $request->date;
        $expense->reference = $request->reference;
        $expense->notes = $request->notes;
        $expense->created_by = auth()->id();
        $expense->save();

        return redirect()->route('admin.financial_journal')
            ->with('success', 'Expense added successfully!');
    }

    /**
     * Show the form for editing the specified expense.
     */
    public function editExpense($id)
    {
        $expense = Expense::findOrFail($id);

        // Get expense categories for dropdown
        $categories = Expense::select('category')
            ->whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->toArray();

        return view('admin.financial_journal.edit_expense', compact('expense', 'categories'));
    }

    /**
     * Update the specified expense in storage.
     */
    public function updateExpense(Request $request, $id)
    {
        $expense = Expense::findOrFail($id);

        // Validate the request
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0',
            'description' => 'required|string|max:255',
            'category' => 'nullable|string|max:100',
            'date' => 'required|date',
            'reference' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update the expense
        $expense->amount = $request->amount;
        $expense->description = $request->description;
        $expense->category = $request->category;
        $expense->date = $request->date;
        $expense->reference = $request->reference;
        $expense->notes = $request->notes;
        $expense->save();

        return redirect()->route('admin.financial_journal')
            ->with('success', 'Expense updated successfully!');
    }

    /**
     * Remove the specified expense from storage.
     */
    public function destroyExpense($id)
    {
        $expense = Expense::findOrFail($id);
        $expense->delete();

        return redirect()->route('admin.financial_journal')
            ->with('success', 'Expense deleted successfully!');
    }
}
