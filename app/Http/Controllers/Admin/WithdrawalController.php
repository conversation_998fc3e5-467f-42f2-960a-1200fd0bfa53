<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Withdrawal;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WithdrawalController extends Controller
{
    public function index()
    {
        $withdrawals = Withdrawal::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        return view('admin.withdrawals.index', compact('withdrawals'));
    }

    public function approve($id)
    {
        DB::beginTransaction();
        try {
            $withdrawal = Withdrawal::findOrFail($id);
            
            // Only process if withdrawal is pending
            if ($withdrawal->status !== 'pending') {
                return redirect()->route('admin.withdrawals.index')
                    ->with('error', 'This withdrawal request has already been processed.');
            }

            // Get user and check balance
            $user = $withdrawal->user;
            if ($user->commission_balance < $withdrawal->amount) {
                throw new \Exception('Insufficient commission balance.');
            }

            // Update withdrawal status
            $withdrawal->status = 'approved';
            $withdrawal->processed_at = now();
            $withdrawal->save();

            DB::commit();
            return redirect()->route('admin.withdrawals.index')
                ->with('success', 'Withdrawal request has been approved successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('admin.withdrawals.index')
                ->with('error', 'Error processing withdrawal: ' . $e->getMessage());
        }
    }

    public function reject(Request $request, $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:1000'
        ]);

        DB::beginTransaction();
        try {
            $withdrawal = Withdrawal::findOrFail($id);
            
            // Only process if withdrawal is pending
            if ($withdrawal->status !== 'pending') {
                return redirect()->route('admin.withdrawals.index')
                    ->with('error', 'This withdrawal request has already been processed.');
            }

            $withdrawal->status = 'rejected';
            $withdrawal->rejection_reason = $request->rejection_reason;
            $withdrawal->processed_at = now();
            $withdrawal->save();

            DB::commit();
            return redirect()->route('admin.withdrawals.index')
                ->with('success', 'Withdrawal request has been rejected.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('admin.withdrawals.index')
                ->with('error', 'Error rejecting withdrawal: ' . $e->getMessage());
        }
    }
}
