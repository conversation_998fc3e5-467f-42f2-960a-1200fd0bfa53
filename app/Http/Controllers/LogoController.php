<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Klpd;
use Illuminate\Support\Facades\Storage;
use GuzzleHttp\Client;
use RecursiveIteratorIterator;
use RecursiveArrayIterator;
use DOMDocument;

class LogoController extends Controller
{
    // Scrape Bing image search page using Guzzle and parse image URLs
    public function search(Request $request)
    {
        $query = $request->query('query');
        $searchUrl = "https://www.bing.com/images/search?q=Logo" . urlencode($query);

        try {
            $client = new Client([
                'headers' => [
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                ],
            ]);
            $response = $client->get($searchUrl);
            $html = (string) $response->getBody();

            // Debug: return the raw HTML content for inspection
            // return response($html)->header('Content-Type', 'text/html');

            // More general approach: extract all JSON objects and find those containing "murl"
            $images = [];
            libxml_use_internal_errors(true);
            $dom = new DOMDocument();
            $dom->loadHTML($html);
            libxml_clear_errors();
            $elems = $dom->getElementsByTagName('a');
            foreach ($elems as $elem) {
                if ($elem->hasAttribute('m')) {
                    $m = json_decode($elem->getAttribute('m'), true);
                    if (isset($m['murl'])) {
                        $images[] = $m['murl'];
                    }
                }
            }

            // Remove duplicates and limit to 20 images
            $images = array_slice(array_unique($images), 0, 200);

            return response()->json($images);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    // Upload selected logo image to server and update klpd record
    public function upload(Request $request)
    {
        $request->validate([
            'kd_klpd' => 'required|string|exists:klpd,kd_klpd',
            'image_url' => 'required|url',
        ]);

        $kdKlpd = $request->input('kd_klpd');
        $imageUrl = $request->input('image_url');

        try {
            $imageContents = file_get_contents($imageUrl);
            $imageName = 'klpd_logos/' . $kdKlpd . '_' . time() . '.png';

            $klpd = Klpd::where('kd_klpd', $kdKlpd)->first();

            // Delete existing logo file if exists
            if ($klpd->logo_path && Storage::disk('public')->exists(str_replace('storage/', '', $klpd->logo_path))) {
                Storage::disk('public')->delete(str_replace('storage/', '', $klpd->logo_path));
            }

            Storage::disk('public')->put($imageName, $imageContents);

            $klpd->logo_path = 'storage/' . $imageName;
            $klpd->save();

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    // Upload logo image file for LPSE
    public function uploadLogoLpse(Request $request)
    {
        if ($request->has('image_url')) {
            // Handle upload by image URL
            $request->validate([
                'kd_lpse' => 'required|string|exists:lpse,kd_lpse',
                'image_url' => 'required|url',
            ]);

            $kdLpse = $request->input('kd_lpse');
            $imageUrl = $request->input('image_url');

            try {
                $imageContents = file_get_contents($imageUrl);
                $imageName = 'lpse_logos/' . $kdLpse . '_' . time() . '.png';

                $lpse = \App\Models\Lpse::where('kd_lpse', $kdLpse)->first();

                if (!$lpse) {
                    if ($request->expectsJson()) {
                        return response()->json(['success' => false, 'message' => 'LPSE not found.'], 404);
                    }
                    return redirect()->back()->with('error', 'LPSE not found.');
                }

                // Delete old logo if exists
                if ($lpse->logo_path && Storage::disk('public')->exists(str_replace('storage/', '', $lpse->logo_path))) {
                    Storage::disk('public')->delete(str_replace('storage/', '', $lpse->logo_path));
                }

                Storage::disk('public')->put($imageName, $imageContents);

                $lpse->logo_path = 'storage/' . $imageName;
                $lpse->save();

                if ($request->expectsJson()) {
                    return response()->json(['success' => true]);
                }
                return redirect()->back()->with('success', 'Logo updated successfully.');
            } catch (\Exception $e) {
                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => 'Failed to upload logo: ' . $e->getMessage()], 500);
                }
                return redirect()->back()->with('error', 'Failed to upload logo: ' . $e->getMessage());
            }
        } 
    }

    // Upload logo image file for KLPD
    public function uploadLogoKlpd(Request $request)
    {
        if ($request->has('image_url')) {
            $kdKlpd = $request->input('kd_klpd');
            $imageUrl = $request->input('image_url');

            try {
                $imageContents = file_get_contents($imageUrl);
                $imageName = 'klpd_logos/' . $kdKlpd . '_' . time() . '.png';

                $klpd = Klpd::where('kd_klpd', $kdKlpd)->first();

                if (!$klpd) {
                    if ($request->expectsJson()) {
                        return response()->json(['success' => false, 'message' => "KLPD {$klpd} xcxnot found."], 404);
                    }
                    return redirect()->back()->with('error', 'KLPD not found.');
                }

                // Delete old logo if exists
                if ($klpd->logo_path && Storage::disk('public')->exists(str_replace('storage/', '', $klpd->logo_path))) {
                    Storage::disk('public')->delete(str_replace('storage/', '', $klpd->logo_path));
                }

                Storage::disk('public')->put($imageName, $imageContents);

                $klpd->logo_path = 'storage/' . $imageName;
                $klpd->save();

                if ($request->expectsJson()) {
                    return response()->json(['success' => true]);
                }
                return redirect()->back()->with('success', 'Logo updated successfully.');
            } catch (\Exception $e) {
                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => 'Failed to upload logo: ' . $e->getMessage()], 500);
                }
                return redirect()->back()->with('error', 'Failed to upload logo: ' . $e->getMessage());
            }
        }
    }
}
