<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TenderLpse;
use App\Models\Lpse;
use App\Models\DataSirup;
use Illuminate\Support\Facades\Auth;

class ForYouController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        
        // Get followed tenders
        $followedTenders = [];
        if ($user->tender_followed) {
            $tenderIds = json_decode($user->tender_followed) ?? [];
            $followedTenders = TenderLpse::whereIn('kode_tender', $tenderIds)
                ->with('lpse')
                ->orderBy('created_at', 'desc')
                ->get();
        }

        // Get followed LPSE
        $followedLpse = [];
        if ($user->lpse_followed) {
            $lpseIds = json_decode($user->lpse_followed) ?? [];
            $followedLpse = Lpse::whereIn('kd_lpse', $lpseIds)
                ->orderBy('created_at', 'desc')
                ->get();
        }

        // Get followed SIRUP
        $followedSirup = [];
        if ($user->sirup_followed) {
            $sirupIds = json_decode($user->sirup_followed) ?? [];
            $followedSirup = DataSirup::whereIn('kode_rup', $sirupIds)
                ->orderBy('created_at', 'desc')
                ->get();
        }

        return view('tenderid.for_you.index', compact('followedTenders', 'followedLpse', 'followedSirup'));
    }
}
