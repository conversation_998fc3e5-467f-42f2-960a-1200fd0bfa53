<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TenderLpse;
use App\Models\Lpse;
use App\Models\Jadwal;
use App\Models\PesertaTender;
use App\Models\PemenangLpse;
use App\Models\ListPerusahaanTender;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\DomCrawler\Crawler;
use Carbon\Carbon;
use Illuminate\Support\Str;

class ManualScrapingController extends Controller
{
    /**
     * Display the manual scraping form
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('admin.manual-scraping.index');
    }

    /**
     * Process the manual scraping form for tender details
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processTenderDetails(Request $request)
    {
        $request->validate([
            'url' => 'required|url',
            'html_content' => 'required|string'
        ]);

        try {
            // Ekstrak kode tender dari URL
            $kodeTender = $this->extractKodeTenderFromUrl($request->url);

            if (!$kodeTender) {
                return redirect()->route('admin.manual-scraping.index')
                    ->with('error', 'Tidak dapat mengekstrak kode tender dari URL. Format URL tidak valid.');
            }

            // Cari tender di database
            $tender = TenderLpse::where('kode_tender', $kodeTender)->first();

            // Jika tender tidak ditemukan, coba ekstrak kd_lpse dari URL
            if (!$tender) {
                $kdLpse = $this->extractKdLpseFromUrl($request->url);

                if (!$kdLpse) {
                    return redirect()->route('admin.manual-scraping.index')
                        ->with('error', 'Tender dengan kode ' . $kodeTender . ' tidak ditemukan dan tidak dapat mengekstrak kd_lpse dari URL.');
                }

                // Buat tender baru
                $tender = new TenderLpse();
                $tender->kode_tender = $kodeTender;
                $tender->kd_lpse = $kdLpse;
                $tender->save();
            }

            // Process HTML content
            $crawler = new Crawler($request->html_content);

            // Extract tender details
            $this->extractTenderDetails($crawler, $tender);

            return redirect()->route('admin.manual-scraping.index')
                ->with('success', 'Detail tender berhasil diproses dan disimpan.');
        } catch (\Exception $e) {
            Log::error('Error processing manual tender details: ' . $e->getMessage());
            return redirect()->route('admin.manual-scraping.index')
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Process the manual scraping form for jadwal tender
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processJadwalTender(Request $request)
    {
        $request->validate([
            'url' => 'required|url',
            'html_content' => 'required|string'
        ]);

        try {
            // Ekstrak kode tender dari URL
            $kodeTender = $this->extractKodeTenderFromUrl($request->url);

            if (!$kodeTender) {
                return redirect()->route('admin.manual-scraping.index')
                    ->with('error', 'Tidak dapat mengekstrak kode tender dari URL. Format URL tidak valid.');
            }

            // Check if tender exists
            $tender = TenderLpse::where('kode_tender', $kodeTender)->first();

            if (!$tender) {
                return redirect()->route('admin.manual-scraping.index')
                    ->with('error', 'Tender dengan kode ' . $kodeTender . ' tidak ditemukan.');
            }

            // Process HTML content
            $crawler = new Crawler($request->html_content);

            // Extract jadwal tender
            $this->extractJadwalTender($crawler, $tender);

            return redirect()->route('admin.manual-scraping.index')
                ->with('success', 'Jadwal tender berhasil diproses dan disimpan.');
        } catch (\Exception $e) {
            Log::error('Error processing manual jadwal tender: ' . $e->getMessage());
            return redirect()->route('admin.manual-scraping.index')
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Process the manual scraping form for peserta tender
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processPesertaTender(Request $request)
    {
        $request->validate([
            'url' => 'required|url',
            'html_content' => 'required|string'
        ]);

        try {
            // Ekstrak kode tender dari URL
            $kodeTender = $this->extractKodeTenderFromUrl($request->url);

            if (!$kodeTender) {
                return redirect()->route('admin.manual-scraping.index')
                    ->with('error', 'Tidak dapat mengekstrak kode tender dari URL. Format URL tidak valid.');
            }

            // Check if tender exists
            $tender = TenderLpse::where('kode_tender', $kodeTender)->first();

            if (!$tender) {
                return redirect()->route('admin.manual-scraping.index')
                    ->with('error', 'Tender dengan kode ' . $kodeTender . ' tidak ditemukan.');
            }

            // Process HTML content
            $crawler = new Crawler($request->html_content);

            // Extract peserta tender
            $this->extractPesertaTender($crawler, $tender);

            return redirect()->route('admin.manual-scraping.index')
                ->with('success', 'Peserta tender berhasil diproses dan disimpan.');
        } catch (\Exception $e) {
            Log::error('Error processing manual peserta tender: ' . $e->getMessage());
            return redirect()->route('admin.manual-scraping.index')
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Process the manual scraping form for pemenang tender
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processPemenangTender(Request $request)
    {
        $request->validate([
            'url' => 'required|url',
            'html_content' => 'required|string'
        ]);

        try {
            // Ekstrak kode tender dari URL
            $kodeTender = $this->extractKodeTenderFromUrl($request->url);

            if (!$kodeTender) {
                return redirect()->route('admin.manual-scraping.index')
                    ->with('error', 'Tidak dapat mengekstrak kode tender dari URL. Format URL tidak valid.');
            }

            // Check if tender exists
            $tender = TenderLpse::where('kode_tender', $kodeTender)->first();

            if (!$tender) {
                return redirect()->route('admin.manual-scraping.index')
                    ->with('error', 'Tender dengan kode ' . $kodeTender . ' tidak ditemukan.');
            }

            // Process HTML content
            $crawler = new Crawler($request->html_content);

            // Extract pemenang tender
            $this->extractPemenangTender($crawler, $tender);

            return redirect()->route('admin.manual-scraping.index')
                ->with('success', 'Pemenang tender berhasil diproses dan disimpan.');
        } catch (\Exception $e) {
            Log::error('Error processing manual pemenang tender: ' . $e->getMessage());
            return redirect()->route('admin.manual-scraping.index')
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Extract tender details from HTML
     *
     * @param Crawler $crawler
     * @param TenderLpse $tender
     * @return void
     */
    private function extractTenderDetails(Crawler $crawler, TenderLpse $tender)
    {
        // Untuk Nama Paket
        if ($crawler->filter('th:contains("Nama Tender")')->count() > 0) {
            $tender->nama_paket = trim($crawler->filter('th:contains("Nama Tender")')->siblings()->text());
        }

        // Untuk Kode Tender
        if ($crawler->filter('th:contains("Kode Tender")')->count() > 0) {
            $kodeTender = trim($crawler->filter('th:contains("Kode Tender")')->siblings()->text());
            $tender->kode_tender = $kodeTender;
        }

        // Untuk Alasan
        if ($crawler->filter('th:contains("Alasan")')->count() > 0) {
            $tender->keterangan = trim($crawler->filter('th:contains("Alasan")')->siblings()->text());
        }

        // Untuk syarat kualifikasi
        if ($crawler->filter('th:contains("Syarat Kualifikasi")')->count() > 0) {
            $tender->syarat_kualifikasi = trim($crawler->filter('th:contains("Syarat Kualifikasi")')->siblings()->html());
        }

        // untuk Kualifikasi Usaha
        if ($crawler->filter('th:contains("Kualifikasi Usaha")')->count() > 0) {
            $tender->kualifikasi_usaha = trim($crawler->filter('th:contains("Kualifikasi Usaha")')->siblings()->text());
        }

        // Untuk Tahap Tender
        if ($crawler->filter('th:contains("Tahap Tender Saat Ini")')->count() > 0) {
            $tahapTender = trim($crawler->filter('th:contains("Tahap Tender Saat Ini")')->siblings()->text());
            if (function_exists('rapikan_str_tahap_tender')) {
                $tahapTender = rapikan_str_tahap_tender($tahapTender);
            }
            $tender->tahap_tender = $tahapTender;
        }

        // Simpan perubahan
        $tender->save();
    }

    /**
     * Extract jadwal tender from HTML
     *
     * @param Crawler $crawler
     * @param TenderLpse $tender
     * @return void
     */
    private function extractJadwalTender(Crawler $crawler, TenderLpse $tender)
    {
        // Hapus jadwal yang ada
        Jadwal::where('kode_tender', $tender->kode_tender)->delete();

        $jadwalTender = [];
        $crawler->filter('table.table-sm tr, table.table tr')->each(function (Crawler $row, $i) use (&$jadwalTender) {
            if ($i > 0 && $row->filter('td')->count() >= 4) { // Skip header tabel
                $jadwalTender[] = [
                    'no' => trim($row->filter('td')->eq(0)->text()),
                    'tahap' => trim($row->filter('td')->eq(1)->text()),
                    'mulai' => $this->parseDateTime(trim($row->filter('td')->eq(2)->text())),
                    'sampai' => $this->parseDateTime(trim($row->filter('td')->eq(3)->text())),
                    'perubahan' => $row->filter('td')->count() > 4 ? trim($row->filter('td')->eq(4)->text()) : null
                ];
            }
        });

        // Simpan jadwal
        foreach ($jadwalTender as $jadwal) {
            if (!empty($jadwal['mulai']) && !empty($jadwal['sampai'])) {
                $tender->jadwal()->create([
                    'step' => $jadwal['no'],
                    'tahap' => $jadwal['tahap'],
                    'tanggal_mulai' => Carbon::parse($jadwal['mulai']),
                    'tanggal_selesai' => Carbon::parse($jadwal['sampai']),
                    'keterangan' => $jadwal['perubahan'] ?? null
                ]);
            }
        }
    }

    /**
     * Extract peserta tender from HTML
     *
     * @param Crawler $crawler
     * @param TenderLpse $tender
     * @return void
     */
    private function extractPesertaTender(Crawler $crawler, TenderLpse $tender)
    {
        // Hapus peserta yang ada
        PesertaTender::where('kode_tender', $tender->kode_tender)->delete();

        $pesertaTender = [];
        $crawler->filter('table.table-sm tr, table.table tr')->each(function (Crawler $row, $i) use (&$pesertaTender) {
            if ($i > 0 && $row->filter('td')->count() >= 2) { // Skip header tabel
                $npwp = null;
                $nama = trim($row->filter('td')->eq(1)->text());

                // Coba ekstrak NPWP jika ada dalam format tertentu
                if (preg_match('/\((\d{2}\.\d{3}\.\d{3}\.\d{1}-\d{3}\.\d{3})\)/', $nama, $matches)) {
                    $npwp = $matches[1];
                    $nama = trim(str_replace('(' . $npwp . ')', '', $nama));
                }

                $pesertaTender[] = [
                    'no' => trim($row->filter('td')->eq(0)->text()),
                    'nama_peserta' => $nama,
                    'npwp' => $npwp,
                    'penawaran' => $row->filter('td')->count() > 2 ? $this->parseRupiah(trim($row->filter('td')->eq(2)->text())) : null,
                    'penawaran_terkoreksi' => $row->filter('td')->count() > 3 ? $this->parseRupiah(trim($row->filter('td')->eq(3)->text())) : null,
                    'hasil_evaluasi' => $row->filter('td')->count() > 4 ? trim($row->filter('td')->eq(4)->text()) : null,
                ];
            }
        });

        // Simpan peserta
        foreach ($pesertaTender as $peserta) {
            // Cari atau buat perusahaan berdasarkan nama dan NPWP menggunakan metode baru
            $perusahaan = ListPerusahaanTender::createOrUpdatePerusahaan(
                $peserta['nama_peserta'],
                $peserta['npwp']
            );

            // Simpan peserta tender
            PesertaTender::create([
                'kode_tender' => $tender->kode_tender,
                'id_peserta' => $perusahaan->id,
                'status' => 'Peserta',
                // 'penawaran' => $peserta['penawaran'],
                // 'penawaran_terkoreksi' => $peserta['penawaran_terkoreksi'],
                // 'hasil_evaluasi' => $peserta['hasil_evaluasi'],
            ]);
        }
    }

    /**
     * Extract pemenang tender from HTML
     *
     * @param Crawler $crawler
     * @param TenderLpse $tender
     * @return void
     */
    private function extractPemenangTender(Crawler $crawler, TenderLpse $tender)
    {
        // Coba ekstrak dari tabel pemenang (format baru)
        $data = [];

        // Cek apakah ada tabel pemenang (biasanya tabel kedua)
        if ($crawler->filter('table.table-sm, table.table')->count() > 1) {
            try {
                $crawler->filter('table.table-sm, table.table')->eq(1)->filter('tr')->each(function ($node) use (&$data) {
                    if ($node->filter('td')->count() > 4) {
                        // Ambil nilai teks asli untuk logging
                        $hargaPenawaranText = $node->filter('td')->eq(3)->text();
                        $hargaTerkoreksiText = $node->filter('td')->eq(4)->text();
                        $hargaNegosiasiText = $node->filter('td')->count() > 5 ? $node->filter('td')->eq(5)->text() : null;

                        // Log nilai teks asli
                        Log::debug("Format tabel - Harga Penawaran text: " . $hargaPenawaranText);
                        Log::debug("Format tabel - Harga Terkoreksi text: " . $hargaTerkoreksiText);
                        if ($hargaNegosiasiText) {
                            Log::debug("Format tabel - Harga Negosiasi text: " . $hargaNegosiasiText);
                        }

                        // Konversi nilai
                        $hargaPenawaran = $this->str_rupiah_to_decimal($hargaPenawaranText);
                        $hargaTerkoreksi = $this->str_rupiah_to_decimal($hargaTerkoreksiText);
                        $hargaNegosiasi = $hargaNegosiasiText ? $this->str_rupiah_to_decimal($hargaNegosiasiText) : null;

                        $data[] = [
                            'Nama Perusahaan' => $node->filter('td')->eq(0)->text(),
                            'Alamat' => $node->filter('td')->eq(1)->text(),
                            'NPWP' => $node->filter('td')->eq(2)->text(),
                            'harga_penawaran' => $hargaPenawaran,
                            'harga_terkoreksi' => $hargaTerkoreksi,
                            'harga_negosiasi' => $hargaNegosiasi,
                        ];
                    }
                });
            } catch (\InvalidArgumentException $e) {
                // Tangani kasus "The current node list is empty"
                if (stripos($e->getMessage(), 'current node list is empty') !== false) {
                    Log::warning("Tabel pemenang kosong atau tidak memiliki format yang diharapkan");
                } else {
                    throw $e; // Rethrow jika bukan error yang diharapkan
                }
            }
        }

        // Jika berhasil mendapatkan data dari tabel
        if (!empty($data)) {
            // Simpan data pemenang ke tabel PemenangLpse
            $this->syncPemenangTender($tender, $data);

            // Log informasi
            Log::info("Berhasil menyimpan data pemenang untuk tender {$tender->kode_tender}");

            return; // Keluar dari fungsi karena sudah berhasil
        }

        // Jika tidak berhasil mendapatkan data dari tabel, coba cara lama (format lama)
        if ($crawler->filter('th:contains("Pemenang")')->count() > 0) {
            $namaPemenang = trim($crawler->filter('th:contains("Pemenang")')->siblings()->text());

            // Untuk Alamat dan NPWP
            $alamat = null;
            $npwp = null;

            if ($crawler->filter('th:contains("Alamat")')->count() > 0) {
                $alamat = trim($crawler->filter('th:contains("Alamat")')->siblings()->text());
            }

            if ($crawler->filter('th:contains("NPWP")')->count() > 0) {
                $npwp = trim($crawler->filter('th:contains("NPWP")')->siblings()->text());
            }

            // Cari atau buat perusahaan berdasarkan nama dan NPWP menggunakan metode baru
            $perusahaan = ListPerusahaanTender::createOrUpdatePerusahaan($namaPemenang, $npwp, $alamat);

            // Untuk Harga Penawaran
            $hargaPenawaran = null;
            if ($crawler->filter('th:contains("Harga Penawaran")')->count() > 0) {
                $hargaPenawaranText = trim($crawler->filter('th:contains("Harga Penawaran")')->siblings()->text());
                Log::debug("Format lama - Harga Penawaran text: " . $hargaPenawaranText);
                $hargaPenawaran = $this->parseRupiah($hargaPenawaranText);
            }

            // Untuk Harga Terkoreksi
            $hargaTerkoreksi = null;
            if ($crawler->filter('th:contains("Harga Terkoreksi")')->count() > 0) {
                $hargaTerkoreksiText = trim($crawler->filter('th:contains("Harga Terkoreksi")')->siblings()->text());
                Log::debug("Format lama - Harga Terkoreksi text: " . $hargaTerkoreksiText);
                $hargaTerkoreksi = $this->parseRupiah($hargaTerkoreksiText);
            }

            // Log nilai harga untuk debugging
            Log::debug("Format lama - Nilai harga_penawaran sebelum disimpan: " . ($hargaPenawaran ?? 'null'));
            Log::debug("Format lama - Nilai harga_terkoreksi sebelum disimpan: " . ($hargaTerkoreksi ?? 'null'));

            // Simpan ke tabel PemenangLpse
            $pemenangLpse = PemenangLpse::updateOrCreate(
                ['kode_tender' => $tender->kode_tender, 'id_peserta' => $perusahaan->id],
                [
                    'harga_penawaran' => $hargaPenawaran,
                    'harga_terkoreksi' => $hargaTerkoreksi
                ]
            );

            // Log nilai yang tersimpan
            Log::debug("Format lama - Nilai harga_penawaran setelah disimpan: " . ($pemenangLpse->harga_penawaran ?? 'null'));
            Log::debug("Format lama - Nilai harga_terkoreksi setelah disimpan: " . ($pemenangLpse->harga_terkoreksi ?? 'null'));

            // Log informasi
            Log::info("Berhasil menyimpan data pemenang untuk tender {$tender->kode_tender} dengan format lama");
        }
    }

    /**
     * Parse rupiah format to integer
     *
     * @param string $rupiahString
     * @return int|null
     */
    private function parseRupiah($rupiahString)
    {
        // Gunakan metode str_rupiah_to_decimal untuk konsistensi
        return $this->str_rupiah_to_decimal($rupiahString);
    }

    /**
     * Convert rupiah string to decimal
     *
     * @param string $rupiahString
     * @return float|null
     */
    private function str_rupiah_to_decimal($rupiahString)
    {
        if (empty($rupiahString) || $rupiahString == '-') {
            return null;
        }

        // Log nilai awal untuk debugging
        Log::debug("str_rupiah_to_decimal input: " . $rupiahString);

        // Cek apakah format rupiah (Rp)
        $isRupiah = (stripos($rupiahString, 'rp') !== false);
        Log::debug("Format rupiah terdeteksi: " . ($isRupiah ? 'Ya' : 'Tidak'));

        // Remove non-numeric characters except decimal point and comma
        $numericString = preg_replace('/[^0-9,.]/', '', $rupiahString);
        Log::debug("Setelah menghapus karakter non-numerik: " . $numericString);

        // Jika format Indonesia (1.234.567,89), ubah ke format internasional (1234567.89)
        if (strpos($numericString, '.') !== false && strpos($numericString, ',') !== false) {
            // Format Indonesia: 1.234.567,89
            $numericString = str_replace('.', '', $numericString); // Hapus titik sebagai pemisah ribuan
            $numericString = str_replace(',', '.', $numericString); // Ganti koma dengan titik untuk desimal
            Log::debug("Setelah konversi format Indonesia: " . $numericString);
        } else if (strpos($numericString, ',') !== false) {
            // Format dengan koma sebagai desimal: 1234567,89
            $numericString = str_replace(',', '.', $numericString);
            Log::debug("Setelah konversi koma ke titik: " . $numericString);
        }

        // Convert to float
        $result = floatval($numericString);

        // Log hasil konversi untuk debugging
        Log::debug("str_rupiah_to_decimal result: " . $result);

        return $result;
    }

    /**
     * Parse date time string to Y-m-d H:i:s format
     *
     * @param string $dateString
     * @return string|null
     */
    private function parseDateTime($dateString)
    {
        try {
            // Try parsing with Indonesian month names
            $months = [
                'Januari' => 'January',
                'Februari' => 'February',
                'Maret' => 'March',
                'April' => 'April',
                'Mei' => 'May',
                'Juni' => 'June',
                'Juli' => 'July',
                'Agustus' => 'August',
                'September' => 'September',
                'Oktober' => 'October',
                'November' => 'November',
                'Desember' => 'December',
            ];

            foreach ($months as $id => $en) {
                $dateString = str_replace($id, $en, $dateString);
            }

            return Carbon::createFromFormat('d F Y H:i', $dateString)->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            Log::error("Date parse error for '{$dateString}': " . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract kode tender from URL
     *
     * @param string $url
     * @return string|null
     */
    private function extractKodeTenderFromUrl($url)
    {
        // Pattern untuk URL LPSE, contoh: https://lpse.jatimprov.go.id/eproc4/lelang/10019666000/pengumumanlelang
        if (preg_match('/\/lelang\/(\d+)\//', $url, $matches)) {
            return $matches[1];
        }

        // Pattern alternatif, contoh: https://lpse.jatimprov.go.id/eproc4/tender/10019666000
        if (preg_match('/\/tender\/(\d+)/', $url, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Extract kd_lpse from URL
     *
     * @param string $url
     * @return string|null
     */
    private function extractKdLpseFromUrl($url)
    {
        // Ekstrak domain dari URL
        $parsedUrl = parse_url($url);
        if (!isset($parsedUrl['host'])) {
            return null;
        }

        $domain = $parsedUrl['host'];

        // Cari LPSE berdasarkan domain
        $lpse = Lpse::where('url', 'like', '%' . $domain . '%')->first();

        if ($lpse) {
            return $lpse->kd_lpse;
        }

        return null;
    }

    /**
     * Sync pemenang tender data to PemenangLpse table
     *
     * @param TenderLpse $tender
     * @param array $data
     * @return void
     */
    private function syncPemenangTender($tender, $data)
    {
        foreach ($data as $pemenang) {
            // Cari atau buat perusahaan berdasarkan nama dan NPWP menggunakan metode baru
            $perusahaan = ListPerusahaanTender::createOrUpdatePerusahaan(
                trim($pemenang['Nama Perusahaan']),
                trim($pemenang['NPWP']),
                trim($pemenang['Alamat'])
            );

            if (!$perusahaan) {
                Log::error("Gagal membuat perusahaan untuk pemenang: " . $pemenang['Nama Perusahaan']);
                continue;
            }

            // Log nilai harga untuk debugging
            Log::debug("Nilai harga_penawaran sebelum disimpan: " . ($pemenang['harga_penawaran'] ?? 'null'));
            Log::debug("Nilai harga_terkoreksi sebelum disimpan: " . ($pemenang['harga_terkoreksi'] ?? 'null'));

            // Simpan ke tabel PemenangLpse
            $pemenangLpse = PemenangLpse::updateOrCreate(
                ['kode_tender' => $tender->kode_tender, 'id_peserta' => $perusahaan->id],
                [
                    'harga_penawaran' => $pemenang['harga_penawaran'],
                    'harga_terkoreksi' => $pemenang['harga_terkoreksi']
                ]
            );

            // Log nilai yang tersimpan
            Log::debug("Nilai harga_penawaran setelah disimpan: " . ($pemenangLpse->harga_penawaran ?? 'null'));
            Log::debug("Nilai harga_terkoreksi setelah disimpan: " . ($pemenangLpse->harga_terkoreksi ?? 'null'));

            Log::info("Pemenang diperbarui: {$pemenang['Nama Perusahaan']} untuk tender {$tender->kode_tender}");
        }
    }
}