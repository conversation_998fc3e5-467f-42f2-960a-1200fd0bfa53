<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\Withdrawal;
use App\Helpers\NotificationHelper;
use App\Notifications\UserRegistrationNotification;
use App\Notifications\SubscriptionPackageNotification;
use App\Notifications\PaymentConfirmationNotification;
use App\Notifications\WithdrawalRequestNotification;
use App\Notifications\WithdrawalProcessedNotification;

class NotificationExampleController extends Controller
{
    /**
     * Contoh penggunaan notifikasi pendaftaran user
     */
    public function sendUserRegistrationNotification(User $user)
    {
        // Menggunakan helper untuk mengirim notifikasi ke user dan admin
        NotificationHelper::sendUserRegistrationNotifications($user);
        
        return response()->json(['message' => 'Notifikasi pendaftaran user berhasil dikirim']);
    }
    
    /**
     * Contoh penggunaan notifikasi paket langganan
     */
    public function sendSubscriptionPackageNotification(UserSubscription $userSubscription)
    {
        // <PERSON>rim notifikasi ke user
        $user = $userSubscription->user;
        $user->notify(new SubscriptionPackageNotification($userSubscription));
        
        return response()->json(['message' => 'Notifikasi paket langganan berhasil dikirim']);
    }
    
    /**
     * Contoh penggunaan notifikasi konfirmasi pembayaran
     */
    public function sendPaymentConfirmationNotification(UserSubscription $userSubscription)
    {
        // Menggunakan helper untuk mengirim notifikasi ke admin
        NotificationHelper::sendPaymentConfirmationNotifications($userSubscription);
        
        return response()->json(['message' => 'Notifikasi konfirmasi pembayaran berhasil dikirim']);
    }
    
    /**
     * Contoh penggunaan notifikasi permintaan withdraw
     */
    public function sendWithdrawalRequestNotification(Withdrawal $withdrawal)
    {
        // Menggunakan helper untuk mengirim notifikasi ke user dan admin
        NotificationHelper::sendWithdrawalRequestNotifications($withdrawal);
        
        return response()->json(['message' => 'Notifikasi permintaan withdraw berhasil dikirim']);
    }
    
    /**
     * Contoh penggunaan notifikasi proses withdraw
     */
    public function sendWithdrawalProcessedNotification(Withdrawal $withdrawal)
    {
        // Kirim notifikasi ke user
        $user = $withdrawal->user;
        $user->notify(new WithdrawalProcessedNotification($withdrawal));
        
        return response()->json(['message' => 'Notifikasi proses withdraw berhasil dikirim']);
    }
}
