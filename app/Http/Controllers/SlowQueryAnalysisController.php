<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SlowQueryAnalysisController extends Controller
{
    /**
     * Display slow query analysis dashboard
     */
    public function index()
    {
        return view('admin.slow-query.index');
    }

    /**
     * Get slow query analysis data via API
     */
    public function getAnalysisData(Request $request)
    {
        $days = $request->get('days', 7);
        $threshold = $request->get('threshold', 100);
        $limit = $request->get('limit', 20);

        try {
            $logFiles = $this->getLogFiles($days);
            $slowQueries = $this->parseLogFiles($logFiles, $threshold);
            
            if (empty($slowQueries)) {
                return response()->json([
                    'queries' => [],
                    'patterns' => [],
                    'recommendations' => [],
                    'summary' => [
                        'total_queries' => 0,
                        'avg_time' => 0,
                        'max_time' => 0,
                        'period_days' => $days
                    ]
                ]);
            }

            // Process and analyze queries
            $topQueries = $this->getTopSlowQueries($slowQueries, $limit);
            $patterns = $this->analyzeQueryPatterns($slowQueries);
            $recommendations = $this->generateRecommendations($slowQueries);
            $summary = $this->generateSummary($slowQueries, $days);

            return response()->json([
                'queries' => $topQueries,
                'patterns' => $patterns,
                'recommendations' => $recommendations,
                'summary' => $summary
            ]);

        } catch (\Exception $e) {
            \Log::error('Slow Query Analysis Error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to analyze slow queries'], 500);
        }
    }

    /**
     * Get log files for the specified number of days
     */
    private function getLogFiles($days)
    {
        $logFiles = [];
        $logPath = storage_path('logs/query');

        for ($i = 0; $i < $days; $i++) {
            $date = Carbon::now()->subDays($i)->format('Y-m-d');
            $slowQueryFile = $logPath . '/slow_query-' . $date . '.log';
            $criticalQueryFile = $logPath . '/critical_query-' . $date . '.log';

            if (File::exists($slowQueryFile)) {
                $logFiles[] = $slowQueryFile;
            }
            if (File::exists($criticalQueryFile)) {
                $logFiles[] = $criticalQueryFile;
            }
        }

        return $logFiles;
    }

    /**
     * Parse log files and extract slow queries
     */
    private function parseLogFiles($logFiles, $threshold)
    {
        $slowQueries = [];

        foreach ($logFiles as $logFile) {
            if (!File::exists($logFile)) {
                continue;
            }

            $content = File::get($logFile);
            $lines = explode("\n", $content);

            foreach ($lines as $line) {
                if (empty(trim($line))) {
                    continue;
                }

                $queryData = $this->parseLogLine($line);
                if ($queryData && $queryData['time'] >= $threshold) {
                    $slowQueries[] = $queryData;
                }
            }
        }

        return $slowQueries;
    }

    /**
     * Parse individual log line
     */
    private function parseLogLine($line)
    {
        // Parse Laravel log format
        if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\].*?(\d+\.?\d*)ms.*?SQL: (.+?)(?:\s+Bindings:|$)/i', $line, $matches)) {
            return [
                'timestamp' => $matches[1],
                'time' => (float) $matches[2],
                'sql' => trim($matches[3]),
                'type' => $this->getQueryType($matches[3])
            ];
        }

        return null;
    }

    /**
     * Get query type (SELECT, INSERT, UPDATE, DELETE)
     */
    private function getQueryType($sql)
    {
        $sql = strtoupper(trim($sql));
        if (strpos($sql, 'SELECT') === 0) return 'SELECT';
        if (strpos($sql, 'INSERT') === 0) return 'INSERT';
        if (strpos($sql, 'UPDATE') === 0) return 'UPDATE';
        if (strpos($sql, 'DELETE') === 0) return 'DELETE';
        return 'OTHER';
    }

    /**
     * Get top slow queries
     */
    private function getTopSlowQueries($slowQueries, $limit)
    {
        // Group similar queries and calculate statistics
        $groupedQueries = [];

        foreach ($slowQueries as $query) {
            $normalizedSql = $this->normalizeSql($query['sql']);
            $key = md5($normalizedSql);

            if (!isset($groupedQueries[$key])) {
                $groupedQueries[$key] = [
                    'sql' => $normalizedSql,
                    'type' => $query['type'],
                    'count' => 0,
                    'total_time' => 0,
                    'max_time' => 0,
                    'min_time' => PHP_FLOAT_MAX,
                    'avg_time' => 0
                ];
            }

            $groupedQueries[$key]['count']++;
            $groupedQueries[$key]['total_time'] += $query['time'];
            $groupedQueries[$key]['max_time'] = max($groupedQueries[$key]['max_time'], $query['time']);
            $groupedQueries[$key]['min_time'] = min($groupedQueries[$key]['min_time'], $query['time']);
        }

        // Calculate averages and sort
        foreach ($groupedQueries as &$group) {
            $group['avg_time'] = round($group['total_time'] / $group['count'], 2);
        }

        // Sort by total impact (count * avg_time)
        uasort($groupedQueries, function ($a, $b) {
            $impactA = $a['count'] * $a['avg_time'];
            $impactB = $b['count'] * $b['avg_time'];
            return $impactB <=> $impactA;
        });

        return array_slice(array_values($groupedQueries), 0, $limit);
    }

    /**
     * Normalize SQL for grouping
     */
    private function normalizeSql($sql)
    {
        // Remove specific values and normalize for grouping
        $sql = preg_replace('/\b\d+\b/', '?', $sql);
        $sql = preg_replace("/'[^']*'/", '?', $sql);
        $sql = preg_replace('/"[^"]*"/', '?', $sql);
        $sql = preg_replace('/\s+/', ' ', $sql);
        return trim($sql);
    }

    /**
     * Analyze query patterns
     */
    private function analyzeQueryPatterns($slowQueries)
    {
        $patterns = [
            'by_type' => [],
            'by_table' => [],
            'by_hour' => array_fill(0, 24, 0)
        ];

        foreach ($slowQueries as $query) {
            // Count by type
            $type = $query['type'];
            if (!isset($patterns['by_type'][$type])) {
                $patterns['by_type'][$type] = ['count' => 0, 'total_time' => 0];
            }
            $patterns['by_type'][$type]['count']++;
            $patterns['by_type'][$type]['total_time'] += $query['time'];

            // Count by table
            $tables = $this->extractTables($query['sql']);
            foreach ($tables as $table) {
                if (!isset($patterns['by_table'][$table])) {
                    $patterns['by_table'][$table] = ['count' => 0, 'total_time' => 0];
                }
                $patterns['by_table'][$table]['count']++;
                $patterns['by_table'][$table]['total_time'] += $query['time'];
            }

            // Count by hour
            $hour = (int) date('H', strtotime($query['timestamp']));
            $patterns['by_hour'][$hour]++;
        }

        return $patterns;
    }

    /**
     * Extract table names from SQL
     */
    private function extractTables($sql)
    {
        $tables = [];
        
        // Simple regex to extract table names
        if (preg_match_all('/(?:FROM|JOIN|UPDATE|INTO)\s+`?(\w+)`?/i', $sql, $matches)) {
            $tables = array_merge($tables, $matches[1]);
        }

        return array_unique($tables);
    }

    /**
     * Generate optimization recommendations
     */
    private function generateRecommendations($slowQueries)
    {
        $recommendations = [];

        // Analyze common issues
        $likeQueries = 0;
        $missingIndexes = [];
        $heavyJoins = 0;

        foreach ($slowQueries as $query) {
            $sql = strtoupper($query['sql']);
            
            if (strpos($sql, 'LIKE') !== false) {
                $likeQueries++;
            }
            
            if (substr_count($sql, 'JOIN') > 2) {
                $heavyJoins++;
            }
        }

        if ($likeQueries > 0) {
            $recommendations[] = [
                'type' => 'FULLTEXT_SEARCH',
                'title' => 'Replace LIKE with FULLTEXT Search',
                'description' => "Found {$likeQueries} queries using LIKE. Consider implementing FULLTEXT indexes for better performance.",
                'priority' => 'high'
            ];
        }

        if ($heavyJoins > 0) {
            $recommendations[] = [
                'type' => 'OPTIMIZE_JOINS',
                'title' => 'Optimize Complex Joins',
                'description' => "Found {$heavyJoins} queries with multiple JOINs. Review join conditions and consider denormalization.",
                'priority' => 'medium'
            ];
        }

        return $recommendations;
    }

    /**
     * Generate summary statistics
     */
    private function generateSummary($slowQueries, $days)
    {
        $totalQueries = count($slowQueries);
        $totalTime = array_sum(array_column($slowQueries, 'time'));
        $avgTime = $totalQueries > 0 ? round($totalTime / $totalQueries, 2) : 0;
        $maxTime = $totalQueries > 0 ? max(array_column($slowQueries, 'time')) : 0;

        return [
            'total_queries' => $totalQueries,
            'avg_time' => $avgTime,
            'max_time' => $maxTime,
            'total_time' => round($totalTime, 2),
            'period_days' => $days,
            'queries_per_day' => $totalQueries > 0 ? round($totalQueries / $days, 1) : 0
        ];
    }
}
