<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Actions\Fortify\CreateNewUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Exception;

class RegisterController extends Controller
{
    /**
     * Handle a registration request for the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Actions\Fortify\CreateNewUser  $creator
     * @return \Illuminate\Http\Response
     */
    public function register(Request $request, CreateNewUser $creator)
    {
        // Validate the request input
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'account_type' => ['required', Rule::in(['Super Admin', 'Individu', 'Perusahaan', 'Bank & Asuransi'])],
            'npwp' => 'nullable|string|size:15|unique:users',
            'company_name' => 'nullable|string|max:255',
            'photo' => 'nullable|image|mimes:jpg,jpeg,png|max:1024',
        ], [
            'name.required' => 'Nama lengkap wajib diisi.',
            'name.max' => 'Nama lengkap maksimal 255 karakter.',
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.max' => 'Email maksimal 255 karakter.',
            'email.unique' => 'Email sudah terdaftar.',
            'password.required' => 'Password wajib diisi.',
            'password.min' => 'Password minimal 8 karakter.',
            'password.confirmed' => 'Konfirmasi password tidak cocok.',
            'account_type.required' => 'Tipe akun wajib dipilih.',
            'account_type.in' => 'Tipe akun tidak valid.',
            'npwp.size' => 'NPWP harus 15 karakter.',
            'npwp.unique' => 'NPWP sudah terdaftar.',
            'company_name.max' => 'Nama perusahaan maksimal 255 karakter.',
            'photo.image' => 'File harus berupa gambar.',
            'photo.mimes' => 'Format gambar harus jpg, jpeg, atau png.',
            'photo.max' => 'Ukuran gambar maksimal 1MB.',
        ]);

        try {
            // Create the user
            $user = $creator->create($validatedData);

            // Log in the user
            Auth::login($user);

            // Redirect to intended location or dashboard
            return redirect()->intended('dashboard');
        } catch (Exception $e) {
            // Handle errors gracefully
            return back()->withErrors(['error' => 'Pendaftaran gagal, silakan coba lagi.']);
        }
    }
}
