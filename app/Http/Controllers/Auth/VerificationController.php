<?php
namespace App\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use App\Http\Controllers\Controller;
use App\Models\User; // Pastikan Anda mengimpor model User
use Illuminate\Support\Facades\Log;

class VerificationController extends Controller
{
    // Verifikasi email berdasarkan ID dan hash
    public function verify(Request $request, $id, $hash)
    {
        Log::info('Email verification request', [
            'id' => $id,
            'hash' => $hash
        ]);

        // Mencari pengguna berdasarkan ID
        $user = User::findOrFail($id);

        Log::info('User found for verification', [
            'user_id' => $user->id,
            'email' => $user->email
        ]);

        // Periksa apakah pengguna sudah memverifikasi email
        if ($user->hasVerifiedEmail()) {
            Log::info('User already verified', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);
            return redirect()->route('verification.success');
        }

        // Periksa apakah tautan verifikasi cocok dengan hash pengguna
        $expectedHash = sha1($user->getEmailForVerification());
        $hashMatches = hash_equals((string) $hash, $expectedHash);

        Log::info('Verification hash check', [
            'user_id' => $user->id,
            'matches' => $hashMatches,
            'provided_hash' => $hash,
            'expected_hash' => $expectedHash
        ]);

        if (!$hashMatches) {
            Log::warning('Invalid verification hash', [
                'user_id' => $user->id,
                'email' => $user->email,
                'provided_hash' => $hash,
                'expected_hash' => $expectedHash
            ]);
            return redirect()->route('verification.notice')->withErrors([
                'expired' => 'Link verifikasi ini sudah tidak berlaku. Silakan minta link verifikasi baru.',
            ]);
        }

        // Tandai email sebagai diverifikasi
        if ($user->markEmailAsVerified()) {
            Log::info('User email verified successfully', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);
            event(new Verified($user));
        }

        return redirect()->route('verification.success');
    }

    // Mengirim ulang tautan verifikasi email
    public function resend(Request $request)
    {
        Log::info('Resend verification email request', [
            'method' => $request->method(),
            'is_authenticated' => auth()->check(),
            'email' => $request->input('email')
        ]);

        // Jika pengguna sudah login, gunakan pengguna yang sedang login
        if (auth()->check()) {
            $user = auth()->user();

            Log::info('Authenticated user requesting verification email', [
                'user_id' => $user->id,
                'email' => $user->email,
                'is_verified' => $user->hasVerifiedEmail()
            ]);

            // Pastikan pengguna belum memverifikasi emailnya
            if (!$user->hasVerifiedEmail()) {
                // Kirim ulang notifikasi verifikasi email
                $user->sendEmailVerificationNotification();
                Log::info('Verification email sent to authenticated user', [
                    'user_id' => $user->id,
                    'email' => $user->email
                ]);
                return back()->with('status', 'Tautan verifikasi baru telah dikirim ke alamat email Anda.');
            }

            Log::warning('User already verified', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);
            return back()->withErrors(['email' => 'Email Anda sudah diverifikasi.']);
        }

        // Jika pengguna tidak login, coba ambil email dari input
        $email = $request->input('email');

        // Jika email tidak ada di input, tampilkan form untuk memasukkan email
        if (!$email) {
            Log::info('Showing resend verification form (no email provided)');
            // Pastikan variabel $errors selalu tersedia dengan menggunakan withErrors()
            return view('tenderid.auth.resend-verification')->withErrors([]);
        }

        Log::info('Looking up user by email', ['email' => $email]);

        // Mencari pengguna berdasarkan email
        $user = User::where('email', $email)->first();

        // Pastikan pengguna ada dan belum memverifikasi emailnya
        if ($user && !$user->hasVerifiedEmail()) {
            // Kirim ulang notifikasi verifikasi email
            $user->sendEmailVerificationNotification();
            Log::info('Verification email sent to user', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);
            return back()->with('status', 'Tautan verifikasi baru telah dikirim ke alamat email Anda.');
        }

        if (!$user) {
            Log::warning('User not found', ['email' => $email]);
        } else if ($user->hasVerifiedEmail()) {
            Log::warning('User already verified', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);
        }

        return back()->withErrors(['email' => 'Email tidak ditemukan atau sudah diverifikasi.']);
    }
}