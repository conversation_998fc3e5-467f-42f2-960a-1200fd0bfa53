<?php

namespace App\Http\Controllers;

use App\Models\DataSirup;
use App\Models\Anggaran;
use App\Models\InstansiSatker;
use App\Models\PaketPengadaan;
use App\Models\TenderLpse;
use App\Models\Ekatalog6;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Carbon\Carbon;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ServerException;

class DataSirupController extends Controller
{
    /**
     * Menampilkan daftar SIRUP dengan fitur pencarian dan pagination.
     */
    public function index(Request $request)
    {
        $search = $request->input('search');
        $perPage = min($request->get('per_page', 25), 100); // Maksimum 100 data per halaman

        if ($search && $search !== '') {
            // Use Meilisearch via Laravel Scout with filters
            $query = DataSirup::search($search);

            // Prepare filter array for Meilisearch
            $filterConditions = [];

            // Filter berdasarkan tahun anggaran
            if ($request->has('tahun_anggaran')) {
                $tahun = $request->input('tahun_anggaran');
                if ($tahun != '') {
                    $filterConditions[] = "tahun_anggaran = " . intval($tahun);
                } else {
                    $filterConditions[] = "tahun_anggaran = " . intval(date('Y'));
                }
            } else {
                $filterConditions[] = "tahun_anggaran = " . intval(date('Y'));
            }

            // Filter berdasarkan rentang pagu
            if ($request->has('filter_pagu') && $request->input('filter_pagu') != '') {
                $filterPagu = $request->input('filter_pagu');
                if (strpos($filterPagu, '-') !== false) {
                    [$paguMin, $paguMax] = explode('-', $filterPagu);
                    $paguMin = intval($paguMin);
                    $filterConditions[] = "total_pagu >= $paguMin";

                    if ($paguMax !== '') {
                        $paguMax = intval($paguMax);
                        $filterConditions[] = "total_pagu <= $paguMax";
                    }
                }
            }

            // Filter berdasarkan Nama KLPD
            if ($request->has('nama_klpd') && $request->input('nama_klpd') != '') {
                $namaKlpd = $request->input('nama_klpd');
                // Escape quotes in the value
                $namaKlpd = str_replace("'", "\\'", $namaKlpd);
                $filterConditions[] = "nama_klpd = '$namaKlpd'";
            }

            // Filter berdasarkan Metode Pemilihan
            if ($request->has('metode_pemilihan') && $request->input('metode_pemilihan') != '') {
                $metodePemilihan = $request->input('metode_pemilihan');
                // Escape quotes in the value
                $metodePemilihan = str_replace("'", "\\'", $metodePemilihan);
                $filterConditions[] = "metode_pemilihan = '$metodePemilihan'";
            }

            // Apply filters if any
            if (!empty($filterConditions)) {
                $filterString = implode(' AND ', $filterConditions);
                \Illuminate\Support\Facades\Log::info('Meilisearch filter: ' . $filterString);

                // Menggunakan callback untuk menerapkan filter langsung ke Meilisearch
                $query = DataSirup::search($search, function ($meilisearch, $query, $options) use ($filterString) {
                    $options['filter'] = $filterString;
                    return $meilisearch->search($query, $options);
                });
            }

            // Apply sorting
            $query->orderBy('tanggal_paket_diumumkan', 'desc');

            $results = $query->paginate($perPage)->appends($request->except('page'));
        } else {
            $query = DataSirup::query();

            // Fitur Filter (misalnya berdasarkan tahun anggaran)
            if ($request->has('tahun_anggaran')) {
                $tahun = $request->input('tahun_anggaran');
                if ($tahun == '') {
                    $query->where('tahun_anggaran', date('Y'));
                } else {
                    $query->where('tahun_anggaran', $tahun);
                }
            } else {
                $query->where('tahun_anggaran', date('Y'));
            }

            // Fitur Filter berdasarkan rentang pagu
            if ($request->has('filter_pagu')) {
                $filterPagu = $request->input('filter_pagu');
                if ($filterPagu == '') {
                } else {
                    if (strpos($filterPagu, '-') !== false) {
                        [$paguMin, $paguMax] = explode('-', $filterPagu);
                        $query->where('total_pagu', '>=', $paguMin);
                        if ($paguMax !== '') {
                            $query->where('total_pagu', '<=', $paguMax);
                        }
                    }
                }
            }

            if ($request->has('pagu')) {
                $pagu = $request->input('pagu');
                $query->where('total_pagu', $pagu);
            }

            // Filter berdasarkan Nama KLPD
            if ($request->has('nama_klpd')) {
                if ($request->input('nama_klpd') == '') {
                } else {
                    $query->where('nama_klpd', $request->input('nama_klpd'));
                }
            }

            // Filter berdasarkan Metode Pemilihan
            if ($request->has('metode_pemilihan')) {
                $metodePemilihan = $request->input('metode_pemilihan');
                if ($metodePemilihan != '') {
                    $query->where('metode_pemilihan', $metodePemilihan);
                }
            }

            $results = $query->orderBy('tanggal_paket_diumumkan', 'desc')->simplePaginate($perPage)->withQueryString();
        }

        // Mengambil daftar tahun anggaran untuk filter dropdown
        $tahun_anggaran = DataSirup::whereNotNull('tahun_anggaran')->where('tahun_anggaran', '!=', '')->distinct()->pluck('tahun_anggaran');
        $nama_klpd = DataSirup::select('nama_klpd')->distinct()->orderBy('nama_klpd')->pluck('nama_klpd');
        $metode_pemilihan = DataSirup::select('metode_pemilihan')->distinct()->orderBy('metode_pemilihan')->pluck('metode_pemilihan');

        return view('tenderid.sirup.index', [
            'data_sirup' => $results,
            'tahun_anggaran' => $tahun_anggaran,
            'nama_klpd' => $nama_klpd,
            'metode_pemilihan' => $metode_pemilihan,
        ]);
    }

    /**
     * Menampilkan detail dari satu SIRUP.
     */
    public function show($id)
    {
        $dataSirup = DataSirup::findOrFail($id);
        $kodeRup = $dataSirup->kode_rup;
        $metode = $dataSirup->metode_pemilihan;

        $status = 'Belum Tayang';
        $tenderData = collect();

        if (in_array($metode, ['Tender', 'Tender Cepat', 'Seleksi', 'Pengadaan Langsung', 'Dikecualikan', 'E-Purchasing', 'Pembayaran untuk Kontrak Tahun Jamak', 'Penunjukan Langsung'])) {
            // Ambil data TenderLpse yang berkaitan dengan Anggaran atau InstansiSatker yang memiliki rup_id sama
            $tenderData = TenderLpse::whereHas('anggaran', function ($query) use ($kodeRup) {
                $query->where('rup_id', $kodeRup);
            })
                ->orWhereHas('instansiSatker', function ($query) use ($kodeRup) {
                    $query->where('rup_id', $kodeRup);
                })->get();

            if ($tenderData->isNotEmpty()) {
                $status = 'Sudah Tayang';
            }
        }

        if ($metode === 'E-Purchasing' and $status == 'Belum Tayang') {
            // Ambil data PaketPengadaan (E-Katalog 5) yang memiliki rup_id sama
            $ekatalog5Data = PaketPengadaan::with([
                'transaksiPaket.produk',
                'penyedia',
                'pelaksana'
            ])
                ->withNilaiKontrak()
                ->where('rup_id', $kodeRup)
                ->whereNotNull('nomor_paket')
                ->get()
                ->map(function ($item) {
                    $item->katalog_version = 'E-Katalog 5';
                    return $item;
                });

            // Ambil data E-Katalog 6
            $ekatalog6Data = Ekatalog6::with(['penyedia', 'klpd'])
                ->where('kode_rup', $kodeRup)
                ->get()
                ->map(function ($item) {
                    $item->katalog_version = 'E-Katalog 6';
                    $item->nomor_paket = $item->kode;
                    $item->instansi_pembeli = $item->klpd->nama_klpd;
                    $item->satuan_kerja = $item->nama_satker;
                    $item->nilai_kontrak = $item->total_pelaksanaan;
                    $item->tanggal_paket = $item->created_at;
                    $item->status_paket = 'paket_selesai';
                    return $item;
                });

            // Gabungkan data dari kedua sumber
            $tenderData = collect();

            if ($ekatalog5Data->isNotEmpty()) {
                $tenderData = $tenderData->concat($ekatalog5Data);
                $status = 'Sudah Klik';
            }

            if ($ekatalog6Data->isNotEmpty()) {
                $tenderData = $tenderData->concat($ekatalog6Data);
                $status = 'Sudah Klik';
            }
        }

        $dataSirup->status_paket = $status;
        $dataSirup->tender_data = $tenderData;

        return view('tenderid.sirup.show', compact('dataSirup'));
    }



    public function getStatusPaket($kode_rup)
    {
        // Log untuk debugging
        \Illuminate\Support\Facades\Log::info('getStatusPaket called with kode_rup: ' . $kode_rup);

        try {
            $sirup = DataSirup::where('kode_rup', $kode_rup)->first();

            if (!$sirup) {
                \Illuminate\Support\Facades\Log::warning('DataSirup not found for kode_rup: ' . $kode_rup);
                return response()->json(['status' => 'Data Tidak Ditemukan'], 200);
            }

            $status = 'Belum Tayang'; // Default value

            if (in_array($sirup->metode_pemilihan, ['Tender', 'Tender Cepat', 'Seleksi', 'Pengadaan Langsung', 'Dikecualikan', 'E-Purchasing', 'Pembayaran untuk Kontrak Tahun Jamak', 'Penunjukan Langsung'])) {
                $status = Anggaran::where('rup_id', $sirup->kode_rup)->exists() ? 'Sudah Tayang' : 'Belum Tayang';
            }

            if ($sirup->metode_pemilihan === 'E-Purchasing' && $status == 'Belum Tayang') {
                // Check in PaketPengadaan (E-Katalog 5)
                $isEkatalog5 = PaketPengadaan::where('rup_id', $sirup->kode_rup)->exists();

                // Check in Ekatalog6
                $isEkatalog6 = Ekatalog6::where('kode_rup', $sirup->kode_rup)->exists();

                if ($isEkatalog5 || $isEkatalog6) {
                    $status = 'Sudah Klik';

                    // Add detail about which e-katalog version
                    $detail = [];
                    if ($isEkatalog5) $detail[] = 'E-Katalog 5';
                    if ($isEkatalog6) $detail[] = 'E-Katalog 6';

                    \Illuminate\Support\Facades\Log::info('Status for kode_rup ' . $kode_rup . ': ' . $status . ' (' . implode(' & ', $detail) . ')');

                    return response()->json([
                        'status' => $status,
                        'sumber' => implode(' & ', $detail)
                    ]);
                } else {
                    $status = 'Belum Klik';
                }
            }

            \Illuminate\Support\Facades\Log::info('Status for kode_rup ' . $kode_rup . ': ' . $status);

            return response()->json(['status' => $status]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in getStatusPaket: ' . $e->getMessage());
            return response()->json(['status' => 'Error: ' . $e->getMessage()], 200);
        }
    }

    public function fixkanPagu($rup_id)
    {
        // Log untuk debugging
        \Illuminate\Support\Facades\Log::info("fixkanPagu called with rup_id: {$rup_id}");

        try {
            // Jalankan command "fixkan:pagu-datasirup" dengan parameter rup_id
            Artisan::call('fixkan:pagu-datasirup', [
                'rup_id' => $rup_id,
            ]);

            // Log output dari command
            $output = Artisan::output();
            \Illuminate\Support\Facades\Log::info("Command output: {$output}");

            // Ambil data Datasirup berdasarkan rup_id
            $data = DataSirup::where('kode_rup', $rup_id)->first();

            // Periksa apakah data ditemukan
            if (!$data) {
                \Illuminate\Support\Facades\Log::warning("DataSirup not found for rup_id: {$rup_id}");
                return response()->json([
                    'message' => 'Data not found for the given rup_id.',
                    'total_pagu' => '0',
                ], 200);
            }

            \Illuminate\Support\Facades\Log::info("Pagu for rup_id {$rup_id}: {$data->total_pagu}");

            // Kembalikan nilai total_pagu
            return response()->json([
                'rup_id' => $rup_id,
                'total_pagu' => format_number_short($data->total_pagu),
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Error in fixkanPagu: {$e->getMessage()}");
            return response()->json([
                'message' => "Error: {$e->getMessage()}",
                'total_pagu' => '0',
            ], 200);
        }
    }




public function cekRUPAktif(Request $request)
{
    // Mengambil rup_id dari request
    $rup_id = $request->input('rup_id');

    // Memeriksa apakah rup_id ada
    if (!$rup_id) {
        return response()->json([
            'message' => 'rup_id parameter is missing.',
        ], 400);
    }

    $datasirup = DataSirup::where('kode_rup', $rup_id)->first();

    if (!$datasirup) {
        return response()->json([
            'message' => 'Data not found for the given rup_id.',
        ], 404);
    }

    // URL API untuk memeriksa keberadaan data SIRUP
    $url = "https://hostestapp.xyz/fetch.php?url=https://sirup.lkpp.go.id/sirup/caripaketctr/search";

    // Parameter pencarian dengan rup_id yang dicari
    $params = [
        'tahunAnggaran' => $datasirup->tahun_anggaran, // Anda bisa menyesuaikan tahun anggaran jika diperlukan
        'jenisPengadaan' => '',
        'metodePengadaan' => '',
        'minPagu' => '',
        'maxPagu' => '',
        'bulan' => '',
        'lokasi' => '',
        'kldi' => '',
        'pdn' => '',
        'ukm' => '',
        'draw' => 1,
        'columns' => [
            ['data' => '', 'name' => '', 'searchable' => false, 'orderable' => false, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'paket', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'pagu', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'jenisPengadaan', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'isPDN', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'isUMK', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'metode', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'pemilihan', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'kldi', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'satuanKerja', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'lokasi', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => '', 'regex' => false]],
            ['data' => 'id', 'name' => '', 'searchable' => true, 'orderable' => true, 'search' => ['value' => $rup_id, 'regex' => false]],
        ],
        'order' => [
            ['column' => 5, 'dir' => 'DESC'],
        ],
        'start' => 0,
        'length' => 10,
        'search' => ['value' => $rup_id, 'regex' => false],
        '_' => time(),
    ];

    // Mengirim permintaan ke API
    $client = new Client();
    try {
        $response = $client->get($url, ['query' => $params]);
        $data = json_decode($response->getBody(), true);

        // Memeriksa apakah data ditemukan
        if (isset($data['data']['data']) && count($data['data']['data']) > 0) {
            // Data ditemukan
            $datasirup->status = 1;
            $datasirup->save();
            return response()->json([
                'rup_id' => $rup_id,
                'status' => 'Aktif',
            ]);
        } else {
            return response()->json([
                'message' => 'Data not found for the given rup_id.',
            ], 404);
        }
    } catch (RequestException | ServerException $e) {
        return response()->json([
            'message' => 'Error checking data from API.',
        ], 500);
    }
}




}