<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use Illuminate\Http\Request;

class SubscriptionPackageController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'check.superadmin']);
    }

    public function index()
    {
        $subscriptions = Subscription::all();
        return view('admin.subscriptions.index', compact('subscriptions'));
    }

    public function create()
    {
        return view('admin.subscriptions.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1',
            'description' => 'nullable|string',
        ]);

        $data = $request->only('name', 'price', 'duration_days', 'description');
        $data['is_active'] = $request->has('is_active');

        Subscription::create($data);

        return redirect()->route('admin.subscriptions.index')->with('success', 'Paket berlangganan berhasil dibuat.');
    }

    public function edit(Subscription $subscription)
    {
        return view('admin.subscriptions.edit', compact('subscription'));
    }

    public function update(Request $request, Subscription $subscription)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1',
            'description' => 'nullable|string',
        ]);

        $data = $request->only('name', 'price', 'duration_days', 'description');
        $data['is_active'] = $request->has('is_active');

        $subscription->update($data);

        return redirect()->route('admin.subscriptions.index')->with('success', 'Paket berlangganan berhasil diperbarui.');
    }

    public function destroy(Subscription $subscription)
    {
        // Cek apakah paket ini memiliki user yang berlangganan
        if ($subscription->userSubscriptions()->count() > 0) {
            return redirect()->route('admin.subscriptions.index')->with('error', 'Paket berlangganan tidak dapat dihapus karena masih digunakan oleh pengguna.');
        }

        $subscription->delete();

        return redirect()->route('admin.subscriptions.index')->with('success', 'Paket berlangganan berhasil dihapus.');
    }

    public function toggleActive(Subscription $subscription)
    {
        $subscription->is_active = !$subscription->is_active;
        $subscription->save();

        $status = $subscription->is_active ? 'diaktifkan' : 'dinonaktifkan';

        return redirect()->route('admin.subscriptions.index')->with('success', "Paket berlangganan berhasil {$status}.");
    }
}
