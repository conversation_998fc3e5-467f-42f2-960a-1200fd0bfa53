<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SyncLog;

class SyncLogController extends Controller
{
    public function index(Request $request)
    {
        // Dapatkan parameter filter, pencarian, dan sorting
        $search = $request->input('search');
        $sortBy = $request->input('sort_by', 'synced_at'); // Default sorting by 'id'
        $sortDirection = $request->input('sort_direction', 'desc'); // Default ascending
        $filters = $request->only(['action', 'table']); // Filter berdasarkan kolom tertentu

        // Query data SyncLog dengan filter, pencarian, dan sorting
        $syncLogs = SyncLog::query()
            ->when($search, function ($query, $search) {
                $query->where('record_id', 'like', "%$search%")
                    ->orWhere('action', 'like', "%$search%")
                    ->orWhere('changes', 'like', "%$search%")
                    ->orWhere('table', 'like', "%$search%");
            })
            ->when($filters, function ($query, $filters) {
                foreach ($filters as $key => $value) {
                    $query->where($key, $value);
                }
            })
            ->orderBy($sortBy, $sortDirection)
            ->paginate(25)
            ->appends(request()->query());; // Pagination, 10 data per halaman

        // Ambil nilai distinct untuk dropdown filter
        $actions = SyncLog::select('action')->distinct()->pluck('action');
        $tables = SyncLog::select('table')->distinct()->pluck('table');

        return view('tenderid.sync_logs.index', compact('syncLogs', 'actions', 'tables'));
    }

}
