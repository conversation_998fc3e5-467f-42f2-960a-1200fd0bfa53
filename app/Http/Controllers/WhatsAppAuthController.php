<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Laravel\Facades\Image;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Imagick\Driver;


class WhatsAppAuthController extends Controller
{

    public function saveQrCode(Request $request)
{
    // Log QR Code yang diterima
    \Log::info('QR Code diterima:', ['qr' => $request->qr]);

    // Ambil data Base64 QR Code
    $base64Image = $request->qr;

    // Menghapus prefix data URI jika ada
    $imageData = substr($base64Image, strpos($base64Image, ',') + 1);

    // Decode Base64 menjadi data gambar
    $image = base64_decode($imageData);

    // Tentukan nama file gambar
    $fileName = 'WA-QRCODE.png';

    // Gunakan ImageManager untuk membaca gambar dari data binary
    $manager = new ImageManager(new Driver());
    $img = $manager->read($image); // Membaca data gambar

    // Manipulasi gambar jika diperlukan
    $img->resize(300, 300); // Resize gambar jika diperlukan

    // Simpan gambar ke storage/public
    $img->save(storage_path('app/public/' . $fileName));

    // Log sesi saat menyimpan
    \Log::info('QR Code berhasil disimpan sebagai gambar:', ['file' => $fileName]);

    return response()->json(['message' => 'QR Code berhasil disimpan sebagai gambar']);
}

    public function showQrCode2()
    {
        // Tentukan nama file gambar QR Code
        $qrCodeFilePath = 'storage/WA-QRCODE.png';

        // Cek apakah file QR Code ada
        if (!Storage::disk('public')->exists('WA-QRCODE.png')) {
            return response()->json(['message' => 'QR Code tidak ditemukan'], 404);
        }

        // URL untuk menampilkan gambar QR Code
        $qrCodeUrl = asset('storage/WA-QRCODE.png');

        return view('tenderid.whatsapp.qr', compact('qrCodeUrl'));
    }


    public function showQrCode()
    {
        try {
            // Gunakan HTTP client untuk mengambil QR code dari API
            $client = new Client();
            $response = $client->get(route('whatsapp.qr.api'));
            $data = json_decode($response->getBody(), true);

            // Log response untuk debugging
            \Log::info('QR Code API response:', ['data' => isset($data['qrCode']) ? 'QR code exists (base64 data)' : $data]);

            // Jika QR code berhasil didapatkan
            if (isset($data['qrCode']) && !empty($data['qrCode'])) {
                // Pastikan QR code adalah data base64 yang valid
                if (strpos($data['qrCode'], 'data:image/png;base64,') === 0) {
                    // Tampilkan QR code
                    return view('tenderid.whatsapp.qr', ['qrCode' => $data['qrCode']]);
                } else {
                    \Log::warning('QR code format tidak valid', [
                        'prefix' => substr($data['qrCode'], 0, 30)
                    ]);

                    // Coba tambahkan prefix jika belum ada
                    $qrCode = 'data:image/png;base64,' . $data['qrCode'];
                    return view('tenderid.whatsapp.qr', ['qrCode' => $qrCode]);
                }
            } else {
                // Jika tidak ada QR code, coba metode alternatif
                \Log::info('QR code tidak ditemukan, mencoba metode alternatif');
                return $this->showQrCode2();
            }
        } catch (\Exception $e) {
            \Log::error('Error getting QR code:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Jika terjadi error, coba metode alternatif
            try {
                \Log::info('Mencoba metode alternatif setelah error');
                return $this->showQrCode2();
            } catch (\Exception $e2) {
                \Log::error('Metode alternatif juga gagal:', [
                    'error' => $e2->getMessage()
                ]);
                return view('tenderid.whatsapp.qr', ['error' => 'Gagal mengambil QR code: ' . $e2->getMessage()]);
            }
        }
    }

    public function checkStatus()
    {
        $client = new Client();

        try {
            // Cek status login bot di Node.js
            $response = $client->get('https://hostestapp.xyz:3000/check-login');
            return response()->json(json_decode($response->getBody(), true));
        } catch (\Exception $e) {
            return response()->json(['isLoggedIn' => false]);
        }
    }

    public function success()
    {
        return view('tenderid.whatsapp.status');
    }
}
