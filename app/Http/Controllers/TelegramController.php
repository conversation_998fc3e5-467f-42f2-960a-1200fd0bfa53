<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\TenderLpse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class TelegramController extends Controller
{
    public function handleWebhook(Request $request)
    {
        try {
            $data = $request->all();

            if (!isset($data['message']['text'])) {
                Log::channel('telegram')->info('No message text received');
                return response()->json(['status' => 'success']);
            }

            $chatId = $data['message']['chat']['id'];
            $text = $data['message']['text'];

            if (strpos($text, '/start') === 0) {
                return $this->handleStartCommand($chatId, $text);
            } elseif (strpos($text, '/akun') === 0) {
                return $this->handleAkunCommand($chatId);
            } elseif (strpos($text, '/tender') === 0) {
                return $this->handleTenderCommand($chatId, $text);
            } else {
                $this->sendTelegramMessage($chatId, "Maaf, perintah tidak dikenal. Coba gunakan /akun atau /tender <id>.");
            }

            return response()->json(['status' => 'success']);

        } catch (\Exception $e) {
            Log::channel('telegram')->error('Error processing webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    private function handleStartCommand($chatId, $text)
    {
        $cekUser = User::where('telegram_bot', $chatId)->first();

        if ($cekUser) {
            $this->sendTelegramMessage($chatId, "Akun Anda sudah terhubung. Gunakan /akun untuk melihat informasi akun Anda.");
            return;
        }

        $email = trim(substr($text, 6)); // Ambil email setelah "/start "

        if (empty($email)) {
            $this->sendTelegramMessage($chatId, "Selamat datang di TenderID! Gunakan perintah /start diikuti email Anda untuk menghubungkan akun.");
            return;
        }

        $user = User::where('email', $email)->first();

        if ($user) {
            $user->telegram_bot = $chatId; // Sesuaikan nama field
            $user->save();

            $this->sendTelegramMessage($chatId, "Akun Anda berhasil terhubung! Anda akan menerima notifikasi tender melalui Telegram.");
        } else {
            $this->sendTelegramMessage($chatId, "Maaf, akun dengan email {$email} tidak ditemukan.");
        }
    }

    private function handleAkunCommand($chatId)
    {
        $user = User::where('telegram_bot', $chatId)->first();

        if ($user) {
            $message = "📋 Informasi Akun Anda:\n";
            $message .= "👤 Nama: {$user->name}\n";
            $message .= "📧 Email: {$user->email}\n";
            $message .= "🗓️ Terdaftar Sejak: {$user->created_at->format('d M Y')}\n";

            $this->sendTelegramMessage($chatId, $message);
        } else {
            $this->sendTelegramMessage($chatId, "Akun Anda belum terhubung. Gunakan /start <email> untuk menghubungkan akun.");
        }
    }

    private function handleTenderCommand($chatId, $text)
    {
        $tenderId = trim(substr($text, 8)); // Ambil ID tender setelah "/tender "

        if (empty($tenderId)) {
            $this->sendTelegramMessage($chatId, "Harap berikan Kode tender. Contoh: /tender 394793");
            return;
        }

        $tender = TenderLpse::find($tenderId);

        if ($tender) {
            $message = "📋 Informasi Tender:\n";
            $message .= "🆔 Kode Tender: {$tender->kode_tender}\n";
            $message .= "📌 Nama Tender: {$tender->nama_paket}\n";
            $message .= "💰 Nilai Pagu: Rp" . convert_to_rupiah($tender->pagu) . "\n";
            $message .= "💰 Nilai HPS: Rp" . convert_to_rupiah($tender->hps) . "\n";
            $message .= "📅 Tahap Saat Ini: {$tender->tahap_tender}\n";
            $message .= "🌐 Detail: [Lihat Tender](https://tenderid.hostestapp.xyz/tender/{$tender->kode_tender})";

            $this->sendTelegramMessage($chatId, $message);
        } else {
            $this->sendTelegramMessage($chatId, "Maaf, tender dengan Kode {$tenderId} tidak ditemukan.");
        }
    }

    private function sendTelegramMessage($chatId, $text)
    {
        try {
            $token = config('services.telegram-bot-api.token');
            $response = Http::post("https://api.telegram.org/bot{$token}/sendMessage", [
                'chat_id' => $chatId,
                'text' => $text,
            ]);

            Log::channel('telegram')->info('Message sent', [
                'chat_id' => $chatId,
                'text' => $text,
                'response' => $response->json()
            ]);

            return $response->json();
        } catch (\Exception $e) {
            Log::channel('telegram')->error('Error sending message', [
                'error' => $e->getMessage(),
                'chat_id' => $chatId,
                'text' => $text
            ]);
            throw $e;
        }
    }

    public function requestTelegramLink(Request $request)
    {
        try {
            $user = auth()->user();
            $botUsername = config('services.telegram-bot-api.username');

            // Ensure the bot username is set
            if (empty($botUsername)) {
                Log::channel('telegram')->error('Telegram bot username not configured');
                return back()->with('error', 'Telegram bot is not properly configured. Please contact the administrator.');
            }

            // Create a clean URL-safe version of the email
            $emailParam = urlencode($user->email);

            // Create the Telegram deep link
            $linkUrl = "https://t.me/{$botUsername}?start={$emailParam}";

            Log::channel('telegram')->info('Telegram link requested', [
                'user_id' => $user->id,
                'email' => $user->email,
                'link_url' => $linkUrl,
                'bot_username' => $botUsername
            ]);

            return view('tenderid.telegram.link', compact('linkUrl'));
        } catch (\Exception $e) {
            Log::channel('telegram')->error('Error generating link', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id()
            ]);

            return back()->with('error', 'Unable to generate Telegram link. Please try again later.');
        }
    }

    /**
     * Disconnect Telegram account from user account
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function disconnectTelegram()
    {
        try {
            $user = auth()->user();

            if ($user->telegram_bot) {
                // Store the old chat ID for logging
                $oldChatId = $user->telegram_bot;

                // Remove the Telegram chat ID
                $user->telegram_bot = null;
                $user->save();

                // Log the disconnection
                Log::channel('telegram')->info('Telegram account disconnected', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'old_chat_id' => $oldChatId
                ]);

                // Send a notification to the user's Telegram
                try {
                    $this->sendTelegramMessage($oldChatId, "Akun Telegram Anda telah diputuskan dari aplikasi TenderID. Anda tidak akan lagi menerima notifikasi tender melalui Telegram.");
                } catch (\Exception $e) {
                    // Just log the error, don't stop the process
                    Log::channel('telegram')->error('Error sending disconnection message', [
                        'error' => $e->getMessage(),
                        'user_id' => $user->id,
                        'chat_id' => $oldChatId
                    ]);
                }

                return redirect()->route('telegram.link')->with('success', 'Akun Telegram Anda berhasil diputuskan.');
            }

            return redirect()->route('telegram.link')->with('info', 'Tidak ada akun Telegram yang tertaut.');

        } catch (\Exception $e) {
            Log::channel('telegram')->error('Error disconnecting Telegram', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return back()->with('error', 'Gagal memutuskan koneksi Telegram. Silakan coba lagi nanti.');
        }
    }
}