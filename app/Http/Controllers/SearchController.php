<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PaketPengadaan;
use App\Models\DataSirup;
use App\Models\TenderLpse;
use App\Models\Ekatalog6;

class SearchController extends Controller
{
    public function searchPaketPengadaan(Request $request)
    {
        $query = $request->input('q');
        $results = PaketPengadaan::search($query)->get();
        return response()->json($results);
    }

    public function searchDataSirup(Request $request)
    {
        $query = $request->input('q');
        $results = DataSirup::search($query)->get();
        return response()->json($results);
    }

    public function searchTenderLpse(Request $request)
    {
        $query = $request->input('q');
        $results = TenderLpse::search($query)->get();
        return response()->json($results);
    }

    public function searchEkatalog6(Request $request)
    {
        $query = $request->input('q');
        $results = Ekatalog6::search($query)->get();
        return response()->json($results);
    }
}
