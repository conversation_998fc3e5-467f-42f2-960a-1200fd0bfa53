<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class EnvConfigController extends Controller
{
    /**
     * Display the environment configuration form.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Baca file .env
        $envContent = File::get(base_path('.env'));

        // Parse file .env menjadi array
        $envVars = $this->parseEnvFile($envContent);

        // Kelompokkan variabel berdasarkan kategori
        $categories = $this->categorizeEnvVars($envVars);

        return view('admin.env-config.index', [
            'categories' => $categories,
            'envVars' => $envVars
        ]);
    }

    /**
     * Update the environment configuration.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        // Validasi input
        $validator = Validator::make($request->all(), [
            'env_vars' => 'required|array',
            'env_vars.*' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Baca file .env
            $envContent = File::get(base_path('.env'));

            // Parse file .env menjadi array
            $envVars = $this->parseEnvFile($envContent);

            // Update nilai variabel
            $updatedVars = $request->input('env_vars');
            foreach ($updatedVars as $key => $value) {
                if (isset($envVars[$key])) {
                    $envVars[$key] = $value;
                }
            }

            // Tulis kembali file .env
            $this->writeEnvFile($envVars);

            // Clear cache
            Artisan::call('config:clear');
            Artisan::call('cache:clear');

            return redirect()->route('env.config.index')
                ->with('success', 'Konfigurasi berhasil diperbarui. Beberapa perubahan mungkin memerlukan restart server untuk diterapkan.');
        } catch (\Exception $e) {
            Log::error('Error updating .env file: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan saat memperbarui file .env: ' . $e->getMessage());
        }
    }

    /**
     * Parse file .env menjadi array.
     *
     * @param  string  $content
     * @return array
     */
    private function parseEnvFile($content)
    {
        $envVars = [];
        $lines = preg_split('/\r\n|\r|\n/', $content);

        foreach ($lines as $line) {
            // Skip komentar atau baris kosong
            if (empty($line) || strpos($line, '#') === 0) {
                continue;
            }

            // Parse key dan value
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);

                // Hapus quotes jika ada
                if (strpos($value, '"') === 0 && strrpos($value, '"') === strlen($value) - 1) {
                    $value = substr($value, 1, -1);
                }

                $envVars[$key] = $value;
            }
        }

        return $envVars;
    }

    /**
     * Tulis array ke file .env.
     *
     * @param  array  $envVars
     * @return void
     */
    private function writeEnvFile($envVars)
    {
        // Buat backup file .env
        $backupPath = base_path('.env.backup-' . date('Y-m-d-H-i-s'));
        File::copy(base_path('.env'), $backupPath);

        // Buat konten file .env baru
        $content = '';
        foreach ($envVars as $key => $value) {
            // Tambahkan quotes jika value mengandung spasi
            if (strpos($value, ' ') !== false) {
                $value = '"' . $value . '"';
            }

            $content .= "{$key}={$value}" . PHP_EOL;
        }

        // Tulis ke file .env
        File::put(base_path('.env'), $content);
    }

    /**
     * Kelompokkan variabel berdasarkan kategori.
     *
     * @param  array  $envVars
     * @return array
     */
    private function categorizeEnvVars($envVars)
    {
        $categories = [
            'App Settings' => [],
            'Database' => [],
            'Mail' => [],
            'Queue' => [],
            'Services' => [],
            'WhatsApp' => [],
            'Other' => []
        ];

        foreach ($envVars as $key => $value) {
            if (strpos($key, 'APP_') === 0) {
                $categories['App Settings'][$key] = $value;
            } elseif (strpos($key, 'DB_') === 0) {
                $categories['Database'][$key] = $value;
            } elseif (strpos($key, 'MAIL_') === 0) {
                $categories['Mail'][$key] = $value;
            } elseif (strpos($key, 'QUEUE_') === 0 || strpos($key, 'REDIS_') === 0) {
                $categories['Queue'][$key] = $value;
            } elseif (strpos($key, 'WHATSAPP_') === 0) {
                $categories['WhatsApp'][$key] = $value;
            } elseif (strpos($key, 'AWS_') === 0 || strpos($key, 'PUSHER_') === 0 || strpos($key, 'TELEGRAM_') === 0) {
                $categories['Services'][$key] = $value;
            } else {
                $categories['Other'][$key] = $value;
            }
        }

        return $categories;
    }
}
