<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PerformanceMonitor
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        // Track database queries
        $queryCount = 0;
        $queryTime = 0;
        
        \DB::listen(function ($query) use (&$queryCount, &$queryTime) {
            $queryCount++;
            $queryTime += $query->time;
        });

        $response = $next($request);

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        $memoryUsage = $endMemory - $startMemory;
        $peakMemory = memory_get_peak_usage(true);

        // Log slow requests (>2 seconds)
        if ($executionTime > 2000) {
            $this->logSlowRequest($request, $response, [
                'execution_time_ms' => round($executionTime, 2),
                'memory_usage_bytes' => $memoryUsage,
                'peak_memory_bytes' => $peakMemory,
                'query_count' => $queryCount,
                'query_time_ms' => round($queryTime, 2),
            ]);
        }

        // Log high memory usage (>50MB)
        if ($memoryUsage > 50 * 1024 * 1024) {
            $this->logHighMemoryUsage($request, [
                'memory_usage_bytes' => $memoryUsage,
                'peak_memory_bytes' => $peakMemory,
                'execution_time_ms' => round($executionTime, 2),
            ]);
        }

        // Log requests with many queries (>50)
        if ($queryCount > 50) {
            $this->logHighQueryCount($request, [
                'query_count' => $queryCount,
                'query_time_ms' => round($queryTime, 2),
                'execution_time_ms' => round($executionTime, 2),
            ]);
        }

        // Add performance headers for debugging (only in debug mode)
        if (config('app.debug')) {
            $response->headers->set('X-Execution-Time', round($executionTime, 2) . 'ms');
            $response->headers->set('X-Memory-Usage', $this->formatBytes($memoryUsage));
            $response->headers->set('X-Peak-Memory', $this->formatBytes($peakMemory));
            $response->headers->set('X-Query-Count', $queryCount);
            $response->headers->set('X-Query-Time', round($queryTime, 2) . 'ms');
        }

        return $response;
    }

    private function logSlowRequest(Request $request, Response $response, array $metrics): void
    {
        $context = [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_id' => auth()->id(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'status_code' => $response->getStatusCode(),
            'execution_time_ms' => $metrics['execution_time_ms'],
            'memory_usage' => $this->formatBytes($metrics['memory_usage_bytes']),
            'peak_memory' => $this->formatBytes($metrics['peak_memory_bytes']),
            'query_count' => $metrics['query_count'],
            'query_time_ms' => $metrics['query_time_ms'],
            'timestamp' => now()->toISOString(),
        ];

        Log::channel('performance')->warning('Slow Request Detected', $context);
    }

    private function logHighMemoryUsage(Request $request, array $metrics): void
    {
        $context = [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_id' => auth()->id(),
            'memory_usage' => $this->formatBytes($metrics['memory_usage_bytes']),
            'peak_memory' => $this->formatBytes($metrics['peak_memory_bytes']),
            'execution_time_ms' => $metrics['execution_time_ms'],
            'timestamp' => now()->toISOString(),
        ];

        Log::channel('performance')->warning('High Memory Usage Detected', $context);
    }

    private function logHighQueryCount(Request $request, array $metrics): void
    {
        $context = [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_id' => auth()->id(),
            'query_count' => $metrics['query_count'],
            'query_time_ms' => $metrics['query_time_ms'],
            'execution_time_ms' => $metrics['execution_time_ms'],
            'timestamp' => now()->toISOString(),
        ];

        Log::channel('performance')->warning('High Query Count Detected', $context);
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
