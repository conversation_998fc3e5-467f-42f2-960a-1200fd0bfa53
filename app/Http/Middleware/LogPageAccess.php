<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use App\Models\PageAccessLog;
use App\Helpers\DeviceDetector;

class LogPageAccess
{
    // Daftar nama route yang dikecualikan
    protected $exceptRoutes = [
        'sirup.status',
        'sirup.pagu',
        'dashboard.chartTender',
        'dashboard.chartEkatalog',
        'dashboard.chartKategoriPekerjaan',
        'dashboard.chartEkatStatusUMKM',
        'dashboard.topTender',
        'dashboard.topPaketPengadaan',
        'dashboard.topEkatalogByPeriod',
        'dashboard.topTendersByPeriod',
        'dashboard.topEkatalog6ByPeriod',
    ];

    public function handle($request, Closure $next)
    {
        // Cek apakah route saat ini ada di daftar pengecualian
        if (!in_array($request->route()->getName(), $this->exceptRoutes)) {
            if (Auth::check()) {
                // Menangkap waktu mulai untuk mengukur waktu muat
                $startTime = microtime(true);

                // Menggunakan DeviceDetector untuk mendapatkan informasi detail
                $detector = new DeviceDetector($request);
                $deviceInfo = $detector->detect();

                $userAgent = $request->header('User-Agent') ?? 'Tidak diketahui';

                // Informasi browser
                $browser = $deviceInfo['browser']['name'];
                $browserVersion = $deviceInfo['browser']['version'];

                // Informasi sistem operasi
                $os = $deviceInfo['os']['name'];
                $osVersion = $deviceInfo['os']['version'];

                // Informasi perangkat
                $deviceType = $deviceInfo['device']['type'];
                $deviceBrand = $deviceInfo['device']['brand'];
                $deviceModel = $deviceInfo['device']['model'];

                // Informasi lokasi
                $location = $deviceInfo['location'];

                // Informasi tambahan
                $additionalInfo = [
                    'isp' => $location['isp'] ?? null,
                    'timezone' => $location['timezone'] ?? null,
                    'referrer' => $request->header('referer'),
                    'route_name' => $request->route()->getName(),
                    'user_agent_full' => $userAgent,
                ];

                // Memproses permintaan
                $response = $next($request);

                // Menghitung waktu muat
                $loadTime = round((microtime(true) - $startTime) * 1000);

                // Menyimpan log akses
                // Tambahkan informasi header Cloudflare ke additional_info
                $additionalInfo['cf_ray'] = $request->header('CF-RAY');
                $additionalInfo['cf_connecting_ip'] = $request->header('CF-Connecting-IP');
                $additionalInfo['cf_ipcountry'] = $request->header('CF-IPCountry');
                $additionalInfo['cf_visitor'] = $request->header('CF-Visitor');
                $additionalInfo['cdn_loop'] = $request->header('CDN-Loop');

                // Jika ini adalah request logout, user_id mungkin sudah null
                // Simpan user_id sebelum logout jika tersedia
                $userId = Auth::id();

                // Jika ini adalah request logout dan user_id null, coba ambil dari session
                if ($userId === null && $request->is('logout')) {
                    // Coba ambil user_id dari session jika tersedia
                    $userId = session('logged_user_id');

                    // Jika masih null, gunakan ID default (1 untuk admin)
                    if ($userId === null) {
                        $userId = 1; // ID default untuk admin atau sistem
                    }
                }

                PageAccessLog::create([
                    'user_id' => $userId,
                    'url' => $request->fullUrl(),
                    'method' => $request->method(),
                    'ip_address' => $detector->ipAddress, // Gunakan IP yang sudah dideteksi oleh DeviceDetector
                    'user_agent' => $userAgent,
                    'browser' => $browser,
                    'browser_version' => $browserVersion,
                    'os' => $os,
                    'os_version' => $osVersion,
                    'device_type' => $deviceType,
                    'device_brand' => $deviceBrand,
                    'device_model' => $deviceModel,
                    'country' => $location['country'],
                    'region' => $location['region'],
                    'city' => $location['city'],
                    'latitude' => $location['latitude'],
                    'longitude' => $location['longitude'],
                    'additional_info' => json_encode($additionalInfo),
                    'load_time_ms' => $loadTime,
                ]);

                return $response;
            }
        }

        return $next($request);
    }
}