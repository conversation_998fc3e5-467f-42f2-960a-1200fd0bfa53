<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckActiveSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();

        if (!$user) {
            // If no authenticated user, proceed (or redirect to login)
            return $next($request);
        }

        // Check if user has an active subscription
        $activeSubscription = $user->userSubscriptions()
            ->where('is_active', true)
            ->where('status', 'active')
            ->first();

        if (!$activeSubscription) {
            // Redirect to subscription selection page if no active subscription
            return redirect()->route('subscriptions.select');
        }

        return $next($request);
    }
}
