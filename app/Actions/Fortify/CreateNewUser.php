<?php

namespace App\Actions\Fortify;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>\Fortify\Contracts\CreatesNewUsers;

class CreateNewUser implements CreatesNewUsers
{
    use PasswordValidationRules;

    /**
     * Create a new user instance.
     *
     * @param  array  $input
     * @return \App\Models\User
     */
    public function create(array $input)
    {
        Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => $this->passwordRules(),
            'account_type' => ['required', 'string'],
            'company_name' => ['nullable', 'string', 'max:255'],
            'npwp' => ['nullable', 'string', 'max:255'],
            'referral_code' => ['nullable', 'string', 'max:255'],
            'phone' => ['required', 'regex:/^0[0-9]+$/', 'max:20', 'unique:users,phone'],
        ], [
            'name.required' => 'Nama lengkap wajib diisi.',
            'name.max' => 'Nama lengkap maksimal 255 karakter.',
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.max' => 'Email maksimal 255 karakter.',
            'email.unique' => 'Email sudah terdaftar.',
            'password.confirmed' => 'Konfirmasi password tidak cocok.',
            'account_type.required' => 'Tipe akun wajib dipilih.',
            'company_name.max' => 'Nama perusahaan maksimal 255 karakter.',
            'npwp.max' => 'NPWP maksimal 255 karakter.',
            'phone.required' => 'Nomor telepon wajib diisi.',
            'phone.regex' => 'Format nomor telepon tidak valid. Harus dimulai dengan angka 0.',
            'phone.max' => 'Nomor telepon maksimal 20 karakter.',
            'phone.unique' => 'Nomor telepon sudah terdaftar.',
        ])->validate();

        // Create the new user instance
        $user = User::create([
            'name' => $input['name'],
            'email' => $input['email'],
            'password' => Hash::make($input['password']),
            'account_type' => $input['account_type'],
            'npwp' => $input['npwp'] ?? null,
            'company_name' => $input['company_name'] ?? null,
            'phone' => $input['phone'] ?? null,
            'photo' => isset($input['photo']) ? $input['photo']->store('photos', 'public') : null,
            'referral_code' => $this->generateUniqueReferralCode(),
        ]);

        // Handle referral code
        $referralCode = !empty($input['referral_code']) ? $input['referral_code'] : 'TENDERID';
        $referrer = \App\Models\User::where('referral_code', $referralCode)->first();

        if ($referrer) {
            \App\Models\Referral::create([
                'referrer_id' => $referrer->id,
                'referred_id' => $user->id,
            ]);
        }


        return $user;
    }

    /**
     * Generate a unique referral code for the user.
     *
     * @return string
     */
    protected function generateUniqueReferralCode()
    {
        do {
            $code = strtoupper(substr(bin2hex(random_bytes(3)), 0, 6));
        } while (\App\Models\User::where('referral_code', $code)->exists());

        return $code;
    }
}
