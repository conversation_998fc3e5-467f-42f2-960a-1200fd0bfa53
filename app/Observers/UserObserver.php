<?php

namespace App\Observers;

use App\Models\User;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class UserObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function created(User $user)
    {
        Log::info('User created, sending WhatsApp welcome message via command', [
            'user_id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone ?? 'not set'
        ]);

        if (!$user->phone) {
            Log::warning('User has no phone number, skipping WhatsApp welcome message', [
                'user_id' => $user->id
            ]);
            return;
        }

        // Jalankan command untuk mengirim pesan WhatsApp selamat datang
        try {
            Artisan::call('whatsapp:send-welcome', [
                'user_id' => $user->id
            ]);

            Log::info('WhatsApp welcome message command executed', [
                'user_id' => $user->id
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to execute WhatsApp welcome message command', [
                'user_id' => $user->id,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
