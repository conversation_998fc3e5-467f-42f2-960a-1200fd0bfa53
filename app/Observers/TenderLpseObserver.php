<?php

namespace App\Observers;

use App\Models\TenderLpse;
use App\Jobs\SendTenderLpseNotifications;

class TenderLpseObserver
{
    /**
     * Handle the TenderLpse "updated" event.
     *
     * @param  \App\Models\TenderLpse  $tender
     * @return void
     */
    public function updated(TenderLpse $tender)
    {
        // Cek jika ada perubahan pada status_tender atau tahap_tender
        if ($tender->isDirty(['status_tender', 'tahap_tender'])) {
            // Dispatch job untuk mengirimkan notifikasi
            SendTenderLpseNotifications::dispatch($tender)
                ->onQueue('notifications') // Menetapkan queue khusus
                ->delay(now()->addSeconds(5)); // Opsional: Tambahkan delay untuk meringankan beban sistem
        }
    }
}
