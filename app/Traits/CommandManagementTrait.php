<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\CommandProgress;
use App\Models\SystemError;

trait CommandManagementTrait
{
    protected $commandStartTime;
    protected $commandTimeout = 3600; // Default timeout: 1 hour
    protected $hourlyCommandTimeout = 3480; // 58 minutes for hourly commands
    protected $forceRun = false;
    protected $progressTotal = 100;
    protected $progressCurrent = 0;
    protected $commandLockFile;
    protected $isHourlyCommand = false;

    /**
     * Initialize command management
     *
     * @param int $timeout Timeout in seconds
     * @param bool $forceRun Force run even if command is already running
     * @return bool True if command can run, false otherwise
     */
    protected function initCommandManagement(int $timeout = 3600, bool $forceRun = false): bool
    {
        $this->commandStartTime = microtime(true);

        // Deteksi apakah ini adalah command hourly berdasarkan nama atau opsi
        $commandName = $this->getName();
        $this->isHourlyCommand = $this->detectHourlyCommand($commandName);

        // Jika command hourly, gunakan timeout 58 menit kecuali jika timeout yang lebih pendek diberikan
        if ($this->isHourlyCommand && $timeout >= 3600) {
            $this->commandTimeout = $this->hourlyCommandTimeout;
            $this->info("Hourly command detected. Setting timeout to 58 minutes.");
        } else {
            $this->commandTimeout = $timeout;
        }

        $this->forceRun = $forceRun;

        // Register shutdown function for cleanup
        register_shutdown_function([$this, 'commandCleanup']);

        // Register signal handlers if pcntl extension is available
        if (extension_loaded('pcntl')) {
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
            pcntl_signal(SIGINT, [$this, 'handleSignal']);
            pcntl_signal(SIGQUIT, [$this, 'handleSignal']);
            pcntl_signal(SIGALRM, [$this, 'handleSignal']);

            // Set alarm for timeout
            pcntl_alarm($this->commandTimeout);
        }

        // Create lock file
        $commandName = $this->getName();

        // Gunakan custom lock name jika disediakan
        $lockName = $this->option('lock-name') ?? $commandName;
        $this->commandLockFile = storage_path("app/locks/{$lockName}.lock");

        // Create locks directory if it doesn't exist
        if (!file_exists(dirname($this->commandLockFile))) {
            mkdir(dirname($this->commandLockFile), 0755, true);
        }

        // Check if command is already running
        if (!$this->forceRun && $this->isCommandRunning()) {
            $this->error("Command '{$commandName}' is already running.");

            // If force run is enabled, kill the existing process
            if ($this->forceRun) {
                $this->killExistingProcess();
                $this->warn("Killed existing process for command '{$commandName}' because --force option was used.");
            } else {
                return false;
            }
        }

        // Mark command as running
        $this->markCommandAsRunning();

        // Initialize CommandProgress
        CommandProgress::updateProgress(
            $commandName,
            0,
            $this->progressTotal,
            "Starting {$commandName}...",
            [
                'started_at' => now()->toDateTimeString(),
                'memory_usage' => $this->getMemoryUsage(),
                'arguments' => $this->arguments(),
                'options' => $this->options()
            ]
        );

        return true;
    }

    /**
     * Handle signals
     *
     * @param int $signal Signal number
     * @return void
     */
    public function handleSignal(int $signal): void
    {
        $commandName = $this->getName();

        switch ($signal) {
            case SIGTERM:
            case SIGINT:
            case SIGQUIT:
                $this->error("Command '{$commandName}' received termination signal.");
                $this->commandCleanup();
                exit(1);
            case SIGALRM:
                $this->error("Command '{$commandName}' timed out after {$this->commandTimeout} seconds.");
                $this->logSystemError(
                    new \Exception("Command timed out after {$this->commandTimeout} seconds."),
                    "Command timeout"
                );
                CommandProgress::markAsFailed(
                    $commandName,
                    "Command timed out after {$this->commandTimeout} seconds.",
                    [
                        'timeout' => $this->commandTimeout,
                        'memory_usage' => $this->getMemoryUsage(),
                        'peak_memory_usage' => $this->getPeakMemoryUsage()
                    ]
                );
                $this->commandCleanup();
                exit(1);
        }
    }

    /**
     * Clean up command resources
     *
     * @return void
     */
    public function commandCleanup(): void
    {
        $commandName = $this->getName();

        // Mark command as not running in database
        if ($this->isCommandRunning()) {
            $this->markCommandAsNotRunning();
            $this->info("Command '{$commandName}' marked as not running.");
        }

        // Remove lock file
        if (file_exists($this->commandLockFile)) {
            unlink($this->commandLockFile);
            $this->info("Lock file for command '{$commandName}' removed.");
        }
    }

    /**
     * Check if command is running
     *
     * @return bool
     */
    protected function isCommandRunning(): bool
    {
        $commandName = $this->getName();

        // Check lock file
        if (file_exists($this->commandLockFile)) {
            $lockData = json_decode(file_get_contents($this->commandLockFile), true);

            // Check if process is still running
            if (isset($lockData['pid'])) {
                $pid = $lockData['pid'];

                // Check if process exists
                if (file_exists("/proc/{$pid}")) {
                    return true;
                }
            }

            // Lock file exists but process is not running, remove lock file
            unlink($this->commandLockFile);
        }

        // Gunakan custom lock name jika disediakan untuk pengecekan database
        $lockName = $this->option('lock-name') ?? $commandName;

        // Check database
        return DB::table('commands_status')
            ->where('command_name', $lockName)
            ->where('is_running', true)
            ->exists();
    }

    /**
     * Kill existing process for this command
     *
     * @return void
     */
    protected function killExistingProcess(): void
    {
        $commandName = $this->getName();

        // Check lock file
        if (file_exists($this->commandLockFile)) {
            $lockData = json_decode(file_get_contents($this->commandLockFile), true);

            // Check if process is still running
            if (isset($lockData['pid'])) {
                $pid = $lockData['pid'];

                // Check if process exists and kill it
                if (file_exists("/proc/{$pid}")) {
                    // Gunakan shell_exec untuk kill process karena posix_kill mungkin tidak tersedia
                    shell_exec("kill -9 {$pid}");
                    $this->info("Killed process {$pid} for command '{$commandName}'");

                    // Wait a moment to ensure process is killed
                    sleep(1);
                }
            }

            // Remove lock file
            unlink($this->commandLockFile);
        }

        // Gunakan custom lock name jika disediakan untuk database
        $lockName = $this->option('lock-name') ?? $commandName;

        // Update database
        DB::table('commands_status')
            ->where('command_name', $lockName)
            ->where('is_running', true)
            ->update(['is_running' => false, 'finished_at' => now()]);
    }

    /**
     * Mark command as running
     *
     * @return void
     */
    protected function markCommandAsRunning(): void
    {
        $commandName = $this->getName();

        // Create lock file
        $lockData = [
            'pid' => getmypid(),
            'started_at' => now()->toDateTimeString(),
            'command' => $commandName,
            'arguments' => $this->arguments(),
            'options' => $this->options()
        ];

        file_put_contents($this->commandLockFile, json_encode($lockData));

        // Gunakan custom lock name jika disediakan untuk database
        $lockName = $this->option('lock-name') ?? $commandName;

        // Update database
        DB::table('commands_status')->updateOrInsert(
            ['command_name' => $lockName],
            ['is_running' => true, 'started_at' => now()]
        );
    }

    /**
     * Mark command as not running
     *
     * @return void
     */
    protected function markCommandAsNotRunning(): void
    {
        $commandName = $this->getName();

        // Gunakan custom lock name jika disediakan untuk database
        $lockName = $this->option('lock-name') ?? $commandName;

        DB::table('commands_status')
            ->where('command_name', $lockName)
            ->update(['is_running' => false, 'finished_at' => now()]);
    }

    /**
     * Update command progress
     *
     * @param int $current Current progress
     * @param int $total Total progress
     * @param string $message Progress message
     * @param array $data Additional data
     * @return void
     */
    protected function updateCommandProgress(int $current, int $total, string $message, array $data = []): void
    {
        $this->progressCurrent = $current;
        $this->progressTotal = $total;

        $commandName = $this->getName();

        // Add standard data
        $data = array_merge($data, [
            'memory_usage' => $this->getMemoryUsage(),
            'elapsed_time' => $this->getElapsedTime()
        ]);

        // Gunakan custom lock name jika disediakan untuk progress
        $lockName = $this->option('lock-name') ?? $commandName;

        CommandProgress::updateProgress($lockName, $current, $total, $message, $data);
    }

    /**
     * Mark command as completed
     *
     * @param string $message Completion message
     * @param array $data Additional data
     * @return void
     */
    protected function markCommandAsCompleted(string $message, array $data = []): void
    {
        $commandName = $this->getName();

        // Add standard data
        $data = array_merge($data, [
            'memory_usage' => $this->getMemoryUsage(),
            'peak_memory_usage' => $this->getPeakMemoryUsage(),
            'elapsed_time' => $this->getElapsedTime()
        ]);

        // Gunakan custom lock name jika disediakan untuk progress
        $lockName = $this->option('lock-name') ?? $commandName;

        CommandProgress::markAsCompleted($lockName, $message, $data);
    }

    /**
     * Log system error
     *
     * @param \Exception $exception Exception to log
     * @param string $context Error context
     * @return void
     */
    protected function logSystemError(\Exception $exception, string $context = ''): void
    {
        $commandName = $this->getName();

        try {
            $errorData = [
                'type' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTrace(),
                'request_method' => 'CLI',
                'request_url' => "artisan {$commandName}",
                'user_id' => null,
                'user_email' => null,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Artisan CLI',
                'request_input' => [
                    'command' => $commandName,
                    'arguments' => $this->arguments(),
                    'options' => $this->options(),
                    'context' => $context
                ],
                'resolved' => false
            ];

            SystemError::create($errorData);
            $this->error("Error logged to system_errors table: " . $exception->getMessage());
        } catch (\Exception $e) {
            $this->error("Failed to log error to system_errors: " . $e->getMessage());
            Log::error("Failed to log error to system_errors: " . $e->getMessage());
        }
    }

    /**
     * Get memory usage in MB
     *
     * @return string
     */
    protected function getMemoryUsage(): string
    {
        return round(memory_get_usage() / 1024 / 1024, 2) . ' MB';
    }

    /**
     * Get peak memory usage in MB
     *
     * @return string
     */
    protected function getPeakMemoryUsage(): string
    {
        return round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB';
    }

    /**
     * Get elapsed time in seconds
     *
     * @return float
     */
    protected function getElapsedTime(): float
    {
        return round(microtime(true) - $this->commandStartTime, 2);
    }

    /**
     * Detect if command is an hourly command
     *
     * @param string $commandName Command name
     * @return bool
     */
    protected function detectHourlyCommand(string $commandName): bool
    {
        // Daftar command yang dijalankan hourly
        $hourlyCommands = [
            'sirup:scrape',
            'scrape:tender-details',
            'sync:peserta-tender',
            'tender:update-status',
            // Tambahkan command hourly lainnya di sini
        ];

        // Cek apakah command ada di daftar hourly commands
        if (in_array($commandName, $hourlyCommands)) {
            return true;
        }

        // Cek apakah command memiliki opsi atau argumen yang menunjukkan ini adalah hourly command
        $options = $this->options();
        if (isset($options['schedule']) && $options['schedule'] === 'hourly') {
            return true;
        }

        return false;
    }

    /**
     * Check if command has exceeded time limit
     * Call this method periodically in long-running commands
     *
     * @param string $message Message to log when time limit is exceeded
     * @return bool True if command should continue, false if it should stop
     */
    protected function checkTimeLimit(string $message = 'Command time limit reached'): bool
    {
        $elapsedTime = $this->getElapsedTime();
        $commandName = $this->getName();

        // Jika command hourly dan sudah berjalan lebih dari 58 menit (3480 detik)
        if ($this->isHourlyCommand && $elapsedTime >= $this->hourlyCommandTimeout) {
            $this->warn("Hourly command '{$commandName}' has been running for {$elapsedTime} seconds, exceeding the 58-minute limit.");
            $this->warn($message);

            // Update progress dan mark command as completed
            $this->updateCommandProgress(
                $this->progressTotal,
                $this->progressTotal,
                "Command stopped due to time limit: {$message}",
                ['time_limit_exceeded' => true, 'elapsed_time' => $elapsedTime]
            );

            $this->markCommandAsCompleted(
                "Command completed due to time limit: {$message}",
                ['time_limit_exceeded' => true, 'elapsed_time' => $elapsedTime]
            );

            // Cleanup dan exit
            $this->commandCleanup();
            return false;
        }

        // Jika command non-hourly dan sudah melebihi timeout yang ditentukan
        if (!$this->isHourlyCommand && $elapsedTime >= $this->commandTimeout) {
            $this->warn("Command '{$commandName}' has been running for {$elapsedTime} seconds, exceeding the timeout of {$this->commandTimeout} seconds.");
            $this->warn($message);

            // Update progress dan mark command as completed
            $this->updateCommandProgress(
                $this->progressTotal,
                $this->progressTotal,
                "Command stopped due to timeout: {$message}",
                ['timeout_exceeded' => true, 'elapsed_time' => $elapsedTime]
            );

            $this->markCommandAsCompleted(
                "Command completed due to timeout: {$message}",
                ['timeout_exceeded' => true, 'elapsed_time' => $elapsedTime]
            );

            // Cleanup dan exit
            $this->commandCleanup();
            return false;
        }

        return true;
    }
}
