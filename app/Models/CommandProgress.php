<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CommandProgress extends Model
{
    use HasFactory;

    protected $table = 'command_progress';
    
    protected $fillable = [
        'command_name',
        'current_step',
        'total_steps',
        'status',
        'message',
        'started_at',
        'finished_at',
        'additional_data'
    ];
    
    protected $casts = [
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
        'additional_data' => 'array'
    ];
    
    // Status constants
    const STATUS_RUNNING = 'running';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_PENDING = 'pending';
    
    /**
     * Update the progress of a command
     *
     * @param string $commandName
     * @param int $currentStep
     * @param int $totalSteps
     * @param string $message
     * @param array $additionalData
     * @return CommandProgress
     */
    public static function updateProgress(
        string $commandName, 
        int $currentStep, 
        int $totalSteps, 
        string $message = '', 
        array $additionalData = []
    ): self {
        return self::updateOrCreate(
            ['command_name' => $commandName],
            [
                'current_step' => $currentStep,
                'total_steps' => $totalSteps,
                'status' => self::STATUS_RUNNING,
                'message' => $message,
                'additional_data' => $additionalData,
                'started_at' => now(),
            ]
        );
    }
    
    /**
     * Mark a command as completed
     *
     * @param string $commandName
     * @param string $message
     * @param array $additionalData
     * @return CommandProgress
     */
    public static function markAsCompleted(
        string $commandName, 
        string $message = 'Command completed successfully', 
        array $additionalData = []
    ): self {
        return self::updateOrCreate(
            ['command_name' => $commandName],
            [
                'status' => self::STATUS_COMPLETED,
                'message' => $message,
                'additional_data' => $additionalData,
                'finished_at' => now(),
            ]
        );
    }
    
    /**
     * Mark a command as failed
     *
     * @param string $commandName
     * @param string $message
     * @param array $additionalData
     * @return CommandProgress
     */
    public static function markAsFailed(
        string $commandName, 
        string $message = 'Command failed', 
        array $additionalData = []
    ): self {
        return self::updateOrCreate(
            ['command_name' => $commandName],
            [
                'status' => self::STATUS_FAILED,
                'message' => $message,
                'additional_data' => $additionalData,
                'finished_at' => now(),
            ]
        );
    }
    
    /**
     * Get the percentage of completion
     *
     * @return float
     */
    public function getPercentageAttribute(): float
    {
        if ($this->total_steps <= 0) {
            return 0;
        }
        
        return min(100, round(($this->current_step / $this->total_steps) * 100, 2));
    }
}
