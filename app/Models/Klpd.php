<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;


class Klpd extends Model
{
    protected $table = 'klpd';
    protected $primaryKey = 'kd_klpd';
    public $incrementing = false;

    protected $fillable = [
        'kd_klpd',
        'nama_klpd',
        'jenis_klpd',
        'kd_provinsi',
        'logo_path',    
        'kd_kabupaten'
    ];

    public function instansiSatker(): HasMany
    {
        return $this->hasMany(InstansiSatker::class, 'kd_klpd', 'kd_klpd');
    }

}
