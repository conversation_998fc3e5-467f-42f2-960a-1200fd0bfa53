<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    protected $fillable = [
        'user_id',
        'user_subscription_id',
        'amount',
        'payment_method',
        'status',
        'confirmation_proof',
        'confirmation_status',
        'paid_at',
    ];

    protected $dates = [
        'paid_at',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function userSubscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class);
    }

    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }
}
