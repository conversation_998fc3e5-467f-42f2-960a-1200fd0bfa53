<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MessageLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'message_type',
        'message',
        'status',
        'error_message',
    ];

    /**
     * Get the user that owns the message log.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
