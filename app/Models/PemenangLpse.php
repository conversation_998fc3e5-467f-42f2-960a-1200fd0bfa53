<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PemenangLpse extends Model
{
    protected $table = 'pemenang_lpse';

    protected $primaryKey = 'kode_tender';

    public $incrementing = false;

    protected $fillable = [
        'kode_tender', 'id_peserta', 'harga_penawaran', 'harga_terkoreksi'
    ];

    public function tenderUmumPublik(): BelongsTo
    {
        return $this->belongsTo(TenderLpse::class, 'kode_tender', 'kode_tender');
    }

    public function peserta(): BelongsTo
    {
        return $this->belongsTo(ListPerusahaanTender::class, 'id_peserta', 'id');
    }
}
