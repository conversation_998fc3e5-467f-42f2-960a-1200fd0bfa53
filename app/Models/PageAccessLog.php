<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PageAccessLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'url',
        'method',
        'accessed_at',
        'ip_address',
        'user_agent',
        'browser',
        'browser_version',
        'os',
        'os_version',
        'device_type',
        'device_brand',
        'device_model',
        'country',
        'region',
        'city',
        'latitude',
        'longitude',
        'additional_info',
        'load_time_ms',
    ];

    protected $casts = [
        'additional_info' => 'array',
        'accessed_at' => 'datetime',
    ];

    // Relasi ke User
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
