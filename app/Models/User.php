<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use App\Notifications\VerifyEmailNotification;
use App\Notifications\ResetPasswordNotification;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'photo',
        'account_type',
        'email',
        'password',
        'role',
        'npwp',
        'company_name',
        'phone',
        'email_verified_at',
        'last_seen',
        'referral_code',
        'commission_balance',
        'tender_followed',
        'lpse_followed',
        'sirup_followed'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'last_seen' => 'datetime',
        'tender_followed' => 'json',
        'lpse_followed' => 'json',
        'sirup_followed' => 'json',
    ];

    /**
     * Set the user's email to lowercase before saving.
     *
     * @param string $value
     * @return void
     */
    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = strtolower($value);
    }

    public function routeNotificationForTelegram()
    {
        return $this->telegram_bot;
    }

    public function scopeOnline($query)
    {
        return $query->where('last_seen', '>', now()->subMinutes(5));
    }

    // Referral and commission relationships and attributes

    public function commissions()
    {
        return $this->hasMany(\App\Models\Commission::class, 'referrer_id');
    }

    public function withdrawals()
    {
        return $this->hasMany(\App\Models\Withdrawal::class);
    }

    public function referrals()
    {
        return $this->hasMany(\App\Models\Referral::class, 'referrer_id');
    }

    public function userSubscriptions()
    {
        return $this->hasMany(\App\Models\UserSubscription::class, 'user_id');
    }

    public function getTotalCommissionsAttribute()
    {
        return $this->commissions()->sum('amount');
    }

    public function getTotalWithdrawalsAttribute()
    {
        return $this->withdrawals()->where('status', 'approved')->sum('amount');
    }

    public function getCommissionBalanceAttribute()
    {
        return $this->total_commissions - $this->total_withdrawals;
    }

    /**
     * Send the email verification notification.
     *
     * @return void
     */
    public function sendEmailVerificationNotification()
    {
        $this->notify(new VerifyEmailNotification);
    }

    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPasswordNotification($token));
    }
}
