<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

use <PERSON><PERSON>\Scout\Searchable;

class Ekatalog6 extends Model
{
    use Searchable;

    protected $table = 'ekatalog6';
    protected $primaryKey = 'kode';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'kode',
        'kode_rup',
        'kode_klpd',
        'tahun_anggaran',
        'nama_satker',
        'sumberdata',
        'id_penyedia',
        'nama_paket',
        'total_pelaksanaan',
        'pdn'
    ];

    protected $casts = [
        'tahun_anggaran' => 'integer',
        'total_pelaksanaan' => 'decimal:0',
        'pdn' => 'decimal:0',
    ];

    public function klpd()
    {
        return $this->belongsTo(Klpd::class, 'kode_klpd', 'kd_klpd');
    }

    public function penyedia()
    {
        return $this->belongsTo(ListPerusahaanTender::class, 'id_penyedia', 'id');
    }

    public function toSearchableArray()
    {
        return [
            'kode' => $this->kode,
            'kode_rup' => $this->kode_rup,
            'kode_klpd' => $this->kode_klpd,
            'tahun_anggaran' => $this->tahun_anggaran,
            'nama_satker' => $this->nama_satker,
            'sumberdata' => $this->sumberdata,
            'id_penyedia' => $this->id_penyedia,
            'nama_paket' => $this->nama_paket,
            'total_pelaksanaan' => $this->total_pelaksanaan,
            'pdn' => $this->pdn,
        ];
    }
}
