<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Produk extends Model
{
    protected $table = 'ekatalog_produk';
    protected $primaryKey = 'id_produk'; // Set primary key ke id_produk

    public $incrementing = true; // Pastikan primary key menggunakan auto increment
    protected $keyType = 'int'; // Tipe dari primary key

    protected $fillable = [
        'nama_produk', 'kategori_lv1', 'kategori_lv2', 'nama_manufaktur', 'jenis_produk'
    ];

    /**
     * Relasi ke tabel transaksi_paket.
     */
    public function transaksiPaket(): HasMany
    {
        return $this->hasMany(TransaksiPaket::class, 'id_produk', 'id_produk');
    }
}
