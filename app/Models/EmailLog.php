<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'recipient_email',
        'subject',
        'body',
        'status',
        'error_message',
        'email_type',
        'metadata',
        'sent_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'sent_at' => 'datetime',
    ];

    /**
     * Get the user that the email was sent to.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include emails of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('email_type', $type);
    }

    /**
     * Scope a query to only include emails with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include emails sent to a specific user.
     */
    public function scopeSentTo($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include emails sent to a specific email address.
     */
    public function scopeSentToEmail($query, $email)
    {
        return $query->where('recipient_email', $email);
    }

    /**
     * Scope a query to only include emails sent within a date range.
     */
    public function scopeSentBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('sent_at', [$startDate, $endDate]);
    }
}
