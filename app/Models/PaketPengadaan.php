<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

use Lara<PERSON>\Scout\Searchable;

class PaketPengadaan extends Model
{
    use Searchable;
    protected $table = 'ekatalog_paket_pengadaan';
    protected $primaryKey = 'id_paket';
    // Menonaktifkan auto-increment karena kolom primary key tidak bernama id
    public $incrementing = true;

    // Tipe kunci primary adalah integer
    protected $keyType = 'int';
    // Di Model Paket
    protected $dates = ['tanggal_paket'];

    protected $fillable = [
        'nama_paket',
        'nomor_paket',
        'pengelola',
        'satuan_kerja',
        'instansi_pembeli',
        'jenis_katalog',
        'etalase',
        'tanggal_paket',
        'status_paket',
        'rup_id',
        'status_umkm',
        'id_penyedia',
        'id_pelaksana'
    ];

    /**
     * Relasi ke tabel transaksi_paket.
     */
    public function transaksiPaket(): HasMany
    {
        return $this->hasMany(TransaksiPaket::class, 'id_paket', 'id_paket');
    }

    /**
     * Relasi ke tabel list_perusahaan_tender sebagai penyedia.
     */
    public function penyedia(): BelongsTo
    {
        return $this->belongsTo(ListPerusahaanTender::class, 'id_penyedia', 'id');
    }

    /**
     * Relasi ke tabel list_perusahaan_tender sebagai pelaksana.
     */
    public function pelaksana(): BelongsTo
    {
        return $this->belongsTo(ListPerusahaanTender::class, 'id_pelaksana', 'id');
    }

    public function produk()
    {
        return $this->hasManyThrough(Produk::class, TransaksiPaket::class, 'id_paket', 'id_produk', 'id_paket', 'id_produk');
    }

    public function dataSirup(): BelongsTo
    {
        return $this->belongsTo(DataSirup::class, 'rup_id', 'kode_rup');
    }

    public function getDataPerTanggal()
    {
        return self::selectRaw('tanggal, COUNT(*) as jumlah')
            ->groupBy('tanggal')
            ->orderBy('tanggal', 'ASC')
            ->get();
    }

    // Tambahkan scope untuk nilai kontrak
    public function scopeWithNilaiKontrak($query)
    {
        return $query->addSelect([
            'nilai_kontrak' => TransaksiPaket::selectRaw(
                'SUM(COALESCE(total_harga, kuantitas_produk * harga_satuan + harga_ongkos_kirim))'
            )
                ->whereColumn('id_paket', 'ekatalog_paket_pengadaan.id_paket')
        ]);
    }

    // Accessor tambahan (opsional)
    public function getNilaiKontrakAttribute()
    {
        return $this->transaksiPaket->sum(function ($transaksi) {
            return $transaksi->total_harga ??
                ($transaksi->kuantitas_produk * $transaksi->harga_satuan +
                    $transaksi->harga_ongkos_kirim);
        });
    }

    public static function getEKatalogCountPerDay()
    {
        // Ambil 10 tanggal terakhir dengan total e-katalog
        $eKatalogCount = self::selectRaw('
        tanggal,
        COUNT(*) as total
    ')
            ->groupBy('tanggal')
            ->orderBy('tanggal', 'desc')
            ->take(10)
            ->get();

        // Urutkan dari tanggal yang paling lama ke yang terbaru
        $eKatalogCount = $eKatalogCount->sortBy('tanggal');

        // Ubah koleksi menjadi array dan reset indeksnya
        $eKatalogCount = array_values($eKatalogCount->toArray());

        // Siapkan output akhir
        $finalOutput = [];

        // Hitung selisih setiap tanggal
        for ($i = 0; $i < count($eKatalogCount); $i++) {
            $current = $eKatalogCount[$i];

            // Item pertama selalu memiliki selisih 0
            if ($i === 0) {
                $finalOutput[$i] = [
                    'tanggal' => $current['tanggal'],
                    'total' => $current['total'],
                    'selisih' => 0
                ];
            } else {
                $previous = $eKatalogCount[$i - 1];
                $finalOutput[$i] = [
                    'tanggal' => $current['tanggal'],
                    'total' => $current['total'],
                    'selisih' => $current['total'] - $previous['total']
                ];
            }
        }

        // Balik urutan array agar sesuai dengan yang diinginkan (terbaru ke terlama)
        $finalOutput = array_reverse($finalOutput, true);

        return $finalOutput;
    }

    public function toSearchableArray()
    {
        return [
            'id_paket' => $this->id_paket,
            'nama_paket' => $this->nama_paket,
            'nomor_paket' => $this->nomor_paket,
            'pengelola' => $this->pengelola,
            'satuan_kerja' => $this->satuan_kerja,
            'instansi_pembeli' => $this->instansi_pembeli,
            'jenis_katalog' => $this->jenis_katalog,
            'etalase' => $this->etalase,
            'tanggal_paket' => $this->tanggal_paket,
            'status_paket' => $this->status_paket,
            'status_umkm' => $this->status_umkm,
        ];
    }
}
