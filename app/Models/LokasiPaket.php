<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LokasiPaket extends Model
{
    protected $table = 'lokasi_paket';

    protected $fillable = [
        'kode_tender', 'kbp_nama', 'prp_nama', 'pkt_lokasi'
    ];

    public function tender(): BelongsTo
    {
        return $this->belongsTo(TenderLpse::class, 'kode_tender', 'kode_tender');
    }
}
