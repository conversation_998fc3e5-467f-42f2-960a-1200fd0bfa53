<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InstansiSatker extends Model
{
    protected $table = 'instansi_satker';

    protected $fillable = [
        'kode_tender', 'rup_id', 'nama_satker', 'nama_instansi', 'jenis_instansi'
    ];

    public function tender(): BelongsTo
    {
        return $this->belongsTo(TenderLpse::class, 'kode_tender', 'kode_tender');
    }

     /**
     * Relasi ke model DataSirup.
     */
    public function dataSirup(): BelongsTo
    {
        return $this->belongsTo(DataSirup::class, 'rup_id', 'kode_rup');
    }
}
