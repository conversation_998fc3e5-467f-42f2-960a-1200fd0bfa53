<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemError extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'type',
        'code',
        'message',
        'file',
        'line',
        'trace',
        'request_url',
        'request_method',
        'request_input',
        'user_id',
        'user_email',
        'ip_address',
        'user_agent',
        'resolved',
        'resolved_at',
        'resolved_by',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'request_input' => 'array',
        'trace' => 'array',
        'resolved' => 'boolean',
        'resolved_at' => 'datetime',
    ];

    /**
     * Get the user that caused the error.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user that resolved the error.
     */
    public function resolvedByUser()
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    /**
     * Scope a query to only include unresolved errors.
     */
    public function scopeUnresolved($query)
    {
        return $query->where('resolved', false);
    }

    /**
     * Scope a query to only include resolved errors.
     */
    public function scopeResolved($query)
    {
        return $query->where('resolved', true);
    }

    /**
     * Scope a query to only include errors of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Mark the error as resolved.
     */
    public function markAsResolved($userId = null, $notes = null)
    {
        $this->resolved = true;
        $this->resolved_at = now();
        $this->resolved_by = $userId;
        
        if ($notes) {
            $this->notes = $notes;
        }
        
        $this->save();
        
        return $this;
    }

    /**
     * Get the error location.
     */
    public function getLocationAttribute()
    {
        return $this->file . ':' . $this->line;
    }

    /**
     * Get the formatted created date.
     */
    public function getCreatedDateAttribute()
    {
        return $this->created_at->format('d M Y H:i:s');
    }

    /**
     * Get the formatted resolved date.
     */
    public function getResolvedDateAttribute()
    {
        return $this->resolved_at ? $this->resolved_at->format('d M Y H:i:s') : null;
    }
}
