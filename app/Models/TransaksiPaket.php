<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TransaksiPaket extends Model
{
    protected $table = 'ekatalog_transaksi_paket';
    protected $primaryKey = 'id_transaksi';

    protected $fillable = [
        'id_paket', 'id_produk', 'kuantitas_produk', 'harga_satuan', 'harga_ongkos_kirim', 'total_harga'
    ];

    /**
     * <PERSON><PERSON>i ke tabel paket_pengadaan.
     */
    public function paketPengadaan(): BelongsTo
    {
        return $this->belongsTo(PaketPengadaan::class, 'id_paket', 'id_paket');
    }

    /**
     * <PERSON>lasi ke tabel produk.
     */
    public function produk(): BelongsTo
    {
        return $this->belongsTo(Produk::class, 'id_produk', 'id_produk');
    }
}
