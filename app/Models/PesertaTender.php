<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PesertaTender extends Model
{
    protected $table = 'peserta_tender';

    protected $fillable = [
        'kode_tender', 'id_peserta', 'status'
    ];

    public function tender(): BelongsTo
    {
        return $this->belongsTo(TenderLpse::class, 'kode_tender', 'kode_tender');
    }

    public function perusahaan(): BelongsTo
    {
        return $this->belongsTo(ListPerusahaanTender::class, 'id_peserta', 'id');
    }
}
