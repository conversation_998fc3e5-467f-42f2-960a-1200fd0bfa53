<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Lpse extends Model
{
    protected $table = 'lpse';
    protected $primaryKey = 'kd_lpse';
    public $incrementing = false;

    protected $fillable = [
        'kd_lpse', 'nama_lpse', 'provinsi', 'alamat', 'email', 'helpdesk', 
        'versi_lpse', 'url'
    ];

    public function tenders(): HasMany
    {
        return $this->hasMany(TenderLpse::class, 'kd_lpse', 'kd_lpse');
    }
}
