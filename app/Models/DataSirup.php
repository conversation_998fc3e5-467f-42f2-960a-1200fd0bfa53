<?php

// app/Models/DataSirup.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use <PERSON><PERSON>\Scout\Searchable;

class DataSirup extends Model
{
    use HasFactory, Searchable;

    protected $table = 'data_sirup';

    protected $fillable = [
        'kode_rup',
        'nama_paket',
        'nama_klpd',
        'satuan_kerja',
        'tahun_anggaran',
        'lokasi_pekerjaan',
        'volume_pekerjaan',
        'uraian_pekerjaan',
        'spesifikasi_pekerjaan',
        'produk_dalam_negeri',
        'umkm',
        'pra_dipa_dpa',
        'sumber_dana',
        'jenis_pengadaan',
        'total_pagu',
        'metode_pemilihan',
        'pemanfaatan_barang_jasa',
        'jadwal_pelaksanaan_kontrak',
        'jadwal_pemilihan_penyedia',
    	'is_processing',
        'tanggal_paket_diumumkan',
    ];

    protected $casts = [
        'lokasi_pekerjaan' => 'array',
        'sumber_dana' => 'array',
        'jenis_pengadaan' => 'array',
        'pemanfaatan_barang_jasa' => 'array',
        'jadwal_pelaksanaan_kontrak' => 'array',
        'jadwal_pemilihan_penyedia' => 'array',
        'tanggal_paket_diumumkan' => 'datetime',
    ];

    public function lpse()
    {
        return LPSE::where('nama_lpse', 'LIKE', "%{$this->nama_klpd}%")->first();
    }

    /**
     * Relasi ke model InstansiSatker.
     */
    public function instansiSatkers()
    {
        return $this->hasMany(InstansiSatker::class, 'rup_id', 'kode_rup');
    }

    /**
     * Relasi ke model Anggaran.
     */
    public function anggarans()
    {
        return $this->hasMany(Anggaran::class, 'rup_id', 'kode_rup');
    }

    /**
     * Relasi ke model PaketPengadaan.
     */
    public function paketPengadaans()
    {
        return $this->hasMany(PaketPengadaan::class, 'rup_id', 'kode_rup');
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $array = [
            'kode_rup' => $this->kode_rup,
            'nama_paket' => $this->nama_paket,
            'nama_klpd' => $this->nama_klpd,
            'satuan_kerja' => $this->satuan_kerja,
            'tahun_anggaran' => $this->tahun_anggaran,
            'lokasi_pekerjaan' => $this->lokasi_pekerjaan,
            'volume_pekerjaan' => $this->volume_pekerjaan,
            'uraian_pekerjaan' => $this->uraian_pekerjaan,
            'spesifikasi_pekerjaan' => $this->spesifikasi_pekerjaan,
            'produk_dalam_negeri' => $this->produk_dalam_negeri,
            'umkm' => $this->umkm,
            'pra_dipa_dpa' => $this->pra_dipa_dpa,
            'sumber_dana' => $this->sumber_dana,
            'jenis_pengadaan' => $this->jenis_pengadaan,
            'total_pagu' => $this->total_pagu,
            'metode_pemilihan' => $this->metode_pemilihan,
            'pemanfaatan_barang_jasa' => $this->pemanfaatan_barang_jasa,
            'jadwal_pelaksanaan_kontrak' => $this->jadwal_pelaksanaan_kontrak,
            'jadwal_pemilihan_penyedia' => $this->jadwal_pemilihan_penyedia,
            'is_processing' => $this->is_processing,
            'tanggal_paket_diumumkan' => $this->tanggal_paket_diumumkan,
        ];

        // Pastikan nilai tahun_anggaran adalah integer untuk filtering
        if (isset($array['tahun_anggaran'])) {
            $array['tahun_anggaran'] = (int)$array['tahun_anggaran'];
        }

        // Pastikan nilai total_pagu adalah integer untuk filtering
        if (isset($array['total_pagu'])) {
            $array['total_pagu'] = (int)$array['total_pagu'];
        }

        // Pastikan nilai produk_dalam_negeri dan umkm adalah boolean untuk filtering
        if (isset($array['produk_dalam_negeri'])) {
            $array['produk_dalam_negeri'] = (bool)$array['produk_dalam_negeri'];
        }

        if (isset($array['umkm'])) {
            $array['umkm'] = (bool)$array['umkm'];
        }

        return $array;
    }

    /**
     * Get the value used to index the model.
     *
     * @return mixed
     */
    public function getScoutKey()
    {
        return $this->kode_rup;
    }

    /**
     * Get the key name used to index the model.
     *
     * @return mixed
     */
    public function getScoutKeyName()
    {
        return 'kode_rup';
    }

    // In app/Models/DataSirup.php
    public static function getSirupCountPerDay()
    {
        // Get the last 10 dates with Sirup count
        $sirupCount = self::selectRaw('
            tanggal_paket_diumumkan_date as tanggal,
            COUNT(*) as total
        ')
            ->groupBy('tanggal')
            ->orderBy('tanggal', 'desc')
            ->take(10)
            ->get();

        // Sort from oldest to newest
        $sirupCount = $sirupCount->sortBy('tanggal');

        // Reset array keys to ensure correct ordering
        $sirupCount = array_values($sirupCount->toArray());

        // Prepare the final output array
        $finalOutput = [];

        // Calculate differences
        for ($i = 0; $i < count($sirupCount); $i++) {
            $current = $sirupCount[$i];

            // First item always has 0 difference
            if ($i === 0) {
                $finalOutput[$i] = [
                    'tanggal' => $current['tanggal'],
                    'total' => $current['total'],
                    'selisih' => 0
                ];
            } else {
                $previous = $sirupCount[$i - 1];
                $finalOutput[$i] = [
                    'tanggal' => $current['tanggal'],
                    'total' => $current['total'],
                    'selisih' => $current['total'] - $previous['total']
                ];
            }
        }

        // Reverse the array to match the desired key order and convert to collection
        return collect(array_reverse($finalOutput, true));
    }
}
