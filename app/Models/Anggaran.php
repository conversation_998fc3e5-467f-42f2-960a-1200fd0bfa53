<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Anggaran extends Model
{
    protected $table = 'anggaran';

    protected $fillable = [
        'kode_tender', 'rup_id', 'sbd_id', 'ang_nilai', 'ang_tahun', 'ang_koderekening'
    ];

    public function tender(): BelongsTo
    {
        return $this->belongsTo(TenderLpse::class, 'kode_tender', 'kode_tender');
    }


    /**
     * Relasi ke model DataSirup.
     */
    public function dataSirup(): BelongsTo
    {
        return $this->belongsTo(DataSirup::class, 'rup_id', 'kode_rup');
    }
}
