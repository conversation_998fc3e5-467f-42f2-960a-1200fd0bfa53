<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ListPerusahaanTender extends Model
{
    protected $table = 'list_perusahaan_tender';

    protected $fillable = [
        'nama_peserta',
        'normalized_name',
        'npwp',
        'partial_npwp',
        'alamat',
        'npwp_blur',
        'pembeda',
    ];

    public function tenders(): HasMany
    {
        return $this->hasMany(PesertaTender::class, 'id_peserta', 'id');
    }

    public function pemenangLpses(): HasMany
    {
        return $this->hasMany(PemenangLpse::class, 'id_peserta', 'id');
    }

    public function paketSebagaiPenyedia(): HasMany
    {
        return $this->hasMany(PaketPengadaan::class, 'id_penyedia', 'id');
    }

    public function paketSebagaiPelaksana(): HasMany
    {
        return $this->hasMany(PaketPengadaan::class, 'id_pelaksana', 'id');
    }

    public static function getPerusahaanCountPerDay()
    {
        // Ambil 10 tanggal terakhir dengan total perusahaan unik
        $perusahaanCount = self::selectRaw('
            DATE(created_at) as tanggal,
            COUNT(DISTINCT normalized_name) as total
        ')
            ->groupBy('tanggal')
            ->orderBy('tanggal', 'desc')
            ->take(10)
            ->get();

        // Urutkan dari tanggal yang paling lama ke yang terbaru
        $perusahaanCount = $perusahaanCount->sortBy('tanggal');

        // Ubah koleksi menjadi array dan reset indeksnya
        $perusahaanCount = array_values($perusahaanCount->toArray());

        // Siapkan output akhir
        $finalOutput = [];

        // Hitung selisih setiap tanggal
        for ($i = 0; $i < count($perusahaanCount); $i++) {
            $current = $perusahaanCount[$i];

            // Item pertama selalu memiliki selisih 0
            if ($i === 0) {
                $finalOutput[$i] = [
                    'tanggal' => $current['tanggal'],
                    'total' => $current['total'],
                    'selisih' => 0,
                ];
            } else {
                $previous = $perusahaanCount[$i - 1];
                $finalOutput[$i] = [
                    'tanggal' => $current['tanggal'],
                    'total' => $current['total'],
                    'selisih' => $current['total'] - $previous['total'],
                ];
            }
        }

        // Balik urutan array agar sesuai dengan yang diinginkan (terbaru ke terlama)
        $finalOutput = array_reverse($finalOutput, true);

        return $finalOutput;
    }

    // Method untuk normalisasi nama perusahaan
    public static function normalizeName(string $name): string
    {
        // Hapus karakter khusus kecuali spasi
        $cleaned = preg_replace('/[^a-zA-Z0-9\s]/', '', $name);

        // Standarisasi huruf kecil dan spasi berlebih
        $cleaned = strtoupper(trim(preg_replace('/\s+/', ' ', $cleaned)));

        // Standarisasi singkatan dan posisi CV/PT
        return self::standardizeAbbreviations($cleaned);
    }

    // Method untuk standarisasi singkatan
    private static function standardizeAbbreviations(string $name): string
    {
        // Daftar singkatan yang dikenali
        $abbreviations = ['PT', 'CV', 'UD', 'RM', 'PD'];

        // Hapus titik & koma sebelum singkatan jika ada
        $name = preg_replace('/\s*[,\.]\s*/', ' ', $name);

        // Pisahkan singkatan jika menempel dengan huruf atau angka
        foreach ($abbreviations as $abbr) {
            $name = preg_replace('/\b' . $abbr . '([0-9A-Z])/i', $abbr . ' $1', $name);
        }

        // Pastikan singkatan di akhir dipindahkan ke depan
        foreach ($abbreviations as $abbr) {
            if (preg_match('/\b' . $abbr . '\b$/i', $name)) {
                $name = trim($abbr . ' ' . preg_replace('/\b' . $abbr . '\b/i', '', $name));
            }
        }

        return trim($name);
    }

    public static function matchScore(string $name1, string $name2): float
    {
        similar_text($name1, $name2, $percent);
        return $percent;
    }

    // Method untuk ekstrak NPWP parsial
    public static function extractPartialNpwp(string $npwp): string
    {
        // Hapus prefix tambahan (huruf atau karakter lain sebelum NPWP)
        $npwp = preg_replace('/^[^\d]+/', '', $npwp);

        // Pisahkan NPWP berdasarkan tanda titik (.) dan tanda hubung (-)
        $parts = preg_split('/[.\-]/', $npwp);

        // Jika format NPWP sesuai (6 bagian)
        if (count($parts) === 6) {
            // Asumsikan format: [group1, group2, group3, group4, group5, group6]
            $group1 = $parts[0]; // 2 karakter
            $group2 = $parts[1]; // 3 karakter
            $group3 = $parts[2]; // 3 karakter
            // $group4 = $parts[3]; // 1 karakter (diabaikan)
            $group5 = $parts[4]; // 3 karakter
            $group6 = $parts[5]; // 3 karakter

            // Ambil:
            // - Digit pertama dari group1, group2, group3
            // - Dua digit terakhir dari group5
            // - Digit terakhir dari group6
            return mb_substr($group1, 0, 1)
                . mb_substr($group2, 0, 1)
                . mb_substr($group3, 0, 1)
                . mb_substr($group5, -2)
                . mb_substr($group6, -1);
        }

        $npwp = preg_replace('/\D/', '', $npwp);

        // Jika panjangnya 7, hapus angka pertama
        if (strlen($npwp) === 7) {
            $npwp = substr($npwp, 1);
        }

        return $npwp;
    }


    public static function processNpwp(string $rawNpwp): array
    {
        // Hapus semua karakter kecuali angka, titik (.), dan tanda hubung (-)
        $cleanNpwp = preg_replace('/[^\d.\-]/', '', $rawNpwp);

        // Cek apakah input sudah dalam format npwp_blur (mengandung '*')
        $isBlurred = strpos($rawNpwp, '*') !== false;

        // Jika input hanya angka (16 digit) → ubah ke format NPWP
        if (!$isBlurred) {
            $digitsOnly = preg_replace('/\D/', '', $rawNpwp);
            if (strlen($digitsOnly) === 15) {
                $cleanNpwp = sprintf(
                    "%s.%s.%s.%s-%s.%s",
                    substr($digitsOnly, 0, 2),
                    substr($digitsOnly, 2, 3),
                    substr($digitsOnly, 5, 3),
                    substr($digitsOnly, 8, 1),
                    substr($digitsOnly, 9, 3),
                    substr($digitsOnly, 12, 3)
                );
            }
        }

        // Jika bukan npwp_blur, set npwp hasil formatting
        $npwp = $isBlurred ? null : $cleanNpwp;

        // Ekstrak partial NPWP
        if ($npwp === null) {
            $partialNpwp = self::extractPartialNpwp($rawNpwp);
        } else {
            $partialNpwp = self::extractPartialNpwp($npwp);
        }


        // Buat npwp_blur berdasarkan partial_npwp
        if (strlen($partialNpwp) === 6) {
            $npwpBlur = sprintf(
                "%s*.%s**.%s**.*-*%s%s.**%s",
                $partialNpwp[0],  // X1
                $partialNpwp[1],  // X2
                $partialNpwp[2],  // X3
                $partialNpwp[3],  // X4
                $partialNpwp[4],  // X5
                $partialNpwp[5]   // X6
            );
        } else {
            $npwpBlur = str_repeat('*', strlen($npwp ?? '')); // Jika partial_npwp tidak valid, blur semua
        }

        return [
            'npwp' => $npwp,
            'npwp_blur' => $npwpBlur,
            'partial_npwp' => $partialNpwp,
        ];
    }

    // Method untuk deduplikasi perusahaan
    public static function findOrCreatePerusahaan(string $namaPeserta, ?string $npwp, ?string $alamat = null)
    {
        $normalizedName = self::normalizeName($namaPeserta);
        $partialNpwp = null;
        $npwpBlur = null;
        $pembeda = null;

        if ($npwp !== null) {
            $prosesNpwp = self::processNpwp($npwp);
            $npwp = $prosesNpwp['npwp'];
            $npwpBlur = $prosesNpwp['npwp_blur'];
            $partialNpwp = $prosesNpwp['partial_npwp'];
        }

        $normalizedNoSpace = str_replace(' ', '', $normalizedName);
        $pembeda = $normalizedNoSpace . ($partialNpwp ?? '');

        // Cari berdasarkan pembeda jika ada
        if (!empty($pembeda)) {
            $existing = self::where('pembeda', $pembeda)->first();
            if ($existing) {
                // Update data jika ada informasi baru
                $updates = [];

                if (!empty($alamat) && (empty($existing->alamat) || $existing->alamat !== $alamat)) {
                    $updates['alamat'] = $alamat;
                }

                if (empty($existing->npwp) && !empty($npwp)) {
                    $updates['npwp'] = $npwp;
                }

                if (empty($existing->npwp_blur) && !empty($npwpBlur)) {
                    $updates['npwp_blur'] = $npwpBlur;
                }

                if (empty($existing->partial_npwp) && !empty($partialNpwp)) {
                    $updates['partial_npwp'] = $partialNpwp;
                    // Perbarui pembeda jika partial_npwp berubah
                    $updates['pembeda'] = $normalizedNoSpace . $partialNpwp;
                }

                if (!empty($updates)) {
                    $existing->update($updates);
                }

                return $existing;
            }
        }

        // Jika tidak ditemukan berdasarkan pembeda, cari berdasarkan nama dengan sistem skor
        $candidates = self::where('normalized_name', 'LIKE', '%' . $normalizedName . '%')->get();
        foreach ($candidates as $candidate) {
            if (self::matchScore($normalizedName, $candidate->normalized_name) >= 85) {
                // Update data jika ada informasi baru
                $updates = [];

                if (!empty($alamat) && (empty($candidate->alamat) || $candidate->alamat !== $alamat)) {
                    $updates['alamat'] = $alamat;
                }

                if (empty($candidate->npwp) && !empty($npwp)) {
                    $updates['npwp'] = $npwp;
                }

                if (empty($candidate->npwp_blur) && !empty($npwpBlur)) {
                    $updates['npwp_blur'] = $npwpBlur;
                }

                if (empty($candidate->partial_npwp) && !empty($partialNpwp)) {
                    $updates['partial_npwp'] = $partialNpwp;
                    // Perbarui pembeda jika partial_npwp berubah
                    $newPembeda = str_replace(' ', '', $candidate->normalized_name) . $partialNpwp;
                    $updates['pembeda'] = $newPembeda;
                }

                if (!empty($updates)) {
                    $candidate->update($updates);
                }

                return $candidate;
            }
        }

        // Jika tidak ditemukan kecocokan, buat baru
        try {
            return self::create([
                'nama_peserta' => $namaPeserta,
                'normalized_name' => $normalizedName,
                'npwp' => $npwp ?? null,
                'npwp_blur' => $npwpBlur ?? null,
                'partial_npwp' => $partialNpwp ?? null,
                'alamat' => $alamat,
                'pembeda' => $pembeda
            ]);
        } catch (\Illuminate\Database\QueryException $e) {
            // Jika terjadi error duplikasi, tambahkan suffix unik ke pembeda
            if ($e->getCode() == 23000) {
                $uniquePembeda = $pembeda . '_' . substr(md5(uniqid()), 0, 8);

                return self::create([
                    'nama_peserta' => $namaPeserta,
                    'normalized_name' => $normalizedName,
                    'npwp' => $npwp ?? null,
                    'npwp_blur' => $npwpBlur ?? null,
                    'partial_npwp' => $partialNpwp ?? null,
                    'alamat' => $alamat,
                    'pembeda' => $uniquePembeda
                ]);
            } else {
                throw $e;
            }
        }
    }

    /**
     * Method untuk membuat atau memperbarui data perusahaan dari berbagai sumber
     *
     * @param string $namaPeserta Nama perusahaan
     * @param string|null $npwp NPWP perusahaan (bisa npwp atau npwp_blur)
     * @param string|null $alamat Alamat perusahaan
     * @return \App\Models\ListPerusahaanTender
     */
    public static function createOrUpdatePerusahaan(string $namaPeserta, ?string $npwp = null, ?string $alamat = null)
    {
        $startTime = microtime(true);
        $timings = [
            'normalize' => 0,
            'process_npwp' => 0,
            'search_pembeda' => 0,
            'search_npwp' => 0,
            'search_name' => 0,
            'match_score' => 0,
        ];

        // Normalize name
        $timeStart = microtime(true);
        $normalizedName = self::normalizeName($namaPeserta);
        $normalizedNoSpace = str_replace(' ', '', $normalizedName);
        $timings['normalize'] = microtime(true) - $timeStart;

        // Proses NPWP jika ada
        $npwpData = null;
        $partialNpwp = null;
        $npwpBlur = null;
        $npwpClean = null;

        if ($npwp !== null) {
            $timeStart = microtime(true);
            $npwpData = self::processNpwp($npwp);
            $npwpClean = $npwpData['npwp'];
            $npwpBlur = $npwpData['npwp_blur'];
            $partialNpwp = $npwpData['partial_npwp'];
            $timings['process_npwp'] = microtime(true) - $timeStart;
        }

        // Buat pembeda berdasarkan nama dan partial NPWP
        $pembeda = $normalizedNoSpace . ($partialNpwp ?? '');

        // Strategi pencarian:
        // 1. Cari berdasarkan pembeda (nama + partial NPWP)
        // 2. Jika tidak ditemukan, cari berdasarkan NPWP lengkap
        // 3. Jika tidak ditemukan, cari berdasarkan nama dengan skor kecocokan

        // Cari berdasarkan pembeda
        $perusahaan = null;
        if (!empty($pembeda)) {
            $timeStart = microtime(true);
            $perusahaan = self::where('pembeda', $pembeda)->first();
            $timings['search_pembeda'] = microtime(true) - $timeStart;
        }

        // Jika tidak ditemukan dan NPWP lengkap tersedia, cari berdasarkan NPWP
        if (!$perusahaan && !empty($npwpClean)) {
            $timeStart = microtime(true);
            $perusahaan = self::where('npwp', $npwpClean)->first();
            $timings['search_npwp'] = microtime(true) - $timeStart;
        }

        // Jika tidak ditemukan, cari berdasarkan nama dengan skor kecocokan
        if (!$perusahaan) {
            $timeStart = microtime(true);
            $candidates = self::where('normalized_name', 'LIKE', '%' . $normalizedName . '%')->get();
            $timings['search_name'] = microtime(true) - $timeStart;

            $timeStart = microtime(true);
            foreach ($candidates as $candidate) {
                if (self::matchScore($normalizedName, $candidate->normalized_name) >= 85) {
                    $perusahaan = $candidate;
                    break;
                }
            }
            $timings['match_score'] = microtime(true) - $timeStart;
        }

        // Jika perusahaan ditemukan, update data jika diperlukan
        if ($perusahaan) {
            $timeStart = microtime(true);
            $updates = [];

            // Update alamat jika ada dan berbeda
            if (!empty($alamat) && (empty($perusahaan->alamat) || $perusahaan->alamat !== $alamat)) {
                $updates['alamat'] = $alamat;
            }

            // Update NPWP jika kosong dan tersedia
            if (empty($perusahaan->npwp) && !empty($npwpClean)) {
                $updates['npwp'] = $npwpClean;
            }

            // Update NPWP blur jika kosong dan tersedia
            if (empty($perusahaan->npwp_blur) && !empty($npwpBlur)) {
                $updates['npwp_blur'] = $npwpBlur;
            }

            // Update partial NPWP jika kosong dan tersedia
            if (empty($perusahaan->partial_npwp) && !empty($partialNpwp)) {
                $updates['partial_npwp'] = $partialNpwp;

                // Update pembeda jika partial NPWP berubah
                $newPembeda = str_replace(' ', '', $perusahaan->normalized_name) . $partialNpwp;
                if ($perusahaan->pembeda !== $newPembeda) {
                    $updates['pembeda'] = $newPembeda;
                }
            }
            $timings['prepare_updates'] = microtime(true) - $timeStart;

            // Lakukan update jika ada perubahan
            if (!empty($updates)) {
                $timeStart = microtime(true);
                $perusahaan->update($updates);
                $timings['update'] = microtime(true) - $timeStart;
            }

            // Log profiling jika waktu total > 100ms
            $totalTime = microtime(true) - $startTime;
            if ($totalTime > 0.1) {
                \Log::channel('query')->info("createOrUpdatePerusahaan slow [{$totalTime}s]: " . json_encode([
                    'nama' => $namaPeserta,
                    'normalized' => $normalizedName,
                    'timings' => $timings,
                    'result' => 'updated',
                    'updates' => !empty($updates)
                ]));
            }

            return $perusahaan;
        }

        // Jika tidak ditemukan, buat baru
        try {
            $timeStart = microtime(true);
            $result = self::create([
                'nama_peserta' => $namaPeserta,
                'normalized_name' => $normalizedName,
                'npwp' => $npwpClean,
                'npwp_blur' => $npwpBlur,
                'partial_npwp' => $partialNpwp,
                'alamat' => $alamat,
                'pembeda' => $pembeda
            ]);
            $timings['create'] = microtime(true) - $timeStart;

            // Log profiling jika waktu total > 100ms
            $totalTime = microtime(true) - $startTime;
            if ($totalTime > 0.1) {
                \Log::channel('query')->info("createOrUpdatePerusahaan slow [{$totalTime}s]: " . json_encode([
                    'nama' => $namaPeserta,
                    'normalized' => $normalizedName,
                    'timings' => $timings,
                    'result' => 'created'
                ]));
            }

            return $result;
        } catch (\Illuminate\Database\QueryException $e) {
            // Jika terjadi error duplikasi, tambahkan suffix unik ke pembeda
            if ($e->getCode() == 23000) {
                $timeStart = microtime(true);
                $uniquePembeda = $pembeda . '_' . substr(md5(uniqid()), 0, 8);

                $result = self::create([
                    'nama_peserta' => $namaPeserta,
                    'normalized_name' => $normalizedName,
                    'npwp' => $npwpClean,
                    'npwp_blur' => $npwpBlur,
                    'partial_npwp' => $partialNpwp,
                    'alamat' => $alamat,
                    'pembeda' => $uniquePembeda
                ]);
                $timings['create_with_unique'] = microtime(true) - $timeStart;

                // Log profiling jika waktu total > 100ms
                $totalTime = microtime(true) - $startTime;
                if ($totalTime > 0.1) {
                    \Log::channel('query')->info("createOrUpdatePerusahaan slow [{$totalTime}s]: " . json_encode([
                        'nama' => $namaPeserta,
                        'normalized' => $normalizedName,
                        'timings' => $timings,
                        'result' => 'created_with_unique'
                    ]));
                }

                return $result;
            } else {
                throw $e;
            }
        }
    }
}
