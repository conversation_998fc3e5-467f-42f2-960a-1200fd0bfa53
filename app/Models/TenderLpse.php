<?php
namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Lara<PERSON>\Scout\Searchable;

class TenderLpse extends Model
{
    use Searchable;
    protected $table      = 'tender_umum_publik';
    protected $primaryKey = 'kode_tender';
    public $incrementing  = false;
    protected $keyType    = 'string';

    // protected $casts = [
    //     'tanggal_paket_dibuat' => 'datetime',
    // ];

    protected $fillable = [
        'kode_tender',
        'kd_lpse',
        'status_tender',
        'nama_paket',
        'pagu',
        'hps',
        'tanggal_paket_dibuat',
        'tanggal_paket_tayang',
        'kategori_pekerjaan',
        'metode_pemilihan',
        'metode_pengadaan',
        'metode_evaluasi',
        'cara_pembayaran',
        'jenis_penetapan_pemenang',
        'apakah_paket_konsolidasi',
        'jumlah_pendaftar',
        'jumlah_penawar',
        'jumlah_kirim_kualifikasi',
        'durasi_tender',
        'versi_paket_spse',
        'tahap_tender',
        'kualifikasi_usaha',
        'syarat_kualifikasi',
    ];

    public function lpse(): BelongsTo
    {
        return $this->belongsTo(Lpse::class, 'kd_lpse', 'kd_lpse');
    }

    public function peserta(): HasMany
    {
        return $this->hasMany(PesertaTender::class, 'kode_tender', 'kode_tender');
    }

    public function pemenang(): HasMany
    {
        return $this->hasMany(PemenangLpse::class, 'kode_tender', 'kode_tender');
    }
    public function instansiSatker(): HasMany
    {
        return $this->hasMany(InstansiSatker::class, 'kode_tender', 'kode_tender');
    }

    public function jadwal(): HasMany
    {
        return $this->hasMany(Jadwal::class, 'kode_tender', 'kode_tender');
    }

    public function anggaran(): HasMany
    {
        return $this->hasMany(Anggaran::class, 'kode_tender', 'kode_tender');
    }

    public function lokasiPaket(): HasMany
    {
        return $this->hasMany(LokasiPaket::class, 'kode_tender', 'kode_tender');
    }

    public function getHpsDecreasePercentage()
    {
        if ($this->pagu && $this->hps) {
            return (($this->pagu - $this->hps) / $this->pagu) * 100;
        }
        return null;
    }

    public function getTenderEndDate()
    {
        $earliestDate = $this->jadwal()->orderBy('tanggal_mulai')->value('tanggal_mulai');
        if ($earliestDate && $this->durasi_tender) {
            return Carbon::parse($earliestDate)->addDays($this->durasi_tender);
        }
        return null;
    }

    public static function getTenderCounts()
    {
        return self::selectRaw('
    COUNT(*) as total,
    COUNT(CASE WHEN status_tender = "Aktif" AND tahap_tender = "Tender Sudah Selesai" THEN 1 ELSE NULL END) as completed,
    COUNT(CASE WHEN status_tender = "Ditutup" THEN 1 ELSE NULL END) as failed,
    COUNT(CASE WHEN status_tender = "Aktif" AND tahap_tender != "Tender Sudah Selesai" THEN 1 ELSE NULL END) as ongoing
')->first();

    }

    public static function getTenderCountPerDay()
    {
        // Get the last 10 dates with tender count
        $tenderCount = self::selectRaw('
        tanggal_paket_tayang_date as tanggal,
        COUNT(*) as total
    ')
            ->groupBy('tanggal')
            ->orderBy('tanggal', 'desc')
            ->take(10)
            ->get();

        // Sort from oldest to newest
        $tenderCount = $tenderCount->sortBy('tanggal');

        // Reset array keys to ensure correct ordering
        $tenderCount = array_values($tenderCount->toArray());

        // Prepare the final output array
        $finalOutput = [];

        // Calculate differences
        for ($i = 0; $i < count($tenderCount); $i++) {
            $current = $tenderCount[$i];

            // First item always has 0 difference
            if ($i === 0) {
                $finalOutput[$i] = [
                    'tanggal' => $current['tanggal'],
                    'total'   => $current['total'],
                    'selisih' => 0,
                ];
            } else {
                $previous        = $tenderCount[$i - 1];
                $finalOutput[$i] = [
                    'tanggal' => $current['tanggal'],
                    'total'   => $current['total'],
                    'selisih' => $current['total'] - $previous['total'],
                ];
            }
        }

        // Reverse the array to match the desired key order
        $finalOutput = array_reverse($finalOutput, true);

        return $finalOutput;
    }

    public function toSearchableArray()
    {
        return [
            'kode_tender' => $this->kode_tender,
            'nama_paket' => $this->nama_paket,
            'status_tender' => $this->status_tender,
            'kategori_pekerjaan' => $this->kategori_pekerjaan,
            'metode_pemilihan' => $this->metode_pemilihan,
            'tanggal_paket_tayang' => $this->tanggal_paket_tayang,
        ];
    }
}
