<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Jadwal extends Model
{
    protected $table = 'jadwal';

    protected $fillable = [
        'kode_tender', 'tahap', 'tanggal_mulai', 'tanggal_selesai', 'step', 'keterangan'
    ];

    public function tender(): BelongsTo
    {
        return $this->belongsTo(TenderLpse::class, 'kode_tender', 'kode_tender');
    }
}
