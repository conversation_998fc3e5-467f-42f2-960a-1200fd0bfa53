<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WhatsAppHelper
{
    /**
     * Fallback API key jika konfigurasi tidak tersedia
     */
    private static $fallbackApiKey = 'tenderid2023';

    /**
     * Send welcome message to user
     *
     * @param string $phone
     * @param array $userData
     * @return bool
     */
    public static function sendWelcomeMessage($phone, $userData)
    {
        // Format nomor telepon jika perlu
        $phone = self::formatPhoneNumber($phone);
        
        // Pastikan userData memiliki semua field yang diperlukan
        if (!isset($userData['name']) || empty($userData['name'])) {
            Log::warning('Missing required field "name" in userData for welcome message');
            $userData['name'] = 'User';
        }
        
        // Pastikan email ada
        if (!isset($userData['email']) || empty($userData['email'])) {
            Log::warning('Missing field "email" in userData for welcome message');
            $userData['email'] = '<EMAIL>';
        }
        
        // Pastikan phone ada dan diformat dengan benar
        $userData['phone'] = $phone;
        
        $payload = [
            'phone' => $phone,
            'userData' => $userData,
        ];
        
        Log::info('Preparing to send welcome message', [
            'phone' => $phone,
            'userData' => $userData,
        ]);
        
        // Untuk debugging, coba kirim langsung dengan curl
        $whatsappServiceUrl = config('services.whatsapp.url');
        
        // Pastikan URL menggunakan HTTPS
        if (strpos($whatsappServiceUrl, 'http:') === 0) {
            $whatsappServiceUrl = str_replace('http:', 'https:', $whatsappServiceUrl);
        }
        
        $fullUrl = $whatsappServiceUrl . '/message/welcome';
        
        // Log full URL for debugging
        Log::debug('DEBUG: WhatsApp welcome message details', [
            'full_url' => $fullUrl,
            'payload' => $payload,
        ]);
        
        // Coba dengan API key hardcoded
        $apiKey = 'tenderid2023';
        
        // Log API key for debugging
        Log::debug('DEBUG: Using API key', [
            'api_key' => $apiKey,
        ]);
        
        // Execute curl command directly
        $ch = curl_init();
        
        // Set curl options
        curl_setopt($ch, CURLOPT_URL, $fullUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'x-api-key: ' . $apiKey,
            'Accept: application/json',
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Disable SSL verification for testing
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // Disable SSL host verification for testing
        
        // Log curl command for debugging
        Log::debug('DEBUG: Executing curl command', [
            'url' => $fullUrl,
            'headers' => [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey,
                'Accept: application/json',
            ],
            'payload' => json_encode($payload),
        ]);
        
        // Execute curl request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        // Close curl
        curl_close($ch);
        
        // Log curl response
        Log::debug('DEBUG: Curl response', [
            'http_code' => $httpCode,
            'response' => $response,
            'error' => $error,
        ]);
        
        // Check if request was successful
        if ($httpCode === 200 && !empty($response)) {
            $responseData = json_decode($response, true);
            
            if (isset($responseData['success']) && $responseData['success']) {
                Log::info('DEBUG: Curl request successful', [
                    'response' => $responseData,
                ]);
                
                return true;
            }
        }
        
        // If we got here, the request failed
        Log::error('DEBUG: Curl request failed', [
            'http_code' => $httpCode,
            'response' => $response,
            'error' => $error,
        ]);
        
        return false;
    }
    
    /**
     * Format nomor telepon untuk memastikan format yang benar
     * 
     * @param string $phone
     * @return string
     */
    private static function formatPhoneNumber($phone)
    {
        // Hapus semua karakter non-digit
        $phone = preg_replace('/\D/', '', $phone);
        
        // Pastikan nomor dimulai dengan 62 (kode negara Indonesia)
        if (substr($phone, 0, 2) !== '62') {
            // Jika dimulai dengan 0, ganti dengan 62
            if (substr($phone, 0, 1) === '0') {
                $phone = '62' . substr($phone, 1);
            } else {
                // Jika tidak dimulai dengan 0, tambahkan 62 di depan
                $phone = '62' . $phone;
            }
        }
        
        Log::debug('Phone number formatted', [
            'original' => $phone,
            'formatted' => $phone
        ]);
        
        return $phone;
    }
}
