<?php

namespace App\Helpers;

class CompanyHelper
{
    public static function normalizeName(string $name): string
    {
        // 1. Hapus karakter khusus kecuali spasi
        $cleaned = preg_replace('/[^a-zA-Z0-9\s]/', '', $name);
        
        // 2. Standarisasi huruf kecil dan spasi berlebih
        $cleaned = strtolower(trim(preg_replace('/\s+/', ' ', $cleaned)));
        
        // 3. Standarisasi singkatan
        return self::standardizeAbbreviations($cleaned);
    }

    private static function standardizeAbbreviations(string $name): string
    {
        $abbreviations = [
            'pt' => 'pt',
            'tbk' => 'tbk',
            'persero' => 'persero',
            'cv' => 'cv',
            'tbk.' => 'tbk',
            'pt.' => 'pt',
            'cv.' => 'cv',
        ];

        return collect(explode(' ', $name))
            ->map(function ($word) use ($abbreviations) {
                return $abbreviations[$word] ?? $word;
            })
            ->implode(' ');
    }

    public static function extractPartialNpwp(string $maskedNpwp): string
    {
        // Ekstrak angka dan strip yang tersisa
        return preg_replace('/[^0-9\-]/', '', $maskedNpwp);
    }

    public static function normalizeKlpdName(string $name): string
    {
        // Replace "Kab." or "KAB." followed by space with "Kabupaten "
        $name = preg_replace('/\bKab\.\s+/i', 'Kabupaten ', $name);

        // Replace "Kota" or "KOTA" with "Kota"
        $name = preg_replace('/\bKota\b/i', 'Kota', $name);

        // Capitalize first letter of each word after replacement
        $name = ucwords(strtolower($name));

        return $name;
    }
}
