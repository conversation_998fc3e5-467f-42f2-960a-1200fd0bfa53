<?php

namespace App\Helpers;

use App\Models\User;
use App\Notifications\UserRegistrationNotification;
use App\Notifications\PaymentConfirmationNotification;
use App\Notifications\WithdrawalRequestNotification;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log;

class NotificationHelper {
    /**
     * Get all super admin users
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getSuperAdmins() {
        return User::where('account_type', 'Super Admin')->get();
    }

    /**
     * Send notification to all super admins
     *
     * @param mixed $notification
     * @return void
     */
    public static function notifySuperAdmins($notification) {
        $superAdmins = self::getSuperAdmins();
        Notification::send($superAdmins, $notification);
    }

    /**
     * Send user registration notification to user and admins
     *
     * @param User $user
     * @return void
     */
    public static function sendUserRegistrationNotifications(User $user) {
        // Notify the user
        $user->notify(new UserRegistrationNotification($user));

        // Notify super admins
        self::notifySuper<PERSON>dmins(new UserRegistrationNotification($user, true));
    }

    /**
     * Send payment confirmation notification to admins
     *
     * @param \App\Models\UserSubscription $userSubscription
     * @return void
     */
    public static function sendPaymentConfirmationNotifications($userSubscription) {
        // Dapatkan semua super admin
        $superAdmins = self::getSuperAdmins();

        // Buat instance notifikasi
        $notification = new PaymentConfirmationNotification($userSubscription);

        // Kirim notifikasi ke semua super admin
        Notification::send($superAdmins, $notification);

        // Kirim notifikasi WhatsApp langsung ke semua super admin
        foreach ($superAdmins as $admin) {
            try {
                // Panggil metode sendWhatsApp dari notifikasi
                $notification->afterSend($admin, 'direct', null);

                // Log pengiriman WhatsApp langsung
                Log::info("Direct WhatsApp notification sent for payment confirmation", [
                    'admin_id' => $admin->id,
                    'admin_phone' => $admin->phone ?? 'not set',
                    'subscription_id' => $userSubscription->id
                ]);
            } catch (\Exception $e) {
                Log::error("Failed to send direct WhatsApp notification: {$e->getMessage()}", [
                    'admin_id' => $admin->id,
                    'subscription_id' => $userSubscription->id
                ]);
            }
        }
    }

    /**
     * Send withdrawal request notification to user and admins
     *
     * @param \App\Models\Withdrawal $withdrawal
     * @return void
     */
    public static function sendWithdrawalRequestNotifications($withdrawal) {
        // Notify the user
        $user = $withdrawal->user;
        $user->notify(new WithdrawalRequestNotification($withdrawal));

        // Notify super admins
        self::notifySuperAdmins(new WithdrawalRequestNotification($withdrawal, true));
    }
}
