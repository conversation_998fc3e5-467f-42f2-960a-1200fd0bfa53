<?php

namespace App\Helpers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class DeviceDetector
{
    protected $userAgent;
    public $ipAddress; // Ubah ke public agar bisa diakses dari luar
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request ?: request();
        $this->userAgent = $this->request->header('User-Agent');

        // Mendapatkan IP asli dari header Cloudflare jika tersedia
        $this->ipAddress = $this->getRealIpAddress();
    }

    /**
     * Mendapatkan alamat IP asli pengunjung dengan mempertimbangkan Cloudflare
     *
     * @return string
     */
    protected function getRealIpAddress()
    {
        // Cek header Cloudflare CF-Connecting-IP
        $cfIp = $this->request->header('CF-Connecting-IP');
        if (!empty($cfIp)) {
            return $cfIp;
        }

        // Cek header X-Forwarded-For (Cloudflare menyimpan IP asli di sini)
        $xForwardedFor = $this->request->header('X-Forwarded-For');
        if (!empty($xForwardedFor)) {
            // X-Forwarded-For bisa berisi beberapa IP, ambil yang pertama (paling kiri)
            $ips = explode(',', $xForwardedFor);
            return trim($ips[0]);
        }

        // Cek header True-Client-IP (Cloudflare juga bisa menggunakan ini)
        $trueClientIp = $this->request->header('True-Client-IP');
        if (!empty($trueClientIp)) {
            return $trueClientIp;
        }

        // Fallback ke IP yang terdeteksi oleh Laravel
        return $this->request->ip();
    }

    /**
     * Mendeteksi semua informasi perangkat dan lokasi
     *
     * @return array
     */
    public function detect()
    {
        return [
            'browser' => $this->detectBrowser(),
            'os' => $this->detectOS(),
            'device' => $this->detectDevice(),
            'location' => $this->detectLocation(),
        ];
    }

    /**
     * Mendeteksi informasi browser
     *
     * @return array
     */
    public function detectBrowser()
    {
        $browser = 'Unknown';
        $version = '';

        if (preg_match('/MSIE|Trident/i', $this->userAgent)) {
            $browser = 'Internet Explorer';
            preg_match('/MSIE\s+([\d.]+)|rv:([\d.]+)/i', $this->userAgent, $matches);
            $version = $matches[1] ?? $matches[2] ?? '';
        } elseif (preg_match('/Firefox/i', $this->userAgent)) {
            $browser = 'Firefox';
            preg_match('/Firefox\/([\d.]+)/i', $this->userAgent, $matches);
            $version = $matches[1] ?? '';
        } elseif (preg_match('/Edge/i', $this->userAgent)) {
            $browser = 'Edge';
            preg_match('/Edge\/([\d.]+)/i', $this->userAgent, $matches);
            $version = $matches[1] ?? '';
        } elseif (preg_match('/Edg/i', $this->userAgent)) {
            $browser = 'Edge (Chromium)';
            preg_match('/Edg\/([\d.]+)/i', $this->userAgent, $matches);
            $version = $matches[1] ?? '';
        } elseif (preg_match('/OPR|Opera/i', $this->userAgent)) {
            $browser = 'Opera';
            preg_match('/OPR\/([\d.]+)|Opera\/([\d.]+)/i', $this->userAgent, $matches);
            $version = $matches[1] ?? $matches[2] ?? '';
        } elseif (preg_match('/Chrome/i', $this->userAgent)) {
            $browser = 'Chrome';
            preg_match('/Chrome\/([\d.]+)/i', $this->userAgent, $matches);
            $version = $matches[1] ?? '';
        } elseif (preg_match('/Safari/i', $this->userAgent)) {
            $browser = 'Safari';
            preg_match('/Version\/([\d.]+)/i', $this->userAgent, $matches);
            $version = $matches[1] ?? '';
        }

        return [
            'name' => $browser,
            'version' => $version
        ];
    }

    /**
     * Mendeteksi informasi sistem operasi
     *
     * @return array
     */
    public function detectOS()
    {
        $os = 'Unknown';
        $version = '';

        if (preg_match('/Windows NT/i', $this->userAgent)) {
            $os = 'Windows';
            preg_match('/Windows NT\s+([\d.]+)/i', $this->userAgent, $matches);
            $ntVersion = $matches[1] ?? '';

            $windowsVersions = [
                '10.0' => '10/11',
                '6.3' => '8.1',
                '6.2' => '8',
                '6.1' => '7',
                '6.0' => 'Vista',
                '5.2' => 'XP x64',
                '5.1' => 'XP',
                '5.0' => '2000'
            ];

            $version = $windowsVersions[$ntVersion] ?? $ntVersion;
        } elseif (preg_match('/Mac OS X/i', $this->userAgent)) {
            $os = 'Mac OS X';
            preg_match('/Mac OS X\s+([\d_.]+)/i', $this->userAgent, $matches);
            $version = str_replace('_', '.', $matches[1] ?? '');
        } elseif (preg_match('/Android/i', $this->userAgent)) {
            $os = 'Android';
            preg_match('/Android\s+([\d.]+)/i', $this->userAgent, $matches);
            $version = $matches[1] ?? '';
        } elseif (preg_match('/iOS/i', $this->userAgent) || preg_match('/iPhone OS/i', $this->userAgent)) {
            $os = 'iOS';
            preg_match('/OS\s+([\d_]+)/i', $this->userAgent, $matches);
            $version = str_replace('_', '.', $matches[1] ?? '');
        } elseif (preg_match('/Linux/i', $this->userAgent)) {
            $os = 'Linux';
            if (preg_match('/Ubuntu/i', $this->userAgent)) {
                $os = 'Ubuntu';
                preg_match('/Ubuntu\/([\d.]+)/i', $this->userAgent, $matches);
                $version = $matches[1] ?? '';
            } elseif (preg_match('/Fedora/i', $this->userAgent)) {
                $os = 'Fedora';
                preg_match('/Fedora\/([\d.]+)/i', $this->userAgent, $matches);
                $version = $matches[1] ?? '';
            }
        }

        return [
            'name' => $os,
            'version' => $version
        ];
    }

    /**
     * Mendeteksi informasi perangkat
     *
     * @return array
     */
    public function detectDevice()
    {
        $type = 'Desktop';
        $brand = 'Unknown';
        $model = '';

        // Detectar dispositivos móviles
        if (preg_match('/Mobile|Android|iPhone|iPad|iPod|Windows Phone/i', $this->userAgent)) {
            $type = 'Mobile';

            // Detectar tablets
            if (preg_match('/Tablet|iPad/i', $this->userAgent)) {
                $type = 'Tablet';
            }

            // Detectar marca y modelo
            if (preg_match('/iPhone/i', $this->userAgent)) {
                $brand = 'Apple';
                $model = 'iPhone';
                if (preg_match('/iPhone\s+OS\s+(\d+)_/i', $this->userAgent, $matches)) {
                    $model .= ' ' . $matches[1];
                }
            } elseif (preg_match('/iPad/i', $this->userAgent)) {
                $brand = 'Apple';
                $model = 'iPad';
                if (preg_match('/CPU\s+OS\s+(\d+)_/i', $this->userAgent, $matches)) {
                    $model .= ' ' . $matches[1];
                }
            } elseif (preg_match('/Samsung/i', $this->userAgent)) {
                $brand = 'Samsung';
                if (preg_match('/SM-([A-Za-z0-9]+)/i', $this->userAgent, $matches)) {
                    $model = 'SM-' . $matches[1];
                }
            } elseif (preg_match('/Huawei|Honor/i', $this->userAgent)) {
                $brand = preg_match('/Honor/i', $this->userAgent) ? 'Honor' : 'Huawei';
                if (preg_match('/(Huawei|Honor)[^;]+/i', $this->userAgent, $matches)) {
                    $model = trim(str_replace(['Huawei', 'Honor'], '', $matches[0]));
                }
            } elseif (preg_match('/Xiaomi|Redmi/i', $this->userAgent)) {
                $brand = 'Xiaomi';
                if (preg_match('/(Redmi|Mi)[^;]+/i', $this->userAgent, $matches)) {
                    $model = $matches[0];
                }
            } elseif (preg_match('/OPPO/i', $this->userAgent)) {
                $brand = 'OPPO';
                if (preg_match('/OPPO\s+([A-Za-z0-9]+)/i', $this->userAgent, $matches)) {
                    $model = $matches[1];
                }
            } elseif (preg_match('/vivo/i', $this->userAgent)) {
                $brand = 'Vivo';
                if (preg_match('/vivo\s+([A-Za-z0-9]+)/i', $this->userAgent, $matches)) {
                    $model = $matches[1];
                }
            } elseif (preg_match('/Pixel/i', $this->userAgent)) {
                $brand = 'Google';
                if (preg_match('/Pixel\s+([A-Za-z0-9]+)/i', $this->userAgent, $matches)) {
                    $model = 'Pixel ' . $matches[1];
                }
            } elseif (preg_match('/OnePlus/i', $this->userAgent)) {
                $brand = 'OnePlus';
                if (preg_match('/OnePlus([A-Za-z0-9\s]+)/i', $this->userAgent, $matches)) {
                    $model = 'OnePlus ' . trim($matches[1]);
                }
            } elseif (preg_match('/Motorola|Moto/i', $this->userAgent)) {
                $brand = 'Motorola';
                if (preg_match('/Moto\s+([A-Za-z0-9]+)/i', $this->userAgent, $matches)) {
                    $model = 'Moto ' . $matches[1];
                }
            }
        } elseif (preg_match('/Macintosh/i', $this->userAgent)) {
            $brand = 'Apple';
            $model = 'Mac';
        } elseif (preg_match('/Windows/i', $this->userAgent)) {
            $brand = 'PC';
            $model = 'Windows';
        } elseif (preg_match('/Linux/i', $this->userAgent)) {
            $brand = 'PC';
            $model = 'Linux';
        }

        return [
            'type' => $type,
            'brand' => $brand,
            'model' => $model
        ];
    }

    /**
     * Mendeteksi informasi lokasi berdasarkan IP
     *
     * @return array
     */
    public function detectLocation()
    {
        // Usar caché para evitar demasiadas solicitudes a la API
        $cacheKey = 'ip_location_' . $this->ipAddress;

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $location = [
            'country' => '',
            'region' => '',
            'city' => '',
            'latitude' => '',
            'longitude' => '',
            'isp' => '',
            'timezone' => ''
        ];

        try {
            // Ignorar IPs locales
            if ($this->isLocalIp($this->ipAddress)) {
                $location['country'] = 'Local';
                $location['city'] = 'Local Network';
                return $location;
            }

            // Usar ipinfo.io (requiere token para uso extendido)
            // $response = Http::get("https://ipinfo.io/{$this->ipAddress}/json");

            // Alternativa: usar ip-api.com (gratuito para uso no comercial)
            $response = Http::get("http://ip-api.com/json/{$this->ipAddress}?fields=status,country,regionName,city,lat,lon,isp,timezone");

            if ($response->successful() && $response->json('status') === 'success') {
                $data = $response->json();
                $location = [
                    'country' => $data['country'] ?? '',
                    'region' => $data['regionName'] ?? '',
                    'city' => $data['city'] ?? '',
                    'latitude' => $data['lat'] ?? '',
                    'longitude' => $data['lon'] ?? '',
                    'isp' => $data['isp'] ?? '',
                    'timezone' => $data['timezone'] ?? ''
                ];

                // Guardar en caché por 1 día
                Cache::put($cacheKey, $location, now()->addDay());
            }
        } catch (\Exception $e) {
            Log::error('Error saat mendeteksi lokasi: ' . $e->getMessage());
        }

        return $location;
    }

    /**
     * Memeriksa apakah IP adalah lokal
     *
     * @param string $ip
     * @return bool
     */
    protected function isLocalIp($ip)
    {
        return in_array($ip, ['127.0.0.1', '::1']) ||
               preg_match('/^192\.168\./', $ip) ||
               preg_match('/^10\./', $ip) ||
               preg_match('/^172\.(1[6-9]|2[0-9]|3[0-1])\./', $ip);
    }
}
