<?php

if (!function_exists('format_number_short')) {
    function format_number_short($number)
    {
        if ($number >= 1000000000000) {
            return round($number / 1000000000000, 1) . 'T';
        } elseif ($number >= 1000000000) {
            return round($number / 1000000000, 1) . 'M';
        } elseif ($number >= 1000000) {
            return round($number / 1000000, 1) . 'Jt';
        } elseif ($number >= 1000) {
            return round($number / 1000, 1) . 'Rb';
        }

        return number_format($number);
    }
}

if (!function_exists('convert_to_rupiah')) {
    function convert_to_rupiah($number)
    {
        return 'Rp ' . number_format($number, 0, ',', '.');
    }
}


if (!function_exists('format_date_indonesian')) {
    function format_date_indonesian($date)
    {
        return \Carbon\Carbon::parse($date)->translatedFormat('d F Y');
    }
}

if (!function_exists('format_date_time')) {
    function format_date_time($date)
    {
        return \Carbon\Carbon::parse($date)->translatedFormat('d F Y H:i');
    }
}

if (!function_exists('getInitials')) {
    function getInitials($name)
    {
        $words = explode(' ', $name);
        if (count($words) >= 2) {
            return strtoupper(substr($words[0], 0, 1) . substr(end($words), 0, 1));
        }
        return strtoupper(substr($name, 0, 2));
    }
}

if (!function_exists('rapikan_str_tahap_tender')) {
    /**
     * Clean up the tender stage string by removing content in brackets.
     *
     * @param string $string
     * @return string
     */
    function rapikan_str_tahap_tender(string $string): string
    {
        $cleanedString = preg_replace('/\[.*?\]/', '', $string);
        return trim($cleanedString);
    }
}

