<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use <PERSON>ymfony\Component\HttpKernel\Exception\NotFoundHttpException;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            $this->logExceptionToDatabase($e);
        });

        // Custom handling for 404 errors
        $this->renderable(function (NotFoundHttpException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Resource not found'], 404);
            }

            return response()->view('errors.404', [], 404);
        });
    }

    /**
     * Log the exception to the database.
     *
     * @param \Throwable $exception
     * @return void
     */
    protected function logExceptionToDatabase(Throwable $exception): void
    {
        try {
            // Skip certain exceptions that we don't want to log
            if ($this->shouldntReport($exception)) {
                return;
            }

            $request = request();

            // Get user information if authenticated
            $user = $request->user();
            $userId = $user ? $user->id : null;
            $userEmail = $user ? $user->email : null;

            // Prepare request input data, filtering out sensitive information
            $input = $request->except(['password', 'password_confirmation', 'current_password']);

            // Create the error record
            \App\Models\SystemError::create([
                'type' => get_class($exception),
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $this->formatTrace($exception->getTrace()),
                'request_url' => $request->fullUrl(),
                'request_method' => $request->method(),
                'request_input' => $input,
                'user_id' => $userId,
                'user_email' => $userEmail,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);
        } catch (\Exception $e) {
            // If there's an error logging the exception, log it to the default logger
            \Illuminate\Support\Facades\Log::error('Error logging exception to database: ' . $e->getMessage(), [
                'exception' => $e,
                'original_exception' => $exception,
            ]);
        }
    }

    /**
     * Format the stack trace to be more readable and remove sensitive information.
     *
     * @param array $trace
     * @return array
     */
    protected function formatTrace(array $trace): array
    {
        return collect($trace)->map(function ($frame) {
            // Remove arguments from the trace to avoid sensitive data
            if (isset($frame['args'])) {
                $frame['args'] = '[FILTERED]';
            }

            return $frame;
        })->toArray();
    }
}
