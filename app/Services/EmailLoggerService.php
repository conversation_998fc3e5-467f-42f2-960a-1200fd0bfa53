<?php

namespace App\Services;

use App\Models\EmailLog;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class EmailLoggerService
{
    /**
     * Log an email that was sent.
     *
     * @param string $recipientEmail
     * @param string $subject
     * @param string|null $body
     * @param string $status
     * @param string|null $errorMessage
     * @param string|null $emailType
     * @param array|null $metadata
     * @param \DateTime|null $sentAt
     * @return EmailLog
     */
    public function logEmail(
        string $recipientEmail,
        string $subject,
        ?string $body = null,
        string $status = 'sent',
        ?string $errorMessage = null,
        ?string $emailType = null,
        ?array $metadata = null,
        ?\DateTime $sentAt = null
    ): EmailLog {
        try {
            // Find user by email if exists
            $user = User::where('email', $recipientEmail)->first();
            
            return EmailLog::create([
                'user_id' => $user ? $user->id : null,
                'recipient_email' => $recipientEmail,
                'subject' => $subject,
                'body' => $body,
                'status' => $status,
                'error_message' => $errorMessage,
                'email_type' => $emailType,
                'metadata' => $metadata,
                'sent_at' => $sentAt ?? now(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log email: ' . $e->getMessage(), [
                'recipient' => $recipientEmail,
                'subject' => $subject,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Update the status of an email log.
     *
     * @param int $emailLogId
     * @param string $status
     * @param string|null $errorMessage
     * @return bool
     */
    public function updateStatus(int $emailLogId, string $status, ?string $errorMessage = null): bool
    {
        try {
            $emailLog = EmailLog::findOrFail($emailLogId);
            
            return $emailLog->update([
                'status' => $status,
                'error_message' => $errorMessage,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update email log status: ' . $e->getMessage(), [
                'email_log_id' => $emailLogId,
                'status' => $status,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }
}
