<?php

namespace App\Logging;

use Monolog\Handler\RotatingFileHandler;
use Monolog\Logger;
use Monolog\LogRecord;

class DockerFriendlyLogger
{
    /**
     * Create a custom Monolog instance for Docker environment
     */
    public function __invoke(array $config)
    {
        $logger = new Logger('query');

        // Use RotatingFileHandler without permission changes
        $handler = new class($config['path'], $config['days'] ?? 7) extends RotatingFileHandler {
            protected function write(LogRecord $record): void
            {
                // Override to prevent permission changes
                if (!$this->url) {
                    $this->url = $this->getTimedFilename();
                }

                // Create directory if it doesn't exist
                $dir = dirname($this->url);
                if (!is_dir($dir)) {
                    @mkdir($dir, 0755, true);
                }

                // Check if file exists and is writable
                if (file_exists($this->url) && !is_writable($this->url)) {
                    // Try to fix ownership and permissions
                    $currentUser = get_current_user() ?: 'www-data';
                    @exec("chown {$currentUser}:{$currentUser} " . escapeshellarg($this->url) . " 2>/dev/null");
                    @chmod($this->url, 0666);
                }

                // If file doesn't exist, create it with proper permissions
                if (!file_exists($this->url)) {
                    @touch($this->url);
                    @chmod($this->url, 0666);
                    $currentUser = get_current_user() ?: 'www-data';
                    @exec("chown {$currentUser}:{$currentUser} " . escapeshellarg($this->url) . " 2>/dev/null");
                }

                // Try to write to file
                $result = @file_put_contents($this->url, (string) $record->formatted, FILE_APPEND | LOCK_EX);

                if ($result === false) {
                    // If writing fails, try alternative approach
                    $fallbackPath = storage_path('logs/fallback_slow_query.log');
                    @file_put_contents($fallbackPath,
                        "[" . date('Y-m-d H:i:s') . "] PERMISSION ERROR - Could not write to {$this->url}\n" .
                        (string) $record->formatted,
                        FILE_APPEND | LOCK_EX
                    );
                }
            }
        };

        $handler->setFormatter(new \Monolog\Formatter\LineFormatter(
            "[%datetime%] %level_name%: %message% %context%\n",
            'Y-m-d H:i:s'
        ));

        $logger->pushHandler($handler);

        return $logger;
    }
}
