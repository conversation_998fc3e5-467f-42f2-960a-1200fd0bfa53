<?php

namespace App\Logging;

use Monolog\Formatter\LineFormatter;
use Monolog\LogRecord;

class QueryLogFormatter
{
    /**
     * Customize the given logger instance.
     */
    public function __invoke($logger)
    {
        foreach ($logger->getHandlers() as $handler) {
            $handler->setFormatter(new class extends LineFormatter {
                public function format(LogRecord $record): string
                {
                    $context = $record->context;
                    
                    // Format for slow query logs
                    if (isset($context['execution_time_ms'])) {
                        $output = sprintf(
                            "[%s] %s: %s [%sms]\n",
                            $record->datetime->format('Y-m-d H:i:s'),
                            strtoupper($record->level->name),
                            $record->message,
                            $context['execution_time_ms']
                        );
                        
                        $output .= sprintf("SQL: %s\n", $context['sql'] ?? 'N/A');
                        $output .= sprintf("Bindings: %s\n", $context['bindings'] ?? 'N/A');
                        $output .= sprintf("Memory: %s (Peak: %s)\n", 
                            $context['memory_usage'] ?? 'N/A',
                            $context['peak_memory'] ?? 'N/A'
                        );
                        $output .= sprintf("Request: %s\n", $context['request_uri'] ?? 'CLI');
                        
                        if (isset($context['stack_trace'])) {
                            $output .= "Stack Trace:\n";
                            foreach (array_slice($context['stack_trace'], 0, 5) as $i => $trace) {
                                $file = $trace['file'] ?? 'unknown';
                                $line = $trace['line'] ?? 'unknown';
                                $function = $trace['function'] ?? 'unknown';
                                $output .= sprintf("  #%d %s:%s %s()\n", $i, basename($file), $line, $function);
                            }
                        }
                        
                        $output .= str_repeat('-', 80) . "\n";
                        
                        return $output;
                    }
                    
                    // Default formatting for other logs
                    return parent::format($record);
                }
            });
        }
    }
}
