<?php

namespace App\Policies;

use App\Models\SystemError;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SystemErrorPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the system errors.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function viewSystemErrors(User $user)
    {
        // Only super admins can view system errors
        return $user->account_type === 'Super Admin';
    }

    /**
     * Determine whether the user can view the system errors.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function viewAny(User $user)
    {
        // Only super admins can view system errors
        return $user->account_type === 'Super Admin';
    }

    /**
     * Determine whether the user can resolve system errors.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function resolveSystemErrors(User $user)
    {
        // Only super admins can resolve system errors
        return $user->account_type === 'Super Admin';
    }

    /**
     * Determine whether the user can delete system errors.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function deleteSystemErrors(User $user)
    {
        // Only super admins can delete system errors
        return $user->account_type === 'Super Admin';
    }
}
