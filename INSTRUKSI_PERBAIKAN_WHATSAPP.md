# Instruksi Perbaikan Pengiriman Pesan WhatsApp

Berikut adalah langkah-langkah untuk menerapkan perbaikan pengiriman pesan WhatsApp selamat datang saat pengguna mendaftar:

## 1. Perbarui File di Laravel

Semua file berikut sudah diperbarui dan siap digunakan:

- `app/Helpers/WhatsAppHelper.php` - Implementasi baru yang menggunakan curl langsung
- `app/Console/Commands/SendWhatsAppWelcomeMessageCommand.php` - Command baru untuk mengirim pesan WhatsApp selamat datang
- `app/Observers/UserObserver.php` - Observer baru untuk menjalankan command saat pengguna mendaftar
- `app/Providers/AppServiceProvider.php` - Mendaftarkan observer
- `app/Providers/EventServiceProvider.php` - Menonaktifkan event listener lama
- `routes/web.php` - Menonaktifkan route `/whatsapp/test-send` untuk menghindari pengiriman ganda

> **Catatan**: Laravel 11 secara otomatis mendaftarkan command yang berada di direktori `app/Console/Commands`, sehingga tidak perlu mendaftarkan command secara eksplisit.

## 2. Perbarui File di WhatsApp Service (Opsional)

Jika Anda memiliki akses ke WhatsApp service, salin file `waservices/src/routes/api.js.new` ke `waservices/src/routes/api.js`:

```bash
cp waservices/src/routes/api.js.new waservices/src/routes/api.js
```

Jika tidak, tidak masalah karena kita sekarang selalu menggunakan curl langsung di Laravel.

## 3. Restart WhatsApp Service (Opsional)

Jika Anda telah memperbarui file di WhatsApp service, restart service:

```bash
cd waservices
pm2 restart waservice # nama service yang benar berdasarkan output pm2 list
```

## 4. Uji Pengiriman Pesan WhatsApp

### Menggunakan Command Artisan

```bash
# Uji dengan ID pengguna
php artisan whatsapp:test-welcome 1

# Uji dengan nomor telepon tertentu
php artisan whatsapp:test-welcome --phone=6281234567890
```

### Menggunakan Endpoint Test

Akses endpoint berikut di browser atau dengan curl:

```
https://tenderid.com/whatsapp/test-send
```

## 5. Periksa Log untuk Debugging

```bash
# Log Laravel
tail -f storage/logs/laravel.log | grep -i whatsapp

# Log WhatsApp Service
tail -f waservices/logs/combined.log
```

## Penjelasan Perbaikan

1. **Format Nomor Telepon**:
   - Menambahkan fungsi untuk memformat nomor telepon dengan benar (menambahkan kode negara 62)
   - Memastikan nomor telepon diformat dengan benar sebelum dikirim ke WhatsApp service

2. **Header API Key**:
   - Mengirim header API key dalam format lowercase (`x-api-key`) dan uppercase (`X-API-KEY`)
   - Memeriksa header API key dalam kedua format di WhatsApp service

3. **Logging yang Lebih Detail**:
   - Menambahkan logging yang lebih detail untuk memudahkan debugging
   - Mencatat semua tahapan pengiriman pesan WhatsApp

4. **Retry Logic**:
   - Meningkatkan logika retry untuk menangani kegagalan pengiriman
   - Menambahkan timeout yang lebih panjang untuk menghindari timeout saat mengirim request

5. **Validasi Data**:
   - Memastikan semua data yang diperlukan tersedia sebelum mengirim pesan
   - Memberikan nilai default untuk data yang tidak tersedia

## Catatan Penting

- Pastikan WhatsApp service terhubung dengan WhatsApp (cek status dengan endpoint `/auth/status`)
- Pastikan nomor telepon pengguna diisi dengan benar saat registrasi
- Pastikan queue worker berjalan jika menggunakan queue untuk event listener

Jika masih mengalami masalah, periksa log untuk informasi lebih detail tentang error yang terjadi.
