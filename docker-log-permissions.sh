#!/bin/bash

# Docker Environment Log Permissions Setup Script
# Optimized for containerized Laravel applications

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration for Docker environment
LOG_BASE_DIR="storage/logs"
DIR_PERMISSIONS="755"
FILE_PERMISSIONS="666"  # More permissive for container sharing

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to create log directories
create_log_directories() {
    print_status "Creating log directories for Docker environment..."
    
    local directories=(
        "${LOG_BASE_DIR}"
        "${LOG_BASE_DIR}/query"
        "${LOG_BASE_DIR}/telegram"
        "${LOG_BASE_DIR}/whatsapp"
        "${LOG_BASE_DIR}/sirup"
        "${LOG_BASE_DIR}/performance"
    )
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_success "Created directory: $dir"
        else
            print_status "Directory already exists: $dir"
        fi
        
        # Set directory permissions (more permissive for containers)
        chmod "$DIR_PERMISSIONS" "$dir" 2>/dev/null || print_warning "Could not set permissions for $dir"
        print_success "Set permissions for directory: $dir"
    done
}

# Function to fix existing log file permissions
fix_log_file_permissions() {
    print_status "Fixing log file permissions for Docker environment..."
    
    # Find all .log files in the logs directory
    find "$LOG_BASE_DIR" -name "*.log" -type f 2>/dev/null | while read -r file; do
        chmod "$FILE_PERMISSIONS" "$file" 2>/dev/null || print_warning "Could not set permissions for $file"
        print_success "Fixed permissions for: $(basename "$file")"
    done
}

# Function to create .gitignore for logs
create_gitignore() {
    print_status "Creating .gitignore for log directory..."
    
    local gitignore_file="${LOG_BASE_DIR}/.gitignore"
    
    cat > "$gitignore_file" << 'EOF'
# Ignore all log files
*.log
*.log.*

# Ignore compressed logs
*.gz
*.zip

# Ignore logrotate state
logrotate.state

# But keep the directory structure
!.gitignore
EOF

    chmod 644 "$gitignore_file" 2>/dev/null || print_warning "Could not set permissions for .gitignore"
    print_success "Created .gitignore for log directory"
}

# Function to create Laravel artisan command for ongoing maintenance
create_maintenance_command() {
    print_status "Setting up Laravel command for log maintenance..."
    
    # Check if our commands exist
    if [[ -f "app/Console/Commands/FixLogPermissions.php" ]]; then
        print_success "FixLogPermissions command already exists"
    else
        print_warning "FixLogPermissions command not found. Please run: php artisan make:command FixLogPermissions"
    fi
    
    if [[ -f "app/Console/Commands/AnalyzeSlowQueries.php" ]]; then
        print_success "AnalyzeSlowQueries command already exists"
    else
        print_warning "AnalyzeSlowQueries command not found. Please run: php artisan make:command AnalyzeSlowQueries"
    fi
}

# Function to test log writing
test_log_writing() {
    print_status "Testing log file writing in Docker environment..."
    
    local test_file="${LOG_BASE_DIR}/docker_permission_test.log"
    
    # Test writing
    if echo "Test log entry - $(date)" > "$test_file" 2>/dev/null; then
        print_success "Log writing test passed"
        rm -f "$test_file" 2>/dev/null
    else
        print_error "Log writing test failed - check permissions"
        return 1
    fi
}

# Function to show Docker-specific recommendations
show_docker_recommendations() {
    print_status "Docker Environment Recommendations:"
    echo
    print_status "1. Run this script periodically or add to your container startup:"
    print_status "   ./docker-log-permissions.sh"
    echo
    print_status "2. Use Laravel commands for ongoing maintenance:"
    print_status "   php artisan logs:fix-permissions"
    print_status "   php artisan query:analyze-slow"
    echo
    print_status "3. Consider adding to your Dockerfile or docker-compose:"
    print_status "   RUN chmod +x docker-log-permissions.sh && ./docker-log-permissions.sh"
    echo
    print_status "4. For log rotation in containers, consider using Docker's logging driver:"
    print_status "   --log-opt max-size=100m --log-opt max-file=3"
    echo
    print_status "5. Monitor logs with:"
    print_status "   docker logs <container_name> --tail 100 -f"
}

# Function to create Docker-specific logrotate config
create_docker_logrotate() {
    print_status "Creating Docker-optimized logrotate configuration..."
    
    local config_file="${LOG_BASE_DIR}/docker-logrotate.conf"
    
    cat > "$config_file" << EOF
# Docker-optimized Laravel Log Rotation Configuration
# For use inside containers

$(pwd)/${LOG_BASE_DIR}/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create ${FILE_PERMISSIONS}
    maxsize 100M
    postrotate
        # Signal Laravel to reopen log files if needed
        # kill -USR1 \$(cat /var/run/php-fpm.pid) 2>/dev/null || true
    endscript
}

$(pwd)/${LOG_BASE_DIR}/query/*.log {
    daily
    missingok
    rotate 5
    compress
    delaycompress
    notifempty
    create ${FILE_PERMISSIONS}
    maxsize 50M
}

$(pwd)/${LOG_BASE_DIR}/performance/*.log {
    daily
    missingok
    rotate 3
    compress
    delaycompress
    notifempty
    create ${FILE_PERMISSIONS}
    maxsize 20M
}
EOF

    chmod 644 "$config_file" 2>/dev/null || print_warning "Could not set permissions for logrotate config"
    print_success "Created Docker logrotate configuration: $config_file"
}

# Main execution
main() {
    print_status "Starting Docker environment log permissions setup..."
    echo
    
    # Check if we're in a Laravel project
    if [[ ! -f "artisan" ]]; then
        print_error "This doesn't appear to be a Laravel project (artisan file not found)"
        exit 1
    fi
    
    create_log_directories
    fix_log_file_permissions
    create_gitignore
    create_docker_logrotate
    create_maintenance_command
    test_log_writing
    
    echo
    print_success "Docker log permissions setup completed successfully!"
    echo
    print_status "Summary:"
    print_status "- Log directories created with ${DIR_PERMISSIONS} permissions"
    print_status "- Log files set to ${FILE_PERMISSIONS} permissions (container-friendly)"
    print_status "- Docker-optimized logrotate configuration created"
    print_status "- .gitignore created for log directory"
    echo
    
    show_docker_recommendations
}

# Run main function
main "$@"
