const express = require('express');
const rateLimit = require('express-rate-limit');
const whatsappController = require('../controllers/whatsappController');
const config = require('../config/config');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting middleware
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Get client IP from various headers
const getClientIP = (req) => {
  return req.headers['cf-connecting-ip'] || 
         req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress;
};

// API Key authentication middleware with improved logging
const authenticateApiKey = (req, res, next) => {
  // Check for API key in both lowercase and uppercase headers
  const apiKey = req.headers['x-api-key'] || req.headers['X-API-KEY'];
  const clientIP = getClientIP(req);
  
  // Log request details for debugging
  logger.debug('API request received', { 
    path: req.path,
    ip: clientIP,
    method: req.method,
    headers: Object.keys(req.headers),
    has_api_key: !!apiKey,
    api_key_length: apiKey ? apiKey.length : 0
  });

  if (!apiKey || apiKey !== config.apiKey) {
    logger.warn('Invalid API key attempt', { 
      ip: clientIP, 
      path: req.path,
      api_key_first_chars: apiKey ? apiKey.substring(0, 4) + '...' : 'none'
    });
    return res.status(401).json({
      success: false,
      error: 'Invalid API key'
    });
  }

  next();
};

// Request logging middleware with more details
const logRequest = (req, res, next) => {
  const clientIP = getClientIP(req);
  const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  req.requestId = requestId;
  
  logger.info('Request started', {
    id: requestId,
    method: req.method,
    path: req.path,
    ip: clientIP,
    user_agent: req.headers['user-agent']
  });
  
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.apiRequest(req.method, req.path, res.statusCode, duration, {
      id: requestId,
      ip: clientIP
    });
  });
  next();
};

// Apply middlewares to all routes
router.use(limiter);
router.use(authenticateApiKey);
router.use(logRequest);

// WhatsApp Authentication Routes
router.get('/auth/qr', whatsappController.getQR);
router.get('/auth/status', whatsappController.getStatus);

// Messaging Routes
router.post('/message/send', whatsappController.sendMessage);
router.post('/message/welcome', whatsappController.sendWelcomeMessage);

// Notification Routes
router.post('/notification/payment', whatsappController.sendPaymentNotification);
router.post('/notification/withdrawal', whatsappController.sendWithdrawalNotification);
router.post('/notification/system', whatsappController.sendSystemNotification);

// Debug route for testing API key
router.get('/debug', (req, res) => {
  res.json({
    success: true,
    message: 'API key is valid',
    ip: getClientIP(req),
    headers: req.headers,
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
router.use((err, req, res, next) => {
  logger.error('API Error', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

module.exports = router;
