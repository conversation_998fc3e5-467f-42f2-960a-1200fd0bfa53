require('dotenv').config();

module.exports = {
  port: process.env.PORT || 3000,
  apiKey: process.env.API_KEY,
  nodeEnv: process.env.NODE_ENV || 'development',
  allowedOrigins: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : ['http://localhost:8000'],
  messageTemplates: {
    // Template untuk registrasi berhasil ke user
    registrationUser: {
      id: 'registrationUser',
      template: `👋 Hai {name}, Selamat Datang di aplikasi tenderID.

✅ Terimakasih sudah mendaftar, Akun anda sudah berhasil d buat.

📧 Selanjutnya, kamu perlu melakukan verifikasi email, cek email dari tenderID pada email yang baru kamu daftarkan.

📥 Kalau tidak ada di Kotak Masuk (Inbox), jangan lupa cek di Spam juga ya.

🙏 Terimakasih..
Salam tenderID.com`
    },

    // Template untuk notifikasi registrasi ke Super Admin
    registrationAdmin: {
      id: 'registrationAdmin',
      template: `📢 *NOTIFIKASI USER BARU*

User baru telah mendaftar dengan detail berikut:
Nama: {name}
Email: {email}
Nomor Telepon: {phone}

Mohon tindak lanjut sesuai prosedur.`
    },

    // Template untuk notifikasi sistem
    notification: {
      id: 'notification',
      template: `📢 *NOTIFIKASI SISTEM*

{message}

Waktu: {timestamp}

Mohon perhatikan notifikasi ini.`
    },

    // Template untuk pembayaran berhasil
    paymentSuccess: {
      id: 'payment_success',
      template: `✅ *PEMBAYARAN BERHASIL*

Detail Pembayaran:
📅 Tanggal: {date}
💰 Jumlah: Rp {amount}
📝 Deskripsi: {description}

Terima kasih atas pembayaran Anda!`
    },

    // Template untuk pembayaran gagal
    paymentFailed: {
      id: 'payment_failed',
      template: `❌ *PEMBAYARAN GAGAL*

Detail Pembayaran:
📅 Tanggal: {date}
💰 Jumlah: Rp {amount}
❌ Alasan: {reason}

Mohon coba lagi atau hubungi tim support kami.`
    },

    // Template untuk withdrawal berhasil
    withdrawalSuccess: {
      id: 'withdrawal_success',
      template: `💰 *WITHDRAWAL BERHASIL*

Detail Withdrawal:
📅 Tanggal: {date}
💰 Jumlah: Rp {amount}
🏦 Bank: {bank}
📝 Status: Berhasil

Dana telah dikirim ke rekening Anda.`
    },

    // Template untuk withdrawal gagal
    withdrawalFailed: {
      id: 'withdrawal_failed',
      template: `❌ *WITHDRAWAL GAGAL*

Detail Withdrawal:
📅 Tanggal: {date}
💰 Jumlah: Rp {amount}
❌ Alasan: {reason}

Mohon hubungi tim support untuk informasi lebih lanjut.`
    },

    // Template untuk pesan format command
    commandHelp: {
      id: 'command_help',
      template: `🔍 *PANDUAN PERINTAH*

Berikut format perintah yang tersedia:

1. !status - Cek status langganan
2. !saldo - Cek saldo
3. !withdraw <jumlah> - Tarik dana
4. !help - Tampilkan panduan ini

Contoh: !withdraw 100000`
    },

    // Template untuk konfirmasi pembayaran
    paymentConfirmation: {
      id: 'paymentConfirmation',
      template: `💰 *KONFIRMASI PEMBAYARAN DITERIMA*

Admin yang terhormat,

Kami telah menerima konfirmasi pembayaran dengan detail berikut:

👤 User: {user_name} ({user_email})
📦 Paket: {package}
💵 Jumlah: Rp {amount}
📅 Tanggal: {date}
💳 Metode: {payment_method}

Mohon segera verifikasi pembayaran ini melalui panel admin.
Terima kasih.`
    }
  }
};
