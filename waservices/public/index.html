<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Integration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
            <div class="text-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800 mb-2">WhatsApp Integration</h1>
                <p class="text-gray-600" id="status">Waiting for QR Code...</p>
            </div>
            
            <div id="qr-container" class="flex justify-center mb-6">
                <img id="qr-code" src="" alt="QR Code" class="hidden max-w-[200px]">
            </div>

            <div id="connection-status" class="text-center mb-6 hidden">
                <i class="fas fa-check-circle text-4xl text-green-500 mb-2"></i>
                <p class="text-green-600 font-semibold">Connected Successfully!</p>
            </div>

            <div class="space-y-4">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h2 class="font-semibold text-gray-700 mb-2">Status</h2>
                    <p id="connection-info" class="text-gray-600">Not connected</p>
                </div>

                <div id="error-container" class="hidden p-4 bg-red-50 rounded-lg">
                    <p id="error-message" class="text-red-600"></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_KEY = 'tenderID';
        const API_BASE_URL = 'https://waservices.tenderid.com/api';
        let checkStatusInterval;

        function showError(message) {
            const errorContainer = document.getElementById('error-container');
            const errorMessage = document.getElementById('error-message');
            errorContainer.classList.remove('hidden');
            errorMessage.textContent = message;
        }

        function hideError() {
            const errorContainer = document.getElementById('error-container');
            errorContainer.classList.add('hidden');
        }

        async function fetchWithTimeout(resource, options = {}) {
            const { timeout = 5000 } = options;
            const controller = new AbortController();
            const id = setTimeout(() => controller.abort(), timeout);

            try {
                const response = await fetch(resource, {
                    ...options,
                    signal: controller.signal
                });
                clearTimeout(id);
                return response;
            } catch (error) {
                clearTimeout(id);
                throw error;
            }
        }

        async function getQRCode() {
            try {
                hideError();
                const response = await fetchWithTimeout(`${API_BASE_URL}/auth/qr`, {
                    headers: {
                        'x-api-key': API_KEY
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                if (data.success && data.qrCode) {
                    const qrImage = document.getElementById('qr-code');
                    qrImage.src = data.qrCode;
                    qrImage.classList.remove('hidden');
                    startStatusCheck();
                } else {
                    throw new Error('Invalid response format');
                }
            } catch (error) {
                console.error('Error fetching QR code:', error);
                showError('Error loading QR Code. Please refresh the page to try again.');
                document.getElementById('status').textContent = 'Error loading QR Code';
            }
        }

        async function checkStatus() {
            try {
                const response = await fetchWithTimeout(`${API_BASE_URL}/auth/status`, {
                    headers: {
                        'x-api-key': API_KEY
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                if (data.success && data.status.ready) {
                    document.getElementById('qr-container').classList.add('hidden');
                    document.getElementById('connection-status').classList.remove('hidden');
                    document.getElementById('connection-info').textContent = 'Connected to WhatsApp';
                    document.getElementById('status').textContent = 'Connected to WhatsApp';
                    clearInterval(checkStatusInterval);
                    hideError();
                }
            } catch (error) {
                console.error('Error checking status:', error);
                showError('Error checking connection status');
            }
        }

        function startStatusCheck() {
            if (checkStatusInterval) {
                clearInterval(checkStatusInterval);
            }
            checkStatusInterval = setInterval(checkStatus, 5000);
        }

        // Initialize
        getQRCode();

        // Cleanup
        window.addEventListener('beforeunload', () => {
            if (checkStatusInterval) {
                clearInterval(checkStatusInterval);
            }
        });
    </script>
</body>
</html>
