// Tender Analysis Charts JavaScript Functions

function renderTenderTrendsChart(data) {
    if (charts.tenderTrends) {
        charts.tenderTrends.destroy();
    }

    const options = {
        series: [{
            name: '<PERSON><PERSON><PERSON>',
            data: data.map(item => ({
                x: item.month_name,
                y: item.total_tenders
            }))
        }, {
            name: '<PERSON> Nilai (Miliar)',
            data: data.map(item => ({
                x: item.month_name,
                y: Math.round(item.total_value / 1000000000)
            }))
        }],
        chart: {
            type: 'line',
            height: 350,
            toolbar: { show: true }
        },
        colors: ['#4e73df', '#1cc88a'],
        stroke: {
            curve: 'smooth',
            width: 3
        },
        xaxis: {
            type: 'category'
        },
        yaxis: [{
            title: { text: 'Ju<PERSON><PERSON> Tender' }
        }, {
            opposite: true,
            title: { text: '<PERSON><PERSON> (Miliar Rupiah)' }
        }],
        tooltip: {
            shared: true,
            intersect: false
        },
        legend: {
            position: 'top'
        }
    };

    charts.tenderTrends = new ApexCharts(document.querySelector("#tender-trends-chart"), options);
    charts.tenderTrends.render();
}

function renderTopLpseList(data) {
    let html = '';

    if (!data || data.length === 0) {
        html = `
            <div class="text-center py-4">
                <i class="material-icons-outlined text-muted" style="font-size: 3rem;">location_city</i>
                <p class="text-muted mt-2 mb-0">Tidak ada data LPSE</p>
                <small class="text-muted">Coba ubah periode waktu</small>
            </div>
        `;
    } else {
        data.forEach((lpse, index) => {
            const rank = index + 1;
            const rankBadge = rank <= 3 ?
                `<div class="position-absolute top-0 end-0 mt-2 me-2">
                    <span class="badge bg-warning px-2 py-1">
                        <i class="material-icons-outlined" style="font-size: 12px; vertical-align: text-bottom;">emoji_events</i>
                        #${rank}
                    </span>
                 </div>` : '';

            // Generate logo HTML
            const logoHtml = lpse.logo_path ?
                `<img src="${lpse.logo_path.replace('http://proyeklpse.test', '')}" alt="${lpse.nama_lpse}" class="rounded-3" style="width: 48px; height: 48px; object-fit: cover;">` :
                `<div class="d-flex align-items-center justify-content-center bg-light rounded-3" style="width: 48px; height: 48px;">
                    <i class="material-icons-outlined text-primary" style="font-size: 24px;">business</i>
                 </div>`;

            html += `
                <div class="vendor-card position-relative p-3 mb-3 rounded shadow-sm border">
                    ${rankBadge}
                    <div class="d-flex align-items-center mb-2">
                        <div class="me-3">
                            ${logoHtml}
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0 fw-bold text-dark">
                                <a href="/lpse/${lpse.kd_lpse}/dashboard" class="text-decoration-none text-dark">
                                    ${lpse.nama_lpse}
                                </a>
                            </h6>
                            <div class="small text-muted d-flex align-items-center">
                                <i class="material-icons-outlined me-1" style="font-size: 14px;">place</i>
                                ${lpse.provinsi}
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-3 pt-2 border-top">
                        <div>
                            <div class="d-flex align-items-center">
                                <i class="material-icons-outlined text-primary me-1" style="font-size: 16px;">gavel</i>
                                <span class="fw-bold">${formatNumber(lpse.total_tenders)}</span>
                                <span class="ms-1 text-muted small">tender</span>
                            </div>
                            <div class="mt-1">
                                <span class="fw-bold text-success">${formatCurrency(lpse.total_value)}</span>
                                <span class="text-muted small">nilai</span>
                            </div>
                            <div class="mt-1">
                                <i class="material-icons-outlined text-info me-1" style="font-size: 14px;">groups</i>
                                <span class="small">${formatNumber(lpse.avg_participants)} peserta rata-rata</span>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="/lpse/${lpse.kd_lpse}/dashboard" class="btn btn-sm btn-light border lpse-dashboard-btn" title="Dashboard LPSE">
                                <i class="material-icons-outlined text-primary" style="font-size: 16px; vertical-align: text-bottom;">dashboard</i>
                                <span class="d-none d-sm-inline ms-1">Dashboard</span>
                            </a>
                        </div>
                    </div>
                </div>
            `;
        });
    }

    $('#top-lpse-list').html(html);
}

function renderCategoryChart(data) {
    if (charts.category) {
        charts.category.destroy();
    }

    const options = {
        series: [{
            name: 'Jumlah Tender',
            data: data.map(item => item.total_tenders)
        }],
        chart: {
            type: 'bar',
            height: 350
        },
        colors: ['#4e73df'],
        xaxis: {
            categories: data.map(item => item.kategori_pekerjaan),
            labels: {
                rotate: -45,
                style: { fontSize: '10px' }
            }
        },
        yaxis: {
            title: { text: 'Jumlah Tender' }
        },
        tooltip: {
            y: {
                formatter: function(val, opts) {
                    const item = data[opts.dataPointIndex];
                    return `${val} tender<br/>Nilai: ${formatCurrency(item.total_value)}<br/>Rata-rata: ${formatCurrency(item.avg_value)}`;
                }
            }
        }
    };

    charts.category = new ApexCharts(document.querySelector("#category-chart"), options);
    charts.category.render();
}

function renderMethodChart(data) {
    if (charts.method) {
        charts.method.destroy();
    }

    const options = {
        series: data.map(item => item.total_tenders),
        chart: {
            type: 'donut',
            height: 350
        },
        labels: data.map(item => item.metode_pemilihan),
        colors: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
        legend: {
            position: 'bottom'
        },
        tooltip: {
            y: {
                formatter: function(val, opts) {
                    const item = data[opts.seriesIndex];
                    return `${val} tender<br/>Nilai: ${formatCurrency(item.total_value)}`;
                }
            }
        }
    };

    charts.method = new ApexCharts(document.querySelector("#method-chart"), options);
    charts.method.render();
}

function renderValueDistributionChart(data) {
    if (charts.valueDistribution) {
        charts.valueDistribution.destroy();
    }

    const options = {
        series: [{
            name: 'Jumlah Tender',
            data: data.map(item => item.total_tenders)
        }],
        chart: {
            type: 'bar',
            height: 350
        },
        colors: ['#1cc88a'],
        xaxis: {
            categories: data.map(item => item.value_range)
        },
        yaxis: {
            title: { text: 'Jumlah Tender' }
        },
        tooltip: {
            y: {
                formatter: function(val, opts) {
                    const item = data[opts.dataPointIndex];
                    return `${val} tender<br/>Total Nilai: ${formatCurrency(item.total_value)}`;
                }
            }
        }
    };

    charts.valueDistribution = new ApexCharts(document.querySelector("#value-distribution-chart"), options);
    charts.valueDistribution.render();
}

function renderDurationChart(data) {
    if (charts.duration) {
        charts.duration.destroy();
    }

    const options = {
        series: [{
            name: 'Jumlah Tender',
            data: data.map(item => item.total_tenders)
        }, {
            name: 'Rata-rata Peserta',
            data: data.map(item => Math.round(item.avg_participants))
        }],
        chart: {
            type: 'bar',
            height: 350
        },
        colors: ['#f6c23e', '#36b9cc'],
        xaxis: {
            categories: data.map(item => item.duration_range)
        },
        yaxis: {
            title: { text: 'Jumlah' }
        },
        legend: {
            position: 'top'
        }
    };

    charts.duration = new ApexCharts(document.querySelector("#duration-chart"), options);
    charts.duration.render();
}

function renderActiveCompaniesTable(data) {
    console.log('=== renderActiveCompaniesTable (external) START ===');
    console.log('Data received:', data);
    console.log('Data type:', typeof data);
    console.log('Is array:', Array.isArray(data));
    console.log('Data length:', data ? data.length : 'null');

    let html = '';

    // Check if data is an error response
    if (data && data.error) {
        console.error('API returned error:', data.error);
        html = `<tr>
            <td colspan="4" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                    <i class="material-icons-outlined" style="font-size: 2rem; color: #dc3545;">error</i>
                    <p class="mt-2 mb-0 text-danger">Error: ${data.error}</p>
                    <small class="text-muted">Periksa log untuk detail</small>
                </div>
            </td>
        </tr>`;
    } else if (!data || !Array.isArray(data) || data.length === 0) {
        console.log('No data available');
        html = `<tr>
            <td colspan="4" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                    <i class="material-icons-outlined" style="font-size: 2rem; color: #ccc;">business_center</i>
                    <p class="mt-2 mb-0 text-muted">Tidak ada data perusahaan aktif</p>
                    <small class="text-muted">Coba ubah periode waktu</small>
                </div>
            </td>
        </tr>`;
    } else {
        // console.log('Processing', data.length, 'companies');

        data.slice(0, 10).forEach((company, index) => {
            console.log(`Processing company ${index + 1}:`, company);

            const winRate = parseFloat(company.win_rate) || 0;
            const participation = parseInt(company.total_participation) || 0;

            let winRateBadge = '';
            if (winRate >= 70) {
                winRateBadge = 'badge bg-success';
            } else if (winRate >= 30) {
                winRateBadge = 'badge bg-warning';
            } else {
                winRateBadge = 'badge bg-danger';
            }

            console.log(`Company ${index + 1} processed:`, {
                name: company.nama_peserta,
                participation: participation,
                winRate: winRate,
                badgeClass: winRateBadge,
                npwp: company.npwp,
                npwp_blur: company.npwp_blur,
                npwp_display: company.npwp || company.npwp_blur || 'NPWP tidak tersedia'
            });

            html += `<tr>
                <td class="text-center">
                    <span class="badge bg-secondary">${index + 1}</span>
                </td>
                <td>
                    <a href="#" class="company-link text-decoration-none fw-bold text-primary" data-pembeda="${company.pembeda}">
                        ${company.nama_peserta}
                    </a>
                    <br><small class="text-muted">NPWP: <code class="text-muted">${company.npwp || company.npwp_blur || 'tidak tersedia'}</code></small>
                </td>
                <td class="text-center">
                    <span class="badge bg-primary">${participation}</span>
                </td>
                <td class="text-center">
                    <span class="${winRateBadge}">${winRate}%</span>
                </td>
            </tr>`;
        });
    }

    console.log('Generated HTML length:', html.length);
    console.log('HTML preview:', html.substring(0, 300));

    const tableBody = $('#active-companies-table tbody');
    console.log('Table body element found:', tableBody.length > 0);

    tableBody.html(html);

    console.log('=== renderActiveCompaniesTable (external) END ===');
}

function renderTopWinnersTable(data) {
    console.log('renderTopWinnersTable called with:', data);
    let html = '';

    if (!data || data.length === 0) {
        html = `<tr>
            <td colspan="4" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                    <i class="material-icons-outlined" style="font-size: 2rem; color: #ccc;">emoji_events</i>
                    <p class="mt-2 mb-0 text-muted">Tidak ada data pemenang tender</p>
                    <small class="text-muted">Coba ubah periode waktu</small>
                </div>
            </td>
        </tr>`;
    } else {
        data.slice(0, 10).forEach((company, index) => {
            console.log('Top winner data:', {
                name: company.nama_peserta,
                wins: company.total_wins,
                value: company.total_value
            });

            html += `<tr>
                <td class="text-center">
                    <span class="badge bg-secondary">${index + 1}</span>
                </td>
                <td>
                    <a href="#" class="company-link text-decoration-none fw-bold text-primary" data-pembeda="${company.pembeda}">
                        ${company.nama_peserta}
                    </a>
                    <br><small class="text-muted">NPWP: <code class="text-muted">${company.npwp || company.npwp_blur || 'tidak tersedia'}</code></small>
                </td>
                <td class="text-center">
                    <span class="badge bg-success">${formatNumber(company.total_wins)}</span>
                </td>
                <td class="text-center">
                    <span class="badge bg-info">${formatCurrency(company.total_value)}</span>
                </td>
            </tr>`;
        });
    }
    $('#top-winners-table tbody').html(html);
}

function renderFraudAnalysisTable(data) {
    console.log('renderFraudAnalysisTable called with:', data);
    let html = '';

    if (!data || data.length === 0) {
        html = `<tr>
            <td colspan="5" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                    <i class="material-icons-outlined" style="font-size: 2rem; color: #ccc;">security</i>
                    <p class="mt-2 mb-0 text-muted">Tidak ada indikasi fraud terdeteksi</p>
                    <small class="text-muted">Atau coba ubah periode waktu</small>
                </div>
            </td>
        </tr>`;
    } else {
        data.slice(0, 15).forEach((tender) => {
            const riskClass = tender.risk_score >= 80 ? 'bg-danger' :
                             tender.risk_score >= 60 ? 'bg-warning' : 'bg-success';
            const riskText = tender.risk_score >= 80 ? 'Tinggi' :
                            tender.risk_score >= 60 ? 'Sedang' : 'Rendah';

            // Format fraud indicators as simple text
            let indicators = '';
            if (Array.isArray(tender.fraud_indicators) && tender.fraud_indicators.length > 0) {
                indicators = tender.fraud_indicators.join('; ');
            } else if (tender.fraud_indicators) {
                indicators = tender.fraud_indicators;
            } else {
                indicators = '-';
            }

            html += `<tr>
                <td>
                    <div>
                        <a href="/tender/${tender.kode_tender}" target="_blank" class="text-decoration-none">
                            <strong class="text-primary">${tender.nama_paket}</strong>
                        </a>
                        <br><small class="text-muted">${tender.kode_tender}</small>
                    </div>
                </td>
                <td>
                    <div class="instansi-lpse-info">
                        <!-- LPSE Information -->
                        <div class="d-flex align-items-center mb-2">
                            ${tender.logo_path ?
                                `<img src="${tender.logo_path}" alt="Logo LPSE" class="lpse-logo me-2" style="width: 32px; height: 32px; object-fit: contain;">` :
                                `<div class="lpse-logo-placeholder me-2" style="width: 32px; height: 32px;"><i class="material-icons-outlined text-muted">business</i></div>`
                            }
                            <div>
                                <a href="/lpse/${tender.kd_lpse || ''}/dashboard" target="_blank" class="text-decoration-none">
                                    <div class="fw-bold text-primary" style="font-size: 0.9rem;">${tender.nama_lpse || `LPSE ${tender.kd_lpse}`}</div>
                                </a>

                            </div>
                        </div>

                    </div>
                </td>
                <td>
                    <div>
                        <a href="#" class="text-decoration-none company-link" data-pembeda="${tender.pembeda}">
                            <span class="fw-bold text-primary">${tender.pemenang}</span>
                        </a>
                        <br><small class="text-muted">NPWP: <code>${tender.npwp || tender.npwp_blur || 'tidak tersedia'}</code></small>
                    </div>
                </td>
                <td>
                    <small class="text-muted">${indicators}</small>
                </td>
                <td class="text-center">
                    <span class="badge ${riskClass}">${tender.risk_score}% ${riskText}</span>
                </td>
            </tr>`;
        });
    }
    $('#fraud-analysis-table tbody').html(html);
}

function renderRegionalChart(data) {
    if (charts.regional) {
        charts.regional.destroy();
    }

    // Sort data by total_tenders and take top 15
    const sortedData = data.sort((a, b) => b.total_tenders - a.total_tenders).slice(0, 15);

    const options = {
        series: [{
            name: 'Jumlah Tender',
            data: sortedData.map(item => item.total_tenders)
        }],
        chart: {
            type: 'bar',
            height: 400,
            toolbar: { show: true }
        },
        colors: ['#e74a3b'],
        xaxis: {
            categories: sortedData.map(item => item.provinsi),
            labels: {
                rotate: -45,
                style: { fontSize: '10px' }
            }
        },
        yaxis: {
            title: { text: 'Jumlah Tender' }
        },
        tooltip: {
            y: {
                formatter: function(val, opts) {
                    const item = sortedData[opts.dataPointIndex];
                    return `${val} tender<br/>Total Nilai: ${formatCurrency(item.total_value)}<br/>Rata-rata Nilai: ${formatCurrency(item.avg_value)}`;
                }
            }
        }
    };

    charts.regional = new ApexCharts(document.querySelector("#regional-chart"), options);
    charts.regional.render();
}

// Utility functions
function formatNumber(num) {
    if (num === null || num === undefined) return '-';
    return new Intl.NumberFormat('id-ID').format(num);
}

function formatCurrency(amount) {
    if (amount === null || amount === undefined || amount === 0) return '-';

    if (amount >= 1000000000000) {
        return 'Rp ' + (amount / 1000000000000).toFixed(1) + ' T';
    } else if (amount >= 1000000000) {
        return 'Rp ' + (amount / 1000000000).toFixed(1) + ' M';
    } else if (amount >= 1000000) {
        return 'Rp ' + (amount / 1000000).toFixed(1) + ' Jt';
    } else {
        return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
    }
}

function exportChart(chartId) {
    const chart = charts[chartId.replace('-chart', '').replace('-', '')];
    if (chart) {
        chart.dataURI().then(({ imgURI, blob }) => {
            const link = document.createElement('a');
            link.href = imgURI;
            link.download = `${chartId}-${new Date().toISOString().split('T')[0]}.png`;
            link.click();
        });
    }
}
