{"version": 3, "sources": ["../src/util.js", "../src/index.js"], "names": ["<PERSON><PERSON>", "$", "TRANSITION_END", "[object Object]", "element", "trigger", "supportsTransitionEnd", "Boolean", "transitionEndEmulator", "duration", "called", "this", "one", "setTimeout", "triggerTransitionEnd", "fn", "mmEmulateTransitionEnd", "event", "special", "bindType", "delegateType", "target", "is", "handleObj", "handler", "apply", "arguments", "NAME", "DATA_KEY", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON>", "toggle", "preventDefault", "triggerElement", "parentTrigger", "subMenu", "Event", "SHOW", "SHOWN", "HIDE", "HIDDEN", "CLICK_DATA_API", "ClassName", "MetisMenu", "config", "transitioning", "init", "self", "conf", "el", "addClass", "find", "children", "attr", "parents", "has", "not", "on", "e", "eTar", "paRent", "parent", "sibLi", "siblings", "sib<PERSON><PERSON>ger", "hasClass", "removeActive", "setActive", "onTransitionStart", "li", "ul", "length", "show", "removeClass", "hide", "elem", "startEvent", "isDefaultPrevented", "toggleElem", "height", "setTransitioning", "scrollHeight", "offsetHeight", "complete", "onTransitionEnd", "css", "isTransitioning", "removeData", "off", "each", "$this", "data", "undefined", "Error", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;6VAEA,MAAMA,EAAO,CAAEC,IACb,MAAMC,EAAiB,gBAEjBF,EAAO,CACXE,eAAgB,kBAEhBC,qBAAqBC,GACnBH,EAAEG,GAASC,QAAQH,IAGrBI,sBAAqB,IACZC,QAAQL,IAoBnB,SAASM,EAAsBC,GAC7B,IAAIC,GAAS,EAYb,OAVAT,EAAEU,MAAMC,IAAIZ,EAAKE,gBAAgB,KAC/BQ,GAAS,KAGXG,YAAW,KACJH,GACHV,EAAKc,qBAAqBH,QAE3BF,GAEIE,KAWT,OAPEV,EAAEc,GAAGC,uBAAyBR,EAE9BP,EAAEgB,MAAMC,QAAQlB,EAAKE,gBAlCd,CACLiB,SAAUjB,EACVkB,aAAclB,EACdC,OAAOc,GACL,GAAIhB,EAAEgB,EAAMI,QAAQC,GAAGX,MACrB,OAAOM,EACJM,UACAC,QACAC,MAAMd,KAAMe,aA+BhB1B,GAvDI,CAwDVC,EAAAA,SCvDG0B,EAAO,YACPC,EAAW,YAGXC,EAAqB5B,EAAAA,QAAEc,GAAGY,GAG1BG,EAAU,CACdC,QAAQ,EACRC,gBAAgB,EAChBC,eAAgB,IAChBC,cAAe,KACfC,QAAS,MAGLC,EAAQ,CACZC,KAAM,iBACNC,MAAO,kBACPC,KAAM,iBACNC,OAAQ,mBACRC,eAAgB,4BAGZC,EACG,YADHA,EAEI,YAFJA,EAGE,UAHFA,EAIM,cAJNA,EAKQ,gBAId,MAAMC,EAEJxC,YAAYC,EAASwC,GACnBjC,KAAKP,QAAUA,EACfO,KAAKiC,OAAS,IACTd,KACAc,GAELjC,KAAKkC,cAAgB,KAErBlC,KAAKmC,OAGP3C,OACE,MAAM4C,EAAOpC,KACPqC,EAAOrC,KAAKiC,OACZK,EAAKhD,EAAAA,QAAEU,KAAKP,SAElB6C,EAAGC,SAASR,GAEZO,EAAGE,KAAK,GAAGH,EAAKd,iBAAiBQ,KAC9BU,SAASJ,EAAKf,gBACdoB,KAAK,gBAAiB,QAEzBJ,EAAGE,KAAK,GAAGH,EAAKd,iBAAiBQ,KAC9BY,QAAQN,EAAKd,eACbgB,SAASR,GAEZO,EAAGE,KAAK,GAAGH,EAAKd,iBAAiBQ,KAC9BY,QAAQN,EAAKd,eACbkB,SAASJ,EAAKf,gBACdoB,KAAK,gBAAiB,QAEzBJ,EAAGE,KAAK,GAAGH,EAAKd,iBAAiBQ,KAC9Ba,IAAIP,EAAKb,SACTiB,SAASJ,EAAKb,SACde,SAAS,GAAGR,KAAsBA,KAErCO,EACGE,KAAKH,EAAKd,eACVsB,IAAI,IAAId,KACRa,IAAIP,EAAKb,SACTiB,SAASJ,EAAKb,SACde,SAASR,GAEZO,EACGE,KAAKH,EAAKd,eAEVkB,SAASJ,EAAKf,gBACdwB,GAAGrB,EAAMK,gBAAgB,SAAUiB,GAClC,MAAMC,EAAO1D,EAAAA,QAAEU,MAEf,GAAmC,SAA/BgD,EAAKN,KAAK,iBACZ,OAGEL,EAAKhB,gBAAwC,MAAtB2B,EAAKN,KAAK,SACnCK,EAAE1B,iBAGJ,MAAM4B,EAASD,EAAKE,OAAOb,EAAKd,eAC1B4B,EAAQF,EAAOG,SAASf,EAAKd,eAC7B8B,EAAaF,EAAMV,SAASJ,EAAKf,gBAEnC2B,EAAOK,SAASvB,IAClBiB,EAAKN,KAAK,gBAAiB,SAC3BN,EAAKmB,aAAaN,KAElBD,EAAKN,KAAK,gBAAiB,QAC3BN,EAAKoB,UAAUP,GACXZ,EAAKjB,SACPgB,EAAKmB,aAAaJ,GAClBE,EAAWX,KAAK,gBAAiB,WAIjCL,EAAKoB,mBACPpB,EAAKoB,kBAAkBV,MAK/BvD,UAAUkE,GACRpE,EAAAA,QAAEoE,GAAInB,SAASR,GACf,MAAM4B,EAAKrE,EAAAA,QAAEoE,GAAIjB,SAASzC,KAAKiC,OAAOT,SAClCmC,EAAGC,OAAS,IAAMD,EAAGL,SAASvB,IAChC/B,KAAK6D,KAAKF,GAIdnE,aAAakE,GACXpE,EAAAA,QAAEoE,GAAII,YAAY/B,GAClB,MAAM4B,EAAKrE,EAAAA,QAAEoE,GAAIjB,SAAS,GAAGzC,KAAKiC,OAAOT,WAAWO,KAChD4B,EAAGC,OAAS,GACd5D,KAAK+D,KAAKJ,GAIdnE,KAAKC,GACH,GAAIO,KAAKkC,eAAiB5C,EAAAA,QAAEG,GAAS6D,SAASvB,GAC5C,OAEF,MAAMiC,EAAO1E,EAAAA,QAAEG,GAETwE,EAAa3E,EAAAA,QAAEmC,MAAMA,EAAMC,MAGjC,GAFAsC,EAAKtE,QAAQuE,GAETA,EAAWC,qBACb,OAKF,GAFAF,EAAKd,OAAOlD,KAAKiC,OAAOV,eAAegB,SAASR,GAE5C/B,KAAKiC,OAAOb,OAAQ,CACtB,MAAM+C,EAAaH,EAAKd,OAAOlD,KAAKiC,OAAOV,eAAe6B,WAAWX,SAAS,GAAGzC,KAAKiC,OAAOT,WAAWO,KACxG/B,KAAK+D,KAAKI,GAGZH,EACGF,YAAY/B,GACZQ,SAASR,GACTqC,OAAO,GAEVpE,KAAKqE,kBAAiB,GAiBtBL,EACGI,OAAO3E,EAAQ,GAAG6E,cAClBrE,IAAIZ,EAAKE,gBAjBK,KAEVS,KAAKiC,QAAWjC,KAAKP,UAG1BuE,EACGF,YAAY/B,GACZQ,SAAS,GAAGR,KAAsBA,KAClCqC,OAAO,IAEVpE,KAAKqE,kBAAiB,GAEtBL,EAAKtE,QAAQ+B,EAAME,WAMlBtB,uBA1KqB,KA6K1Bb,KAAKC,GACH,GACEO,KAAKkC,gBAAkB5C,EAAAA,QAAEG,GAAS6D,SAASvB,GAE3C,OAGF,MAAMiC,EAAO1E,EAAAA,QAAEG,GAETwE,EAAa3E,EAAAA,QAAEmC,MAAMA,EAAMG,MAGjC,GAFAoC,EAAKtE,QAAQuE,GAETA,EAAWC,qBACb,OAGFF,EAAKd,OAAOlD,KAAKiC,OAAOV,eAAeuC,YAAY/B,GAEnDiC,EAAKI,OAAOJ,EAAKI,UAAU,GAAGG,aAE9BP,EACGzB,SAASR,GACT+B,YAAY/B,GACZ+B,YAAY/B,GAEf/B,KAAKqE,kBAAiB,GAEtB,MAAMG,EAAW,KAEVxE,KAAKiC,QAAWjC,KAAKP,UAGtBO,KAAKkC,eAAiBlC,KAAKiC,OAAOwC,iBACpCzE,KAAKiC,OAAOwC,kBAGdzE,KAAKqE,kBAAiB,GACtBL,EAAKtE,QAAQ+B,EAAMI,QAEnBmC,EACGF,YAAY/B,GACZQ,SAASR,KAGQ,IAAlBiC,EAAKI,UAA0C,SAAxBJ,EAAKU,IAAI,WAClCF,IAEAR,EACGI,OAAO,GACPnE,IAAIZ,EAAKE,eAAgBiF,GACzBnE,uBA/NmB,KAmO1Bb,iBAAiBmF,GACf3E,KAAKkC,cAAgByC,EAGvBnF,UACEF,EAAAA,QAAEsF,WAAW5E,KAAKP,QAASwB,GAE3B3B,EAAAA,QAAEU,KAAKP,SACJ+C,KAAKxC,KAAKiC,OAAOV,eAEjBkB,SAASzC,KAAKiC,OAAOX,gBACrBuD,IAAIpD,EAAMK,gBAEb9B,KAAKkC,cAAgB,KACrBlC,KAAKiC,OAAS,KACdjC,KAAKP,QAAU,KAGjBD,uBAAuByC,GAErB,OAAOjC,KAAK8E,MAAK,WACf,MAAMC,EAAQzF,EAAAA,QAAEU,MAChB,IAAIgF,EAAOD,EAAMC,KAAK/D,GACtB,MAAMoB,EAAO,IACRlB,KACA4D,EAAMC,UACa,iBAAX/C,GAAuBA,EAASA,EAAS,IAQtD,GALK+C,IACHA,EAAO,IAAIhD,EAAUhC,KAAMqC,GAC3B0C,EAAMC,KAAK/D,EAAU+D,IAGD,iBAAX/C,EAAqB,CAC9B,QAAqBgD,IAAjBD,EAAK/C,GACP,MAAM,IAAIiD,MAAM,oBAAoBjD,MAEtC+C,EAAK/C,iBAWb3C,EAAAA,QAAEc,GAAGY,GAAQgB,EAAUmD,gBACvB7F,EAAAA,QAAEc,GAAGY,GAAMoE,YAAcpD,EACzB1C,EAAAA,QAAEc,GAAGY,GAAMqE,WAAa,KAEtB/F,EAAAA,QAAEc,GAAGY,GAAQE,EACNc,EAAUmD", "sourcesContent": ["import $ from 'jquery';\n\nconst Util = (($) => { // eslint-disable-line no-shadow\n  const TRANSITION_END = 'transitionend';\n\n  const Util = { // eslint-disable-line no-shadow\n    TRANSITION_END: 'mmTransitionEnd',\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(TRANSITION_END);\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(TRANSITION_END);\n    },\n  };\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: TRANSITION_END,\n      delegateType: TRANSITION_END,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event\n            .handleObj\n            .handler\n            .apply(this, arguments); // eslint-disable-line prefer-rest-params\n        }\n        return undefined;\n      },\n    };\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false;\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true;\n    });\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this);\n      }\n    }, duration);\n\n    return this;\n  }\n\n  function setTransitionEndSupport() {\n    $.fn.mmEmulateTransitionEnd = transitionEndEmulator; // eslint-disable-line no-param-reassign\n    // eslint-disable-next-line no-param-reassign\n    $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent();\n  }\n\n  setTransitionEndSupport();\n\n  return Util;\n})($);\n\nexport default Util;\n", "import $ from 'jquery';\nimport Util from './util';\n\nconst NAME = 'metisMenu';\nconst DATA_KEY = 'metisMenu';\nconst EVENT_KEY = `.${DATA_KEY}`;\nconst DATA_API_KEY = '.data-api';\nconst JQUERY_NO_CONFLICT = $.fn[NAME];\nconst TRANSITION_DURATION = 350;\n\nconst Default = {\n  toggle: true,\n  preventDefault: true,\n  triggerElement: 'a',\n  parentTrigger: 'li',\n  subMenu: 'ul',\n};\n\nconst Event = {\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  CLICK_DATA_API: `click${EVENT_KEY}${DATA_API_KEY}`,\n};\n\nconst ClassName = {\n  METIS: 'metismenu',\n  ACTIVE: 'mm-active',\n  SHOW: 'mm-show',\n  COLLAPSE: 'mm-collapse',\n  COLLAPSING: 'mm-collapsing',\n  COLLAPSED: 'mm-collapsed',\n};\n\nclass MetisMenu {\n  // eslint-disable-line no-shadow\n  constructor(element, config) {\n    this.element = element;\n    this.config = {\n      ...Default,\n      ...config,\n    };\n    this.transitioning = null;\n\n    this.init();\n  }\n\n  init() {\n    const self = this;\n    const conf = this.config;\n    const el = $(this.element);\n\n    el.addClass(ClassName.METIS); // add metismenu class to element\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the trigger element\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .addClass(ClassName.ACTIVE);\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the triggers of all parents\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`);\n\n    el\n      .find(conf.parentTrigger)\n      .not(`.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(ClassName.COLLAPSE);\n\n    el\n      .find(conf.parentTrigger)\n      // .has(conf.subMenu)\n      .children(conf.triggerElement)\n      .on(Event.CLICK_DATA_API, function (e) { // eslint-disable-line func-names\n        const eTar = $(this);\n\n        if (eTar.attr('aria-disabled') === 'true') {\n          return;\n        }\n\n        if (conf.preventDefault && eTar.attr('href') === '#') {\n          e.preventDefault();\n        }\n\n        const paRent = eTar.parent(conf.parentTrigger);\n        const sibLi = paRent.siblings(conf.parentTrigger);\n        const sibTrigger = sibLi.children(conf.triggerElement);\n\n        if (paRent.hasClass(ClassName.ACTIVE)) {\n          eTar.attr('aria-expanded', 'false');\n          self.removeActive(paRent);\n        } else {\n          eTar.attr('aria-expanded', 'true');\n          self.setActive(paRent);\n          if (conf.toggle) {\n            self.removeActive(sibLi);\n            sibTrigger.attr('aria-expanded', 'false');\n          }\n        }\n\n        if (conf.onTransitionStart) {\n          conf.onTransitionStart(e);\n        }\n      });\n  }\n\n  setActive(li) {\n    $(li).addClass(ClassName.ACTIVE);\n    const ul = $(li).children(this.config.subMenu);\n    if (ul.length > 0 && !ul.hasClass(ClassName.SHOW)) {\n      this.show(ul);\n    }\n  }\n\n  removeActive(li) {\n    $(li).removeClass(ClassName.ACTIVE);\n    const ul = $(li).children(`${this.config.subMenu}.${ClassName.SHOW}`);\n    if (ul.length > 0) {\n      this.hide(ul);\n    }\n  }\n\n  show(element) {\n    if (this.transitioning || $(element).hasClass(ClassName.COLLAPSING)) {\n      return;\n    }\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.SHOW);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).addClass(ClassName.ACTIVE);\n\n    if (this.config.toggle) {\n      const toggleElem = elem.parent(this.config.parentTrigger).siblings().children(`${this.config.subMenu}.${ClassName.SHOW}`);\n      this.hide(toggleElem);\n    }\n\n    elem\n      .removeClass(ClassName.COLLAPSE)\n      .addClass(ClassName.COLLAPSING)\n      .height(0);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`)\n        .height('');\n\n      this.setTransitioning(false);\n\n      elem.trigger(Event.SHOWN);\n    };\n\n    elem\n      .height(element[0].scrollHeight)\n      .one(Util.TRANSITION_END, complete)\n      .mmEmulateTransitionEnd(TRANSITION_DURATION);\n  }\n\n  hide(element) {\n    if (\n      this.transitioning || !$(element).hasClass(ClassName.SHOW)\n    ) {\n      return;\n    }\n\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.HIDE);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).removeClass(ClassName.ACTIVE);\n    // eslint-disable-next-line no-unused-expressions\n    elem.height(elem.height())[0].offsetHeight;\n\n    elem\n      .addClass(ClassName.COLLAPSING)\n      .removeClass(ClassName.COLLAPSE)\n      .removeClass(ClassName.SHOW);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      if (this.transitioning && this.config.onTransitionEnd) {\n        this.config.onTransitionEnd();\n      }\n\n      this.setTransitioning(false);\n      elem.trigger(Event.HIDDEN);\n\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(ClassName.COLLAPSE);\n    };\n\n    if (elem.height() === 0 || elem.css('display') === 'none') {\n      complete();\n    } else {\n      elem\n        .height(0)\n        .one(Util.TRANSITION_END, complete)\n        .mmEmulateTransitionEnd(TRANSITION_DURATION);\n    }\n  }\n\n  setTransitioning(isTransitioning) {\n    this.transitioning = isTransitioning;\n  }\n\n  dispose() {\n    $.removeData(this.element, DATA_KEY);\n\n    $(this.element)\n      .find(this.config.parentTrigger)\n      // .has(this.config.subMenu)\n      .children(this.config.triggerElement)\n      .off(Event.CLICK_DATA_API);\n\n    this.transitioning = null;\n    this.config = null;\n    this.element = null;\n  }\n\n  static jQueryInterface(config) {\n    // eslint-disable-next-line func-names\n    return this.each(function () {\n      const $this = $(this);\n      let data = $this.data(DATA_KEY);\n      const conf = {\n        ...Default,\n        ...$this.data(),\n        ...(typeof config === 'object' && config ? config : {}),\n      };\n\n      if (!data) {\n        data = new MetisMenu(this, conf);\n        $this.data(DATA_KEY, data);\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined) {\n          throw new Error(`No method named \"${config}\"`);\n        }\n        data[config]();\n      }\n    });\n  }\n}\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = MetisMenu.jQueryInterface; // eslint-disable-line no-param-reassign\n$.fn[NAME].Constructor = MetisMenu; // eslint-disable-line no-param-reassign\n$.fn[NAME].noConflict = () => {\n  // eslint-disable-line no-param-reassign\n  $.fn[NAME] = JQUERY_NO_CONFLICT; // eslint-disable-line no-param-reassign\n  return MetisMenu.jQueryInterface;\n};\n\nexport default MetisMenu;\n"]}