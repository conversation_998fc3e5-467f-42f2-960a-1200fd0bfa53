import puppeteer from 'puppeteer';

(async () => {
    const url = process.argv[2];  // Mengambil URL dari parameter command line
    if (!url) {
        console.error('URL is required!');
        process.exit(1);
    }

    try {
        const browser = await puppeteer.launch({
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        const page = await browser.newPage();

        // Akses halaman dengan URL yang diberikan
        await page.goto(url, { waitUntil: 'domcontentloaded' });

        // Debugging: Ambil seluruh HTML dari halaman untuk memeriksa apakah halaman dimuat dengan benar
        const pageContent = await page.content();
        console.log('Page content:', pageContent);

        // Ambil judul halaman
        const title = await page.title();
        console.log('Page title:', title);

        await browser.close();
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
})();
