

$(function () {
  "use strict";


  /* scrollar */

  new PerfectScrollbar(".notify-list")

  //  new PerfectScrollbar(".search-content")

  // new PerfectScrollbar(".mega-menu-widgets")



  /* toggle button */

  $(".btn-toggle").click(function () {
    $("body").hasClass("toggled") ? ($("body").removeClass("toggled"), $(".sidebar-wrapper").unbind("hover")) : ($("body").addClass("toggled"), $(".sidebar-wrapper").hover(function () {
      $("body").addClass("sidebar-hovered")
    }, function () {
      $("body").removeClass("sidebar-hovered")
    }))
  })




  /* menu */

  $(function () {
    $('#sidenav').metisMenu();
  });

  $(".sidebar-close").on("click", function () {
    $("body").removeClass("toggled")
  })



  /* dark mode button */

  $(document).ready(function() {
    // Fungsi untuk menerapkan tema
    function applyTheme(theme) {
      $('html').attr('data-bs-theme', theme);
      if(theme === 'dark') {
        $('.dark-mode i').text('light_mode');
      } else {
        $('.dark-mode i').text('dark_mode');
      }
    }
  
    // Cek preferensi tema saat halaman dimuat
    const savedTheme = localStorage.getItem('theme') || 'light';
    applyTheme(savedTheme);
  
    // Event listener untuk tombol mode gelap
    $(".dark-mode").click(function () {
      // Dapatkan tema saat ini
      let currentTheme = $('html').attr('data-bs-theme');
      // Tentukan tema baru
      let newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      // Terapkan tema baru
      applyTheme(newTheme);
      // Simpan preferensi tema di localStorage
      localStorage.setItem('theme', newTheme);
    });
  });
  



  /* switcher */

  $("#LightTheme").on("click", function () {
    $("html").attr("data-bs-theme", "light")
  }),

    $("#DarkTheme").on("click", function () {
      $("html").attr("data-bs-theme", "dark")
    }),

    $("#SemiDarkTheme").on("click", function () {
      $("html").attr("data-bs-theme", "semi-dark")
    }),

    $("#BoderedTheme").on("click", function () {
      $("html").attr("data-bs-theme", "bodered-theme")
    })



  /* search control */
$(document).ready(function () {
    $(".search-control, .mobile-search-btn").click(function () {
        $(".search-popup").addClass("d-block");
        $(".search-close").addClass("d-block");
    });

    $(".search-close, .mobile-search-close").click(function () {
        $(".search-popup").removeClass("d-block");
        $(".search-close").removeClass("d-block");
    });

    // Tutup pop-up saat menekan tombol Escape
    $(document).keydown(function (event) {
        if (event.key === "Escape") {
            $(".search-popup").removeClass("d-block");
            $(".search-close").removeClass("d-block");
        }
    });

    // Tutup pop-up jika klik di luar area pencarian
    $(document).click(function (event) {
        if (!$(event.target).closest(".search-popup, .search-control, .mobile-search-btn").length) {
            $(".search-popup").removeClass("d-block");
            $(".search-close").removeClass("d-block");
        }
    });
});


  /* menu active */

  $(function () {
    for (var e = window.location, o = $(".metismenu li a").filter(function () {
      return this.href == e
    }).addClass("").parent().addClass("mm-active"); o.is("li");) o = o.parent("").addClass("mm-show").parent("").addClass("mm-active")
  });



});










