@extends('layouts.master')

@section('content')
<div class="page-wrapper">
    <div class="page-content">
        <div class="container mt-4">
            <div class="card">
                <div class="card-header tenderid-header">
                    <h4>Manual Tender Data Input</h4>
                </div>
                <div class="card-body">

                    @if(session('status'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('status') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form action="{{ route('manual.tender.process') }}" method="POST">
                        @csrf
                        <div class="form-group">
                            <label for="tender_json" class="form-label">Paste Tender JSON Data Here:</label>
                            <textarea name="tender_json" id="tender_json" rows="15" class="form-control" placeholder="Paste JSON array of tender data...">{{ old('tender_json') }}</textarea>
                        </div>
                        <button type="submit" class="btn btn-primary mt-3">Process Data</button>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>
@endsection
