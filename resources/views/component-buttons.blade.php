@extends('layouts.master')

@section('title', 'Buttons')
@section('css')
	
@endsection 
@section('content')
<x-page-title title="Components" pagetitle="Buttons" />

        <div class="row row-cols-1 row-cols-lg-2">
          <div class="col">
            <div class="card">
              <div class="card-header">
                <h6 class="card-title mb-0 py-1">Colos Buttons</h6>
              </div>
              <div class="card-body">
                <div class="row row-cols-auto g-3">
                  <div class="col">
                    <button type="button" class="btn ripple btn-primary px-5">Primary</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn ripple btn-danger px-5">Danger</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-success px-5">Success</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-info px-5">Info</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-warning px-5">Warning</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-dark px-5">Dark</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-secondary px-5">Secondary</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-light px-5">Light</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col">
            <div class="card">
              <div class="card-header">
                <h6 class="card-title mb-0 py-1">Raised Buttons</h6>
              </div>
              <div class="card-body">
                <div class="row row-cols-auto g-3">
                  <div class="col">
                    <button type="button" class="btn btn-primary px-5 raised">Primary</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-danger px-5 raised">Danger</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-success px-5 raised">Success</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-info px-5 raised">Info</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-warning px-5 raised">Warning</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-dark px-5 raised">Dark</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-secondary px-5 raised">Secondary</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-light px-5 raised">Light</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col">
            <div class="card">
              <div class="card-header">
                <h6 class="card-title mb-0 py-1">Outline Buttons</h6>
              </div>
              <div class="card-body">
                <div class="row row-cols-auto g-3">
                  <div class="col">
                    <button type="button" class="btn btn-outline-primary px-5">Primary</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-danger px-5">Danger</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-success px-5">Success</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-info px-5">Info</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-warning px-5">Warning</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-dark px-5">Dark</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-secondary px-5">Secondary</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col">
            <div class="card">
              <div class="card-header">
                <h6 class="card-title mb-0 py-1">Inverse Buttons</h6>
              </div>
              <div class="card-body">
                <div class="row row-cols-auto g-3">
                  <div class="col">
                    <button type="button" class="btn btn-inverse-primary px-5">Primary</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-inverse-danger px-5">Danger</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-inverse-success px-5">Success</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-inverse-info px-5">Info</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-inverse-warning px-5">Warning</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-inverse-dark px-5">Dark</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-inverse-secondary px-5">Secondary</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col">
            <div class="card">
              <div class="card-header">
                <h6 class="card-title mb-0 py-1">Icon Buttons</h6>
              </div>
              <div class="card-body">
                <div class="row row-cols-auto g-3">
                  <div class="col">
                    <button type="button" class="btn btn-primary px-4 raised d-flex gap-2"><i class="material-icons-outlined">search</i>Search</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-danger px-4 raised d-flex gap-2"><i class="material-icons-outlined">home</i>Home</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-success px-4 raised d-flex gap-2"><i class="material-icons-outlined">account_circle</i>Account</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-info px-4 raised d-flex gap-2"><i class="material-icons-outlined">cloud_download</i>Download</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-warning px-4 raised d-flex gap-2"><i class="material-icons-outlined">cloud_upload</i>Upload</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-dark px-4 raised d-flex gap-2"><i class="material-icons-outlined">delete</i>Delete</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-secondary px-4 raised d-flex gap-2"><i class="material-icons-outlined">event</i>Event</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-light px-4 raised d-flex gap-2"><i class="material-icons-outlined">question_answer</i>Comments</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col">
            <div class="card">
              <div class="card-header">
                <h6 class="card-title mb-0 py-1">Outline Icon Buttons</h6>
              </div>
              <div class="card-body">
                <div class="row row-cols-auto g-3">
                  <div class="col">
                    <button type="button" class="btn btn-outline-primary px-4 d-flex gap-2"><i class="material-icons-outlined">search</i>Search</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-danger px-4 d-flex gap-2"><i class="material-icons-outlined">home</i>Home</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-success px-4 d-flex gap-2"><i class="material-icons-outlined">account_circle</i>Account</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-info px-4 d-flex gap-2"><i class="material-icons-outlined">cloud_download</i>Download</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-warning px-4 d-flex gap-2"><i class="material-icons-outlined">cloud_upload</i>Upload</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-dark px-4 d-flex gap-2"><i class="material-icons-outlined">delete</i>Delete</button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-secondary px-4 d-flex gap-2"><i class="material-icons-outlined">event</i>Event</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col">
            <div class="card">
              <div class="card-header">
                <h6 class="card-title mb-0 py-1">Only Icon Buttons</h6>
              </div>
              <div class="card-body">
                <div class="row row-cols-auto g-3">
                  <div class="col">
                    <button type="button" class="btn btn-primary raised d-flex gap-2"><i class="material-icons-outlined">search</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-danger raised d-flex gap-2"><i class="material-icons-outlined">home</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-success raised d-flex gap-2"><i class="material-icons-outlined">account_circle</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-info raised d-flex gap-2"><i class="material-icons-outlined">cloud_download</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-warning raised d-flex gap-2"><i class="material-icons-outlined">cloud_upload</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-dark raised d-flex gap-2"><i class="material-icons-outlined">delete</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-secondary raised d-flex gap-2"><i class="material-icons-outlined">event</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-light raised d-flex gap-2"><i class="material-icons-outlined">question_answer</i></button>
                  </div>
                  <hr class="w-100">
                  <div class="col">
                    <button type="button" class="btn btn-primary btn-circle raised rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">lock_open</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-danger btn-circle raised rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">chat_bubble_outline</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-success btn-circle raised rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">call</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-info btn-circle raised rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">location_on</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-warning btn-circle raised rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">send</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-dark btn-circle raised rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">clear</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-secondary btn-circle raised rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">push_pin</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-light btn-circle raised rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">archive</i></button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col">
            <div class="card">
              <div class="card-header">
                <h6 class="card-title mb-0 py-1">Only Outline Icon Buttons</h6>
              </div>
              <div class="card-body">
                <div class="row row-cols-auto g-3">
                  <div class="col">
                    <button type="button" class="btn btn-outline-primary d-flex gap-2"><i class="material-icons-outlined">search</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-danger d-flex gap-2"><i class="material-icons-outlined">home</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-success d-flex gap-2"><i class="material-icons-outlined">account_circle</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-info d-flex gap-2"><i class="material-icons-outlined">cloud_download</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-warning d-flex gap-2"><i class="material-icons-outlined">cloud_upload</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-dark d-flex gap-2"><i class="material-icons-outlined">delete</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-secondary d-flex gap-2"><i class="material-icons-outlined">event</i></button>
                  </div>
                 
                  <hr class="w-100">
                  <div class="col">
                    <button type="button" class="btn btn-outline-primary btn-circle rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">lock_open</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-danger btn-circle rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">chat_bubble_outline</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-success btn-circle rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">call</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-info btn-circle rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">location_on</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-warning btn-circle rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">send</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-dark btn-circle rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">clear</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-secondary btn-circle rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">push_pin</i></button>
                  </div>
                  <div class="col">
                    <button type="button" class="btn btn-outline-light btn-circle rounded-circle d-flex gap-2 wh-48"><i class="material-icons-outlined">archive</i></button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-lg-12">
            <div class="card radius-10">
							<div class="card-body">
								<div>
									<h5 class="card-title">Group Buttons</h5>
								</div>
								<hr>
								<div class="row row-cols-auto g-3">
									<div class="col">
										<div class="btn-group" role="group" aria-label="First group">
											<button type="button" class="btn btn-primary">1</button>
											<button type="button" class="btn btn-primary">2</button>
											<button type="button" class="btn btn-primary">3</button>
											<button type="button" class="btn btn-primary">4</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group" role="group" aria-label="First group">
											<button type="button" class="btn btn-dark">5</button>
											<button type="button" class="btn btn-dark">6</button>
											<button type="button" class="btn btn-dark">7</button>
											<button type="button" class="btn btn-dark">8</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group" role="group" aria-label="First group">
											<button type="button" class="btn btn-warning">9</button>
											<button type="button" class="btn btn-warning">10</button>
											<button type="button" class="btn btn-warning">11</button>
											<button type="button" class="btn btn-warning">12</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group" role="group" aria-label="First group">
											<button type="button" class="btn btn-outline-secondary">13</button>
											<button type="button" class="btn btn-outline-secondary">14</button>
											<button type="button" class="btn btn-outline-secondary">15</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group" role="group" aria-label="First group">
											<button type="button" class="btn btn-outline-primary">16</button>
											<button type="button" class="btn btn-outline-primary">17</button>
											<button type="button" class="btn btn-outline-primary">18</button>
											<button type="button" class="btn btn-outline-primary">19</button>
											<button type="button" class="btn btn-outline-primary">20</button>
										</div>
									</div>
									<div class="w-100 border-top"></div>
									<div class="col">
										<div class="btn-group" role="group" aria-label="Button group with nested dropdown">
											<button type="button" class="btn btn-primary">1</button>
											<button type="button" class="btn btn-primary">2</button>
											<div class="btn-group" role="group">
												<button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">Dropdown</button>
												<ul class="dropdown-menu">
													<li><a class="dropdown-item" href="#">Dropdown link</a>
													</li>
													<li><a class="dropdown-item" href="#">Dropdown link</a>
													</li>
												</ul>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="btn-group" role="group" aria-label="Button group with nested dropdown">
											<button type="button" class="btn btn-danger">1</button>
											<button type="button" class="btn btn-danger">2</button>
											<div class="btn-group" role="group">
												<button type="button" class="btn btn-danger dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">Dropdown</button>
												<ul class="dropdown-menu">
													<li><a class="dropdown-item" href="#">Dropdown link</a>
													</li>
													<li><a class="dropdown-item" href="#">Dropdown link</a>
													</li>
												</ul>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="btn-group" role="group" aria-label="Button group with nested dropdown">
											<button type="button" class="btn btn-success">1</button>
											<button type="button" class="btn btn-success">2</button>
											<div class="btn-group" role="group">
												<button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">Dropdown</button>
												<ul class="dropdown-menu">
													<li><a class="dropdown-item" href="#">Dropdown link</a>
													</li>
													<li><a class="dropdown-item" href="#">Dropdown link</a>
													</li>
												</ul>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="btn-group" role="group" aria-label="Button group with nested dropdown">
											<button type="button" class="btn btn-info">1</button>
											<button type="button" class="btn btn-info">2</button>
											<div class="btn-group" role="group">
												<button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">Dropdown</button>
												<ul class="dropdown-menu">
													<li><a class="dropdown-item" href="#">Dropdown link</a>
													</li>
													<li><a class="dropdown-item" href="#">Dropdown link</a>
													</li>
												</ul>
											</div>
										</div>
									</div>
									<div class="w-100 border-top"></div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-secondary">Left</button>
											<button type="button" class="btn btn-secondary">Middle</button>
											<button type="button" class="btn btn-secondary">Right</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-primary">Left</button>
											<button type="button" class="btn btn-primary">Middle</button>
											<button type="button" class="btn btn-primary">Right</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-danger">Left</button>
											<button type="button" class="btn btn-danger">Middle</button>
											<button type="button" class="btn btn-danger">Right</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-success">Left</button>
											<button type="button" class="btn btn-success">Middle</button>
											<button type="button" class="btn btn-success">Right</button>
										</div>
									</div>
									<div class="w-100 border-top"></div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-outline-secondary">Left</button>
											<button type="button" class="btn btn-outline-secondary">Middle</button>
											<button type="button" class="btn btn-outline-secondary">Right</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-outline-primary">Left</button>
											<button type="button" class="btn btn-outline-primary">Middle</button>
											<button type="button" class="btn btn-outline-primary">Right</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-outline-danger">Left</button>
											<button type="button" class="btn btn-outline-danger">Middle</button>
											<button type="button" class="btn btn-outline-danger">Right</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-outline-success">Left</button>
											<button type="button" class="btn btn-outline-success">Middle</button>
											<button type="button" class="btn btn-outline-success">Right</button>
										</div>
									</div>
									<div class="w-100 border-top"></div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-dark d-flex"><i class="material-icons-outlined">shopping_cart</i>
											</button>
											<button type="button" class="btn btn-dark d-flex"><i class="material-icons-outlined">favorite_border</i>
											</button>
											<button type="button" class="btn btn-dark d-flex"><i class="material-icons-outlined">account_circle</i>
											</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-danger d-flex"><i class="material-icons-outlined">close</i>
											</button>
											<button type="button" class="btn btn-danger d-flex"><i class="material-icons-outlined">menu</i>
											</button>
											<button type="button" class="btn btn-danger d-flex"><i class="material-icons-outlined">check</i>
											</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-success d-flex"><i class="material-icons-outlined">event_available</i>
											</button>
											<button type="button" class="btn btn-success d-flex"><i class="material-icons-outlined">expand_circle_down</i>
											</button>
											<button type="button" class="btn btn-success d-flex"><i class="material-icons-outlined">ondemand_video</i>
											</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-primary d-flex"><i class="material-icons-outlined">dark_mode</i>
											</button>
											<button type="button" class="btn btn-primary d-flex"><i class="material-icons-outlined">sell</i>
											</button>
											<button type="button" class="btn btn-primary d-flex"><i class="material-icons-outlined">gpp_good</i>
											</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-warning d-flex"><i class="material-icons-outlined">widgets</i>
											</button>
											<button type="button" class="btn btn-warning d-flex"><i class="material-icons-outlined">access_time</i>
											</button>
											<button type="button" class="btn btn-warning d-flex"><i class="material-icons-outlined">reviews</i>
											</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-info d-flex"><i class="material-icons-outlined">photo_camera</i>
											</button>
											<button type="button" class="btn btn-info d-flex"><i class="material-icons-outlined">remove_red_eye</i>
											</button>
											<button type="button" class="btn btn-info d-flex"><i class="material-icons-outlined">video_camera_back</i>
											</button>
										</div>
									</div>
									<div class="w-100 border-top"></div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-outline-dark d-flex"><i class="material-icons-outlined">edit</i>
											</button>
											<button type="button" class="btn btn-outline-dark d-flex"><i class="material-icons-outlined">adjust</i>
											</button>
											<button type="button" class="btn btn-outline-dark d-flex"><i class="material-icons-outlined">crop</i>
											</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-outline-info d-flex"><i class="material-icons-outlined">filter_drama</i>
											</button>
											<button type="button" class="btn btn-outline-info d-flex"><i class="material-icons-outlined">color_lens</i>
											</button>
											<button type="button" class="btn btn-outline-info d-flex"><i class="material-icons-outlined">tag_faces</i>
											</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-outline-warning d-flex"><i class="material-icons-outlined">light</i>
											</button>
											<button type="button" class="btn btn-outline-warning d-flex"><i class="material-icons-outlined">coffee</i>
											</button>
											<button type="button" class="btn btn-outline-warning d-flex"><i class="material-icons-outlined">door_back</i>
											</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group">
											<button type="button" class="btn btn-outline-primary d-flex"><i class="material-icons-outlined">file_upload</i>
											</button>
											<button type="button" class="btn btn-outline-primary d-flex"><i class="material-icons-outlined">grid_view</i>
											</button>
											<button type="button" class="btn btn-outline-primary d-flex"><i class="material-icons-outlined">folder</i>
											</button>
										</div>
									</div>
									<div class="w-100 border-top"></div>
									<div class="col">
										<div class="btn-group-vertical">
											<button type="button" class="btn btn-dark">Button</button>
											<button type="button" class="btn btn-dark">Button</button>
											<button type="button" class="btn btn-dark">Button</button>
											<button type="button" class="btn btn-dark">Button</button>
											<button type="button" class="btn btn-dark">Button</button>
											<button type="button" class="btn btn-dark">Button</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group-vertical">
											<button type="button" class="btn btn-warning">Button</button>
											<button type="button" class="btn btn-warning">Button</button>
											<button type="button" class="btn btn-warning">Button</button>
											<button type="button" class="btn btn-warning">Button</button>
											<button type="button" class="btn btn-warning">Button</button>
											<button type="button" class="btn btn-warning">Button</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group-vertical">
											<button type="button" class="btn btn-outline-danger">Button</button>
											<button type="button" class="btn btn-outline-danger">Button</button>
											<button type="button" class="btn btn-outline-danger active">Button</button>
											<button type="button" class="btn btn-outline-danger">Button</button>
											<button type="button" class="btn btn-outline-danger">Button</button>
											<button type="button" class="btn btn-outline-danger">Button</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group-vertical">
											<button type="button" class="btn btn-outline-success">Button</button>
											<button type="button" class="btn btn-outline-success">Button</button>
											<button type="button" class="btn btn-outline-success">Button</button>
											<button type="button" class="btn btn-outline-success">Button</button>
											<button type="button" class="btn btn-outline-success">Button</button>
											<button type="button" class="btn btn-outline-success">Button</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group-vertical">
											<button type="button" class="btn btn-danger d-flex"><i class="material-icons-outlined">tag_faces</i>
											</button>
											<button type="button" class="btn btn-danger d-flex"><i class="material-icons-outlined">message</i>
											</button>
											<button type="button" class="btn btn-danger d-flex"><i class="material-icons-outlined">cancel</i>
											</button>
											<button type="button" class="btn btn-danger d-flex"><i class="material-icons-outlined">mail_outline</i>
											</button>
											<button type="button" class="btn btn-danger d-flex"><i class="material-icons-outlined">video_library</i>
											</button>
											<button type="button" class="btn btn-danger d-flex"><i class="material-icons-outlined">volume_up</i>
											</button>
										</div>
									</div>
									<div class="col">
										<div class="btn-group-vertical">
											<button type="button" class="btn btn-primary d-flex"><i class="material-icons-outlined">notifications</i>
											</button>
											<button type="button" class="btn btn-primary d-flex"><i class="material-icons-outlined">school</i>
											</button>
											<button type="button" class="btn btn-primary d-flex"><i class="material-icons-outlined">share</i>
											</button>
											<button type="button" class="btn btn-primary d-flex"><i class="material-icons-outlined">science</i>
											</button>
											<button type="button" class="btn btn-primary d-flex"><i class="material-icons-outlined">thumb_up_alt</i>
											</button>
											<button type="button" class="btn btn-primary d-flex"><i class="material-icons-outlined">whatshot</i>
											</button>
										</div>
									</div>
								</div>
								<!--end row-->
							</div>
						</div>
          </div>

        </div><!--end row-->
@endsection 