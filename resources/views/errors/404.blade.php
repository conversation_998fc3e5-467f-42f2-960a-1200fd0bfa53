@extends('layouts.master')

@section('title', 'Halaman Tidak <PERSON>')

@section('css')
<style>
    .error-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .error-card {
        max-width: 900px;
        width: 100%;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin: 0 auto;
    }

    .error-image {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 0 auto;
    }

    .error-code {
        font-size: 6.5rem;
        font-weight: 700;
        line-height: 1;
        background: linear-gradient(90deg, #007bff, #6f42c1, #fd7e14);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 20px;
    }

    .error-message {
        font-size: 1.75rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1.5rem;
    }

    .error-details {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 2rem;
    }

    .btn-gradient {
        background: linear-gradient(90deg, #007bff, #6f42c1);
        border: none;
        color: white !important;
        transition: all 0.3s ease;
        padding: 12px 25px;
        font-size: 1rem;
        font-weight: 500;
    }

    .btn-gradient:hover, .btn-gradient:focus, .btn-gradient:active {
        background: linear-gradient(90deg, #0069d9, #6610f2);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        color: white !important;
    }

    .btn-outline-gradient {
        background: transparent;
        border: 2px solid #6f42c1;
        color: #6f42c1;
        transition: all 0.3s ease;
        padding: 12px 25px;
        font-size: 1rem;
        font-weight: 500;
    }

    .btn-outline-gradient:hover, .btn-outline-gradient:focus, .btn-outline-gradient:active {
        background: linear-gradient(90deg, #007bff, #6f42c1);
        color: white !important;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-color: transparent;
    }

    .copyright {
        text-align: center;
        font-size: 0.9rem;
        color: #6c757d;
        margin-top: 30px;
    }

    @media (max-width: 991.98px) {
        .error-code {
            font-size: 5rem;
        }
        .error-message {
            font-size: 1.5rem;
        }
    }
</style>
@endsection

@section('content')
<div class="error-container">
    <div class="container">
        <div class="card error-card">
            <div class="card-body p-0">
                <div class="row g-0">
                    <div class="col-lg-6 d-flex align-items-center justify-content-center p-5">
                        <div class="text-center">
                            <h1 class="error-code mb-4">404</h1>
                            <h2 class="error-message">Halaman Tidak Ditemukan</h2>
                            <p class="error-details">
                                Maaf, halaman yang Anda cari tidak dapat ditemukan atau telah dipindahkan.
                            </p>
                            <div class="d-grid gap-3 d-md-flex justify-content-md-center mt-4">
                                <a href="{{ url('/') }}" class="btn btn-gradient px-4 py-2 rounded-pill">
                                    <i class="bi bi-house-door me-2"></i>Kembali ke Beranda
                                </a>
                                <button onclick="window.history.back()" class="btn btn-outline-gradient px-4 py-2 rounded-pill">
                                    <i class="bi bi-arrow-left me-2"></i>Halaman Sebelumnya
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 bg-light d-flex align-items-center justify-content-center p-4">
                        <img src="{{ URL::asset('build/images/404-illustration.svg') }}" alt="404 Illustration" class="error-image" style="max-height: 400px;">
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
@endsection
