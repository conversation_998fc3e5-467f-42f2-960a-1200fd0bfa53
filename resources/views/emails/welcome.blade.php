<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Selamat Datang di TenderID!</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #f0f4f9;
            color: #344767;
            line-height: 1.5;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 650px;
            margin: 0 auto;
            padding: 20px;
            filter: drop-shadow(0 10px 25px rgba(0, 0, 0, 0.1));
        }

        .header {
            text-align: center;
            padding: 0;
            margin-bottom: 15px;
        }

        .header-banner {
            background: linear-gradient(135deg, #2dce89, #1aae6f);
            border-radius: 12px 12px 0 0;
            padding: 30px 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .logo {
            max-height: 70px;
            margin-bottom: 15px;
        }

        .header-title {
            color: #ffffff;
            font-size: 28px;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin: 10px 0 0;
            font-weight: 300;
        }

        .content {
            background-color: #ffffff;
            border-radius: 0 0 12px 12px;
            padding: 35px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid #eaedf2;
            border-top: none;
        }

        h1 {
            color: #344767;
            font-size: 24px;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 25px;
            text-align: center;
        }

        p {
            color: #495057;
            font-size: 15px;
            line-height: 1.7;
            margin: 0 0 15px;
        }

        .greeting {
            font-size: 17px;
            margin-bottom: 20px;
        }

        .button-container {
            text-align: center;
            margin: 35px 0;
        }

        .button {
            display: inline-block;
            background: linear-gradient(to right, #2dce89, #1aae6f);
            color: #ffffff !important;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 16px;
            box-shadow: 0 5px 15px rgba(45, 206, 137, 0.3);
            transition: transform 0.3s, box-shadow 0.3s;
            border: none;
            cursor: pointer;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(45, 206, 137, 0.4);
        }

        .panel {
            background-color: #f8f9fa;
            border-left: 4px solid #2dce89;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        }

        .info-panel {
            background-color: rgba(45, 206, 137, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin: 25px 0;
            border: 1px solid rgba(45, 206, 137, 0.2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.04);
        }

        .info-item {
            margin-bottom: 12px;
            padding-left: 5px;
            font-size: 15px;
        }

        .info-item strong {
            color: #344767;
            display: inline-block;
            width: 140px;
        }

        .features {
            margin: 25px 0;
            padding-left: 0;
            list-style-type: none;
        }

        .features li {
            margin-bottom: 15px;
            color: #495057;
            position: relative;
            padding-left: 35px;
            display: flex;
            align-items: flex-start;
        }

        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            top: 0;
            width: 25px;
            height: 25px;
            background-color: rgba(45, 206, 137, 0.1);
            border-radius: 50%;
            color: #2dce89;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(45, 206, 137, 0.15);
        }

        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #e9ecef, transparent);
            margin: 30px 0;
        }

        .feature-title {
            font-weight: 600;
            color: #344767;
            margin-bottom: 15px;
            font-size: 17px;
        }

        .contact-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            text-align: center;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .contact-info p {
            margin: 5px 0;
            font-size: 14px;
        }

        .footer {
            text-align: center;
            padding: 30px 0 10px;
            color: #6c757d;
            font-size: 13px;
        }

        .social-links {
            margin: 25px 0;
            text-align: center;
        }

        .social-link {
            display: inline-block;
            margin: 0 8px;
            transition: transform 0.3s;
        }

        .social-link:hover {
            transform: translateY(-3px);
        }

        .footer-logo {
            max-width: 120px;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .welcome-message {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: rgba(45, 206, 137, 0.05);
            border-radius: 10px;
            border: 1px dashed rgba(45, 206, 137, 0.3);
            box-shadow: 0 5px 15px rgba(45, 206, 137, 0.08);
        }

        .welcome-message h2 {
            color: #2dce89;
            font-size: 20px;
            margin-top: 0;
            margin-bottom: 10px;
        }

        .welcome-message p {
            margin: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-banner">
                <img src="{{ asset('build/images/logo1.png') }}" alt="TenderID Logo" class="logo">
                <h1 class="header-title">Selamat Datang di TenderID!</h1>
                <p class="header-subtitle">Platform Informasi Tender dan Pengadaan Terpercaya di Indonesia</p>
            </div>
        </div>

        <div class="content">
            <p class="greeting">Halo <strong>{{ $user->name }}</strong>,</p>

            <div class="welcome-message">
                <h2>🎉 Selamat Bergabung! 🎉</h2>
                <p>Akun Anda telah berhasil dibuat dan siap digunakan.</p>
            </div>

            <p>Terima kasih telah bergabung dengan TenderID, platform informasi tender dan pengadaan terpercaya di Indonesia. Kami sangat senang Anda menjadi bagian dari komunitas kami.</p>

            <div class="info-panel">
                <p style="margin-top: 0; font-weight: 600; color: #344767; margin-bottom: 15px;">Informasi Akun Anda:</p>
                <div class="info-item"><strong>Nama:</strong> {{ $user->name }}</div>
                <div class="info-item"><strong>Email:</strong> {{ $user->email }}</div>
                <div class="info-item"><strong>Tanggal Bergabung:</strong> {{ $user->created_at->format('d M Y') }}</div>
            </div>

            <div class="divider"></div>

            <p class="feature-title">Dengan TenderID, Anda dapat:</p>
            <ul class="features">
                <li>
                    <div>
                        <strong style="display: block; color: #344767; margin-bottom: 5px;">Akses Informasi Tender Terbaru</strong>
                        <span>Dapatkan informasi tender terbaru dari berbagai sumber resmi di seluruh Indonesia.</span>
                    </div>
                </li>
                <li>
                    <div>
                        <strong style="display: block; color: #344767; margin-bottom: 5px;">Notifikasi Tender Sesuai Preferensi</strong>
                        <span>Terima notifikasi tender yang sesuai dengan preferensi dan bidang usaha Anda.</span>
                    </div>
                </li>
                <li>
                    <div>
                        <strong style="display: block; color: #344767; margin-bottom: 5px;">Analisis Peluang & Kompetitor</strong>
                        <span>Analisis peluang tender dan pelajari strategi kompetitor untuk meningkatkan peluang kemenangan.</span>
                    </div>
                </li>
                <li>
                    <div>
                        <strong style="display: block; color: #344767; margin-bottom: 5px;">Pantau Status Tender</strong>
                        <span>Pantau status tender yang Anda ikuti secara real-time dan dapatkan update terbaru.</span>
                    </div>
                </li>
            </ul>

            <div class="button-container">
                <a href="{{ route('dashboard') }}" class="button">Akses Dashboard</a>
            </div>

            <div class="divider"></div>

            <p>Jika Anda memiliki pertanyaan atau membutuhkan bantuan, jangan ragu untuk menghubungi tim dukungan kami.</p>

            <p>Salam,<br><strong>Tim TenderID</strong></p>

            <div class="contact-info">
                <p>Butuh bantuan? Hubungi kami di <a href="mailto:<EMAIL>" style="color: #2dce89; text-decoration: none;"><EMAIL></a></p>
                <p>Atau kunjungi <a href="{{ url('/') }}" style="color: #2dce89; text-decoration: none;">tenderID.com</a></p>
            </div>
        </div>

        <div class="footer">
            <img src="{{ asset('build/images/logo1.png') }}" alt="TenderID Logo" class="footer-logo">

            <div class="social-links">
                <a href="#" class="social-link">
                    <img src="https://cdn-icons-png.flaticon.com/512/733/733547.png" width="28" height="28" alt="Facebook">
                </a>
                <a href="#" class="social-link">
                    <img src="https://cdn-icons-png.flaticon.com/512/3256/3256013.png" width="28" height="28" alt="Twitter">
                </a>
                <a href="#" class="social-link">
                    <img src="https://cdn-icons-png.flaticon.com/512/3536/3536505.png" width="28" height="28" alt="Instagram">
                </a>
            </div>

            <p>&copy; {{ date('Y') }} TenderID. All rights reserved.</p>
            <p>Anda menerima email ini karena Anda telah mendaftar di TenderID. Jika Anda tidak melakukan pendaftaran, silakan abaikan email ini.</p>
        </div>
    </div>
</body>
</html>
