<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reset Password - TenderID</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #f0f4f9;
            color: #344767;
            line-height: 1.5;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 650px;
            margin: 0 auto;
            padding: 20px;
            filter: drop-shadow(0 10px 25px rgba(0, 0, 0, 0.1));
        }

        .header {
            text-align: center;
            padding: 0;
            margin-bottom: 15px;
        }

        .header-banner {
            background: linear-gradient(135deg, #fb6340, #f5365c);
            border-radius: 12px 12px 0 0;
            padding: 30px 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .logo {
            max-height: 70px;
            margin-bottom: 15px;
        }

        .header-title {
            color: #ffffff;
            font-size: 28px;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin: 10px 0 0;
            font-weight: 300;
        }

        .content {
            background-color: #ffffff;
            border-radius: 0 0 12px 12px;
            padding: 35px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid #eaedf2;
            border-top: none;
        }

        h1 {
            color: #344767;
            font-size: 24px;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 25px;
            text-align: center;
        }

        p {
            color: #495057;
            font-size: 15px;
            line-height: 1.7;
            margin: 0 0 15px;
        }

        .greeting {
            font-size: 17px;
            margin-bottom: 20px;
        }

        .button-container {
            text-align: center;
            margin: 35px 0;
        }

        .button {
            display: inline-block;
            background: linear-gradient(to right, #fb6340, #f5365c);
            color: #ffffff !important;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 16px;
            box-shadow: 0 5px 15px rgba(245, 54, 92, 0.3);
            transition: transform 0.3s, box-shadow 0.3s;
            border: none;
            cursor: pointer;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(245, 54, 92, 0.4);
        }

        .panel {
            background-color: #f8f9fa;
            border-left: 4px solid #f5365c;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        }

        .panel-warning {
            border-left-color: #ffc107;
            background-color: rgba(255, 193, 7, 0.08);
        }

        .panel-danger {
            border-left-color: #dc3545;
            background-color: rgba(220, 53, 69, 0.05);
        }

        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #e9ecef, transparent);
            margin: 30px 0;
        }

        .url-display {
            word-break: break-all;
            font-size: 13px;
            color: #6c757d;
            margin-top: 15px;
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            border: 1px dashed #dee2e6;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .security-tips {
            margin: 25px 0;
        }

        .security-tip {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
            display: flex;
            align-items: flex-start;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.04);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .security-tip:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(245, 54, 92, 0.1);
        }

        .security-tip-icon {
            margin-right: 15px;
            font-size: 20px;
            color: #f5365c;
        }

        .security-tip-content {
            flex: 1;
        }

        .security-tip-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #344767;
        }

        .contact-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            text-align: center;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .contact-info p {
            margin: 5px 0;
            font-size: 14px;
        }

        .footer {
            text-align: center;
            padding: 30px 0 10px;
            color: #6c757d;
            font-size: 13px;
        }

        .social-links {
            margin: 25px 0;
            text-align: center;
        }

        .social-link {
            display: inline-block;
            margin: 0 8px;
            transition: transform 0.3s;
        }

        .social-link:hover {
            transform: translateY(-3px);
        }

        .footer-logo {
            max-width: 120px;
            margin-bottom: 15px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-banner">
                <img src="{{ asset('build/images/logo1.png') }}" alt="TenderID Logo" class="logo">
                <h1 class="header-title">Reset Password</h1>
                <p class="header-subtitle">Permintaan pengaturan ulang password akun Anda</p>
            </div>
        </div>

        <div class="content">
            <p class="greeting">Halo <strong>{{ $user->name }}</strong>,</p>

            <p>Anda menerima email ini karena kami menerima permintaan reset password untuk akun TenderID Anda.</p>

            <div class="panel">
                <p style="margin: 0;">Untuk mengatur ulang password Anda, silakan klik tombol di bawah ini:</p>
            </div>

            <div class="button-container">
                <a href="{{ $resetUrl }}" class="button">Reset Password</a>
            </div>

            <div class="panel panel-warning">
                <p style="margin: 0; font-size: 14px;">
                    <strong>Catatan Penting:</strong><br>
                    • Tautan reset password ini akan kedaluwarsa dalam 60 menit.<br>
                    • Jika Anda tidak meminta reset password, tidak ada tindakan lebih lanjut yang diperlukan.<br>
                    • Untuk keamanan, password baru Anda harus berbeda dari password sebelumnya.
                </p>
            </div>

            <div class="divider"></div>

            <h2 style="font-size: 18px; color: #344767; margin-bottom: 15px;">Tips Keamanan Akun</h2>

            <div class="security-tips">
                <div class="security-tip">
                    <div class="security-tip-icon">🔒</div>
                    <div class="security-tip-content">
                        <div class="security-tip-title">Gunakan Password yang Kuat</div>
                        <p style="margin: 0; font-size: 14px;">Kombinasikan huruf besar, huruf kecil, angka, dan simbol untuk membuat password yang sulit ditebak.</p>
                    </div>
                </div>

                <div class="security-tip">
                    <div class="security-tip-icon">🔄</div>
                    <div class="security-tip-content">
                        <div class="security-tip-title">Ganti Password Secara Berkala</div>
                        <p style="margin: 0; font-size: 14px;">Ubah password Anda secara rutin untuk meningkatkan keamanan akun.</p>
                    </div>
                </div>

                <div class="security-tip">
                    <div class="security-tip-icon">⚠️</div>
                    <div class="security-tip-content">
                        <div class="security-tip-title">Waspada Phishing</div>
                        <p style="margin: 0; font-size: 14px;">TenderID tidak akan pernah meminta password atau informasi sensitif melalui email.</p>
                    </div>
                </div>
            </div>

            <div class="divider"></div>

            <p>Jika Anda mengalami masalah saat mengklik tombol "Reset Password", salin dan tempel URL di bawah ini ke browser web Anda:</p>
            <div class="url-display">{{ $resetUrl }}</div>

            <p>Salam,<br><strong>Tim TenderID</strong></p>

            <div class="contact-info">
                <p>Jika Anda tidak melakukan permintaan ini, silakan hubungi kami segera di <a href="mailto:<EMAIL>" style="color: #f5365c; text-decoration: none;"><EMAIL></a></p>
                <p>Atau kunjungi <a href="{{ url('/') }}" style="color: #f5365c; text-decoration: none;">tenderID.com</a></p>
            </div>
        </div>

        <div class="footer">
            <img src="{{ asset('build/images/logo1.png') }}" alt="TenderID Logo" class="footer-logo">

            <div class="social-links">
                <a href="#" class="social-link">
                    <img src="https://cdn-icons-png.flaticon.com/512/733/733547.png" width="28" height="28" alt="Facebook">
                </a>
                <a href="#" class="social-link">
                    <img src="https://cdn-icons-png.flaticon.com/512/3256/3256013.png" width="28" height="28" alt="Twitter">
                </a>
                <a href="#" class="social-link">
                    <img src="https://cdn-icons-png.flaticon.com/512/3536/3536505.png" width="28" height="28" alt="Instagram">
                </a>
            </div>

            <p>&copy; {{ date('Y') }} TenderID. All rights reserved.</p>
            <p>Platform Informasi Tender dan Pengadaan Terpercaya di Indonesia</p>
        </div>
    </div>
</body>
</html>
