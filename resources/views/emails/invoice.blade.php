<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Detail Invoice - TenderID</title>
    <style>
        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #f8f9fa;
            color: #344767;
            line-height: 1.5;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
        }
        .logo {
            max-height: 60px;
        }
        .content {
            background-color: #ffffff;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        h1 {
            color: #344767;
            font-size: 22px;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 20px;
        }
        p {
            color: #495057;
            font-size: 15px;
            line-height: 1.6;
            margin: 0 0 15px;
        }
        .button {
            display: inline-block;
            background-color: #0d6efd;
            color: #ffffff !important;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            margin: 20px 0;
            text-align: center;
        }
        .button-success {
            background-color: #198754;
        }
        .panel {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-box-title {
            font-weight: 600;
            color: #344767;
            margin-bottom: 15px;
            font-size: 16px;
        }
        .info-item {
            margin-bottom: 8px;
        }
        .bank-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            color: #6c757d;
            font-size: 13px;
        }
        .social-links {
            margin-top: 20px;
            text-align: center;
        }
        .social-link {
            display: inline-block;
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{ asset('build/images/logo1.png') }}" alt="TenderID Logo" class="logo">
        </div>

        <div class="content">
            <h1>Detail Invoice</h1>

            <p>Halo <strong>{{ $userSubscription->user->name }}</strong>,</p>

            <p>Terima kasih atas langganan Anda di TenderID. Berikut adalah detail invoice Anda:</p>

            <div class="panel">
                <div class="info-box-title">Informasi Langganan</div>

                <div class="info-item"><strong>Paket:</strong> {{ $userSubscription->subscription->name }}</div>
                <div class="info-item"><strong>Tanggal Mulai:</strong> {{ $userSubscription->start_date->format('d M Y') }}</div>
                <div class="info-item"><strong>Tanggal Berakhir:</strong> {{ $userSubscription->end_date->format('d M Y') }}</div>
                <div class="info-item"><strong>Total Harga:</strong> Rp {{ number_format($userSubscription->total_amount, 0, ',', '.') }}</div>
                <div class="info-item"><strong>Status:</strong> {{ ucfirst($userSubscription->status) }}</div>
            </div>

            @if($userSubscription->status === 'pending')
            <p>Silakan lakukan pembayaran ke salah satu rekening berikut:</p>

            <div class="bank-info">
                <strong>Bank BCA</strong><br>
                No. Rekening: **********<br>
                Atas Nama: PT TenderID Indonesia
            </div>

            <div class="bank-info">
                <strong>Bank Mandiri</strong><br>
                No. Rekening: **********<br>
                Atas Nama: PT TenderID Indonesia
            </div>

            <p>Setelah melakukan pembayaran, silakan konfirmasi melalui halaman konfirmasi pembayaran.</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ route('subscriptions.confirm-payment', $userSubscription->id) }}" class="button">Konfirmasi Pembayaran</a>
            </div>
            @endif

            @if($userSubscription->status === 'active')
            <p>Terima kasih atas pembayaran Anda. Langganan Anda telah aktif dan Anda dapat mengakses semua fitur premium TenderID.</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ route('dashboard') }}" class="button button-success">Akses Dashboard</a>
            </div>
            @endif

            <p>Jika Anda memiliki pertanyaan, silakan hubungi tim dukungan kami.</p>

            <p>Salam,<br>Tim TenderID</p>
        </div>

        <div class="footer">
            <p>&copy; {{ date('Y') }} TenderID. All rights reserved.</p>
            <p><a href="{{ url('/') }}" style="color: #0d6efd; text-decoration: none;">tenderID.com</a> - Platform Informasi Tender dan Pengadaan Terpercaya</p>

            <div class="social-links">
                <a href="#" class="social-link">
                    <img src="https://cdn-icons-png.flaticon.com/512/733/733547.png" width="24" height="24" alt="Facebook">
                </a>
                <a href="#" class="social-link">
                    <img src="https://cdn-icons-png.flaticon.com/512/3256/3256013.png" width="24" height="24" alt="Twitter">
                </a>
                <a href="#" class="social-link">
                    <img src="https://cdn-icons-png.flaticon.com/512/3536/3536505.png" width="24" height="24" alt="Instagram">
                </a>
            </div>

            <p>Invoice ini dibuat secara otomatis. Silakan simpan untuk arsip Anda.</p>
        </div>
    </div>
</body>
</html>
