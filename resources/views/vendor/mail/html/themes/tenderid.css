/* Base */
body,
body *:not(html):not(style):not(br):not(tr):not(code) {
    box-sizing: border-box;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
    position: relative;
}

body {
    -webkit-text-size-adjust: none;
    background-color: #f8f9fa;
    color: #344767;
    height: 100%;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    width: 100% !important;
}

p,
ul,
ol,
blockquote {
    line-height: 1.5;
    text-align: left;
}

a {
    color: #0d6efd;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

a img {
    border: none;
}

/* Typography */

h1 {
    color: #344767;
    font-size: 22px;
    font-weight: 600;
    margin-top: 0;
    text-align: left;
}

h2 {
    color: #344767;
    font-size: 18px;
    font-weight: 600;
    margin-top: 0;
    text-align: left;
}

h3 {
    color: #344767;
    font-size: 16px;
    font-weight: 600;
    margin-top: 0;
    text-align: left;
}

p {
    color: #495057;
    font-size: 15px;
    line-height: 1.6em;
    margin-top: 0;
    text-align: left;
}

p.sub {
    font-size: 13px;
    color: #6c757d;
}

img {
    max-width: 100%;
}

/* Layout */

.wrapper {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    width: 100%;
}

.content {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 0;
    padding: 0;
    width: 100%;
}

/* Header */

.header {
    padding: 25px 0;
    text-align: center;
}

.header a {
    color: #344767;
    font-size: 19px;
    font-weight: bold;
    text-decoration: none;
}

/* Logo */

.logo {
    height: 60px;
    max-height: 60px;
}

/* Body */

.body {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    width: 100%;
}

.inner-body {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 570px;
    background-color: #ffffff;
    border-color: #e9ecef;
    border-radius: 12px;
    border-width: 1px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    margin: 0 auto;
    padding: 0;
    width: 570px;
}

/* Subcopy */

.subcopy {
    border-top: 1px solid #e9ecef;
    margin-top: 25px;
    padding-top: 25px;
}

.subcopy p {
    font-size: 13px;
    color: #6c757d;
}

/* Footer */

.footer {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 570px;
    margin: 0 auto;
    padding: 0;
    text-align: center;
    width: 570px;
}

.footer p {
    color: #6c757d;
    font-size: 13px;
    text-align: center;
}

.footer a {
    color: #6c757d;
    text-decoration: underline;
}

/* Tables */

.table table {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 30px auto;
    width: 100%;
}

.table th {
    border-bottom: 1px solid #edeff2;
    color: #344767;
    font-weight: 600;
    padding-bottom: 8px;
    text-align: left;
}

.table td {
    color: #495057;
    font-size: 15px;
    line-height: 18px;
    padding: 10px 0;
}

.content-cell {
    max-width: 100vw;
    padding: 32px;
}

/* Buttons */

.action {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 30px auto;
    padding: 0;
    text-align: center;
    width: 100%;
}

.button {
    -webkit-text-size-adjust: none;
    border-radius: 8px;
    color: #fff;
    display: inline-block;
    overflow: hidden;
    text-decoration: none;
    font-weight: 500;
    padding: 12px 24px;
    font-size: 15px;
    transition: all 0.3s ease;
}

.button-blue,
.button-primary {
    background-color: #0d6efd;
    border-bottom: 10px solid #0d6efd;
    border-left: 18px solid #0d6efd;
    border-right: 18px solid #0d6efd;
    border-top: 10px solid #0d6efd;
}

.button-green,
.button-success {
    background-color: #198754;
    border-bottom: 10px solid #198754;
    border-left: 18px solid #198754;
    border-right: 18px solid #198754;
    border-top: 10px solid #198754;
}

.button-red,
.button-error {
    background-color: #dc3545;
    border-bottom: 10px solid #dc3545;
    border-left: 18px solid #dc3545;
    border-right: 18px solid #dc3545;
    border-top: 10px solid #dc3545;
}

/* Panels */

.panel {
    border-left: #0d6efd solid 4px;
    margin: 21px 0;
}

.panel-content {
    background-color: #f8f9fa;
    color: #495057;
    padding: 16px;
}

.panel-content p {
    color: #495057;
}

.panel-item {
    padding: 0;
}

.panel-item p:last-of-type {
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Utilities */

.break-all {
    word-break: break-all;
}

/* Custom TenderID Styles */
.greeting {
    color: #344767;
    font-weight: 600;
    font-size: 20px;
    margin-bottom: 20px;
}

.info-box {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
}

.info-box-title {
    font-weight: 600;
    color: #344767;
    margin-bottom: 10px;
}

.social-links {
    margin-top: 20px;
    text-align: center;
}

.social-link {
    display: inline-block;
    margin: 0 5px;
}
