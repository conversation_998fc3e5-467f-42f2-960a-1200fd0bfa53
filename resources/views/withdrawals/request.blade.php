@extends('layouts.master')

@section('title', 'Ajukan Penarikan')

@section('css')
<style>
    /* Custom header with diagonal pattern */
    .diagonal-pattern-header {
        background: linear-gradient(135deg, #f57c00, #ffb74d);
        color: white;
        position: relative;
        overflow: hidden;
        padding: 16px 20px;
    }

    .diagonal-pattern-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
            45deg,
            rgba(255, 255, 255, 0.1),
            rgba(255, 255, 255, 0.1) 10px,
            rgba(255, 255, 255, 0) 10px,
            rgba(255, 255, 255, 0) 20px
        );
        z-index: 0;
    }

    .diagonal-pattern-header > * {
        position: relative;
        z-index: 1;
    }

    .balance-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s;
        border: none;
    }

    .balance-card:hover {
        transform: translateY(-5px);
    }

    .balance-amount {
        font-size: 2.5rem;
        font-weight: 700;
        color: #f57c00;
        text-align: center;
        margin: 15px 0;
    }

    .requirements-card {
        border-left: 4px solid #f57c00;
        background-color: #fff8e1;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .requirements-card h5 {
        color: #f57c00;
        margin-bottom: 10px;
    }

    .requirements-card ul {
        padding-left: 20px;
    }

    .form-section {
        background-color: #fff;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .form-label {
        font-weight: 500;
        color: #344767;
    }

    .input-group-text {
        background-color: #f8f9fa;
        border: 1px solid #ced4da;
        color: #6c757d;
    }

    .submit-btn {
        background: linear-gradient(135deg, #f57c00, #ffb74d);
        border: none;
        padding: 12px;
        font-weight: 500;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        background: linear-gradient(135deg, #e65100, #f57c00);
    }
</style>
@endsection

@section('content')

<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card rounded-4 mb-4">
            <div class="card-header diagonal-pattern-header">
                <h5 class="mb-0 text-white">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">account_balance_wallet</i>
                    Ajukan Penarikan
                </h5>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success d-flex align-items-center" role="alert">
                        <i class="material-icons-outlined me-2">check_circle</i>
                        <div>{{ session('success') }}</div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                        <i class="material-icons-outlined me-2">error</i>
                        <div>{{ session('error') }}</div>
                    </div>
                @endif

                <!-- Balance Card -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card balance-card">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="wh-48 d-flex align-items-center justify-content-center bg-light-warning rounded-circle me-3">
                                        <i class="material-icons-outlined text-warning">payments</i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 text-muted">Saldo Komisi Anda</h6>
                                    </div>
                                </div>
                                <div class="balance-amount">Rp {{ number_format($commissionBalance, 0, ',', '.') }}</div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Requirements Card -->
                <div class="requirements-card">
                    <h5 class="d-flex align-items-center">
                        <i class="material-icons-outlined me-2">info</i>
                        Persyaratan Penarikan:
                    </h5>
                    <ul class="mb-0">
                        <li>Saldo komisi minimum yang diperlukan: Rp 50.000</li>
                        <li>Jumlah penarikan minimum: Rp 50.000</li>
                        <li>Penarikan akan diproses dalam 1-3 hari kerja</li>
                    </ul>
                </div>

                <!-- Withdrawal Form -->
                <div class="form-section">
                    <form action="{{ route('withdrawals.submit') }}" method="POST">
                        @csrf

                        <div class="mb-4">
                            <label for="amount" class="form-label">Jumlah Penarikan</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="number"
                                    class="form-control @error('amount') is-invalid @enderror"
                                    id="amount"
                                    name="amount"
                                    min="50000"
                                    max="{{ $commissionBalance }}"
                                    value="{{ old('amount', $commissionBalance) }}"
                                    required>
                            </div>
                            <small class="text-muted">Minimum Rp 50.000, maksimum Rp {{ number_format($commissionBalance, 0, ',', '.') }}</small>
                            @error('amount')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="bank_name" class="form-label">Nama Bank</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="material-icons-outlined">account_balance</i>
                                </span>
                                <input type="text"
                                    class="form-control @error('bank_name') is-invalid @enderror"
                                    id="bank_name"
                                    name="bank_name"
                                    placeholder="Contoh: BCA, Mandiri, BNI, dll"
                                    value="{{ old('bank_name') }}"
                                    required>
                            </div>
                            @error('bank_name')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="account_number" class="form-label">Nomor Rekening</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="material-icons-outlined">credit_card</i>
                                </span>
                                <input type="text"
                                    class="form-control @error('account_number') is-invalid @enderror"
                                    id="account_number"
                                    name="account_number"
                                    placeholder="Masukkan nomor rekening Anda"
                                    value="{{ old('account_number') }}"
                                    required>
                            </div>
                            @error('account_number')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="account_name" class="form-label">Nama Pemilik Rekening</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="material-icons-outlined">person</i>
                                </span>
                                <input type="text"
                                    class="form-control @error('account_name') is-invalid @enderror"
                                    id="account_name"
                                    name="account_name"
                                    placeholder="Masukkan nama pemilik rekening"
                                    value="{{ old('account_name') }}"
                                    required>
                            </div>
                            @error('account_name')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="note" class="form-label">Catatan (opsional)</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="material-icons-outlined">note</i>
                                </span>
                                <textarea
                                    class="form-control @error('note') is-invalid @enderror"
                                    id="note"
                                    name="note"
                                    placeholder="Tambahkan catatan jika diperlukan"
                                    rows="3">{{ old('note') }}</textarea>
                            </div>
                            @error('note')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-lg submit-btn text-white">
                                <i class="material-icons-outlined me-2" style="vertical-align: sub;">send</i>
                                Ajukan Permintaan Penarikan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- History Link Card -->
        <div class="card rounded-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Ingin melihat riwayat penarikan Anda?</h5>
                        <p class="text-muted mb-0">Lihat status dan riwayat penarikan Anda</p>
                    </div>
                    <a href="{{ route('withdrawals.history') }}" class="btn btn-outline-primary">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">history</i>
                        Riwayat Penarikan
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Format amount input with thousand separator
        const amountInput = document.getElementById('amount');

        // Set initial value to maximum (full balance)
        if (amountInput.value === '') {
            amountInput.value = amountInput.max;
        }
    });
</script>
@endsection
