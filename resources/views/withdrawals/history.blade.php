@extends('layouts.master')

@section('title', 'Riwayat Penarikan')

@section('css')
<style>
    /* Custom header with diagonal pattern */
    .diagonal-pattern-header {
        background: linear-gradient(135deg, #f57c00, #ffb74d);
        color: white;
        position: relative;
        overflow: hidden;
        padding: 16px 20px;
    }

    .diagonal-pattern-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
            45deg,
            rgba(255, 255, 255, 0.1),
            rgba(255, 255, 255, 0.1) 10px,
            rgba(255, 255, 255, 0) 10px,
            rgba(255, 255, 255, 0) 20px
        );
        z-index: 0;
    }

    .diagonal-pattern-header > * {
        position: relative;
        z-index: 1;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .withdrawal-amount {
        font-weight: 600;
        color: #f57c00;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    .table th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .reason-btn {
        border-radius: 20px;
        padding: 4px 12px;
        font-size: 0.75rem;
    }

    /* Animation for pulse */
    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.05);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .pulse {
        animation: pulse 2s infinite;
    }
</style>
@endsection

@section('content')
<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card rounded-4">
            <div class="card-header diagonal-pattern-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-white">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">history</i>
                    Riwayat Penarikan
                </h5>
                <a href="{{ route('withdrawals.request') }}" class="btn btn-sm btn-light">
                    <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">add</i>
                    Ajukan Penarikan Baru
                </a>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success d-flex align-items-center" role="alert">
                        <i class="material-icons-outlined me-2">check_circle</i>
                        <div>{{ session('success') }}</div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                        <i class="material-icons-outlined me-2">error</i>
                        <div>{{ session('error') }}</div>
                    </div>
                @endif

                @if($withdrawals->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Jumlah</th>
                                    <th>Bank</th>
                                    <th>Nomor Rekening</th>
                                    <th>Nama Pemilik</th>
                                    <th>Status</th>
                                    <th>Tanggal Pengajuan</th>
                                    <th>Tanggal Diproses</th>
                                    <th>Catatan</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($withdrawals as $withdrawal)
                                    <tr>
                                        <td><span class="fw-medium">#{{ $withdrawal->id }}</span></td>
                                        <td><span class="withdrawal-amount">Rp {{ number_format($withdrawal->amount, 0, ',', '.') }}</span></td>
                                        <td>{{ $withdrawal->bank_name }}</td>
                                        <td>{{ $withdrawal->account_number }}</td>
                                        <td>{{ $withdrawal->account_name ?: '-' }}</td>
                                        <td>
                                            @if($withdrawal->status === 'pending')
                                                <span class="status-badge bg-warning">
                                                    <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">pending</i>
                                                    Menunggu
                                                </span>
                                            @elseif($withdrawal->status === 'approved')
                                                <span class="status-badge bg-success">
                                                    <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">check_circle</i>
                                                    Disetujui
                                                </span>
                                            @else
                                                <span class="status-badge bg-danger"
                                                    data-bs-toggle="tooltip"
                                                    title="{{ $withdrawal->rejection_reason }}">
                                                    <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">cancel</i>
                                                    Ditolak
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-medium">{{ $withdrawal->created_at->format('d M Y') }}</span>
                                                <small class="text-muted">{{ $withdrawal->created_at->format('H:i') }} WIB</small>
                                            </div>
                                        </td>
                                        <td>
                                            @if($withdrawal->processed_at)
                                                <div class="d-flex flex-column">
                                                    <span class="fw-medium">{{ \Carbon\Carbon::parse($withdrawal->processed_at)->format('d M Y') }}</span>
                                                    <small class="text-muted">{{ \Carbon\Carbon::parse($withdrawal->processed_at)->format('H:i') }} WIB</small>
                                                </div>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($withdrawal->status === 'rejected')
                                                <button type="button"
                                                    class="reason-btn btn btn-sm btn-outline-danger"
                                                    data-bs-toggle="tooltip"
                                                    title="{{ $withdrawal->rejection_reason }}">
                                                    <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">info</i>
                                                    Lihat Alasan
                                                </button>
                                            @else
                                                {{ $withdrawal->note ?: '-' }}
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4 flex-wrap">
                        <div class="text-muted small mb-2 mb-md-0">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">info</i>
                            Menampilkan {{ $withdrawals->firstItem() ?? 0 }} - {{ $withdrawals->lastItem() ?? 0 }} dari {{ $withdrawals->total() }} penarikan
                        </div>
                        <div>
                            {{ $withdrawals->links('vendor.pagination.simple') }}
                        </div>
                    </div>
                @else
                    <div class="empty-state">
                        <i class="material-icons-outlined pulse">account_balance_wallet</i>
                        <h5>Belum Ada Riwayat Penarikan</h5>
                        <p>Anda belum pernah melakukan penarikan komisi. Ajukan penarikan untuk menarik saldo komisi Anda.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk menampilkan alasan penolakan -->
<div class="modal fade" id="reasonModal" tabindex="-1" aria-labelledby="reasonModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reasonModalLabel">Alasan Penolakan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="rejectionReason"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        // Handle click on rejection reason button
        document.querySelectorAll('.reason-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                var reason = this.getAttribute('title');
                document.getElementById('rejectionReason').textContent = reason;
                var reasonModal = new bootstrap.Modal(document.getElementById('reasonModal'));
                reasonModal.show();
            });
        });
    });
</script>
@endsection
