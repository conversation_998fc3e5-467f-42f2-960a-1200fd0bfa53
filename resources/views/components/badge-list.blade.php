{{-- resources/views/components/badge-list.blade.php --}}
@props(['data', 'badgeClass' => 'bg-info text-dark', 'property' => null, 'properties' => []])

@if(is_array($data) && !empty($data))
    @foreach($data as $item)
        @if(is_array($item) && $property)
            <span class="badge {{ $badgeClass }}">{{ $item[$property] ?? 'N/A' }}</span>
        @elseif(is_object($item) && $property)
            <span class="badge {{ $badgeClass }}">{{ $item->$property ?? 'N/A' }}</span>
        @elseif(is_array($item) && !empty($properties))
            @php
                $displayText = '';
                foreach($properties as $prop){
                    if(isset($item[$prop])){
                        $displayText .= ucfirst($prop) . ': ' . $item[$prop] . ' - ';
                    }
                }
                $displayText = rtrim($displayText, ' - ');
                if(empty($displayText)){
                    $displayText = 'N/A';
                }
            @endphp
            <span class="badge {{ $badgeClass }}">{{ $displayText }}</span>
        @elseif(is_object($item) && !empty($properties))
            @php
                $displayText = '';
                foreach($properties as $prop){
                    if(isset($item->$prop)){
                        $displayText .= ucfirst($prop) . ': ' . $item->$prop . ' - ';
                    }
                }
                $displayText = rtrim($displayText, ' - ');
                if(empty($displayText)){
                    $displayText = 'N/A';
                }
            @endphp
            <span class="badge {{ $badgeClass }}">{{ $displayText }}</span>
        @elseif(is_string($item))
            <span class="badge {{ $badgeClass }}">{{ $item }}</span>
        @else
            <span class="badge {{ $badgeClass }}">{{ json_encode($item) }}</span>
        @endif
    @endforeach
@elseif(is_string($data) && !empty($data))
    {{ $data }}
@else
    <span class="text-muted">-</span>
@endif
