@extends('layouts.master')

@section('content')
<div class="page-wrapper">
    <div class="page-content">
        <div class="container mt-4">
            <div class="card">
                <div class="card-header">
                    <h4>Automated Tender Data Fetch</h4>
                </div>
                <div class="card-body">
                    <p>Click the button below to fetch tender data directly from the API using your IP address. The data will be processed and saved to the database.</p>

                    <button id="fetchBtn" class="btn btn-primary mb-3">Fetch and Process Tender Data</button>

                    <div id="result" style="white-space: pre-wrap; max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('fetchBtn').addEventListener('click', async () => {
    const fetchBtn = document.getElementById('fetchBtn');
    const resultDiv = document.getElementById('result');
    fetchBtn.disabled = true;
    resultDiv.textContent = 'Fetching tender data...';

    try {
        const tahun = new Date().getFullYear();
        const kdLpse = prompt('Enter LPSE code to fetch data for:', '');
        if (!kdLpse) {
            resultDiv.textContent = 'LPSE code is required.';
            fetchBtn.disabled = false;
            return;
        }
        const apiUrl = `https://isb.lkpp.go.id/isb-2/api/satudata/TenderUmumPublik/${tahun}/${kdLpse}`;

        const response = await fetch(apiUrl);
        if (!response.ok) {
            throw new Error('Failed to fetch API data: ' + response.status + ' ' + response.statusText);
        }
        const data = await response.json();

        if (!Array.isArray(data) || data.length === 0) {
            resultDiv.textContent = 'No tender data found for LPSE code ' + kdLpse;
            fetchBtn.disabled = false;
            return;
        }

        resultDiv.textContent = 'Fetched ' + data.length + ' tenders. Processing...\n\n' + JSON.stringify(data, null, 2);

        // Submit data to server for processing
        const processResponse = await fetch('{{ route('manual.tender.process') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ tender_json: JSON.stringify(data) })
        });

        if (!processResponse.ok) {
            const errorText = await processResponse.text();
            throw new Error('Failed to process data: ' + processResponse.status + ' ' + processResponse.statusText + '\\n' + errorText);
        }

        const processResultText = await processResponse.text();
        resultDiv.textContent += '\\n\\nProcessing complete. Please refresh the page or check logs for details.';

    } catch (error) {
        console.error('Fetch error:', error);
        resultDiv.textContent = 'Error: ' + error.message;
    } finally {
        fetchBtn.disabled = false;
    }
});
</script>
@endsection
