<div class="card border-primary my-4 shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0 text-white"><i class="material-icons-outlined align-middle me-2">payments</i>Konfirmasi Pembayaran</h5>
    </div>
    <div class="card-body">
        @if(session('error'))
            <div class="alert alert-danger d-flex align-items-center" role="alert">
                <i class="material-icons-outlined me-2">error</i>
                <div>{{ session('error') }}</div>
            </div>
        @endif

        @if(session('success'))
            <div class="alert alert-success d-flex align-items-center" role="alert">
                <i class="material-icons-outlined me-2">check_circle</i>
                <div>{{ session('success') }}</div>
            </div>
        @endif

        <div class="alert alert-info mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="material-icons-outlined fs-2 me-3">info</i>
                </div>
                <div>
                    <h5 class="alert-heading">Petunjuk Konfirmasi Pembayaran</h5>
                    <p><PERSON><PERSON><PERSON> isi form di bawah ini setelah Anda melakukan transfer ke rekening yang telah ditentukan.</p>
                    <p class="mb-0">Pastikan jumlah yang Anda transfer sesuai dengan total yang tertera pada invoice, termasuk kode unik.</p>
                </div>
            </div>
        </div>

        <form action="{{ route('payments.confirm.submit', $userSubscription->id) }}" method="POST" enctype="multipart/form-data" class="row g-3">
            @csrf
            <div class="col-md-6">
                <div class="form-floating mb-3">
                    <input type="number" step="0.01" class="form-control @error('amount') is-invalid @enderror" id="amount" name="amount" required value="{{ old('amount', $userSubscription->total_amount) }}" placeholder="Jumlah Pembayaran">
                    <label for="amount">Jumlah Pembayaran (Rp)</label>
                    @error('amount')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">Masukkan jumlah sesuai dengan total invoice: Rp {{ number_format($userSubscription->total_amount, 0, ',', '.') }}</div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-floating mb-3">
                    <select class="form-select @error('payment_method') is-invalid @enderror" id="payment_method" name="payment_method" required>
                        <option value="" disabled {{ old('payment_method') ? '' : 'selected' }}>Pilih metode pembayaran</option>
                        <option value="Transfer Bank BCA" {{ old('payment_method') == 'Transfer Bank BCA' ? 'selected' : '' }}>Transfer Bank BCA</option>
                        <option value="Transfer Bank Mandiri" {{ old('payment_method') == 'Transfer Bank Mandiri' ? 'selected' : '' }}>Transfer Bank Mandiri</option>
                        <option value="Transfer Bank BNI" {{ old('payment_method') == 'Transfer Bank BNI' ? 'selected' : '' }}>Transfer Bank BNI</option>
                        <option value="Transfer Bank BRI" {{ old('payment_method') == 'Transfer Bank BRI' ? 'selected' : '' }}>Transfer Bank BRI</option>
                        <option value="Transfer Bank Lainnya" {{ old('payment_method') == 'Transfer Bank Lainnya' ? 'selected' : '' }}>Transfer Bank Lainnya</option>
                    </select>
                    <label for="payment_method">Metode Pembayaran</label>
                    @error('payment_method')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="col-12">
                <label for="confirmation_proof" class="form-label">Bukti Transfer Pembayaran</label>
                <div class="input-group mb-3">
                    <span class="input-group-text"><i class="material-icons-outlined">upload_file</i></span>
                    <input type="file" class="form-control @error('confirmation_proof') is-invalid @enderror" id="confirmation_proof" name="confirmation_proof" accept=".jpg,.jpeg,.png,.pdf" required>
                    @error('confirmation_proof')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="form-text">Upload bukti transfer dalam format JPG, JPEG, PNG, atau PDF. Maksimal ukuran file 2MB.</div>
            </div>

            <div class="col-12 mt-4">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="material-icons-outlined align-middle me-2">send</i>Kirim Konfirmasi Pembayaran
                </button>
            </div>
        </form>
    </div>
</div>
