@extends('layouts.app')

@section('title', 'User Profile')

@section('content')
<div class="container mt-4">
    <h2>User Profile</h2>
    <div class="card p-4">
        <div class="row">
            <div class="col-md-4 text-center">
                @if($user->photo)
                    <img src="{{ asset('storage/' . $user->photo) }}" alt="Profile Photo" class="img-fluid rounded-circle mb-3" style="max-width: 150px;">
                @else
                    <img src="{{ asset('images/default-profile.png') }}" alt="Default Profile Photo" class="img-fluid rounded-circle mb-3" style="max-width: 150px;">
                @endif
            </div>
            <div class="col-md-8">
                <table class="table table-borderless">
                    <tbody>
                        <tr>
                            <th>Name:</th>
                            <td>{{ $user->name }}</td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>{{ $user->email }}</td>
                        </tr>
                        <tr>
                            <th>Account Type:</th>
                            <td>{{ $user->account_type }}</td>
                        </tr>
                        <tr>
                            <th>Referral Code:</th>
                            <td>{{ $user->referral_code }}</td>
                        </tr>
                        <tr>
                            <th>Total Commissions:</th>
                            <td>{{ number_format($user->total_commissions, 2) }}</td>
                        </tr>
                        <tr>
                            <th>Commission Balance:</th>
                            <td>{{ number_format($user->commission_balance, 2) }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
