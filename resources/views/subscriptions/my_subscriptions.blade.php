@extends('layouts.master')

@section('title', '<PERSON><PERSON><PERSON>')

@section('css')
<style>
    .subscription-card {
        border-radius: 12px;
        overflow: hidden;
        transition: transform 0.2s;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        height: 100%;
    }

    .subscription-card:hover {
        transform: translateY(-5px);
    }

    .subscription-card .card-header {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        border-bottom: none;
        padding: 16px 20px;
    }

    .subscription-card.active .card-header {
        background: linear-gradient(90deg, #43a047, #66bb6a);
    }

    .subscription-card.pending .card-header {
        background: linear-gradient(90deg, #ff9800, #ffb74d);
    }

    .subscription-card.expired .card-header {
        background: linear-gradient(90deg, #757575, #9e9e9e);
    }

    .subscription-card.rejected .card-header {
        background: linear-gradient(90deg, #e53935, #ef5350);
    }

    .subscription-info {
        padding: 20px;
    }

    .subscription-info .info-item {
        margin-bottom: 15px;
    }

    .subscription-info .info-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .subscription-info .info-value {
        font-weight: 500;
        color: #344767;
    }

    .days-remaining {
        font-size: 0.85rem;
        padding: 5px 10px;
        border-radius: 20px;
        display: inline-block;
        margin-top: 5px;
    }

    .referral-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .referral-card .card-header {
        background: linear-gradient(90deg, #7b1fa2, #9c27b0);
        color: white;
        border-bottom: none;
    }

    .commission-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .commission-card .card-header {
        background: linear-gradient(90deg, #f57c00, #ffb74d);
        color: white;
        border-bottom: none;
    }

    .commission-balance {
        font-size: 2rem;
        font-weight: 700;
        color: #344767;
        margin: 20px 0;
    }

    .referral-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-bottom: 1px solid #f1f1f1;
    }

    .referral-item:last-child {
        border-bottom: none;
    }

    .referral-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    .referral-info {
        flex: 1;
    }

    .referral-name {
        font-weight: 500;
        color: #344767;
        margin-bottom: 3px;
    }

    .referral-email {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 5px 10px;
        border-radius: 20px;
    }

    .referral-code-box {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
        border: 1px dashed #dee2e6;
    }

    .referral-code {
        font-family: monospace;
        font-size: 1.2rem;
        font-weight: 600;
        color: #344767;
        letter-spacing: 1px;
    }


</style>
@endsection

@section('content')

<!-- Subscription Summary Section -->
<div class="row mb-4">
    @php
        $activeSubscription = $userSubscriptions->where('status', 'active')
            ->where('end_date', '>=', now())
            ->first();

        $pendingSubscription = $userSubscriptions->where('status', 'pending')
            ->first();

        $totalReferrals = $referrals->count();
    @endphp

    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-primary bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-primary">card_membership</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Status Langganan</h6>
                    <h3 class="mb-0">
                        @if($activeSubscription)
                            <span class="text-success">Aktif</span>
                        @elseif($pendingSubscription)
                            <span class="text-warning">Menunggu</span>
                        @else
                            <span class="text-secondary">Tidak Ada Langganan</span>
                        @endif
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-success bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-success">people</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Referral Saya</h6>
                    <h3 class="mb-0">{{ $totalReferrals }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-warning bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-warning">payments</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Saldo Komisi</h6>
                    <h3 class="mb-0">Rp {{ number_format($commissionBalance, 0, ',', '.') }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Section -->
<div class="row">
    <!-- Subscriptions Section -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header d-flex align-items-center justify-content-between">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">card_membership</i>
                    Langganan Saya
                </h5>
                <a href="{{ route('subscriptions.select') }}" class="btn btn-sm btn-primary">
                    <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">add</i>
                    Berlangganan
                </a>
            </div>
            <div class="card-body">
                @if($userSubscriptions->isEmpty())
                    <div class="empty-state">
                        <i class="material-icons-outlined">card_membership</i>
                        <h5>Belum Ada Langganan</h5>
                        <p>Anda belum memiliki langganan. Mulai berlangganan dengan salah satu paket kami.</p>
                        <a href="{{ route('subscriptions.select') }}" class="btn btn-primary mt-3">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">add_circle</i>
                            Lihat Paket Langganan
                        </a>
                    </div>
                @else
                    <div class="row">
                        @foreach($userSubscriptions as $userSubscription)
                            @php
                                $statusClass = '';
                                $daysRemaining = 0;
                                $statusIcon = 'info';

                                if ($userSubscription->status === 'active') {
                                    $statusClass = 'active';
                                    $statusText = 'Aktif';
                                    $daysRemaining = \Carbon\Carbon::now()->diffInDays(\Carbon\Carbon::parse($userSubscription->end_date), false);
                                    $statusIcon = 'check_circle';

                                    if ($daysRemaining <= 0) {
                                        $statusText = 'Kadaluarsa';
                                        $statusClass = 'expired';
                                        $statusIcon = 'event_busy';
                                    }
                                } elseif ($userSubscription->status === 'pending') {
                                    $statusClass = 'pending';
                                    $statusText = 'Menunggu';
                                    $statusIcon = 'pending';
                                } elseif ($userSubscription->status === 'rejected') {
                                    $statusClass = 'rejected';
                                    $statusText = 'Ditolak';
                                    $statusIcon = 'cancel';
                                } else {
                                    $statusClass = 'expired';
                                    $statusText = 'Kadaluarsa';
                                    $statusIcon = 'event_busy';
                                }

                                // Get payment information
                                $payment = $userSubscription->payments()->latest()->first();
                            @endphp
                            <div class="col-md-6 mb-4">
                                <div class="subscription-card {{ $statusClass }}">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">{{ $userSubscription->subscription->name }}</h5>
                                        <span class="status-badge bg-white text-{{ $statusClass === 'active' ? 'success' : ($statusClass === 'pending' ? 'warning' : ($statusClass === 'rejected' ? 'danger' : 'secondary')) }}">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">{{ $statusIcon }}</i>
                                            {{ $statusText }}
                                        </span>
                                    </div>
                                    <div class="subscription-info">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <div class="info-item">
                                                    <div class="info-label">Durasi</div>
                                                    <div class="info-value">{{ $userSubscription->subscription->duration_days }} hari</div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="info-item">
                                                    <div class="info-label">Harga</div>
                                                    <div class="info-value">Rp {{ number_format($userSubscription->subscription->price, 0, ',', '.') }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <div class="info-item">
                                                    <div class="info-label">Tanggal Mulai</div>
                                                    <div class="info-value">{{ \Carbon\Carbon::parse($userSubscription->start_date)->format('d M Y') }}</div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="info-item">
                                                    <div class="info-label">Tanggal Berakhir</div>
                                                    <div class="info-value">{{ \Carbon\Carbon::parse($userSubscription->end_date)->format('d M Y') }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        @if($userSubscription->status === 'active')
                                            <div class="progress mb-3" style="height: 8px;">
                                                @php
                                                    $totalDays = \Carbon\Carbon::parse($userSubscription->start_date)->diffInDays(\Carbon\Carbon::parse($userSubscription->end_date));
                                                    $daysUsed = \Carbon\Carbon::parse($userSubscription->start_date)->diffInDays(now());
                                                    $percentUsed = min(100, max(0, ($daysUsed / $totalDays) * 100));

                                                    $progressClass = 'bg-success';
                                                    if ($percentUsed > 75) {
                                                        $progressClass = 'bg-warning';
                                                    }
                                                    if ($percentUsed > 90) {
                                                        $progressClass = 'bg-danger';
                                                    }
                                                @endphp
                                                <div class="progress-bar {{ $progressClass }}" role="progressbar" style="width: {{ $percentUsed }}%" aria-valuenow="{{ $percentUsed }}" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        @endif

                                        @if($userSubscription->status === 'active' && $daysRemaining > 0)
                                            <div class="days-remaining bg-{{ $daysRemaining <= 7 ? 'danger' : ($daysRemaining <= 30 ? 'warning' : 'success') }} text-white text-center mb-3">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">schedule</i>
                                                {{ $daysRemaining }} hari tersisa
                                            </div>
                                        @elseif($userSubscription->status === 'active' && $daysRemaining <= 0)
                                            <div class="days-remaining bg-danger text-white text-center mb-3">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">event_busy</i>
                                                Kadaluarsa
                                            </div>
                                        @endif

                                        @if($payment)
                                            <div class="info-item mb-3">
                                                <div class="info-label">Status Pembayaran</div>
                                                <div class="info-value">
                                                    <span class="badge bg-{{ $payment->status == 'confirmed' ? 'success' : ($payment->status == 'pending' ? 'warning' : 'danger') }}">
                                                        @if($payment->status == 'confirmed')
                                                            Dikonfirmasi
                                                        @elseif($payment->status == 'pending')
                                                            Menunggu
                                                        @elseif($payment->status == 'rejected')
                                                            Ditolak
                                                        @else
                                                            {{ ucfirst($payment->status) }}
                                                        @endif
                                                    </span>
                                                    <span class="ms-2 text-muted small">{{ $payment->payment_method }}</span>
                                                </div>
                                            </div>
                                        @endif

                                        <div class="d-flex justify-content-between mt-4">
                                            <a href="{{ route('subscriptions.invoice.show', $userSubscription->id) }}" class="btn btn-sm btn-primary">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">receipt</i>
                                                Lihat Faktur
                                            </a>

                                            @if($userSubscription->status === 'active' && $daysRemaining <= 14 && $daysRemaining > 0)
                                                <a href="{{ route('payments.confirm.form', $userSubscription->id) }}" class="btn btn-sm btn-success">
                                                    <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">autorenew</i>
                                                    Perpanjang
                                                </a>
                                            @elseif($userSubscription->status === 'active' && $daysRemaining <= 0)
                                                <a href="{{ route('subscriptions.select') }}" class="btn btn-sm btn-warning">
                                                    <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">add_circle</i>
                                                    Berlangganan Lagi
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Referrals and Commission Section -->
    <div class="col-lg-4">
        <!-- Referral Code Section -->
        <div class="card mb-4">
            <div class="card-header d-flex align-items-center justify-content-between">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">share</i>
                    Kode Referral Saya
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Bagikan kode referral Anda kepada teman dan dapatkan komisi 10% saat mereka berlangganan!</p>

                <div class="referral-code-box">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="referral-code">{{ auth()->user()->referral_code ?? 'Belum ada kode referral' }}</span>
                        <button class="btn btn-sm btn-outline-primary" onclick="copyReferralCode()" id="copy-btn">
                            <i class="material-icons-outlined" style="font-size: 1rem;">content_copy</i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Commission Balance Section -->
        <div class="card commission-card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">payments</i>
                    Saldo Komisi
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="commission-balance">Rp {{ number_format($commissionBalance, 0, ',', '.') }}</div>
                    <p class="text-muted">Dapatkan komisi 10% untuk setiap langganan referral</p>
                </div>

                @php
                    $user = Auth::user();
                    $totalCommissions = $user->commissions()->sum('amount');
                    $totalWithdrawals = $user->withdrawals()->where('status', 'approved')->sum('amount');
                    $pendingWithdrawals = $user->withdrawals()->where('status', 'pending')->sum('amount');
                @endphp

                <div class="row mb-4">
                    <div class="col-6">
                        <div class="info-item">
                            <div class="info-label">Total Diterima</div>
                            <div class="info-value text-success">Rp {{ number_format($totalCommissions, 0, ',', '.') }}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="info-item">
                            <div class="info-label">Total Penarikan</div>
                            <div class="info-value text-danger">Rp {{ number_format($totalWithdrawals, 0, ',', '.') }}</div>
                        </div>
                    </div>
                </div>

                @if($pendingWithdrawals > 0)
                    <div class="alert alert-warning d-flex align-items-center mb-3" role="alert">
                        <i class="material-icons-outlined me-2">pending</i>
                        <div>
                            Anda memiliki penarikan tertunda sebesar Rp {{ number_format($pendingWithdrawals, 0, ',', '.') }}
                        </div>
                    </div>
                @endif

                <div class="d-grid gap-2">
                    <a href="{{ route('withdrawals.request') }}" class="btn btn-warning {{ $commissionBalance < 50000 ? 'disabled' : '' }}">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">account_balance_wallet</i>
                        Ajukan Penarikan
                    </a>
                    <a href="{{ route('withdrawals.history') }}" class="btn btn-outline-secondary">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">history</i>
                        Riwayat Penarikan
                    </a>
                    <a href="{{ route('commissions.history') }}" class="btn btn-outline-primary">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">monetization_on</i>
                        Riwayat Komisi
                    </a>
                </div>

                @if($commissionBalance < 50000)
                    <div class="alert alert-info mt-3 mb-0 d-flex align-items-center" role="alert">
                        <i class="material-icons-outlined me-2">info</i>
                        <div>
                            Jumlah penarikan minimum adalah Rp 50.000
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Referrals Section -->
        <div class="card referral-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">people</i>
                    Referral Saya
                </h5>
            </div>
            <div class="card-body p-0">
                @if($referrals->isEmpty())
                    <div class="empty-state py-4">
                        <i class="material-icons-outlined">people</i>
                        <h5>Belum Ada Referral</h5>
                        <p>Bagikan kode referral Anda untuk mulai mendapatkan komisi!</p>
                    </div>
                @else
                    <div class="referral-list">
                        @foreach($referrals as $referral)
                            <div class="referral-item">
                                <div class="referral-avatar">
                                    @if($referral->referred->photo)
                                        <img src="{{ asset("storage/{$referral->referred->photo}") }}" alt="{{ $referral->referred->name }}" class="rounded-circle" style="width: 100%; height: 100%; object-fit: cover;">
                                    @else
                                        <i class="material-icons-outlined text-primary">person</i>
                                    @endif
                                </div>
                                <div class="referral-info">
                                    <div class="referral-name">{{ $referral->referred->name }}</div>
                                    <div class="referral-email">{{ $referral->referred->email }}</div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    @if($referrals->count() > 5)
                        <div class="text-center p-3">
                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#allReferralsModal">
                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">people</i>
                                Lihat Semua Referral
                            </button>
                        </div>
                    @endif

                    <!-- All Referrals Modal -->
                    <div class="modal fade" id="allReferralsModal" tabindex="-1" aria-labelledby="allReferralsModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header" style="background: linear-gradient(90deg, #7b1fa2, #9c27b0); color: white;">
                                    <h5 class="modal-title text-white" id="allReferralsModalLabel">
                                        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">people</i>
                                        Semua Referral Saya
                                    </h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    @if($referrals->isEmpty())
                                        <div class="empty-state py-4">
                                            <i class="material-icons-outlined">people</i>
                                            <h5>Belum Ada Referral</h5>
                                            <p>Bagikan kode referral Anda untuk mulai mendapatkan komisi!</p>
                                        </div>
                                    @else
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Pengguna</th>
                                                        <th>Email</th>
                                                        <th>Tanggal Bergabung</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($referrals as $referral)
                                                        <tr>
                                                            <td>
                                                                <div class="d-flex align-items-center">
                                                                    <div class="referral-avatar me-2">
                                                                        @if($referral->referred->photo)
                                                                            <img src="{{ asset("storage/{$referral->referred->photo}") }}" alt="{{ $referral->referred->name }}" class="rounded-circle" style="width: 100%; height: 100%; object-fit: cover;">
                                                                        @else
                                                                            <i class="material-icons-outlined text-primary">person</i>
                                                                        @endif
                                                                    </div>
                                                                    <span class="fw-medium">{{ $referral->referred->name }}</span>
                                                                </div>
                                                            </td>
                                                            <td>{{ $referral->referred->email }}</td>
                                                            <td>{{ $referral->created_at->format('d M Y') }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @endif
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                    <a href="{{ route('commissions.history') }}" class="btn btn-primary">
                                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">payments</i>
                                        Lihat Riwayat Komisi
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function copyReferralCode() {
        const referralCode = document.querySelector('.referral-code').innerText;
        navigator.clipboard.writeText(referralCode).then(() => {
            const copyBtn = document.getElementById('copy-btn');
            copyBtn.innerHTML = '<i class="material-icons-outlined" style="font-size: 1rem;">check</i>';
            copyBtn.classList.remove('btn-outline-primary');
            copyBtn.classList.add('btn-success');

            setTimeout(() => {
                copyBtn.innerHTML = '<i class="material-icons-outlined" style="font-size: 1rem;">content_copy</i>';
                copyBtn.classList.remove('btn-success');
                copyBtn.classList.add('btn-outline-primary');
            }, 2000);

            // Show toast notification
            if (typeof showToast === 'function') {
                showToast('Kode referral berhasil disalin ke clipboard!', 'success');
            }
        });
    }
</script>
@endsection
