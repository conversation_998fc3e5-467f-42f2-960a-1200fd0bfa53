@extends('layouts.master')

@section('title', '<PERSON><PERSON><PERSON>')

@section('css')
<style>
    /* Card Styles */
    .card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s;
        border: none;
    }

    /* Header Styles */
    .tenderid_header {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        padding: 16px 20px;
    }

    /* Package Card Styles */
    .package-card {
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        border: 1px solid #e9ecef;
        background-color: #fff;
    }

    .package-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .package-header {
        padding: 20px;
        border-bottom: 1px solid #e9ecef;
        background-color: #f8f9fa;
    }

    .package-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0;
        color: #344767;
    }

    .package-body {
        padding: 20px;
    }

    .package-price {
        font-size: 1.75rem;
        font-weight: 700;
        color: #4481eb;
        margin-bottom: 10px;
    }

    .package-duration {
        display: inline-block;
        background-color: rgba(68, 129, 235, 0.1);
        color: #4481eb;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 15px;
    }

    .package-description {
        color: #6c757d;
        margin-bottom: 20px;
        min-height: 60px;
    }

    /* Active Subscription Styles */
    .subscription-info-card {
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .subscription-info-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .subscription-info-icon {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        background-color: rgba(68, 129, 235, 0.1);
    }

    .subscription-info-icon i {
        color: #4481eb;
        font-size: 24px;
    }

    .subscription-info-content h6 {
        font-weight: 600;
        color: #344767;
        margin-bottom: 5px;
    }

    .subscription-info-content p {
        color: #6c757d;
        margin-bottom: 0;
    }

    .badge-status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* Feature List Styles */
    .feature-list {
        list-style: none;
        padding-left: 0;
        margin-bottom: 20px;
    }

    .feature-list li {
        padding: 8px 0;
        display: flex;
        align-items: center;
    }

    .feature-list li i {
        color: #4481eb;
        margin-right: 10px;
        font-size: 18px;
    }

    /* Button Styles */
    .btn-select-package {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-select-package:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .package-card {
            margin-bottom: 20px;
        }
    }
</style>
@endsection

@section('content')

<div class="container py-4">
    @if($activeSubscription)
    <div class="card mb-4">
        <div class="card-header tenderid_header d-flex align-items-center">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">verified</i>
            <h5 class="mb-0 text-white">Langganan Anda Sudah Aktif</h5>
        </div>
        <div class="card-body p-4">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="subscription-info-card d-flex align-items-center">
                        <div class="subscription-info-icon">
                            <i class="material-icons-outlined">card_membership</i>
                        </div>
                        <div class="subscription-info-content">
                            <h6>Paket Langganan</h6>
                            <p>{{ $activeSubscription->subscription->name }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="subscription-info-card d-flex align-items-center">
                        <div class="subscription-info-icon">
                            <i class="material-icons-outlined">verified</i>
                        </div>
                        <div class="subscription-info-content">
                            <h6>Status</h6>
                            <span class="badge-status bg-success text-white">
                                <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">check_circle</i>
                                {{ ucfirst($activeSubscription->status) }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="subscription-info-card d-flex align-items-center">
                        <div class="subscription-info-icon">
                            <i class="material-icons-outlined">event</i>
                        </div>
                        <div class="subscription-info-content">
                            <h6>Tanggal Mulai</h6>
                            <p>{{ \Carbon\Carbon::parse($activeSubscription->start_date)->format('d M Y') }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="subscription-info-card d-flex align-items-center">
                        <div class="subscription-info-icon">
                            <i class="material-icons-outlined">event_busy</i>
                        </div>
                        <div class="subscription-info-content">
                            <h6>Tanggal Berakhir</h6>
                            <p>{{ \Carbon\Carbon::parse($activeSubscription->end_date)->format('d M Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            @php
                $now = \Carbon\Carbon::now();
                $paymentOpenDate = \Carbon\Carbon::parse($activeSubscription->end_date)->subWeeks(2);
                $daysLeft = $now->diffInDays(\Carbon\Carbon::parse($activeSubscription->end_date), false);
            @endphp

            <div class="card border shadow-none rounded-4 mt-3">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="wh-48 d-flex align-items-center justify-content-center bg-light rounded-circle me-3" style="width: 48px; height: 48px;">
                            <span class="material-icons-outlined text-primary">schedule</span>
                        </div>
                        <div>
                            <h6 class="mb-0 fw-bold">Status Langganan</h6>
                            <p class="mb-0 text-secondary">
                                @if($daysLeft > 0)
                                    Langganan Anda akan berakhir dalam {{ $daysLeft }} hari
                                @else
                                    Langganan Anda telah berakhir
                                @endif
                            </p>
                        </div>
                    </div>

                    @if($now->greaterThanOrEqualTo($paymentOpenDate) && $now->lessThanOrEqualTo(\Carbon\Carbon::parse($activeSubscription->end_date)))
                        <div class="text-center mt-4">
                            <a href="{{ route('payments.confirm.form', $activeSubscription->id) }}" class="btn btn-primary btn-select-package">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">credit_card</i>
                                Perpanjang Langganan
                            </a>
                        </div>
                    @else
                        <div class="alert alert-info mt-3 d-flex align-items-center" role="alert">
                            <i class="material-icons-outlined me-2">info</i>
                            <div>Halaman perpanjangan langganan akan tersedia 2 minggu sebelum langganan berakhir.</div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @else
    <div class="card mb-4">
        <div class="card-header tenderid_header d-flex align-items-center">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">card_membership</i>
            <h5 class="mb-0 text-white">Pilih Paket Langganan</h5>
        </div>
        <div class="card-body p-4">
            <div class="alert alert-info d-flex align-items-center mb-4" role="alert">
                <i class="material-icons-outlined me-2">info</i>
                <div>Silakan pilih paket langganan yang sesuai dengan kebutuhan Anda.</div>
            </div>

            <div class="row">
                @foreach($subscriptions as $subscription)
                <div class="col-md-4 mb-4">
                    <div class="package-card">
                        <div class="package-header">
                            <h5 class="package-title">{{ $subscription->name }}</h5>
                        </div>
                        <div class="package-body">
                            <div class="package-price">Rp {{ number_format($subscription->price, 0, ',', '.') }}</div>
                            <div class="package-duration">
                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">schedule</i>
                                {{ $subscription->duration_days }} hari
                            </div>
                            <div class="package-description">
                                {{ $subscription->description ?: 'Paket langganan standar untuk mengakses semua fitur TenderID.' }}
                            </div>

                            <ul class="feature-list">
                                <li>
                                    <i class="material-icons-outlined">check_circle</i>
                                    <span>Akses semua fitur TenderID</span>
                                </li>
                                <li>
                                    <i class="material-icons-outlined">check_circle</i>
                                    <span>Notifikasi tender terbaru</span>
                                </li>
                                <li>
                                    <i class="material-icons-outlined">check_circle</i>
                                    <span>Dukungan pelanggan</span>
                                </li>
                            </ul>

                            <form action="{{ route('subscriptions.invoice') }}" method="POST">
                                @csrf
                                <input type="hidden" name="subscription_id" value="{{ $subscription->id }}">
                                <button type="submit" class="btn btn-primary btn-select-package w-100">
                                    <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">shopping_cart</i>
                                    Pilih Paket
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
