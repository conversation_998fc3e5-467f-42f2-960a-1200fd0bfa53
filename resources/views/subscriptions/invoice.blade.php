@extends('layouts.master')

@section('title', 'Invoice Langganan')

@section('content')
<x-page-title title="Invoice" pagetitle="<PERSON>ganan" />
<style>
  .card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: none;
  }
  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
  }
  .card-body {
    padding: 1rem 1.25rem;
  }
  .table-invoice {
    width: 100%;
    border-collapse: collapse;
  }
  .table-invoice th, .table-invoice td {
    border: 1px solid #dee2e6;
    padding: 0.75rem;
    vertical-align: top;
  }
  .table-invoice th {
    background-color: #e9ecef;
    text-align: left;
  }
  .text-inverse {
    color: #495057;
  }
  .bg-light {
    background-color: #f8f9fa !important;
  }
  .bg-primary {
    background-color: #0d6efd !important;
  }
  .text-white {
    color: #fff !important;
  }
  .mb-0 {
    margin-bottom: 0 !important;
  }
  .p-4 {
    padding: 1.5rem !important;
  }
  .m-0 {
    margin: 0 !important;
  }
  .invoice-detail {
    font-size: 0.875rem;
    color: #6c757d;
  }
  .row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.75rem;
    margin-left: -0.75rem;
  }
  .col {
    flex: 1 0 0%;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
  }
  .col-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .text-center {
    text-align: center !important;
  }
  .text-right {
    text-align: right !important;
  }
  .btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    user-select: none;
  }
  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
  }
  .btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
  }
  .btn-dark {
    color: #fff;
    background-color: #212529;
    border-color: #212529;
  }
  .me-2 {
    margin-right: 0.5rem !important;
  }
  .shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0,0,0,.175) !important;
  }
  .radius-10 {
    border-radius: 10px !important;
  }
</style>

<div class="card radius-10 shadow-lg border-0">
  <div class="card-header py-3 bg-primary text-white">
    <div class="row align-items-center g-3">
      <div class="col-12 col-lg-6">
        <h5 class="mb-0 text-white"><i class="material-icons-outlined align-middle me-2">receipt_long</i>Invoice Langganan {{ config('app.name') }}</h5>
      </div>
      <div class="col-12 col-lg-6 text-md-end">
        <a href="{{ route('subscriptions.invoice.print', $userSubscription->id) }}" target="_blank" class="btn btn-light btn-sm">
          <i class="material-icons-outlined align-middle me-1">print</i>Cetak Invoice
        </a>
      </div>
    </div>
  </div>

  <div class="card-body border-bottom">
    <div class="row">
      <div class="col-md-6 mb-4">
        <div class="invoice-info-card bg-light p-4 rounded h-100">
          <h6 class="fw-bold mb-3"><i class="material-icons-outlined align-middle me-2 text-primary">business</i>Dari</h6>
          <address class="mb-0">
            <strong class="text-inverse fs-5">{{ config('app.name') }}</strong><br>
            Jalan FO, Internet<br>
            Online IIX<br>
          </address>
        </div>
      </div>
      <div class="col-md-6 mb-4">
        <div class="invoice-info-card bg-light p-4 rounded h-100">
          <h6 class="fw-bold mb-3"><i class="material-icons-outlined align-middle me-2 text-primary">person</i>Untuk</h6>
          <address class="mb-0">
            <strong class="text-inverse fs-5">{{ auth()->user()->name }}</strong><br>
            {{ auth()->user()->email }}<br>
          </address>
        </div>
      </div>
    </div>

    <div class="row mt-2">
      <div class="col-md-6 mb-3">
        <div class="invoice-info-card bg-light p-4 rounded">
          <h6 class="fw-bold mb-3"><i class="material-icons-outlined align-middle me-2 text-primary">event</i>Periode Langganan</h6>
          <p class="mb-0 fs-5">{{ \Carbon\Carbon::parse($userSubscription->start_date)->format('d F Y') }} - {{ \Carbon\Carbon::parse($userSubscription->end_date)->format('d F Y') }}</p>
        </div>
      </div>
      <div class="col-md-6 mb-3">
        <div class="invoice-info-card bg-light p-4 rounded">
          <h6 class="fw-bold mb-3"><i class="material-icons-outlined align-middle me-2 text-primary">receipt</i>Detail Invoice</h6>
          <p class="mb-0">
            <span class="badge bg-primary fs-6 mb-2">Invoice #{{ $userSubscription->id }}</span><br>
            Paket Langganan
          </p>
        </div>
      </div>
    </div>
  </div>
  <div class="card-body">
    <h5 class="fw-bold mb-4"><i class="material-icons-outlined align-middle me-2 text-primary">shopping_cart</i>Detail Paket</h5>
    <div class="table-responsive">
      <table class="table table-invoice table-striped border">
        <thead class="bg-light">
          <tr>
            <th class="fw-bold">Nama Paket</th>
            <th class="text-center fw-bold" style="width: 20%;">Harga</th>
            <th class="text-center fw-bold" style="width: 20%;">Kode Unik</th>
            <th class="text-end fw-bold" style="width: 20%;">Total Harga</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <i class="material-icons-outlined text-primary fs-3">card_membership</i>
                </div>
                <div class="ms-3">
                  <h6 class="mb-0">{{ $userSubscription->subscription->name }}</h6>
                  <span class="badge bg-info">Durasi: {{ $userSubscription->subscription->duration_days }} hari</span>
                </div>
              </div>
            </td>
            <td class="text-center">Rp {{ number_format($userSubscription->subscription->price, 0, ',', '.') }}</td>
            <td class="text-center">
              <span class="badge bg-warning text-dark fs-6">{{ $userSubscription->unique_code }}</span>
            </td>
            <td class="text-end fw-bold">Rp {{ number_format($userSubscription->total_amount, 0, ',', '.') }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <hr>

    @php
      $payment = \App\Models\Payment::where('user_subscription_id', $userSubscription->id)
                ->where('user_id', auth()->id())
                ->latest()
                ->first();
    @endphp

   @if($payment)
      @if($payment->confirmation_status === 'confirmed')
        <div class="card border-success mb-4">
          <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="material-icons-outlined align-middle me-2">check_circle</i>Pembayaran Berhasil</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Jumlah Pembayaran</label>
                  <h5>Rp {{ number_format($payment->amount, 0, ',', '.') }}</h5>
                </div>
                <div class="mb-3">
                  <label class="form-label text-muted">Metode Pembayaran</label>
                  <h5>{{ $payment->payment_method }}</h5>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Status</label>
                  <h5><span class="badge bg-success fs-6">{{ ucfirst($payment->confirmation_status) }}</span></h5>
                </div>
                <div class="mb-3">
                  <label class="form-label text-muted">Bukti Transfer</label>
                  <div>
                    <a href="{{ asset('storage/' . $payment->confirmation_proof) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                      <i class="material-icons-outlined align-middle me-1">visibility</i>Lihat Bukti
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      @else
        <div class="card border-info mb-4">
          <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="material-icons-outlined align-middle me-2">pending</i>Menunggu Konfirmasi</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Jumlah Pembayaran</label>
                  <h5>Rp {{ number_format($payment->amount, 0, ',', '.') }}</h5>
                </div>
                <div class="mb-3">
                  <label class="form-label text-muted">Metode Pembayaran</label>
                  <h5>{{ $payment->payment_method }}</h5>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Status</label>
                  <h5><span class="badge bg-warning text-dark fs-6">Menunggu Konfirmasi</span></h5>
                </div>
                <div class="mb-3">
                  <label class="form-label text-muted">Bukti Transfer</label>
                  <div>
                    <a href="{{ asset('storage/' . $payment->confirmation_proof) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                      <i class="material-icons-outlined align-middle me-1">visibility</i>Lihat Bukti
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div class="alert alert-info mt-3">
              <i class="material-icons-outlined align-middle me-2">info</i>
              Pembayaran Anda sedang diproses. Admin akan segera mengkonfirmasi pembayaran Anda.
            </div>
          </div>
        </div>
      @endif
    @else
      @include('payments._confirm_payment_form', ['userSubscription' => $userSubscription])
    @endif


    <div class="card mt-4 border-0 shadow">
      <div class="card-header bg-light">
        <h5 class="mb-0 fw-bold"><i class="material-icons-outlined align-middle me-2 text-primary">calculate</i>Ringkasan Pembayaran</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-8">
            <div class="table-responsive">
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted">Subtotal</td>
                  <td class="text-end fw-bold">Rp {{ number_format($userSubscription->subscription->price, 0, ',', '.') }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Kode Unik</td>
                  <td class="text-end fw-bold">{{ $userSubscription->unique_code }}</td>
                </tr>
                <tr class="border-top">
                  <td class="text-muted fs-5">Total Pembayaran</td>
                  <td class="text-end fs-4 fw-bold text-primary">Rp {{ number_format($userSubscription->total_amount, 0, ',', '.') }}</td>
                </tr>
              </table>
            </div>
          </div>
          <div class="col-md-4">
            <div class="bg-primary text-white p-4 rounded text-center h-100 d-flex flex-column justify-content-center">
              <h6 class="mb-2">TOTAL YANG HARUS DIBAYAR</h6>
              <h3 class="mb-0 fw-bold">Rp {{ number_format($userSubscription->total_amount, 0, ',', '.') }}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- begin invoice-note -->
    <div class="card mt-4 border-0 shadow">
      <div class="card-header bg-light">
        <h5 class="mb-0 fw-bold"><i class="material-icons-outlined align-middle me-2 text-primary">info</i>Informasi Pembayaran</h5>
      </div>
      <div class="card-body">
        <div class="alert alert-warning">
          <div class="d-flex">
            <div class="flex-shrink-0">
              <i class="material-icons-outlined fs-2 me-3">account_balance</i>
            </div>
            <div>
              <h5 class="alert-heading">Petunjuk Pembayaran</h5>
              <p class="mb-0">Silakan transfer total harga <strong>Rp {{ number_format($userSubscription->total_amount, 0, ',', '.') }}</strong> (termasuk kode unik) HANYA ke rekening:</p>
              <div class="d-flex align-items-center mt-2 p-3 bg-light rounded">
                <div class="me-3">
                  <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5c/Bank_Central_Asia.svg/2560px-Bank_Central_Asia.svg.png" alt="BCA" height="30">
                </div>
                <div>
                  <h5 class="mb-0">Bank BCA</h5>
                  <p class="mb-0 fs-5 fw-bold">587 022 2892</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-4">
          <h6 class="fw-bold"><i class="material-icons-outlined align-middle me-2">notes</i>Catatan Penting:</h6>
          <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex">
              <i class="material-icons-outlined text-primary me-2">check_circle</i>
              <span>Invoice ini adalah bukti transaksi yang sah.</span>
            </li>
            <li class="list-group-item d-flex">
              <i class="material-icons-outlined text-primary me-2">check_circle</i>
              <span>Pembayaran harus dilakukan hari ini.</span>
            </li>
            <li class="list-group-item d-flex">
              <i class="material-icons-outlined text-primary me-2">check_circle</i>
              <span>Jika ada pertanyaan mengenai invoice ini, silakan hubungi support.</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!-- end invoice-note -->
  </div>

  <div class="card-footer py-3 bg-transparent">
    <p class="text-center mb-2">
      TERIMA KASIH ATAS KERJASAMA ANDA
    </p>
    <p class="text-center d-flex align-items-center gap-3 justify-content-center mb-0">
      <span><i class="bi bi-globe"></i> www.tenderid.com</span>
      <span><i class="bi bi-telephone-fill"></i> T:+62 82123082994</span>
      <span><i class="bi bi-envelope-fill"></i> <EMAIL></span>
    </p>
  </div>
</div>
@endsection
