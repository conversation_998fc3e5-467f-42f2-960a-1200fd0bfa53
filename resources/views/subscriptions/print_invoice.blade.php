@extends('layouts.print')

@section('title', 'Invoice Langganan')

@section('content')
<style>
  .card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: none;
  }
  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
  }
  .card-body {
    padding: 1rem 1.25rem;
  }
  .table-invoice {
    width: 100%;
    border-collapse: collapse;
  }
  .table-invoice th, .table-invoice td {
    border: 1px solid #dee2e6;
    padding: 0.75rem;
    vertical-align: top;
  }
  .table-invoice th {
    background-color: #e9ecef;
    text-align: left;
  }
  .text-inverse {
    color: #495057;
  }
  .bg-light {
    background-color: #f8f9fa !important;
  }
  .bg-primary {
    background-color: #0d6efd !important;
  }
  .text-white {
    color: #fff !important;
  }
  .mb-0 {
    margin-bottom: 0 !important;
  }
  .p-4 {
    padding: 1.5rem !important;
  }
  .m-0 {
    margin: 0 !important;
  }
  .invoice-detail {
    font-size: 0.875rem;
    color: #6c757d;
  }
  .row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.75rem;
    margin-left: -0.75rem;
  }
  .col {
    flex: 1 0 0%;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
  }
  .col-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .text-center {
    text-align: center !important;
  }
  .text-right {
    text-align: right !important;
  }
  .btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    user-select: none;
  }
  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
  }
  .btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
  }
  .btn-dark {
    color: #fff;
    background-color: #212529;
    border-color: #212529;
  }
  .me-2 {
    margin-right: 0.5rem !important;
  }
  .shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0,0,0,.175) !important;
  }
  .radius-10 {
    border-radius: 10px !important;
  }
</style>

<div class="card radius-10">
  <div class="card-header py-3">
    <div class="row align-items-center g-3">
      <div class="col-12 col-lg-6">
        <h5 class="mb-0">Invoice Langganan {{ config('app.name') }}</h5>
      </div>
      <div class="col-12 col-lg-6 text-md-end">
      </div>
    </div>
  </div>
  <div class="card-header py-2">
    <div class="row row-cols-1 row-cols-lg-3">
      <div class="col">
        <div>
          <small>Dari</small>
          <address class="m-t-5 m-b-5">
            <strong class="text-inverse">{{ config('app.name') }}</strong><br>
            Jalan FO, Internet<br>
            Online IIX<br>
          </address>
        </div>
      </div>
      <div class="col">
        <div>
          <small>Untuk</small>
          <address class="m-t-5 m-b-5">
            <strong class="text-inverse">{{ auth()->user()->name }}</strong><br>
            {{ auth()->user()->email }}<br>
          </address>
        </div>
      </div>
      <div class="col">
        <div>
          <small>Invoice / Periode Langganan</small>
          <div><b>{{ \Carbon\Carbon::parse($userSubscription->start_date)->format('d F Y') }} - {{ \Carbon\Carbon::parse($userSubscription->end_date)->format('d F Y') }}</b></div>
          <div class="invoice-detail">
            #{{ $userSubscription->id }}<br>
            Paket Langganan
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-invoice">
        <thead>
          <tr>
            <th>Nama Paket</th>
            <th class="text-center" style="width: 20%;">Harga</th>
            <th class="text-center" style="width: 20%;">Kode Unik</th>
            <th class="text-right" style="width: 20%;">Total Harga</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              <span class="text-inverse">{{ $userSubscription->subscription->name }}</span><br>
              <small>Durasi: {{ $userSubscription->subscription->duration_days }} hari</small>
            </td>
            <td class="text-center">Rp {{ number_format($userSubscription->subscription->price, 2) }}</td>
            <td class="text-center">{{ $userSubscription->unique_code }}</td>
            <td class="text-right">Rp {{ number_format($userSubscription->total_amount, 2) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="row bg-light align-items-center m-0">
      <div class="col col-auto p-4">
        <p class="mb-0">SUBTOTAL</p>
        <h4 class="mb-0">Rp {{ number_format($userSubscription->subscription->price, 2) }}</h4>
      </div>
      <div class="col col-auto p-4">
        <i class="bi bi-plus-lg text-muted"></i>
      </div>
      <div class="col col-auto me-auto p-4">
        <p class="mb-0">KODE UNIK</p>
        <h4 class="mb-0">{{ $userSubscription->unique_code }}</h4>
      </div>
      <div class="col bg-primary col-auto p-4">
        <p class="mb-0 text-white">TOTAL</p>
        <h4 class="mb-0 text-white">Rp {{ number_format($userSubscription->total_amount, 2) }}</h4>
      </div>
    </div><!--end row-->

    <hr>
    <!-- begin invoice-note -->
    <div class="my-3">
      <h5 class="mb-3">Catatan Invoice</h5>
      * Invoice ini adalah bukti transaksi yang sah.<br>
      * Silakan transfer total harga termasuk kode unik HANYA ke rekening <strong>Bank BCA : ************</strong>.<br>
      * Pembayaran harus dilakukan hari ini.<br>
      * Jika ada pertanyaan mengenai invoice ini, silakan hubungi support.
    </div>
    <!-- end invoice-note -->
  </div>

  <div class="card-footer py-3 bg-transparent">
    <p class="text-center mb-2">
      TERIMA KASIH ATAS KERJASAMA ANDA
    </p>
    <p class="text-center d-flex align-items-center gap-3 justify-content-center mb-0">
      <span><i class="bi bi-globe"></i> www.tenderid.com</span>
      <span><i class="bi bi-telephone-fill"></i> T:+62 ***********</span>
      <span><i class="bi bi-envelope-fill"></i> <EMAIL></span>
    </p>
  </div>
</div>
@endsection

@section('scripts')
<script>
  window.onload = function() {
    window.print();
  }
</script>
@endsection