<script src="{{ URL::asset('build/js/bootstrap.bundle.min.js') }}"></script>

<!--plugins-->
<script src="{{ URL::asset('build/js/jquery.min.js') }}"></script>
<!--plugins-->
<script src="{{ URL::asset('build/plugins/perfect-scrollbar/js/perfect-scrollbar.js') }}"></script>
<script src="{{ URL::asset('build/plugins/metismenu/metisMenu.min.js') }}"></script>
<script src="{{ URL::asset('build/plugins/simplebar/js/simplebar.min.js') }}"></script>
<script src="{{ URL::asset('build/js/main.js?v=12345') }}"></script>

<!-- Force HTTPS for all AJAX requests -->
<script>
    $(document).ready(function() {
        // Set up AJAX to always use HTTPS
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                // Force HTTPS for all AJAX requests
                if (settings.url.indexOf('http:') === 0) {
                    settings.url = settings.url.replace('http:', 'https:');
                }
            }
        });
    });
</script>