@php
use Illuminate\Support\Facades\Auth;
@endphp

<header class="top-header">
  <nav class="navbar navbar-expand align-items-center gap-4">
    <div class="btn-toggle">
      <a href="javascript:;"><i class="material-icons-outlined">menu</i></a>
    </div>
    <div class="search-bar flex-grow-1">
    <div class="position-relative">
        <input id="search-input" class="form-control rounded-5 px-5 search-control d-lg-block d-none" type="text" placeholder="Search tender aja (yang lain ntar saat berbayar)">
        <span class="material-icons-outlined position-absolute d-lg-block d-none ms-3 translate-middle-y start-0 top-50">search </span>
        <span class="material-icons-outlined position-absolute me-3 translate-middle-y end-0 top-50 search-close">close</span>

        <div class="search-popup p-3">
            <div class="card rounded-4 overflow-hidden">
            <div class="card-header d-lg-none">
              <div class="position-relative">
                <input id="mobile-search-input" class="form-control rounded-5 px-5 mobile-search-control" type="text" placeholder="Search tender aja (yang lain ntar saat berbayar)">
                <span
                  class="material-icons-outlined position-absolute ms-3 translate-middle-y start-0 top-50">search</span>
                <span
                  class="material-icons-outlined position-absolute me-3 translate-middle-y end-0 top-50 mobile-search-close">close</span>
              </div>
            </div>
                <div class="card-body search-content">
                    <p class="search-title">Hasil Pencarian</p>
                    <div id="search-results" class="search-list d-flex flex-column gap-2">
                        <p class="text-muted">Masukkan setidaknya 3 huruf kata kunci untuk mencari...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    <ul class="navbar-nav gap-1 nav-right-links align-items-center">
      <li class="nav-item d-lg-none mobile-search-btn">
        <a class="nav-link" href="javascript:;"><i class="material-icons-outlined">search</i></a>
      </li>

      <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle dropdown-toggle-nocaret" data-bs-auto-close="outside"
          data-bs-toggle="dropdown" href="javascript:;"><i class="material-icons-outlined">apps</i></a>
        <div class="dropdown-menu dropdown-menu-end dropdown-apps shadow-lg p-3">
          <div class="border rounded-4 overflow-hidden">
            <div class="row row-cols-3 g-0 border-bottom">
              <div class="col border-end">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/01.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Gmail</p>
                  </div>
                </div>
              </div>
              <div class="col border-end">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/02.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Skype</p>
                  </div>
                </div>
              </div>
              <div class="col">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/03.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Slack</p>
                  </div>
                </div>
              </div>
            </div><!--end row-->

            <div class="row row-cols-3 g-0 border-bottom">
              <div class="col border-end">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/04.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">YouTube</p>
                  </div>
                </div>
              </div>
              <div class="col border-end">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/05.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Google</p>
                  </div>
                </div>
              </div>
              <div class="col">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/06.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Instagram</p>
                  </div>
                </div>
              </div>
            </div><!--end row-->

            <div class="row row-cols-3 g-0 border-bottom">
              <div class="col border-end">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/07.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Spotify</p>
                  </div>
                </div>
              </div>
              <div class="col border-end">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/08.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Yahoo</p>
                  </div>
                </div>
              </div>
              <div class="col">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/09.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Facebook</p>
                  </div>
                </div>
              </div>
            </div><!--end row-->

            <div class="row row-cols-3 g-0">
              <div class="col border-end">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/10.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Figma</p>
                  </div>
                </div>
              </div>
              <div class="col border-end">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/11.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Paypal</p>
                  </div>
                </div>
              </div>
              <div class="col">
                <div class="app-wrapper d-flex flex-column gap-2 text-center">
                  <div class="app-icon">
                    <img src="{{ URL::asset('build/images/apps/12.png') }}" width="36" alt="">
                  </div>
                  <div class="app-name">
                    <p class="mb-0">Photo</p>
                  </div>
                </div>
              </div>
            </div><!--end row-->
          </div>
        </div>
      </li>

      <li class="nav-item dropdown">
  <a class="nav-link dropdown-toggle dropdown-toggle-nocaret position-relative" data-bs-auto-close="outside"
    data-bs-toggle="dropdown" href="javascript:;">
    <i class="material-icons-outlined">notifications</i>
    <span class="badge-notify">{{ auth()->user()->unreadNotifications->count() }}</span>
  </a>
  <div class="dropdown-menu dropdown-notify dropdown-menu-end shadow">
    <div class="px-3 py-1 d-flex align-items-center justify-content-between border-bottom">
      <h5 class="notiy-title mb-0">Notifications</h5>
      <!-- Opsi lain misalnya Archive All, Mark all as read, dsb. -->
    </div>
    <div class="notify-list">
      @foreach(auth()->user()->notifications as $notification)
      <div>
        <a class="dropdown-item border-bottom py-2" href="{{ route('tender_lpse.detail', $notification->data['tender_id']) }}">
          <div class="d-flex align-items-center gap-3">
            <div>
              <img src="{{ asset($notification->data['avatar'] ?? 'build/images/logo-default.png') }}"
                   class="rounded-circle" width="45" height="45" alt="">
            </div>
            <div>
              <h5 class="notify-title">{{ $notification->data['nama_paket'] ?? 'Notifikasi' }}</h5>
              <p class="mb-0 notify-desc">Tahap Tender menjadi {{ $notification->data['tahap_tender'] ?? '' }}</p>
              <p class="mb-0 notify-time">{{ $notification->created_at->diffForHumans() }}</p>
            </div>
            <div class="notify-close position-absolute end-0 me-3">
              <i class="material-icons-outlined fs-6">close</i>
            </div>
          </div>
        </a>
      </div>
      @endforeach
    </div>
  </div>
</li>

      <li class="nav-item dropdown">
      <a href="javascript:;" class="dropdown-toggle dropdown-toggle-nocaret" data-bs-toggle="dropdown">
        @if (Auth::user()->avatar)
            <img src="{{ Auth::user()->avatar }}" class="rounded-circle p-1 border" width="45" height="45" alt="{{ Auth::user()->name }}">
        @else
            <div class="rounded-circle p-1 border d-flex align-items-center justify-content-center bg-primary text-white" style="width: 45px; height: 45px;">
                {{ getInitials(Auth::user()->name) }}
            </div>
        @endif
    </a>
        <div class="dropdown-menu dropdown-user dropdown-menu-end shadow">
        <a class="dropdown-item gap-2 py-2" href="javascript:;">
            <div class="text-center">
                @if (Auth::user()->avatar)
                    <img src="{{ Auth::user()->avatar }}" class="rounded-circle p-1 shadow mb-3" width="90" height="90" alt="{{ Auth::user()->name }}">
                @else
                    <div class="rounded-circle p-1 shadow mb-3 d-flex align-items-center justify-content-center bg-primary text-white mx-auto" style="width: 90px; height: 90px; font-size: 2em;">
                        {{ getInitials(Auth::user()->name) }}
                    </div>
                @endif
                <h5 class="user-name mb-0 fw-bold">Hello, {{ Auth::user()->name }}</h5>
            </div>
        </a>
          <hr class="dropdown-divider">
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="javascript:;"><i
              class="material-icons-outlined">person_outline</i>Profile</a>
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="javascript:;"><i
              class="material-icons-outlined">local_bar</i>Setting</a>
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="javascript:;"><i
              class="material-icons-outlined">dashboard</i>Dashboard</a>
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="javascript:;"><i
              class="material-icons-outlined">account_balance</i>Earning</a>
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="javascript:;"><i
              class="material-icons-outlined">cloud_download</i>Downloads</a>
          <hr class="dropdown-divider">
          <form method="POST" action="{{ route('logout') }}" x-data>
            @csrf
            <button type="submit" class="dropdown-item d-flex align-items-center gap-2 py-2">
              <i data-lucide="log-out" class="material-icons-outlined">power_settings_new</i>
              {{ __('Sign Out') }}
            </button>
          </form>
        </div>
      </li>
    </ul>
  </nav>
</header>
<!--end top-header-->
