<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title') | TenderID - Platform Monitoring Tender Indonesia</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ URL::asset('build/images/favicon-32x32.png') }}" type="image/png">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Icons+Outlined" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --info-color: #0dcaf0;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #212529;
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: #333;
            line-height: 1.6;
        }

        .navbar-brand img {
            height: 40px;
        }

        .navbar {
            padding: 15px 0;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-scrolled {
            padding: 10px 0;
            background-color: rgba(255, 255, 255, 0.98) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-nav .nav-item .nav-link {
            position: relative;
            color: #555;
            font-weight: 500;
            padding: 0.5rem 0.8rem;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-item .nav-link:hover {
            color: var(--primary-color);
        }

        .navbar-nav .nav-item .nav-link.active {
            color: var(--primary-color);
        }

        .navbar-nav .nav-item .nav-link.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0.8rem;
            right: 0.8rem;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 2px;
        }

        .hero-section {
            background: linear-gradient(135deg, #0d6efd 0%, #3a8eff 50%, #6610f2 100%);
            background-size: 200% 200%;
            animation: gradientAnimation 15s ease infinite;
            color: white;
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }

        @keyframes gradientAnimation {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTI4MCAxNDAiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iI2ZmZmZmZiI+PHBhdGggZD0iTTEyODAgMTQwVjBTOTkzLjQ2IDE0MCA2NDAgMTM5IDAgMCAwIDB2MTQweiIvPjwvZz48L3N2Zz4=');
            background-size: 100% 100px;
            background-position: bottom;
            background-repeat: no-repeat;
            z-index: 1;
            opacity: 0.3;
        }

        .hero-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxyZWN0IGlkPSJwYXR0ZXJuLWJhY2tncm91bmQiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InRyYW5zcGFyZW50Ij48L3JlY3Q+PGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMSIgZmlsbD0iI2ZmZiIgZmlsbC1vcGFjaXR5PSIwLjE1Ij48L2NpcmNsZT48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IGZpbGw9InVybCgjcGF0dGVybikiIGhlaWdodD0iMTAwJSIgd2lkdGg9IjEwMCUiPjwvcmVjdD48L3N2Zz4=');
            z-index: 1;
            opacity: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .floating-shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            z-index: 1;
            animation-name: float-animation;
            animation-timing-function: ease-in-out;
            animation-iteration-count: infinite;
            animation-direction: alternate;
        }

        .shape-1 {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-duration: 8s;
        }

        .shape-2 {
            width: 60px;
            height: 60px;
            top: 20%;
            right: 15%;
            animation-duration: 6s;
        }

        .shape-3 {
            width: 40px;
            height: 40px;
            bottom: 30%;
            left: 20%;
            animation-duration: 10s;
        }

        .shape-4 {
            width: 100px;
            height: 100px;
            bottom: 20%;
            right: 10%;
            animation-duration: 12s;
        }

        @keyframes float-animation {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            100% {
                transform: translateY(20px) rotate(10deg);
            }
        }

        .feature-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .feature-icon {
            font-size: 48px;
            color: var(--primary-color);
        }

        .tender-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            height: 100%;
        }

        .tender-card:hover {
            transform: translateY(-5px);
        }

        .tender-card .card-body {
            padding: 1.5rem;
        }

        .stats-section {
            background-color: var(--light-color);
            padding: 80px 0;
        }

        .stat-item {
            text-align: center;
            padding: 30px 15px;
            border-radius: 10px;
            background-color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1rem;
            color: var(--secondary-color);
        }

        .cta-section {
            background: linear-gradient(135deg, #6610f2 0%, #0d6efd 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 50px 0 20px;
        }

        .footer-links h5 {
            color: white;
            margin-bottom: 20px;
        }

        .footer-links ul {
            list-style: none;
            padding-left: 0;
        }

        .footer-links ul li {
            margin-bottom: 10px;
        }

        .footer-links ul li a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links ul li a:hover {
            color: white;
        }

        .social-links a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.1);
            color: white;
            margin-right: 10px;
            transition: background-color 0.3s ease;
        }

        .social-links a:hover {
            background-color: var(--primary-color);
        }

        .copyright {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            color: rgba(255,255,255,0.5);
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 60px 0;
            }

            .stats-section {
                padding: 40px 0;
            }

            .cta-section {
                padding: 40px 0;
            }
        }
    </style>

    @yield('css')
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="{{ route('landing.index') }}">
                    <img src="{{ URL::asset('build/images/logo-icon.png') }}" alt="TenderID Logo">
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav mx-auto">
                        <li class="nav-item mx-2">
                            <a class="nav-link {{ request()->routeIs('landing.index') ? 'active fw-semibold' : '' }}" href="{{ route('landing.index') }}">Beranda</a>
                        </li>
                        <li class="nav-item mx-2">
                            <a class="nav-link {{ request()->routeIs('landing.features') ? 'active fw-semibold' : '' }}" href="{{ route('landing.features') }}">Fitur</a>
                        </li>
                        <li class="nav-item mx-2">
                            <a class="nav-link {{ request()->routeIs('landing.pricing') ? 'active fw-semibold' : '' }}" href="{{ route('landing.pricing') }}">Harga</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        @auth
                            <a href="{{ route('dashboard') }}" class="btn btn-outline-primary me-2">Dashboard</a>
                        @else
                            <a href="{{ route('login') }}" class="btn btn-outline-primary me-2">Masuk</a>
                            <a href="{{ route('register') }}" class="btn btn-primary">Daftar</a>
                        @endauth
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <img src="{{ URL::asset('build/images/logo-icon.png') }}" alt="TenderID Logo" class="mb-4" style="height: 40px;">
                    <p>TenderID adalah platform monitoring tender terlengkap di Indonesia. Dapatkan informasi tender, SIRUP, dan e-Katalog secara real-time.</p>
                    <div class="social-links mt-3">
                        <a href="#"><i class="bi bi-facebook"></i></a>
                        <a href="#"><i class="bi bi-twitter"></i></a>
                        <a href="#"><i class="bi bi-instagram"></i></a>
                        <a href="#"><i class="bi bi-linkedin"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 mb-4 footer-links">
                    <h5>Tautan</h5>
                    <ul>
                        <li><a href="{{ route('landing.index') }}">Beranda</a></li>
                        <li><a href="{{ route('landing.features') }}">Fitur</a></li>
                        <li><a href="{{ route('landing.pricing') }}">Harga</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-4 mb-4 footer-links">
                    <h5>Layanan</h5>
                    <ul>
                        <li><a href="#">Monitoring Tender</a></li>
                        <li><a href="#">Monitoring SIRUP</a></li>
                        <li><a href="#">Monitoring e-Katalog</a></li>
                        <li><a href="#">Notifikasi Telegram</a></li>
                        <li><a href="#">Notifikasi WhatsApp</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-4 mb-4 footer-links">
                    <h5>Kontak Kami</h5>
                    <ul>
                        <li><i class="bi bi-geo-alt me-2"></i> IIX Station, Jawa Barat, Indonesia</li>
                        <li><i class="bi bi-envelope me-2"></i> <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a></li>
                    </ul>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; {{ date('Y') }} TenderID. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Navbar Scroll Effect -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const navbar = document.querySelector('.navbar');

            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('navbar-scrolled');
                } else {
                    navbar.classList.remove('navbar-scrolled');
                }
            });
        });
    </script>

    @yield('scripts')
</body>
</html>
