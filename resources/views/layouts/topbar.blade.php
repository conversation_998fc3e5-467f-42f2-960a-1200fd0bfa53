@php
  use Illuminate\Support\Facades\Auth;

  // Function to get initials from name
  if (!function_exists('getInitials')) {
    function getInitials($name) {
      $words = explode(' ', $name);
      $initials = '';

      foreach ($words as $word) {
        $initials .= strtoupper(substr($word, 0, 1));
        if (strlen($initials) >= 2) break;
      }

      return $initials;
    }
  }
@endphp

<header class="top-header">
  <nav class="navbar navbar-expand align-items-center gap-4">
    <div class="btn-toggle">
      <a href="javascript:;"><i class="material-icons-outlined">menu</i></a>
    </div>

    <ul class="navbar-nav gap-1 nav-right-links align-items-center ms-auto">
      <li class="nav-item d-lg-none mobile-search-btn">
        <a class="nav-link" href="javascript:;"><i class="material-icons-outlined">search</i></a>
      </li>

      <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle dropdown-toggle-nocaret position-relative" data-bs-auto-close="outside"
          data-bs-toggle="dropdown" href="javascript:;">
          <i class="material-icons-outlined">notifications</i>
          @if(auth()->user()->unreadNotifications->count() > 0)
            <span class="badge-notify">{{ auth()->user()->unreadNotifications->count() }}</span>
          @endif
        </a>
        <div class="dropdown-menu dropdown-notify dropdown-menu-end shadow">
          <div class="px-3 py-2 d-flex align-items-center justify-content-between border-bottom">
            <h5 class="notiy-title mb-0">Notifikasi</h5>
            @if(auth()->user()->unreadNotifications->count() > 0)
              <a href="{{ route('notifications.markAllAsRead') }}" class="btn btn-sm btn-outline-primary">
                <i class="material-icons-outlined" style="font-size: 16px;">done_all</i> Tandai Semua Dibaca
              </a>
            @endif
          </div>

          <div class="notify-list">
            @if(auth()->user()->notifications->count() > 0)
              @foreach(auth()->user()->notifications()->latest()->take(5)->get() as $notification)
                <div>
                  @php
                    $notifData = $notification->data;
                    $notifType = $notifData['type'] ?? '';
                    $notifIcon = 'notifications';
                    $notifColor = 'primary';
                    $notifUrl = '#';

                    if (isset($notifData['tender_id'])) {
                      $notifUrl = route('tender_lpse.detail', $notifData['tender_id']);
                      $notifIcon = 'assignment';
                      $notifColor = 'success';
                    } elseif (isset($notifData['sirup_id'])) {
                      $notifUrl = route('sirup.detail', $notifData['sirup_id']);
                      $notifIcon = 'description';
                      $notifColor = 'info';
                    } elseif (isset($notifData['user_subscription_id'])) {
                      $notifUrl = route('subscriptions.index');
                      $notifIcon = 'card_membership';
                      $notifColor = 'warning';
                    } elseif (isset($notifData['withdrawal_id'])) {
                      $notifUrl = route('withdrawals.history');
                      $notifIcon = 'monetization_on';
                      $notifColor = 'success';
                    } elseif ($notifType == 'welcome' || $notifType == 'admin_notification') {
                      $notifUrl = route('tenderid.profile.index');
                      $notifIcon = 'person';
                      $notifColor = 'primary';
                    }
                  @endphp

                  <a class="dropdown-item border-bottom py-2 {{ $notification->read_at ? '' : 'bg-light-primary' }}"
                    href="{{ $notifUrl }}">
                    <div class="d-flex align-items-center gap-3">
                      <div class="wh-45 d-flex align-items-center justify-content-center bg-{{ $notifColor }} rounded-circle">
                        <i class="material-icons-outlined text-white">{{ $notifIcon }}</i>
                      </div>
                      <div class="flex-grow-1">
                        <h6 class="notify-title mb-0">{{ $notifData['nama_paket'] ?? ($notifData['subscription_name'] ?? 'Notifikasi') }}</h6>
                        <p class="mb-0 notify-desc">
                          @if(isset($notifData['tahap_tender']))
                            Tahap Tender menjadi {{ $notifData['tahap_tender'] }}
                          @elseif(isset($notifData['status_tender']))
                            Status: {{ $notifData['status_tender'] }}
                          @elseif($notifType == 'welcome')
                            Selamat datang di TenderID!
                          @elseif($notifType == 'subscription_selected')
                            Paket langganan {{ $notifData['subscription_name'] }} dipilih
                          @elseif($notifType == 'payment_confirmation')
                            Konfirmasi pembayaran berhasil
                          @elseif($notifType == 'withdrawal_request')
                            Permintaan penarikan dana
                          @elseif($notifType == 'withdrawal_processed')
                            Penarikan dana {{ $notifData['status'] == 'approved' ? 'disetujui' : 'ditolak' }}
                          @else
                            {{ $notifType ?? 'Notifikasi baru' }}
                          @endif
                        </p>
                        <p class="mb-0 notify-time">{{ $notification->created_at->diffForHumans() }}</p>
                      </div>
                      @if(!$notification->read_at)
                        <div class="ms-auto">
                          <span class="badge rounded-pill bg-primary">Baru</span>
                        </div>
                      @endif
                    </div>
                  </a>
                </div>
              @endforeach

              <div class="text-center py-2">
                <a href="{{ route('notifications.index') }}" class="btn btn-sm btn-link text-primary">
                  Lihat Semua Notifikasi
                </a>
              </div>
            @else
              <div class="text-center py-5">
                <div class="mb-2">
                  <i class="material-icons-outlined text-muted" style="font-size: 48px;">notifications_off</i>
                </div>
                <p class="mb-0 text-muted">Belum ada notifikasi</p>
              </div>
            @endif
          </div>
        </div>
      </li>

      <li class="nav-item dropdown">
        <a href="javascript:;" class="dropdown-toggle dropdown-toggle-nocaret" data-bs-toggle="dropdown">
          @if (Auth::user()->photo)
            <img src="{{ asset('storage/' . Auth::user()->photo) }}" class="rounded-circle p-1 border" width="45" height="45"
            alt="{{ Auth::user()->name }}" style="object-fit: cover;">
          @else
            <div class="rounded-circle p-1 border d-flex align-items-center justify-content-center bg-primary text-white"
            style="width: 45px; height: 45px;">
            {{ getInitials(Auth::user()->name) }}
            </div>
          @endif
        </a>
        <div class="dropdown-menu dropdown-user dropdown-menu-end shadow">
          <a class="dropdown-item gap-2 py-2" href="{{ route('tenderid.profile.index') }}">
            <div class="text-center">
              @if (Auth::user()->photo)
                <img src="{{ asset('storage/' . Auth::user()->photo) }}" class="rounded-circle p-1 shadow mb-3" width="90" height="90"
                alt="{{ Auth::user()->name }}" style="object-fit: cover;">
              @else
                <div
                class="rounded-circle p-1 shadow mb-3 d-flex align-items-center justify-content-center bg-primary text-white mx-auto"
                style="width: 90px; height: 90px; font-size: 2em;">
                {{ getInitials(Auth::user()->name) }}
                </div>
              @endif
              <h5 class="user-name mb-0 fw-bold">Hello, {{ Auth::user()->name }}</h5>
              <p class="text-muted small mb-0">{{ Auth::user()->email }}</p>
            </div>
          </a>
          <hr class="dropdown-divider">

          <!-- User Profile -->
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="{{ route('tenderid.profile.index') }}">
            <i class="material-icons-outlined">person_outline</i>Profile
          </a>

          <!-- Subscription -->
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="{{ route('subscriptions.select') }}">
            <i class="material-icons-outlined">card_membership</i>Subscription
          </a>

          <!-- Dashboard -->
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="{{ route('dashboard') }}">
            <i class="material-icons-outlined">dashboard</i>Dashboard
          </a>

          <!-- Followed Items -->
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="{{ route('for-you') }}">
            <i class="material-icons-outlined">favorite</i>Yang Diikuti
          </a>

          <!-- Notifications -->
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="{{ route('notifications.index') }}">
            <i class="material-icons-outlined">notifications</i>Notifikasi
            @if(auth()->user()->unreadNotifications->count() > 0)
              <span class="badge rounded-pill bg-danger ms-auto">{{ auth()->user()->unreadNotifications->count() }}</span>
            @endif
          </a>

          @if(Auth::user()->commission_balance > 0)
          <!-- Withdraw -->
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="{{ route('withdrawals.request') }}">
            <i class="material-icons-outlined">monetization_on</i>Tarik Saldo
            <span class="badge rounded-pill bg-success ms-auto">Rp {{ number_format(Auth::user()->commission_balance, 0, ',', '.') }}</span>
          </a>
          @endif

          @if(Auth::user()->account_type === 'Super Admin')
          <hr class="dropdown-divider">
          <!-- Admin Menu -->
          <a class="dropdown-item d-flex align-items-center gap-2 py-2" href="{{ route('admin.users.index') }}">
            <i class="material-icons-outlined">admin_panel_settings</i>Admin Panel
          </a>
          @endif

          <hr class="dropdown-divider">
          <form method="POST" action="{{ route('logout') }}" x-data>
            @csrf
            <button type="submit" class="dropdown-item d-flex align-items-center gap-2 py-2">
              <i class="material-icons-outlined">power_settings_new</i>
              {{ __('Sign Out') }}
            </button>
          </form>
        </div>
      </li>
    </ul>
  </nav>
</header>
<!--end top-header-->