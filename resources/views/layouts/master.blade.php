<!doctype html>
<html lang="en" data-bs-theme="light">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!--favicon-->
    <link rel="icon" href="{{ URL::asset('build/images/favicon-32x32.png') }}" type="image/png">
    <title>@yield('title') | TenderID 2.0</title>

    <!-- Force HTTPS for all requests and assets -->
    <script>
        // Check if the current protocol is HTTP and redirect to HTTPS if needed
        if (window.location.protocol === 'http:') {
            window.location.href = window.location.href.replace('http:', 'https:');
        }

        // Force all asset URLs to use HTTPS
        (function() {
            try {
                // Create a new MutationObserver to watch for changes to the DOM
                var observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                            mutation.addedNodes.forEach(function(node) {
                                // Check if node is an Element with tagName property
                                if (node.nodeType === 1 && node.tagName) {
                                    if (node.tagName === 'LINK' || node.tagName === 'SCRIPT' || node.tagName === 'IMG') {
                                        var src = node.src || node.href;
                                        if (src && typeof src === 'string' && src.indexOf('http:') === 0) {
                                            if (node.src) {
                                                node.src = node.src.replace('http:', 'https:');
                                            } else if (node.href) {
                                                node.href = node.href.replace('http:', 'https:');
                                            }
                                        }
                                    }
                                }
                            });
                        }
                    });
                });

                // Start observing the document
                observer.observe(document, { childList: true, subtree: true });

                // Also fix existing elements
                document.querySelectorAll('link[href^="http:"], script[src^="http:"], img[src^="http:"]').forEach(function(el) {
                    if (el.src) {
                        el.src = el.src.replace('http:', 'https:');
                    } else if (el.href) {
                        el.href = el.href.replace('http:', 'https:');
                    }
                });
            } catch (e) {
                console.error('Error in HTTPS enforcement script:', e);
            }
        })();
    </script>

    @yield('css')

    @include('layouts.head-css')

    <style>
        .tenderid_header {
            background: linear-gradient(90deg, #007bff, #6f42c1, #fd7e14);
            color: white !important;
        }

        .card.shadow-lg {
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5) !important;
        }

        .wh-40 {
            width: 40px;
            height: 40px;
        }

        .wh-45 {
            width: 45px;
            height: 45px;
        }

        .wh-48 {
            width: 48px;
            height: 48px;
        }

        .wh-120 {
            width: 120px;
            height: 120px;
        }

        .bg-light-primary {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-light-success {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-light-warning {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-light-danger {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .bg-light-info {
            background-color: rgba(13, 202, 240, 0.1);
        }

        /* Footer styling */
        .page-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #e9ecef;
            background-color: #fff;
            box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .page-footer .footer-content {
            width: 100%;
        }

        .page-footer a {
            transition: all 0.2s ease;
        }

        .page-footer a:hover {
            color: #0d6efd !important;
        }

        @media (max-width: 767.98px) {
            .page-footer {
                text-align: center;
            }

            .page-footer .justify-content-md-end {
                justify-content: center !important;
            }
        }

        /* Alternative Footer styling */
        .page-footer-alt {
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
            box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.05);
        }

        .page-footer-alt h5,
        .page-footer-alt h6 {
            color: #344767;
        }

        .page-footer-alt a {
            transition: all 0.2s ease;
        }

        .page-footer-alt a:hover {
            color: #0d6efd !important;
            text-decoration: underline !important;
        }

        .page-footer-alt .social-icons a {
            width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f1f1f1;
            transition: all 0.3s ease;
        }

        .page-footer-alt .social-icons a:hover {
            background-color: #0d6efd;
            color: white !important;
        }
    </style>


</head>

<body>

    @include('layouts.topbar')
    @include('layouts.sidebar')

    <!--start main wrapper-->
    <main class="main-wrapper" id="content">
        <div class="main-content">

            @yield('content')

        </div>
    </main>
    <!--end main wrapper-->

    <!--start overlay-->
    <div class="overlay btn-toggle"></div>
    <!--end overlay-->

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="liveToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header" id="toast-header">
                <i class="material-icons-outlined me-2" id="toast-icon">check_circle</i>
                <strong class="me-auto" id="toast-title">Notification</strong>
                <small>Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toast-message">
                Message goes here
            </div>
        </div>
    </div>

    @include('layouts.footer')

    @include('layouts.vendor-scripts')

    <script>
        // Toast notification function
        function showToast(message, type = 'success') {
            const toast = document.getElementById('liveToast');
            const toastHeader = document.getElementById('toast-header');
            const toastIcon = document.getElementById('toast-icon');
            const toastTitle = document.getElementById('toast-title');
            const toastMessage = document.getElementById('toast-message');

            // Set toast content based on type
            if (type === 'success') {
                toastHeader.className = 'toast-header bg-success text-white';
                toastIcon.textContent = 'check_circle';
                toastTitle.textContent = 'Success';
            } else if (type === 'error') {
                toastHeader.className = 'toast-header bg-danger text-white';
                toastIcon.textContent = 'error';
                toastTitle.textContent = 'Error';
            } else if (type === 'warning') {
                toastHeader.className = 'toast-header bg-warning text-dark';
                toastIcon.textContent = 'warning';
                toastTitle.textContent = 'Warning';
            } else if (type === 'info') {
                toastHeader.className = 'toast-header bg-info text-white';
                toastIcon.textContent = 'info';
                toastTitle.textContent = 'Information';
            }

            // Set message
            toastMessage.textContent = message;

            // Show toast
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // Check for flash messages
        document.addEventListener('DOMContentLoaded', function() {
            @if(session('success'))
                showToast("{!! addslashes(session('success')) !!}", 'success');
            @endif

            @if(session('error'))
                showToast("{!! addslashes(session('error')) !!}", 'error');
            @endif

            @if(session('warning'))
                showToast("{!! addslashes(session('warning')) !!}", 'warning');
            @endif

            @if(session('info'))
                showToast("{!! addslashes(session('info')) !!}", 'info');
            @endif
        });
    </script>

    @yield('scripts')

</body>

</html>