@extends('layouts.master')

@section('title', 'Alerts')
@section('css')
	
@endsection 
@section('content')
<x-page-title title="Components" pagetitle="Alerts" />

       <div class="row row-cols-1 row-cols-xl-2 g-4">
        <div class="col">
          <div class="card">
            <div class="card-body">
              <p class="fw-bold fs-5">Border Examples</p>

              <div class="d-flex flex-column gap-2">
                <div class="alert alert-border-primary alert-dismissible fade show">
                  <div class="d-flex align-items-center">
                    <div class="font-35 text-primary"><span class="material-icons-outlined fs-2">notifications</span>
                    </div>
                    <div class="ms-3">
                      <h6 class="mb-0 text-primary">Primary Alerts</h6>
                      <div class="">A simple primary alert—check it out!</div>
                    </div>
                  </div>
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <div class="alert alert-border-secondary alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-secondary"><span class="material-icons-outlined fs-2">favorite_border</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-secondary">Secondary Alerts</h6>
											<div class="">A simple secondary alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>

                <div class="alert alert-border-success alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-success"><span class="material-icons-outlined fs-2">check_circle</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-success">Success Alerts</h6>
											<div class="">A simple success alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-border-danger alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-danger"><span class="material-icons-outlined fs-2">report_gmailerrorred</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-danger">Danger Alerts</h6>
											<div class="">A simple danger alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-border-warning alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-warning"><span class="material-icons-outlined fs-2">report_problem</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-warning">Warning Alerts</h6>
											<div class="">A simple Warning alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-border-info alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-info"><span class="material-icons-outlined fs-2">info</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-info">Info Alerts</h6>
											<div class="">A simple info alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
              </div>
            </div>
          </div>
         </div>

         <div class="col">
          <div class="card">
            <div class="card-body">
              <p class="fw-bold fs-5">Color Examples Without Icons</p>

              <div class="d-flex flex-column gap-2">
                <div class="alert alert-border-primary alert-dismissible fade show">
                  <div class="">A simple primary alert—check it out!</div>
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <div class="alert alert-border-secondary alert-dismissible fade show">
									<div class="">A simple primary alert—check it out!</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>

                <div class="alert alert-border-success alert-dismissible fade show">
									<div class="">A simple primary alert—check it out!</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-border-danger alert-dismissible fade show">
									<div class="">A simple primary alert—check it out!</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-border-warning alert-dismissible fade show">
									<div class="">A simple primary alert—check it out!</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-border-info alert-dismissible fade show">
									<div class="">A simple primary alert—check it out!</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
              </div>
            </div>
          </div>
         </div>

         <div class="col">
          <div class="card">
            <div class="card-body">
              <p class="fw-bold fs-5">Color Examples</p>

              <div class="d-flex flex-column gap-2">
                <div class="alert alert-primary border-0 bg-primary alert-dismissible fade show">
                  <div class="d-flex align-items-center">
                    <div class="font-35 text-white"><span class="material-icons-outlined fs-2">home</span>
                    </div>
                    <div class="ms-3">
                      <h6 class="mb-0 text-white">Primary Alerts</h6>
                      <div class="text-white">A simple primary alert—check it out!</div>
                    </div>
                  </div>
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div class="alert alert-secondary border-0 bg-secondary alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-white"><span class="material-icons-outlined fs-2">favorite_border</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-white">Secondary Alerts</h6>
											<div class="text-white">A simple secondary alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-success border-0 bg-success alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-white"><span class="material-icons-outlined fs-2">check_circle</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-white">Success Alerts</h6>
											<div class="text-white">A simple success alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-danger border-0 bg-danger alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-white"><span class="material-icons-outlined fs-2">report_gmailerrorred</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-white">Danger Alerts</h6>
											<div class="text-white">A simple danger alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-warning border-0 bg-warning alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-dark"><span class="material-icons-outlined fs-2">report_problem</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-dark">Warning Alerts</h6>
											<div class="text-dark">A simple Warning alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-info border-0 bg-info alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-dark"><span class="material-icons-outlined fs-2">info</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-dark">Info Alerts</h6>
											<div class="text-dark">A simple info alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
                <div class="alert alert-dark border-0 bg-dark alert-dismissible fade show">
									<div class="d-flex align-items-center">
										<div class="font-35 text-white"><span class="material-icons-outlined fs-2">lightbulb</span>
										</div>
										<div class="ms-3">
											<h6 class="mb-0 text-white">Dark Alerts</h6>
											<div class="text-white">A simple dark alert—check it out!</div>
										</div>
									</div>
									<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
								</div>
              </div>
            </div>
          </div>
         </div>

         <div class="col">
          <div class="card">
            <div class="card-body">
              <p class="fw-bold fs-5">Color Examples Without Icons</p>
              <div class="d-flex flex-column gap-2">
                  <div class="alert alert-primary border-0 bg-primary alert-dismissible fade show">
                    <div class="text-white">A simple primary alert—check it out!</div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>
                  <div class="alert alert-secondary border-0 bg-secondary alert-dismissible fade show">
                    <div class="text-white">A simple secondary alert—check it out!</div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>
                  <div class="alert alert-success border-0 bg-success alert-dismissible fade show">
                    <div class="text-white">A simple success alert—check it out!</div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>
                  <div class="alert alert-danger border-0 bg-danger alert-dismissible fade show">
                    <div class="text-white">A simple danger alert—check it out!</div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>
                  <div class="alert alert-warning border-0 bg-warning alert-dismissible fade show">
                    <div class="text-dark">A simple Warning alert—check it out!</div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>
                  <div class="alert alert-info border-0 bg-info alert-dismissible fade show">
                    <div class="text-dark">A simple info alert—check it out!</div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>
                  <div class="alert alert-dark border-0 bg-dark alert-dismissible fade show">
                    <div class="text-white">A simple dark alert—check it out!</div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>
              </div>
            </div>
          </div>
         </div>


       </div><!--end row-->
@endsection 