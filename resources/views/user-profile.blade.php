@extends('layouts.master')

@section('title', 'User Profile')
@section('css')
	
@endsection 
@section('content')
<x-page-title title="Pages" pagetitle="User Profile" />

        <div class="row">
          <div class="col-12 col-lg-8 col-xl-9">
            <div class="card overflow-hidden">
              <div class="card-body">
                <div class="position-relative">
                  <img src="{{ URL::asset('build/images/carousels/22.png') }}" class="img-fluid rounded" alt="">
                  <div class="position-absolute top-100 start-50 translate-middle">
                    <img src="{{ URL::asset('build/images/avatars/06.png') }}" width="110" height="110" class="rounded-circle raised p-1 bg-white" alt="">
                  </div>
                </div>
                <div class="mt-5 d-flex align-items-start justify-content-between">
                  <div class="">
                    <h3 class="mb-2"><PERSON><PERSON></h3>
                    <p class="mb-1">Engineer at BB Agency Industry</p>
                    <p>New York, United States</p>
                    <div class="">
                      <span class="badge rounded-pill bg-primary">UX Research</span>
                      <span class="badge rounded-pill bg-primary">CX Strategy</span>
                      <span class="badge rounded-pill bg-primary">Project Management</span>
                    </div>
                  </div>
                  <div class="">
                     <a href="javascript:;" class="btn btn-primary"><i class="bi bi-chat me-2"></i>Send Message</a>
                  </div>
                </div>
              </div>
            </div>
            <div class="card">
              <div class="card-body">
                <h4 class="mb-2">About Me</h4>
                <p class="">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters.</p>
                <p>Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source.</p>
                <p>It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters</p>
              </div>
            </div>
          </div>
          <div class="col-12 col-lg-4 col-xl-3">
            <div class="card">
              <div class="card-body">
                <h5 class="mb-3">Location</h5>
                 <p class="mb-0"><i class="bi bi-geo-alt-fill me-2"></i>Kalkio Network</p>
              </div>
            </div>

            <div class="card">
              <div class="card-body">
                <h5 class="mb-3">Connect</h5>
                 <p class=""><i class="bi bi-browser-edge me-2"></i>www.example.com</p>
                 <p class=""><i class="bi bi-facebook me-2"></i>Facebook</p>
                 <p class=""><i class="bi bi-twitter me-2"></i>Twitter</p>
                 <p class="mb-0"><i class="bi bi-linkedin me-2"></i>LinkedIn</p>
              </div>
            </div>

            <div class="card">
              <div class="card-body">
                <h5 class="mb-3">Skills</h5>
                 <div class="mb-3">
                  <p class="mb-1">Web Design</p>
                  <div class="progress" style="height: 5px;">
                   <div class="progress-bar" role="progressbar" style="width: 45%"></div>
                  </div>
                 </div>
                 <div class="mb-3">
                  <p class="mb-1">HTML5</p>
                  <div class="progress" style="height: 5px;">
                   <div class="progress-bar" role="progressbar" style="width: 55%"></div>
                  </div>
                 </div>
                 <div class="mb-3">
                  <p class="mb-1">PHP7</p>
                  <div class="progress" style="height: 5px;">
                   <div class="progress-bar" role="progressbar" style="width: 65%"></div>
                  </div>
                 </div>
                 <div class="mb-3">
                  <p class="mb-1">CSS3</p>
                  <div class="progress" style="height: 5px;">
                   <div class="progress-bar" role="progressbar" style="width: 75%"></div>
                  </div>
                 </div>
                 <div class="mb-0">
                  <p class="mb-1">Photoshop</p>
                  <div class="progress" style="height: 5px;">
                   <div class="progress-bar" role="progressbar" style="width: 85%"></div>
                  </div>
                 </div>

              </div>
            </div>

          </div>
        </div>
@endsection  