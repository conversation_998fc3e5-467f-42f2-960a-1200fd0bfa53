@extends('layouts.master')

@section('title', 'Email Lo<PERSON>')

@section('css')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<style>
    .card-stats {
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-stats:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    .stats-icon-primary {
        background-color: rgba(68, 129, 235, 0.1);
        color: #4481eb;
    }

    .stats-icon-success {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .stats-icon-danger {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .stats-icon-warning {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .stats-icon-info {
        background-color: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
    }

    .badge-status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .email-subject {
        font-weight: 500;
        color: #344767;
        text-decoration: none;
        display: block;
        margin-bottom: 0.25rem;
    }

    .email-recipient {
        font-size: 0.8125rem;
        color: #6c757d;
    }
</style>
@endsection

@section('content')
<x-page-title title="Email Logs" pagetitle="Admin" />

<div class="row">
    <!-- Stats Cards -->
    <div class="col-md-3 mb-4">
        <div class="card card-stats">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="stats-icon stats-icon-primary">
                        <i class="material-icons-outlined">email</i>
                    </div>
                    <div>
                        <h6 class="mb-0 fw-bold">Total Emails</h6>
                        <h3 class="mb-0">{{ number_format($stats['total']) }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card card-stats">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="stats-icon stats-icon-success">
                        <i class="material-icons-outlined">check_circle</i>
                    </div>
                    <div>
                        <h6 class="mb-0 fw-bold">Sent Successfully</h6>
                        <h3 class="mb-0">{{ number_format($stats['sent']) }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card card-stats">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="stats-icon stats-icon-danger">
                        <i class="material-icons-outlined">error</i>
                    </div>
                    <div>
                        <h6 class="mb-0 fw-bold">Failed</h6>
                        <h3 class="mb-0">{{ number_format($stats['failed']) }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card card-stats">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="stats-icon stats-icon-info">
                        <i class="material-icons-outlined">today</i>
                    </div>
                    <div>
                        <h6 class="mb-0 fw-bold">Today</h6>
                        <h3 class="mb-0">{{ number_format($stats['today']) }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart Card -->
<div class="card mb-4">
    <div class="card-header tenderid_header text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0 text-white">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">bar_chart</i>
            Email Activity (Last 30 Days)
        </h5>
    </div>
    <div class="card-body">
        <canvas id="emailChart" height="100"></canvas>
    </div>
</div>

<!-- Filters and Email Logs Table -->
<div class="card">
    <div class="card-header tenderid_header text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0 text-white">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">email</i>
            Email Logs
        </h5>
    </div>
    <div class="card-body">
        @if(session('success'))
        <div class="alert alert-success d-flex align-items-center mb-4" role="alert">
            <i class="material-icons-outlined me-2">check_circle</i>
            <div>{{ session('success') }}</div>
        </div>
        @endif

        @if(session('error'))
        <div class="alert alert-danger d-flex align-items-center mb-4" role="alert">
            <i class="material-icons-outlined me-2">error</i>
            <div>{{ session('error') }}</div>
        </div>
        @endif

        <!-- Filters -->
        <form action="{{ route('admin.email_logs.index') }}" method="GET" class="mb-4">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="email_type" class="form-label">Email Type</label>
                        <select class="form-select" id="email_type" name="email_type">
                            <option value="">All Types</option>
                            @foreach($emailTypes as $type)
                                <option value="{{ $type }}" {{ request('email_type') == $type ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('_', ' ', $type)) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="sent" {{ request('status') == 'sent' ? 'selected' : '' }}>Sent</option>
                            <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>Failed</option>
                            <option value="delivered" {{ request('status') == 'delivered' ? 'selected' : '' }}>Delivered</option>
                            <option value="opened" {{ request('status') == 'opened' ? 'selected' : '' }}>Opened</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label for="recipient" class="form-label">Recipient</label>
                        <input type="text" class="form-control" id="recipient" name="recipient" value="{{ request('recipient') }}" placeholder="Search by email">
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label for="date_range" class="form-label">Date Range</label>
                        <input type="text" class="form-control" id="date_range" name="date_range" value="{{ request('date_range') }}" placeholder="Select date range">
                    </div>
                </div>

                <div class="col-12 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">filter_list</i>
                        Filter
                    </button>
                    <a href="{{ route('admin.email_logs.index') }}" class="btn btn-secondary">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">clear</i>
                        Clear
                    </a>
                </div>
            </div>
        </form>

        <!-- Table -->
        <div class="table-responsive">
            <table class="table table-hover align-middle">
                <thead class="bg-light">
                    <tr>
                        <th>ID</th>
                        <th>Recipient</th>
                        <th>Subject</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Sent At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($emailLogs as $log)
                        <tr>
                            <td class="fw-medium">{{ $log->id }}</td>
                            <td>
                                <div class="d-flex flex-column">
                                    <span class="email-recipient">{{ $log->recipient_email }}</span>
                                    @if($log->user)
                                        <small class="text-muted">{{ $log->user->name }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <a href="{{ route('admin.email_logs.show', $log->id) }}" class="email-subject">
                                    {{ \Illuminate\Support\Str::limit($log->subject, 50) }}
                                </a>
                            </td>
                            <td>
                                @if($log->email_type)
                                    <span class="badge bg-info text-white">
                                        {{ ucfirst(str_replace('_', ' ', $log->email_type)) }}
                                    </span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @if($log->status === 'sent')
                                    <span class="badge-status bg-success text-white">
                                        <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">check_circle</i>
                                        Sent
                                    </span>
                                @elseif($log->status === 'failed')
                                    <span class="badge-status bg-danger text-white">
                                        <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">error</i>
                                        Failed
                                    </span>
                                @elseif($log->status === 'delivered')
                                    <span class="badge-status bg-primary text-white">
                                        <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">done_all</i>
                                        Delivered
                                    </span>
                                @elseif($log->status === 'opened')
                                    <span class="badge-status bg-info text-white">
                                        <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">visibility</i>
                                        Opened
                                    </span>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex flex-column">
                                    <span class="fw-medium">{{ $log->sent_at->format('d M Y') }}</span>
                                    <small class="text-muted">{{ $log->sent_at->format('H:i') }} WIB</small>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex gap-2">
                                    <a href="{{ route('admin.email_logs.show', $log->id) }}" class="btn btn-sm btn-info" title="View Details">
                                        <i class="material-icons-outlined" style="font-size: 1rem;">visibility</i>
                                    </a>
                                    <form action="{{ route('admin.email_logs.resend', $log->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-sm btn-primary" title="Resend Email">
                                            <i class="material-icons-outlined" style="font-size: 1rem;">send</i>
                                        </button>
                                    </form>
                                    <form action="{{ route('admin.email_logs.destroy', $log->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this email log?');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                            <i class="material-icons-outlined" style="font-size: 1rem;">delete</i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="material-icons-outlined mb-3" style="font-size: 3rem; color: #e9ecef;">email_off</i>
                                    <h5>No Email Logs Found</h5>
                                    <p class="text-muted">No email logs match your search criteria.</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div class="text-muted small">
                Showing {{ $emailLogs->firstItem() ?? 0 }} - {{ $emailLogs->lastItem() ?? 0 }} of {{ $emailLogs->total() }} email logs
            </div>
            <div>
                {{ $emailLogs->appends(request()->query())->links('vendor.pagination.simple') }}
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment/min/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize date range picker
        $('#date_range').daterangepicker({
            autoUpdateInput: false,
            locale: {
                cancelLabel: 'Clear',
                format: 'YYYY-MM-DD'
            }
        });

        $('#date_range').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
        });

        $('#date_range').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });

        // Initialize chart
        var ctx = document.getElementById('emailChart').getContext('2d');
        var dailyStats = @json($dailyStats);

        var labels = [];
        var data = [];

        dailyStats.forEach(function(stat) {
            labels.push(moment(stat.date).format('MMM D'));
            data.push(stat.count);
        });

        var chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Emails Sent',
                    data: data,
                    backgroundColor: 'rgba(68, 129, 235, 0.1)',
                    borderColor: '#4481eb',
                    borderWidth: 2,
                    pointBackgroundColor: '#4481eb',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y + ' emails';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    });
</script>
@endsection
