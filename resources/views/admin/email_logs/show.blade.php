@extends('layouts.master')

@section('title', 'Email Log Details')

@section('css')
<style>
    .email-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .email-header-item {
        margin-bottom: 15px;
    }

    .email-header-label {
        font-weight: 600;
        color: #344767;
        margin-bottom: 5px;
    }

    .email-header-value {
        color: #6c757d;
    }

    .email-body {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        background-color: #fff;
    }

    .email-body-iframe {
        width: 100%;
        min-height: 500px;
        border: none;
    }

    .badge-status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .metadata-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .metadata-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .metadata-title i {
        margin-right: 10px;
        color: #4481eb;
    }

    .metadata-item {
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .metadata-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .metadata-label {
        font-weight: 500;
        color: #344767;
    }

    .metadata-value {
        color: #6c757d;
        word-break: break-all;
    }
</style>
@endsection

@section('content')
<x-page-title title="Email Log Details" pagetitle="Admin" />

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header tenderid_header text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-white">
                        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">email</i>
                        Email Details
                    </h5>
                    <div>
                        <a href="{{ route('admin.email_logs.index') }}" class="btn btn-sm btn-light">
                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">arrow_back</i>
                            Back to List
                        </a>
                        <form action="{{ route('admin.email_logs.resend', $emailLog->id) }}" method="POST" class="d-inline ms-2">
                            @csrf
                            <input type="hidden" name="from_detail" value="1">
                            <button type="submit" class="btn btn-sm btn-primary">
                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">send</i>
                                Kirim Ulang Email
                            </button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                    <div class="alert alert-success d-flex align-items-center mb-4" role="alert">
                        <i class="material-icons-outlined me-2">check_circle</i>
                        <div>{{ session('success') }}</div>
                    </div>
                    @endif

                    @if(session('error'))
                    <div class="alert alert-danger d-flex align-items-center mb-4" role="alert">
                        <i class="material-icons-outlined me-2">error</i>
                        <div>{{ session('error') }}</div>
                    </div>
                    @endif

                    <div class="email-header">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="email-header-item">
                                    <div class="email-header-label">Subject</div>
                                    <div class="email-header-value">{{ $emailLog->subject }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="email-header-item">
                                    <div class="email-header-label">Status</div>
                                    <div class="email-header-value">
                                        @if($emailLog->status === 'sent')
                                            <span class="badge-status bg-success text-white">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">check_circle</i>
                                                Sent
                                            </span>
                                        @elseif($emailLog->status === 'failed')
                                            <span class="badge-status bg-danger text-white">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">error</i>
                                                Failed
                                            </span>
                                        @elseif($emailLog->status === 'delivered')
                                            <span class="badge-status bg-primary text-white">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">done_all</i>
                                                Delivered
                                            </span>
                                        @elseif($emailLog->status === 'opened')
                                            <span class="badge-status bg-info text-white">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">visibility</i>
                                                Opened
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="email-header-item">
                                    <div class="email-header-label">Recipient</div>
                                    <div class="email-header-value">{{ $emailLog->recipient_email }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="email-header-item">
                                    <div class="email-header-label">Sent At</div>
                                    <div class="email-header-value">{{ $emailLog->sent_at->format('d M Y H:i:s') }} WIB</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="email-header-item">
                                    <div class="email-header-label">User</div>
                                    <div class="email-header-value">
                                        @if($emailLog->user)
                                            {{ $emailLog->user->name }} ({{ $emailLog->user->email }})
                                        @else
                                            <span class="text-muted">No associated user</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="email-header-item">
                                    <div class="email-header-label">Email Type</div>
                                    <div class="email-header-value">
                                        @if($emailLog->email_type)
                                            <span class="badge bg-info text-white">
                                                {{ ucfirst(str_replace('_', ' ', $emailLog->email_type)) }}
                                            </span>
                                        @else
                                            <span class="text-muted">Not specified</span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            @if($emailLog->error_message)
                            <div class="col-12">
                                <div class="email-header-item">
                                    <div class="email-header-label">Error Message</div>
                                    <div class="email-header-value text-danger">{{ $emailLog->error_message }}</div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="mb-3">Email Content</h5>
                            <div class="email-body">
                                @if($emailLog->body)
                                    <iframe id="emailBodyFrame" class="email-body-iframe" srcdoc="{{ $emailLog->body }}"></iframe>
                                @else
                                    <div class="text-center py-5">
                                        <i class="material-icons-outlined mb-3" style="font-size: 3rem; color: #e9ecef;">email_off</i>
                                        <h5>No Content Available</h5>
                                        <p class="text-muted">The email body content is not available.</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-4">
                            @if($emailLog->metadata)
                                <div class="metadata-card">
                                    <div class="metadata-title">
                                        <i class="material-icons-outlined">info</i>
                                        Metadata
                                    </div>

                                    @if(isset($emailLog->metadata['has_attachments']))
                                        <div class="metadata-item">
                                            <div class="metadata-label">Attachments</div>
                                            <div class="metadata-value">
                                                {{ $emailLog->metadata['has_attachments'] ? 'Yes' : 'No' }}
                                            </div>
                                        </div>
                                    @endif

                                    @if(isset($emailLog->metadata['cc']) && !empty($emailLog->metadata['cc']))
                                        <div class="metadata-item">
                                            <div class="metadata-label">CC</div>
                                            <div class="metadata-value">
                                                @foreach($emailLog->metadata['cc'] as $email => $name)
                                                    <div>{{ $email }}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif

                                    @if(isset($emailLog->metadata['bcc']) && !empty($emailLog->metadata['bcc']))
                                        <div class="metadata-item">
                                            <div class="metadata-label">BCC</div>
                                            <div class="metadata-value">
                                                @foreach($emailLog->metadata['bcc'] as $email => $name)
                                                    <div>{{ $email }}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif

                                    @if(isset($emailLog->metadata['headers']) && !empty($emailLog->metadata['headers']))
                                        <div class="metadata-item">
                                            <div class="metadata-label">Headers</div>
                                            <div class="metadata-value">
                                                <pre class="mb-0" style="font-size: 0.8rem;">{{ json_encode($emailLog->metadata['headers'], JSON_PRETTY_PRINT) }}</pre>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endif

                            <div class="d-grid gap-2">
                                <form action="{{ route('admin.email_logs.destroy', $emailLog->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this email log?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger w-100">
                                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">delete</i>
                                        Delete Email Log
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Adjust iframe height to fit content
        const iframe = document.getElementById('emailBodyFrame');
        if (iframe) {
            iframe.onload = function() {
                iframe.style.height = iframe.contentWindow.document.body.scrollHeight + 'px';
            };
        }
    });
</script>
@endsection
