@extends('layouts.master')

@section('title', 'Add Subscription Package')

@section('content')
<div class="container mt-4">
    <h1>Add Subscription Package</h1>

    <form action="{{ route('admin.subscriptions.store') }}" method="POST">
        @csrf

        <div class="mb-3">
            <label for="name" class="form-label">Package Name</label>
            <input type="text" class="form-control" id="name" name="name" value="{{ old('name') }}" required>
            @error('name')
            <div class="text-danger">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="price" class="form-label">Price</label>
            <input type="number" step="0.01" class="form-control" id="price" name="price" value="{{ old('price') }}" required>
            @error('price')
            <div class="text-danger">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="duration_days" class="form-label">Duration (days)</label>
            <input type="number" class="form-control" id="duration_days" name="duration_days" value="{{ old('duration_days') }}" required>
            @error('duration_days')
            <div class="text-danger">{{ $message }}</div>
            @enderror
        </div>

        <button type="submit" class="btn btn-primary">Add Package</button>
        <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-secondary">Cancel</a>
    </form>
</div>
@endsection
