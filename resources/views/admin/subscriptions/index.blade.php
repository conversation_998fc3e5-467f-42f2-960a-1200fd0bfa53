
@extends('layouts.master')

@section('title', 'Ke<PERSON><PERSON> Pak<PERSON> Berlangganan')

@section('css')
<style>
    .card-header-blue {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        padding: 16px 20px;
    }

    .badge-status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .price-tag {
        font-weight: 600;
        color: #4481eb;
    }

    .duration-tag {
        background-color: rgba(68, 129, 235, 0.1);
        color: #4481eb;
        padding: 4px 10px;
        border-radius: 4px;
        font-size: 0.85rem;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
    }

    .action-buttons form {
        margin-bottom: 0;
    }
</style>
@endsection

@section('content')
<x-page-title title="Kelola Paket Berlangganan" pagetitle="Admin" />

<div class="card shadow-lg">
    <div class="card-header card-header-blue d-flex justify-content-between align-items-center">
        <h5 class="mb-0 text-white">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">card_membership</i>
            <PERSON><PERSON><PERSON>et Berlangganan
        </h5>
        <!-- Button trigger modal -->
        <button type="button" class="btn btn-sm btn-primary text-white" data-bs-toggle="modal" data-bs-target="#addSubscriptionModal">
            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">add</i>
            Tambah Paket Baru
        </button>
    </div>
    <div class="card-body">
        @if(session('success'))
        <div class="alert alert-success d-flex align-items-center" role="alert">
            <i class="material-icons-outlined me-2">check_circle</i>
            <div>{{ session('success') }}</div>
        </div>
        @endif

        @if(session('error'))
        <div class="alert alert-danger d-flex align-items-center" role="alert">
            <i class="material-icons-outlined me-2">error</i>
            <div>{{ session('error') }}</div>
        </div>
        @endif

        <div class="table-responsive">
            <table class="table table-hover align-middle">
                <thead class="bg-light">
                    <tr>
                        <th>Nama Paket</th>
                        <th>Harga</th>
                        <th>Durasi</th>
                        <th>Status</th>
                        <th>Deskripsi</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($subscriptions as $subscription)
                    <tr>
                        <td class="fw-medium">{{ $subscription->name }}</td>
                        <td><span class="price-tag">Rp {{ number_format($subscription->price, 0, ',', '.') }}</span></td>
                        <td><span class="duration-tag">{{ $subscription->duration_days }} hari</span></td>
                        <td>
                            @if($subscription->is_active)
                                <span class="badge-status bg-success text-white">
                                    <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">check_circle</i>
                                    Aktif
                                </span>
                            @else
                                <span class="badge-status bg-danger text-white">
                                    <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">cancel</i>
                                    Nonaktif
                                </span>
                            @endif
                        </td>
                        <td>{{ $subscription->description ?: '-' }}</td>
                        <td>
                            <div class="action-buttons">
                                <!-- Toggle Active Status -->
                                <form action="{{ route('admin.subscriptions.toggle-active', $subscription->id) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-sm {{ $subscription->is_active ? 'btn-outline-danger' : 'btn-outline-success' }}" title="{{ $subscription->is_active ? 'Nonaktifkan' : 'Aktifkan' }} paket">
                                        <i class="material-icons-outlined" style="font-size: 1rem;">{{ $subscription->is_active ? 'toggle_off' : 'toggle_on' }}</i>
                                    </button>
                                </form>

                                <!-- Edit button triggers modal -->
                                <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editSubscriptionModal{{ $subscription->id }}" title="Edit paket">
                                    <i class="material-icons-outlined" style="font-size: 1rem;">edit</i>
                                </button>

                                <!-- Delete button -->
                                <form action="{{ route('admin.subscriptions.destroy', $subscription->id) }}" method="POST" onsubmit="return confirm('Apakah Anda yakin ingin menghapus paket ini?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger" title="Hapus paket">
                                        <i class="material-icons-outlined" style="font-size: 1rem;">delete</i>
                                    </button>
                                </form>
                            </div>

                            <!-- Edit Modal -->
                            <div class="modal fade" id="editSubscriptionModal{{ $subscription->id }}" tabindex="-1" aria-labelledby="editSubscriptionModalLabel{{ $subscription->id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form action="{{ route('admin.subscriptions.update', $subscription->id) }}" method="POST">
                                            @csrf
                                            @method('PUT')
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="editSubscriptionModalLabel{{ $subscription->id }}">Edit Paket Berlangganan</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label for="name{{ $subscription->id }}" class="form-label">Nama Paket</label>
                                                    <input type="text" class="form-control" id="name{{ $subscription->id }}" name="name" value="{{ old('name', $subscription->name) }}" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="price{{ $subscription->id }}" class="form-label">Harga (Rp)</label>
                                                    <input type="number" class="form-control" id="price{{ $subscription->id }}" name="price" value="{{ old('price', $subscription->price) }}" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="duration_days{{ $subscription->id }}" class="form-label">Durasi (hari)</label>
                                                    <input type="number" class="form-control" id="duration_days{{ $subscription->id }}" name="duration_days" value="{{ old('duration_days', $subscription->duration_days) }}" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="description{{ $subscription->id }}" class="form-label">Deskripsi</label>
                                                    <textarea class="form-control" id="description{{ $subscription->id }}" name="description" rows="3">{{ old('description', $subscription->description) }}</textarea>
                                                </div>
                                                <div class="form-check form-switch mb-3">
                                                    <input class="form-check-input" type="checkbox" id="is_active{{ $subscription->id }}" name="is_active" {{ $subscription->is_active ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="is_active{{ $subscription->id }}">Paket Aktif</label>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                <button type="submit" class="btn btn-primary">Perbarui Paket</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Add Modal -->
        <div class="modal fade" id="addSubscriptionModal" tabindex="-1" aria-labelledby="addSubscriptionModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="{{ route('admin.subscriptions.store') }}" method="POST">
                        @csrf
                        <div class="modal-header">
                            <h5 class="modal-title" id="addSubscriptionModalLabel">Tambah Paket Berlangganan</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nama Paket</label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ old('name') }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="price" class="form-label">Harga (Rp)</label>
                                <input type="number" class="form-control" id="price" name="price" value="{{ old('price') }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="duration_days" class="form-label">Durasi (hari)</label>
                                <input type="number" class="form-control" id="duration_days" name="duration_days" value="{{ old('duration_days') }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">Deskripsi</label>
                                <textarea class="form-control" id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">Paket Aktif</label>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-primary">Tambah Paket</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</div>
@endsection
