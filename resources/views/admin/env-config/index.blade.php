@extends('layouts.master')

@section('title', 'Environment Configuration')

@section('css')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<style>
    /* Card Styles */
    .env-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        border: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        margin-bottom: 20px;
        overflow: hidden;
    }
    .env-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 24px rgba(0,0,0,0.1);
    }
    .env-card .card-body {
        padding: 20px;
    }

    /* Category Styles */
    .category-title {
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 15px;
        margin-bottom: 25px;
        font-weight: 600;
        color: #333;
        display: flex;
        align-items: center;
    }
    .category-title i {
        font-size: 24px;
        margin-right: 10px;
        background: linear-gradient(45deg, #4481eb, #04befe);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    /* Form Control Styles */
    .form-control.env-input {
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        padding: 12px 15px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        transition: all 0.3s;
    }
    .form-control.env-input:focus {
        border-color: #4481eb;
        box-shadow: 0 0 0 0.2rem rgba(68, 129, 235, 0.25);
    }

    /* Label Styles */
    .env-key {
        font-weight: 600;
        color: #444;
        margin-bottom: 8px;
        display: block;
        font-size: 0.95rem;
    }

    /* Sensitive Field Styles */
    .sensitive-field {
        position: relative;
    }
    .toggle-visibility {
        cursor: pointer;
        transition: all 0.3s;
    }
    .toggle-visibility:hover {
        opacity: 0.8;
    }
    .input-group {
        border-radius: 8px;
        overflow: hidden;
    }
    .input-group-text {
        border: none;
    }

    /* Card Header Styles */
    .card-header {
        background: linear-gradient(135deg, #4481eb, #04befe);
        color: white;
        padding: 15px 20px;
        border-bottom: none;
        font-weight: 600;
        display: flex;
        align-items: center;
    }
    .card-header i {
        margin-right: 10px;
        font-size: 20px;
    }

    /* Info Text Styles */
    .backup-info {
        font-size: 0.85rem;
        color: #6c757d;
        background-color: #f8f9fa;
        padding: 10px 15px;
        border-radius: 8px;
        border-left: 4px solid #4481eb;
    }

    /* Tab Navigation Styles */
    .list-group-item {
        border: none;
        border-radius: 8px !important;
        margin-bottom: 8px;
        padding: 12px 15px;
        font-weight: 500;
        transition: all 0.3s;
    }
    .list-group-item:hover {
        background-color: #f8f9fa;
        transform: translateX(5px);
    }
    .list-group-item.active {
        background: linear-gradient(135deg, #4481eb, #04befe);
        border: none !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        color: white;
    }
    .list-group-item.active .category-icon-wrapper {
        transform: scale(1.1);
        background: rgba(255, 255, 255, 0.2) !important;
    }
    .list-group-item.active .fw-medium {
        color: white;
        font-weight: 600 !important;
    }
    .list-group-item.active .text-muted {
        color: rgba(255, 255, 255, 0.8) !important;
    }
    .list-group-item.active .badge {
        background-color: white !important;
        color: #4481eb !important;
    }
    .category-icon-wrapper {
        transition: all 0.3s;
    }
    .list-group-item:hover .category-icon-wrapper {
        transform: scale(1.1);
    }

    /* Button Styles */
    .btn-primary {
        background: linear-gradient(135deg, #4481eb, #04befe);
        border: none;
        padding: 10px 20px;
        font-weight: 500;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(68, 129, 235, 0.3);
        transition: all 0.3s;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(68, 129, 235, 0.4);
    }

    /* Alert Styles */
    .alert {
        border-radius: 10px;
        border: none;
        padding: 15px 20px;
    }
    .alert-warning {
        background-color: #fff8e1;
        color: #f57c00;
        border-left: 4px solid #f57c00;
    }
    .alert-success {
        background-color: #e8f5e9;
        color: #2e7d32;
        border-left: 4px solid #2e7d32;
    }
    .alert-danger {
        background-color: #fbe9e7;
        color: #d32f2f;
        border-left: 4px solid #d32f2f;
    }

    /* Description Text Styles */
    .form-text {
        margin-top: 8px;
        font-size: 0.8rem;
        color: #6c757d;
    }

    /* Badge Styles */
    .badge {
        font-weight: 500;
        padding: 5px 10px;
        border-radius: 20px;
    }

    /* Animation */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    .tab-pane {
        animation: fadeIn 0.3s ease-in-out;
    }
</style>
@endsection

@section('content')
<div class="page-content">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-white">
                        <i class="material-icons-outlined me-2">settings</i> Environment Configuration
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-warning mb-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="material-icons-outlined" style="font-size: 2rem;">warning</i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Perhatian!</h5>
                                <p class="mb-0">Mengubah variabel lingkungan dapat memengaruhi cara kerja aplikasi Anda. Lakukan perubahan dengan hati-hati.
                                Backup file .env Anda saat ini akan dibuat sebelum perubahan apa pun diterapkan.</p>
                            </div>
                        </div>
                    </div>

                    @if(session('success'))
                        <div class="alert alert-success">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="material-icons-outlined" style="font-size: 2rem;">check_circle</i>
                                </div>
                                <div>
                                    <h5 class="alert-heading">Berhasil!</h5>
                                    <p class="mb-0">{{ session('success') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="material-icons-outlined" style="font-size: 2rem;">error</i>
                                </div>
                                <div>
                                    <h5 class="alert-heading">Error!</h5>
                                    <p class="mb-0">{{ session('error') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <form action="{{ route('env.config.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-3 mb-4">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header text-white">
                                        <i class="material-icons-outlined me-2">category</i> Categories
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="list-group list-group-flush" id="env-categories-tab" role="tablist">
                                            @foreach($categories as $category => $vars)
                                                @if(count($vars) > 0)
                                                    @php
                                                        $categoryColor = '';
                                                        $categoryIcon = '';

                                                        switch($category) {
                                                            case 'App Settings':
                                                                $categoryColor = 'linear-gradient(135deg, #4481eb, #04befe)';
                                                                $categoryIcon = 'fa-cog';
                                                                break;
                                                            case 'Database':
                                                                $categoryColor = 'linear-gradient(135deg, #43e97b, #38f9d7)';
                                                                $categoryIcon = 'fa-database';
                                                                break;
                                                            case 'Mail':
                                                                $categoryColor = 'linear-gradient(135deg, #fa709a, #fee140)';
                                                                $categoryIcon = 'fa-envelope';
                                                                break;
                                                            case 'Queue':
                                                                $categoryColor = 'linear-gradient(135deg, #f093fb, #f5576c)';
                                                                $categoryIcon = 'fa-tasks';
                                                                break;
                                                            case 'Services':
                                                                $categoryColor = 'linear-gradient(135deg, #5ee7df, #b490ca)';
                                                                $categoryIcon = 'fa-plug';
                                                                break;
                                                            case 'WhatsApp':
                                                                $categoryColor = 'linear-gradient(135deg, #00c6fb, #005bea)';
                                                                $categoryIcon = 'fa-whatsapp';
                                                                break;
                                                            default:
                                                                $categoryColor = 'linear-gradient(135deg, #a1c4fd, #c2e9fb)';
                                                                $categoryIcon = 'fa-wrench';
                                                        }
                                                    @endphp

                                                    <a class="list-group-item list-group-item-action {{ $loop->first ? 'active' : '' }}"
                                                    id="{{ Str::slug($category) }}-tab"
                                                    data-bs-toggle="list"
                                                    href="#{{ Str::slug($category) }}"
                                                    role="tab">
                                                        <div class="d-flex align-items-center">
                                                            <div class="category-icon-wrapper me-3" style="width: 36px; height: 36px; border-radius: 50%; background: {{ $categoryColor }}; display: flex; align-items: center; justify-content: center;">
                                                                <i class="fas {{ $categoryIcon }} text-white"></i>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <span class="fw-medium">{{ $category }}</span>
                                                                    <span class="badge bg-primary rounded-pill">{{ count($vars) }}</span>
                                                                </div>
                                                                <small class="text-muted d-block" style="font-size: 0.75rem;">
                                                                    @switch($category)
                                                                        @case('App Settings')
                                                                            Application configuration
                                                                            @break
                                                                        @case('Database')
                                                                            Database connections
                                                                            @break
                                                                        @case('Mail')
                                                                            Email configuration
                                                                            @break
                                                                        @case('Queue')
                                                                            Queue and jobs
                                                                            @break
                                                                        @case('Services')
                                                                            Third-party services
                                                                            @break
                                                                        @case('WhatsApp')
                                                                            WhatsApp integration
                                                                            @break
                                                                        @default
                                                                            Other settings
                                                                    @endswitch
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </a>
                                                @endif
                                            @endforeach
                                        </div>
                                    </div>
                                </div>

                                <div class="card border-0 shadow-sm mt-4">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="material-icons-outlined me-2">tips_and_updates</i> Tips
                                        </h5>
                                        <ul class="list-unstyled mb-0">
                                            <li class="mb-2">
                                                <i class="material-icons-outlined text-success me-2" style="font-size: 1rem;">check_circle</i>
                                                Klik ikon mata untuk melihat nilai sensitif
                                            </li>
                                            <li class="mb-2">
                                                <i class="material-icons-outlined text-success me-2" style="font-size: 1rem;">check_circle</i>
                                                Backup otomatis dibuat saat menyimpan
                                            </li>
                                            <li>
                                                <i class="material-icons-outlined text-success me-2" style="font-size: 1rem;">check_circle</i>
                                                Cache akan dibersihkan otomatis
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-9">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body p-4">
                                        <div class="tab-content">
                                            @foreach($categories as $category => $vars)
                                                @if(count($vars) > 0)
                                                    <div class="tab-pane fade {{ $loop->first ? 'show active' : '' }}"
                                                        id="{{ Str::slug($category) }}"
                                                        role="tabpanel">
                                                        <h4 class="category-title">
                                                            @switch($category)
                                                                @case('App Settings')
                                                                    <i class="material-icons-outlined me-2">settings</i>
                                                                    @break
                                                                @case('Database')
                                                                    <i class="material-icons-outlined me-2">storage</i>
                                                                    @break
                                                                @case('Mail')
                                                                    <i class="material-icons-outlined me-2">email</i>
                                                                    @break
                                                                @case('Queue')
                                                                    <i class="material-icons-outlined me-2">queue</i>
                                                                    @break
                                                                @case('Services')
                                                                    <i class="material-icons-outlined me-2">extension</i>
                                                                    @break
                                                                @case('WhatsApp')
                                                                    <i class="material-icons-outlined me-2">chat</i>
                                                                    @break
                                                                @default
                                                                    <i class="material-icons-outlined me-2">build</i>
                                                            @endswitch
                                                            {{ $category }} Configuration
                                                        </h4>

                                                        <div class="row">
                                                            @foreach($vars as $key => $value)
                                                                <div class="col-md-6 mb-3">
                                                                    <div class="card env-card">
                                                                        <div class="card-body">
                                                                            <label for="{{ $key }}" class="form-label env-key">
                                                                                {{ $key }}
                                                                                @php
                                                                                    $isSensitive = Str::contains($key, ['KEY', 'SECRET', 'PASSWORD', 'PASS', 'TOKEN', 'AUTH']);
                                                                                    $displayValue = $isSensitive ? str_repeat('•', 8) : $value;
                                                                                @endphp
                                                                                @if($isSensitive)
                                                                                    <span class="badge bg-warning text-dark float-end">
                                                                                        <i class="material-icons-outlined me-1" style="font-size: 0.75rem;">security</i> Sensitive
                                                                                    </span>
                                                                                @endif
                                                                            </label>

                                                                            <div class="{{ $isSensitive ? 'sensitive-field' : '' }}">
                                                                                <div class="input-group">
                                                                                    <input type="text"
                                                                                        class="form-control env-input"
                                                                                        id="{{ $key }}"
                                                                                        name="env_vars[{{ $key }}]"
                                                                                        value="{{ $value }}"
                                                                                        placeholder="{{ $displayValue }}"
                                                                                        {{ $isSensitive ? 'type=password data-real-value="'.$value.'"' : '' }}>

                                                                                    @if($isSensitive)
                                                                                        <span class="input-group-text toggle-visibility" data-target="{{ $key }}" style="cursor: pointer; background: linear-gradient(135deg, #4481eb, #04befe); color: white;">
                                                                                            <i class="material-icons-outlined" style="font-size: 1rem;">visibility_off</i>
                                                                                        </span>
                                                                                    @endif
                                                                                </div>
                                                                            </div>

                                                                            <small class="form-text text-muted">
                                                                                @switch($key)
                                                                                    @case('APP_ENV')
                                                                                        Environment mode (production, local, development)
                                                                                        @break
                                                                                    @case('APP_DEBUG')
                                                                                        Enable debug mode (true, false)
                                                                                        @break
                                                                                    @case('DB_CONNECTION')
                                                                                        Database driver (mysql, pgsql, sqlite)
                                                                                        @break
                                                                                    @case('MAIL_MAILER')
                                                                                        Mail driver (smtp, sendmail, mailgun)
                                                                                        @break
                                                                                    @case('QUEUE_CONNECTION')
                                                                                        Queue driver (sync, database, redis)
                                                                                        @break
                                                                                    @case('WHATSAPP_SERVICE_URL')
                                                                                        WhatsApp service URL
                                                                                        @break
                                                                                    @default
                                                                                        Configuration value for {{ $key }}
                                                                                @endswitch
                                                                            </small>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                @endif
                                            @endforeach
                                        </div>
                                    </div>
                                </div>

                                <div class="card border-0 shadow-sm mt-4">
                                    <div class="card-body d-flex justify-content-between align-items-center">
                                        <p class="backup-info mb-0">
                                            <i class="material-icons-outlined me-1" style="font-size: 1rem;">info</i>
                                            Backup file .env Anda akan dibuat di <code>.env.backup-[timestamp]</code>
                                        </p>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="material-icons-outlined me-1" style="font-size: 1rem;">save</i> Simpan Perubahan
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle password visibility with animation
        document.querySelectorAll('.toggle-visibility').forEach(function(toggle) {
            // Set initial state - all sensitive fields are hidden
            const targetId = toggle.getAttribute('data-target');
            const input = document.getElementById(targetId);
            const icon = toggle.querySelector('i');

            // Store original value in a data attribute if not already set
            if (!input.hasAttribute('data-original-value')) {
                input.setAttribute('data-original-value', input.value);
            }

            // Set initial state
            input.type = 'password';

            // Add click event
            toggle.addEventListener('click', function() {
                // Add animation class
                input.classList.add('animate__animated', 'animate__fadeIn');

                if (input.type === 'password') {
                    // Show the value
                    input.type = 'text';
                    icon.textContent = 'visibility';

                    // Make sure the real value is displayed
                    const realValue = input.getAttribute('data-real-value');
                    if (realValue) {
                        input.value = realValue;
                    }

                    // Show a tooltip
                    showTooltip(this, 'Nilai terlihat');
                } else {
                    // Hide the value
                    input.type = 'password';
                    icon.textContent = 'visibility_off';

                    // Show a tooltip
                    showTooltip(this, 'Nilai tersembunyi');
                }

                // Remove animation class after animation completes
                setTimeout(function() {
                    input.classList.remove('animate__animated', 'animate__fadeIn');
                }, 500);
            });
        });

        // Add hover effect to env cards
        document.querySelectorAll('.env-card').forEach(function(card) {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 12px 24px rgba(0,0,0,0.1)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.05)';
            });
        });

        // Add confirmation before form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Apakah Anda yakin ingin menyimpan perubahan pada file .env? Perubahan ini dapat memengaruhi cara kerja aplikasi.')) {
                this.submit();
            }
        });

        // Function to show tooltip
        function showTooltip(element, message) {
            // Create tooltip element
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip-custom';
            tooltip.textContent = message;
            tooltip.style.position = 'absolute';
            tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            tooltip.style.color = 'white';
            tooltip.style.padding = '5px 10px';
            tooltip.style.borderRadius = '4px';
            tooltip.style.fontSize = '12px';
            tooltip.style.zIndex = '1000';
            tooltip.style.opacity = '0';
            tooltip.style.transition = 'opacity 0.3s';

            // Position the tooltip
            const rect = element.getBoundingClientRect();
            document.body.appendChild(tooltip);
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
            tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';

            // Show and hide the tooltip
            setTimeout(function() {
                tooltip.style.opacity = '1';
            }, 10);

            setTimeout(function() {
                tooltip.style.opacity = '0';
                setTimeout(function() {
                    document.body.removeChild(tooltip);
                }, 300);
            }, 1500);
        }

        // Add search functionality for variables
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'form-control mb-3';
        searchInput.placeholder = 'Cari variabel...';
        searchInput.style.borderRadius = '8px';
        searchInput.style.padding = '10px 15px';

        // Insert search input before the tab content
        const tabContent = document.querySelector('.tab-content');
        tabContent.parentNode.insertBefore(searchInput, tabContent);

        // Add search functionality
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            document.querySelectorAll('.env-card').forEach(function(card) {
                const keyElement = card.querySelector('.env-key');
                const key = keyElement.textContent.toLowerCase();

                if (key.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
</script>
@endsection
