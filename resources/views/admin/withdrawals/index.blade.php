@extends('layouts.master')

@section('title', 'Manage Withdrawal Requests')

@section('css')
<style>
    .filter-toggle-icon {
        transition: transform 0.3s ease;
    }

    .collapsed .filter-toggle-icon {
        transform: rotate(180deg);
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    .product-box {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        overflow: hidden;
        background-color: #f8f9fa;
    }

    .product-box img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .withdrawal-row-pending {
        background-color: rgba(255, 193, 7, 0.05);
    }

    .withdrawal-row-approved {
        background-color: rgba(25, 135, 84, 0.05);
    }

    .withdrawal-row-rejected {
        background-color: rgba(220, 53, 69, 0.05);
    }
</style>
@endsection

@section('content')
<x-page-title title="Withdrawal Requests" pagetitle="Withdrawal Management" />

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
        <h5 class="card-title mb-0">
            <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
                <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
                <span>Filter Withdrawals</span>
                <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
            </a>
        </h5>
        <span class="badge bg-white text-primary" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">payments</i>
            {{ number_format(count($withdrawals), 0, ',', '.') }} Withdrawals
        </span>
    </div>
    <div class="collapse show" id="filterCollapse">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.withdrawals.index') }}">
                <div class="row g-3">
                    <!-- User Search -->
                    <div class="col-md-3">
                        <label for="user" class="form-label">User</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">person</i></span>
                            <input type="text" name="user" id="user" class="form-control" placeholder="Search by name or email..." value="{{ request('user') }}">
                        </div>
                    </div>

                    <!-- Status Filter -->
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">info</i></span>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                            </select>
                        </div>
                    </div>

                    <!-- Date Range -->
                    <div class="col-md-3">
                        <label for="date_range" class="form-label">Date Range</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">date_range</i></span>
                            <select name="date_range" id="date_range" class="form-select">
                                <option value="">All Time</option>
                                <option value="today" {{ request('date_range') == 'today' ? 'selected' : '' }}>Today</option>
                                <option value="yesterday" {{ request('date_range') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                                <option value="this_week" {{ request('date_range') == 'this_week' ? 'selected' : '' }}>This Week</option>
                                <option value="last_week" {{ request('date_range') == 'last_week' ? 'selected' : '' }}>Last Week</option>
                                <option value="this_month" {{ request('date_range') == 'this_month' ? 'selected' : '' }}>This Month</option>
                                <option value="last_month" {{ request('date_range') == 'last_month' ? 'selected' : '' }}>Last Month</option>
                            </select>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="col-md-4 d-flex align-items-end">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">search</i>
                                Apply
                            </button>
                            <a href="{{ route('admin.withdrawals.index') }}" class="btn btn-outline-secondary ms-2">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">refresh</i>
                                Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Summary Section -->
<div class="row mb-4">
    @php
        $pendingCount = $withdrawals->where('status', 'pending')->count();
        $approvedCount = $withdrawals->where('status', 'approved')->count();
        $rejectedCount = $withdrawals->where('status', 'rejected')->count();

        $totalPendingAmount = $withdrawals->where('status', 'pending')->sum('amount');
        $totalApprovedAmount = $withdrawals->where('status', 'approved')->sum('amount');
    @endphp

    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-warning bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-warning">pending</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Pending Withdrawals</h6>
                    <h3 class="mb-0">{{ $pendingCount }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-success bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-success">check_circle</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Approved Withdrawals</h6>
                    <h3 class="mb-0">{{ $approvedCount }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-danger bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-danger">cancel</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Rejected Withdrawals</h6>
                    <h3 class="mb-0">{{ $rejectedCount }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-primary bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-primary">payments</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Total Pending Amount</h6>
                    <h3 class="mb-0">Rp {{ number_format($totalPendingAmount, 0, ',', '.') }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Withdrawals Table Section -->
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5 class="card-title mb-0">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">payments</i>
            Withdrawal Requests
        </h5>
    </div>
    <div class="card-body p-0">
        @if(session('success'))
            <div class="alert alert-success m-3">
                <i class="material-icons-outlined me-2">check_circle</i>
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger m-3">
                <i class="material-icons-outlined me-2">error</i>
                {{ session('error') }}
            </div>
        @endif

        @if(count($withdrawals) > 0)
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Amount</th>
                            <th>Bank Details</th>
                            <th>Status</th>
                            <th>Requested At</th>
                            <th>Processed At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($withdrawals as $withdrawal)
                            @php
                                $rowClass = '';
                                if ($withdrawal->status === 'pending') {
                                    $rowClass = 'withdrawal-row-pending';
                                } elseif ($withdrawal->status === 'approved') {
                                    $rowClass = 'withdrawal-row-approved';
                                } elseif ($withdrawal->status === 'rejected') {
                                    $rowClass = 'withdrawal-row-rejected';
                                }
                            @endphp
                            <tr class="{{ $rowClass }}">
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="product-box">
                                            @if($withdrawal->user->photo)
                                                <img src="{{ asset("storage/{$withdrawal->user->photo}") }}" alt="{{ $withdrawal->user->name }}" class="rounded-3">
                                            @else
                                                <i class="material-icons-outlined text-primary">person</i>
                                            @endif
                                        </div>
                                        <div>
                                            <a href="{{ route('admin.users.show', $withdrawal->user->id) }}" class="fw-medium text-dark">
                                                {{ $withdrawal->user->name }}
                                            </a>
                                            <div class="small text-muted">{{ $withdrawal->user->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-medium">Rp {{ number_format($withdrawal->amount, 0, ',', '.') }}</div>
                                    <div class="small text-muted">ID: #{{ $withdrawal->id }}</div>
                                </td>
                                <td>
                                    <div class="fw-medium">{{ $withdrawal->bank_name }}</div>
                                    <div class="small text-muted">{{ $withdrawal->account_number }}</div>
                                    <div class="small text-muted">{{ $withdrawal->account_name ?: 'No account name' }}</div>
                                </td>
                                <td>
                                    @if($withdrawal->status === 'pending')
                                        <span class="badge status-badge bg-warning">Pending</span>
                                    @elseif($withdrawal->status === 'approved')
                                        <span class="badge status-badge bg-success">Approved</span>
                                    @else
                                        <span class="badge status-badge bg-danger"
                                            data-bs-toggle="tooltip"
                                            title="{{ $withdrawal->rejection_reason }}">
                                            Rejected
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="small">{{ $withdrawal->created_at->format('d M Y') }}</div>
                                    <div class="small text-muted">{{ $withdrawal->created_at->format('H:i') }}</div>
                                </td>
                                <td>
                                    @if($withdrawal->processed_at)
                                        <div class="small">{{ \Carbon\Carbon::parse($withdrawal->processed_at)->format('d M Y') }}</div>
                                        <div class="small text-muted">{{ \Carbon\Carbon::parse($withdrawal->processed_at)->format('H:i') }}</div>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($withdrawal->status === 'pending')
                                        <div class="d-flex flex-column gap-2">
                                            <form action="{{ route('admin.withdrawals.approve', $withdrawal->id) }}" method="POST">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-success w-100" onclick="return confirm('Are you sure you want to approve this withdrawal request?')">
                                                    <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">check_circle</i>
                                                    Approve
                                                </button>
                                            </form>
                                            <button type="button"
                                                class="btn btn-sm btn-danger w-100"
                                                data-bs-toggle="modal"
                                                data-bs-target="#rejectModal"
                                                onclick="setRejectFormAction({{ $withdrawal->id }})">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">cancel</i>
                                                Reject
                                            </button>
                                        </div>
                                    @else
                                        <span class="text-muted small">Processed</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <div class="text-muted small">
                    @if(method_exists($withdrawals, 'firstItem') && method_exists($withdrawals, 'lastItem') && method_exists($withdrawals, 'total'))
                        Menampilkan {{ $withdrawals->firstItem() ?? 0 }} - {{ $withdrawals->lastItem() ?? 0 }} dari {{ $withdrawals->total() }} withdrawals
                    @else
                        Menampilkan {{ count($withdrawals) }} withdrawals
                    @endif
                </div>
                <div>
                    {{ $withdrawals->links('vendor.pagination.simple') }}
                </div>
            </div>
        @else
            <div class="empty-state">
                <i class="material-icons-outlined">payments</i>
                <h5>No Withdrawal Requests Found</h5>
                <p>There are no withdrawal requests matching your criteria</p>
            </div>
        @endif
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="rejectForm" method="POST">
                @csrf
                <div class="modal-header" style="background: linear-gradient(90deg, #e53935, #ef5350); color: white;">
                    <h5 class="modal-title">
                        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">cancel</i>
                        Reject Withdrawal Request
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="material-icons-outlined me-2">info</i>
                        Please provide a reason for rejecting this withdrawal request. This reason will be visible to the user.
                    </div>
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">Reason for Rejection</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" required placeholder="Enter reason for rejection..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">close</i>
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">cancel</i>
                        Reject Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        // Toggle filter collapse icon
        const filterCollapse = document.getElementById('filterCollapse');
        const filterToggleIcon = document.querySelector('.filter-toggle-icon');

        filterCollapse.addEventListener('hidden.bs.collapse', function () {
            filterToggleIcon.textContent = 'expand_more';
        });

        filterCollapse.addEventListener('shown.bs.collapse', function () {
            filterToggleIcon.textContent = 'expand_less';
        });
    });

    function setRejectFormAction(id) {
        document.getElementById('rejectForm').action = "{{ url('admin/withdrawals') }}/" + id + "/reject";

        // Clear previous rejection reason
        document.getElementById('rejection_reason').value = '';
    }
</script>
@endsection
