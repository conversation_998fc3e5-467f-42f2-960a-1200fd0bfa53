@extends('layouts.master')

@section('title', 'Detail Kesalahan Sistem')

@section('css')
<style>
    /* Card styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 16px 24px;
        display: flex;
        align-items: center;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .card-body {
        padding: 24px;
    }

    /* Error header */
    .error-header {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
    }

    .error-badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 500;
    }

    /* Error sections */
    .error-section {
        margin-bottom: 2rem;
    }

    .error-section-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        color: #344767;
    }

    /* Code blocks */
    .code-block {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 300px;
        overflow-y: auto;
        font-size: 0.9rem;
        color: #333;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Trace items */
    .trace-item {
        padding: 1rem;
        border-radius: 8px;
        background-color: #f8f9fa;
        margin-bottom: 0.75rem;
        transition: all 0.2s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .trace-item:hover {
        background-color: rgba(13, 110, 253, 0.05);
        transform: translateY(-2px);
    }

    .trace-file {
        font-weight: 600;
        color: #344767;
    }

    .trace-line {
        color: #dc3545;
        font-weight: 600;
    }

    .trace-function {
        color: #0d6efd;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        font-size: 0.9rem;
    }

    /* Request info */
    .request-info {
        display: flex;
        flex-wrap: wrap;
    }

    .request-info-item {
        flex: 0 0 50%;
        margin-bottom: 0.75rem;
    }

    .request-info-label {
        font-weight: 600;
        color: #344767;
    }

    /* Icons and info items */
    .detail-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background-color: rgba(13, 110, 253, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #0d6efd;
    }

    .detail-icon i {
        font-size: 1.5rem;
    }

    .detail-info {
        flex: 1;
    }

    .company-info-item {
        background-color: #f8f9fa;
        border-radius: 10px;
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .company-info-item:hover {
        background-color: rgba(13, 110, 253, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    /* Buttons */
    .btn {
        border-radius: 6px;
        font-weight: 500;
        padding: 0.5rem 1rem;
        transition: all 0.2s ease;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .btn i {
        vertical-align: middle;
        font-size: 1rem;
    }

    /* List group */
    .list-group-item {
        border-left: none;
        border-right: none;
        padding: 1rem;
        transition: all 0.2s ease;
    }

    .list-group-item:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    .list-group-item:first-child {
        border-top: none;
    }

    /* Product box */
    .product-box {
        border-radius: 8px;
        background-color: rgba(13, 110, 253, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
@endsection

@section('content')
<div class="page-breadcrumb d-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="material-icons-outlined">home</i></a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.system-errors.index') }}">Monitoring Kesalahan</a></li>
                <li class="breadcrumb-item active" aria-current="page">Detail Kesalahan</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex align-items-center py-3">
                <div>
                    <h5 class="mb-0 fw-bold">Detail Kesalahan Sistem</h5>
                </div>
                <div class="ms-auto d-flex gap-2">
                    <a href="{{ route('admin.system-errors.index') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="material-icons-outlined">arrow_back</i> Kembali
                    </a>
                    @if(!$systemError->resolved)
                        <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#resolveModal">
                            <i class="material-icons-outlined">check</i> Selesaikan
                        </button>
                    @endif
                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="material-icons-outlined">delete</i> Hapus
                    </button>
                </div>
            </div>
            <div class="card-body">
                <p class="text-secondary mb-4">ID Kesalahan: <span class="fw-bold">#{{ $systemError->id }}</span></p>

                <!-- Error Header -->
                <div class="card mb-4">
                    <div class="card-header py-3">
                        <h5 class="card-title d-flex align-items-center mb-0">
                            <i class="material-icons-outlined me-2 text-primary">error_outline</i>
                            Detail Kesalahan
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="d-flex align-items-start mb-4">
                                    <div class="product-box me-3" style="width: 60px; height: 60px;">
                                        <i class="material-icons-outlined text-primary" style="font-size: 2.5rem;">error_outline</i>
                                    </div>
                                    <div>
                                        <h4 class="mb-2 fw-bold text-dark">{{ $systemError->message }}</h4>
                                        <div class="d-flex flex-wrap gap-2">
                                            <span class="badge bg-primary error-badge">
                                                <i class="material-icons-outlined me-1">category</i> {{ class_basename($systemError->type) }}
                                            </span>
                                            @if($systemError->code)
                                                <span class="badge bg-secondary error-badge">
                                                    <i class="material-icons-outlined me-1">code</i> Kode: {{ $systemError->code }}
                                                </span>
                                            @endif
                                            <span class="badge {{ $systemError->resolved ? 'bg-success' : 'bg-danger' }} error-badge">
                                                <i class="material-icons-outlined me-1">{{ $systemError->resolved ? 'check_circle' : 'error' }}</i>
                                                {{ $systemError->resolved ? 'Terselesaikan' : 'Belum Selesai' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="company-info-item p-3 mb-3">
                                    <div class="d-flex align-items-start gap-3">
                                        <div class="detail-icon">
                                            <i class="material-icons-outlined">code</i>
                                        </div>
                                        <div class="detail-info">
                                            <p class="fw-bold mb-1 text-dark">Lokasi File</p>
                                            <p class="mb-0">{{ $systemError->file }}:<span class="text-danger">{{ $systemError->line }}</span></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="company-info-item p-3">
                                    <div class="d-flex align-items-start gap-3">
                                        <div class="detail-icon">
                                            <i class="material-icons-outlined">schedule</i>
                                        </div>
                                        <div class="detail-info">
                                            <p class="fw-bold mb-1 text-dark">Waktu Kejadian</p>
                                            <p class="mb-0">{{ $systemError->created_at->format('d M Y H:i:s') }} ({{ $systemError->created_at->diffForHumans() }})</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header py-3">
                                    <h5 class="card-title d-flex align-items-center mb-0">
                                        <i class="material-icons-outlined me-2 text-primary">info</i>
                                        Informasi Tambahan
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @if($systemError->user)
                                        <div class="mb-3">
                                            <div class="company-info-item p-3">
                                                <div class="d-flex align-items-start gap-3">
                                                    <div class="detail-icon">
                                                        <i class="material-icons-outlined">person</i>
                                                    </div>
                                                    <div class="detail-info">
                                                        <p class="fw-bold mb-1 text-dark">User</p>
                                                        <p class="mb-0">{{ $systemError->user->name }} <br><small class="text-muted">{{ $systemError->user_email }}</small></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif

                                    <div class="mb-3">
                                        <div class="company-info-item p-3">
                                            <div class="d-flex align-items-start gap-3">
                                                <div class="detail-icon">
                                                    <i class="material-icons-outlined">public</i>
                                                </div>
                                                <div class="detail-info">
                                                    <p class="fw-bold mb-1 text-dark">IP Address</p>
                                                    <p class="mb-0">{{ $systemError->ip_address }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    @if($systemError->resolved)
                                        <div class="mb-3">
                                            <div class="company-info-item p-3" style="background-color: rgba(25, 135, 84, 0.05); border-color: rgba(25, 135, 84, 0.2);">
                                                <div class="d-flex align-items-start gap-3">
                                                    <div class="detail-icon" style="background-color: rgba(25, 135, 84, 0.1); color: #198754;">
                                                        <i class="material-icons-outlined">check_circle</i>
                                                    </div>
                                                    <div class="detail-info">
                                                        <p class="fw-bold mb-1 text-success">Status Penyelesaian</p>
                                                        <p class="mb-0">Diselesaikan pada: {{ $systemError->resolved_at->format('d M Y H:i:s') }}</p>
                                                        @if($systemError->resolvedByUser)
                                                            <p class="mb-0 text-muted">Oleh: {{ $systemError->resolvedByUser->name }}</p>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error Details -->
                <div class="row">
                    <div class="col-md-8">
                        <!-- Stack Trace -->
                        <div class="card mb-4">
                            <div class="card-header py-3">
                                <h5 class="card-title d-flex align-items-center mb-0">
                                    <i class="material-icons-outlined me-2 text-primary">layers</i>
                                    Stack Trace
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="trace-list">
                                    @if(is_array($systemError->trace) && count($systemError->trace) > 0)
                                        @foreach($systemError->trace as $index => $frame)
                                            <div class="trace-item">
                                                <div class="d-flex justify-content-between align-items-start mb-1">
                                                    <div class="trace-file">
                                                        {{ $frame['file'] ?? '[internal function]' }}
                                                        @if(isset($frame['line']))
                                                            <span class="trace-line">:{{ $frame['line'] }}</span>
                                                        @endif
                                                    </div>
                                                    <span class="badge bg-secondary">{{ $index + 1 }}</span>
                                                </div>
                                                <div class="trace-function">
                                                    {{ $frame['class'] ?? '' }}{{ $frame['type'] ?? '' }}{{ $frame['function'] ?? '' }}()
                                                </div>
                                                @if(isset($frame['args']) && count($frame['args']) > 0)
                                                    <div class="mt-2">
                                                        <small class="text-muted">Arguments:</small>
                                                        <div class="code-block mt-1" style="max-height: 100px; font-size: 0.8rem;">{{ json_encode($frame['args'], JSON_PRETTY_PRINT) }}</div>
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    @else
                                        <div class="alert alert-warning d-flex align-items-center">
                                            <i class="material-icons-outlined me-2">warning</i>
                                            <div>Stack trace tidak tersedia</div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Request Information -->
                        <div class="card mb-4">
                            <div class="card-header py-3">
                                <h5 class="card-title d-flex align-items-center mb-0">
                                    <i class="material-icons-outlined me-2 text-primary">http</i>
                                    Informasi Request
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="company-info-item p-3 mb-3">
                                    <div class="d-flex align-items-start gap-3">
                                        <div class="detail-icon">
                                            <i class="material-icons-outlined">link</i>
                                        </div>
                                        <div class="detail-info">
                                            <p class="fw-bold mb-1 text-dark">URL Request</p>
                                            <p class="mb-0 text-break">{{ $systemError->request_url }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-3 mb-3">
                                    <div class="col-md-4">
                                        <div class="company-info-item p-3 h-100">
                                            <div class="d-flex flex-column">
                                                <p class="fw-bold mb-1 text-dark">Method</p>
                                                <p class="mb-0">
                                                    <span class="badge bg-{{ $systemError->request_method == 'GET' ? 'success' : ($systemError->request_method == 'POST' ? 'primary' : 'warning') }} px-3 py-2">
                                                        {{ $systemError->request_method }}
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="company-info-item p-3 h-100">
                                            <div class="d-flex flex-column">
                                                <p class="fw-bold mb-1 text-dark">IP Address</p>
                                                <p class="mb-0">{{ $systemError->ip_address }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="company-info-item p-3 h-100">
                                            <div class="d-flex flex-column">
                                                <p class="fw-bold mb-1 text-dark">User Agent</p>
                                                <p class="mb-0 text-truncate">
                                                    <span class="d-inline-block text-truncate" style="max-width: 100%;" title="{{ $systemError->user_agent }}">
                                                        {{ $systemError->user_agent }}
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                @if(is_array($systemError->request_input) && count($systemError->request_input) > 0)
                                    <div class="company-info-item p-3">
                                        <div class="d-flex align-items-start gap-3">
                                            <div class="detail-icon">
                                                <i class="material-icons-outlined">input</i>
                                            </div>
                                            <div class="detail-info">
                                                <p class="fw-bold mb-1 text-dark">Request Input</p>
                                                <div class="code-block">{{ json_encode($systemError->request_input, JSON_PRETTY_PRINT) }}</div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- Resolution Information -->
                        @if($systemError->resolved && $systemError->notes)
                            <div class="card mb-4">
                                <div class="card-header py-3" style="background-color: rgba(25, 135, 84, 0.05);">
                                    <h5 class="card-title d-flex align-items-center mb-0">
                                        <i class="material-icons-outlined me-2 text-success">description</i>
                                        Catatan Penyelesaian
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="company-info-item p-3" style="border-color: rgba(25, 135, 84, 0.2);">
                                        <div class="d-flex align-items-start gap-3">
                                            <div class="detail-icon" style="background-color: rgba(25, 135, 84, 0.1); color: #198754;">
                                                <i class="material-icons-outlined">notes</i>
                                            </div>
                                            <div class="detail-info">
                                                <p class="fw-bold mb-1 text-success">Catatan</p>
                                                <p class="mb-0">{{ $systemError->notes }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Similar Errors -->
                        <div class="card">
                            <div class="card-header py-3">
                                <h5 class="card-title d-flex align-items-center mb-0">
                                    <i class="material-icons-outlined me-2 text-primary">find_replace</i>
                                    Kesalahan Serupa
                                </h5>
                            </div>
                            <div class="card-body">
                                @php
                                    $similarErrors = \App\Models\SystemError::where('id', '!=', $systemError->id)
                                        ->where('type', $systemError->type)
                                        ->orderBy('created_at', 'desc')
                                        ->limit(5)
                                        ->get();
                                @endphp

                                @if($similarErrors->count() > 0)
                                    <div class="list-group list-group-flush">
                                        @foreach($similarErrors as $error)
                                            <a href="{{ route('admin.system-errors.show', $error) }}" class="list-group-item list-group-item-action">
                                                <div class="d-flex w-100 justify-content-between align-items-center">
                                                    <div class="d-flex align-items-center gap-2">
                                                        <div class="product-box" style="width: 32px; height: 32px;">
                                                            <i class="material-icons-outlined text-primary" style="font-size: 16px;">error_outline</i>
                                                        </div>
                                                        <div>
                                                            <div class="text-truncate fw-medium" style="max-width: 180px; color: #344767;">{{ $error->message }}</div>
                                                            <small class="text-muted">{{ $error->created_at->diffForHumans() }}</small>
                                                        </div>
                                                    </div>
                                                    <span class="badge {{ $error->resolved ? 'bg-success' : 'bg-danger' }}">
                                                        {{ $error->resolved ? 'Selesai' : 'Belum' }}
                                                    </span>
                                                </div>
                                            </a>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="alert alert-info d-flex align-items-center">
                                        <i class="material-icons-outlined me-2">info</i>
                                        <div>Tidak ada kesalahan serupa yang ditemukan</div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resolve Modal -->
<div class="modal fade" id="resolveModal" tabindex="-1" aria-labelledby="resolveModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <form action="{{ route('admin.system-errors.resolve', $systemError) }}" method="POST">
                @csrf
                <div class="modal-header bg-light">
                    <h5 class="modal-title fw-semibold" id="resolveModalLabel">
                        <i class="material-icons-outlined text-success me-2">check_circle</i>
                        Selesaikan Kesalahan
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="alert alert-info d-flex align-items-center mb-4">
                        <i class="material-icons-outlined me-2">info</i>
                        <div>Apakah Anda yakin ingin menandai kesalahan ini sebagai terselesaikan?</div>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label fw-medium">Catatan (opsional)</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Tambahkan catatan tentang penyelesaian..."></textarea>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="material-icons-outlined me-1">close</i> Batal
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="material-icons-outlined me-1">check</i> Selesaikan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <form action="{{ route('admin.system-errors.destroy', $systemError) }}" method="POST">
                @csrf
                @method('DELETE')
                <div class="modal-header bg-light">
                    <h5 class="modal-title fw-semibold" id="deleteModalLabel">
                        <i class="material-icons-outlined text-danger me-2">delete</i>
                        Hapus Kesalahan
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="alert alert-warning d-flex align-items-center mb-0">
                        <i class="material-icons-outlined me-2">warning</i>
                        <div>Apakah Anda yakin ingin menghapus kesalahan ini? Tindakan ini tidak dapat dibatalkan.</div>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="material-icons-outlined me-1">close</i> Batal
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="material-icons-outlined me-1">delete</i> Hapus
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
