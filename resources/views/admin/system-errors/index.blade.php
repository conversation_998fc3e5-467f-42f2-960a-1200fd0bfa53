@extends('layouts.master')

@section('title', 'Monitoring Kesalahan Sistem')

@section('css')
<style>
    /* Card styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 16px 24px;
        display: flex;
        align-items: center;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .card-body {
        padding: 24px;
    }

    /* Stat cards */
    .stat-card {
        border-radius: 12px;
        overflow: hidden;
        transition: transform 0.3s ease;
        height: 100%;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-card .card-body {
        padding: 1.5rem;
    }

    .stat-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .stat-card.bg-primary, .stat-card.bg-danger, .stat-card.bg-success,
    .stat-card.bg-warning, .stat-card.bg-info {
        color: white;
    }

    .stat-card h3, .stat-card p {
        color: white;
    }

    /* Filter section */
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
    }

    /* Table styling */
    .table th {
        font-weight: 600;
        color: #344767;
        border-bottom-width: 1px;
    }

    .table td {
        vertical-align: middle;
        padding: 12px 16px;
    }

    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }

    /* Badge styling */
    .badge {
        font-weight: 500;
        padding: 0.5em 0.8em;
        border-radius: 6px;
    }

    /* Empty state */
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
    }

    .empty-state i {
        font-size: 4rem;
        color: #d3d7dc;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #344767;
    }

    .empty-state p {
        color: #6c757d;
    }

    /* Product box */
    .product-box {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        background-color: rgba(13, 110, 253, 0.1);
    }

    .product-title {
        font-weight: 600;
        color: #344767;
        text-decoration: none;
    }

    .product-title:hover {
        color: #007bff;
        text-decoration: underline;
    }

    /* Buttons */
    .btn {
        border-radius: 6px;
        font-weight: 500;
        padding: 0.5rem 1rem;
        transition: all 0.2s ease;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .btn i {
        vertical-align: middle;
        font-size: 1rem;
    }

    /* Pagination */
    .pagination .page-link {
        border-radius: 4px;
        margin: 0 2px;
        color: #007bff;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
    }
</style>
@endsection

@section('content')
<div class="page-breadcrumb d-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">Admin</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="material-icons-outlined">home</i></a></li>
                <li class="breadcrumb-item active" aria-current="page">Monitoring Kesalahan Sistem</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex align-items-center py-3">
                <div>
                    <h5 class="mb-0 fw-bold">Monitoring Kesalahan Sistem</h5>
                </div>
                <div class="ms-auto d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                        <i class="material-icons-outlined">filter_list</i> Filter
                    </button>
                    @if($unresolvedCount > 0)
                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#bulkResolveModal">
                            <i class="material-icons-outlined">done_all</i> Selesaikan Semua
                        </button>
                    @endif
                    @if($resolvedCount > 0)
                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteResolvedModal">
                            <i class="material-icons-outlined">delete_sweep</i> Hapus Terselesaikan
                        </button>
                    @endif
                </div>
            </div>
            <div class="card-body">
                <p class="text-secondary mb-4">Pantau dan kelola kesalahan sistem aplikasi. Total: <span class="fw-bold">{{ $statistics['total'] }}</span> kesalahan.</p>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card bg-primary text-white">
                            <div class="card-body d-flex align-items-center p-4">
                                <div class="stat-icon me-3">
                                    <i class="material-icons-outlined">error_outline</i>
                                </div>
                                <div>
                                    <h3 class="mb-1 fw-bold">{{ $statistics['total'] }}</h3>
                                    <p class="mb-0">Total Error</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-danger text-white">
                            <div class="card-body d-flex align-items-center p-4">
                                <div class="stat-icon me-3">
                                    <i class="material-icons-outlined">report_problem</i>
                                </div>
                                <div>
                                    <h3 class="mb-1 fw-bold">{{ $statistics['unresolved'] }}</h3>
                                    <p class="mb-0">Belum Selesai</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-success text-white">
                            <div class="card-body d-flex align-items-center p-4">
                                <div class="stat-icon me-3">
                                    <i class="material-icons-outlined">check_circle_outline</i>
                                </div>
                                <div>
                                    <h3 class="mb-1 fw-bold">{{ $statistics['resolved'] }}</h3>
                                    <p class="mb-0">Terselesaikan</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-info text-white">
                            <div class="card-body d-flex align-items-center p-4">
                                <div class="stat-icon me-3">
                                    <i class="material-icons-outlined">today</i>
                                </div>
                                <div>
                                    <h3 class="mb-1 fw-bold">{{ $statistics['today'] }}</h3>
                                    <p class="mb-0">Hari Ini</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card bg-warning text-white">
                            <div class="card-body d-flex align-items-center p-4">
                                <div class="stat-icon me-3">
                                    <i class="material-icons-outlined">date_range</i>
                                </div>
                                <div>
                                    <h3 class="mb-1 fw-bold">{{ $statistics['this_week'] }}</h3>
                                    <p class="mb-0">Minggu Ini</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-secondary text-white">
                            <div class="card-body d-flex align-items-center p-4">
                                <div class="stat-icon me-3">
                                    <i class="material-icons-outlined">calendar_month</i>
                                </div>
                                <div>
                                    <h3 class="mb-1 fw-bold">{{ $statistics['this_month'] }}</h3>
                                    <p class="mb-0">Bulan Ini</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filter Section -->
                <div class="collapse show" id="filterCollapse">
                    <div class="filter-section mb-4">
                        <h5 class="card-title d-flex align-items-center mb-3">
                            <i class="material-icons-outlined me-2 text-primary">filter_list</i>
                            Filter Kesalahan Sistem
                        </h5>
                        <form action="{{ route('admin.system-errors.index') }}" method="GET">
                            <div class="row g-3">
                                <!-- Pencarian -->
                                <div class="col-md-3">
                                    <label for="search" class="form-label">Pencarian</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="material-icons-outlined">search</i></span>
                                        <input type="text" name="search" id="search" class="form-control"
                                            placeholder="Cari pesan, file, email..." value="{{ $search }}">
                                    </div>
                                    <div class="form-text">Cari berdasarkan pesan error atau file</div>
                                </div>

                                <!-- Status -->
                                <div class="col-md-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="all" {{ $status == 'all' ? 'selected' : '' }}>Semua</option>
                                        <option value="unresolved" {{ $status == 'unresolved' ? 'selected' : '' }}>Belum Selesai</option>
                                        <option value="resolved" {{ $status == 'resolved' ? 'selected' : '' }}>Terselesaikan</option>
                                    </select>
                                </div>

                                <!-- Tipe Error -->
                                <div class="col-md-3">
                                    <label for="type" class="form-label">Tipe Error</label>
                                    <select name="type" id="type" class="form-select">
                                        <option value="">Semua Tipe</option>
                                        @foreach($errorTypes as $errorType)
                                            <option value="{{ $errorType }}" {{ $type == $errorType ? 'selected' : '' }}>
                                                {{ class_basename($errorType) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Urutkan -->
                                <div class="col-md-3">
                                    <label for="sort_by" class="form-label">Urutkan</label>
                                    <div class="input-group">
                                        <select name="sort_by" id="sort_by" class="form-select">
                                            <option value="created_at" {{ $sortBy == 'created_at' ? 'selected' : '' }}>Tanggal</option>
                                            <option value="type" {{ $sortBy == 'type' ? 'selected' : '' }}>Tipe</option>
                                            <option value="file" {{ $sortBy == 'file' ? 'selected' : '' }}>File</option>
                                        </select>
                                        <select name="sort_order" id="sort_order" class="form-select">
                                            <option value="desc" {{ $sortOrder == 'desc' ? 'selected' : '' }}>Menurun</option>
                                            <option value="asc" {{ $sortOrder == 'asc' ? 'selected' : '' }}>Menaik</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Tanggal Mulai -->
                                <div class="col-md-3">
                                    <label for="start_date" class="form-label">Tanggal Mulai</label>
                                    <input type="date" name="start_date" id="start_date" class="form-control" value="{{ $startDate }}">
                                </div>

                                <!-- Tanggal Akhir -->
                                <div class="col-md-3">
                                    <label for="end_date" class="form-label">Tanggal Akhir</label>
                                    <input type="date" name="end_date" id="end_date" class="form-control" value="{{ $endDate }}">
                                </div>

                                <!-- Tombol -->
                                <div class="col-md-6 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="material-icons-outlined">filter_alt</i> Terapkan Filter
                                    </button>
                                    <a href="{{ route('admin.system-errors.index') }}" class="btn btn-outline-secondary">
                                        <i class="material-icons-outlined">clear</i> Reset
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Error List -->
                @if($errors->count() > 0)
                <div class="card mb-4">
                    <div class="card-header py-3">
                        <h5 class="card-title d-flex align-items-center mb-0">
                            <i class="material-icons-outlined me-2 text-primary">error_outline</i>
                            Daftar Kesalahan Sistem
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table align-middle mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 5%;">ID</th>
                                        <th style="width: 10%;">Tipe</th>
                                        <th style="width: 35%;">Pesan</th>
                                        <th style="width: 20%;">Lokasi</th>
                                        <th style="width: 10%;">Waktu</th>
                                        <th style="width: 10%;">Status</th>
                                        <th style="width: 10%;">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($errors as $error)
                                    <tr>
                                    <td>
                                        <a href="{{ route('admin.system-errors.show', $error) }}" class="fw-semibold text-primary">
                                            #{{ $error->id }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ class_basename($error->type) }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="product-box" style="width: 30px; height: 30px; background-color: rgba(13, 110, 253, 0.1); display: flex; align-items: center; justify-content: center;">
                                                <i class="material-icons-outlined text-primary">error_outline</i>
                                            </div>
                                            <div class="product-info">
                                                <a href="{{ route('admin.system-errors.show', $error) }}" class="product-title">
                                                    {{ \Illuminate\Support\Str::limit($error->message, 70) }}
                                                </a>
                                                <p class="mb-0 product-category small text-muted">
                                                    {{ $error->created_at->format('d M Y H:i:s') }}
                                                </p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-truncate">
                                            <span class="fw-medium">{{ basename($error->file) }}</span>:<span class="text-danger">{{ $error->line }}</span>
                                        </div>
                                    </td>
                                    <td>{{ $error->created_at->diffForHumans() }}</td>
                                    <td>
                                        @if($error->resolved)
                                            <span class="badge bg-success">Terselesaikan</span>
                                        @else
                                            <span class="badge bg-danger">Belum Selesai</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('admin.system-errors.show', $error) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Lihat Detail">
                                                <i class="material-icons-outlined">visibility</i>
                                            </a>
                                            @if(!$error->resolved)
                                                <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#resolveModal{{ $error->id }}" title="Selesaikan">
                                                    <i class="material-icons-outlined">check</i>
                                                </button>
                                            @endif
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $error->id }}" title="Hapus">
                                                <i class="material-icons-outlined">delete</i>
                                            </button>
                                        </div>
                                    </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center p-3 border-top">
                            <div class="text-muted small">
                                Menampilkan {{ $errors->firstItem() ?? 0 }} - {{ $errors->lastItem() ?? 0 }} dari {{ $errors->total() }} kesalahan
                            </div>
                            <div>
                                {{ $errors->links('vendor.pagination.simple') }}
                            </div>
                        </div>
                    </div>
                </div>
                @else
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="empty-state py-5">
                            <i class="material-icons-outlined text-success">sentiment_satisfied_alt</i>
                            <h5 class="mt-3">Tidak ada kesalahan sistem yang ditemukan</h5>
                            <p class="text-muted">Sistem berjalan dengan baik atau tidak ada kesalahan yang tercatat</p>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Modals for each error -->
@foreach($errors as $error)
    <!-- Resolve Modal -->
    <div class="modal fade" id="resolveModal{{ $error->id }}" tabindex="-1" aria-labelledby="resolveModalLabel{{ $error->id }}" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow">
                <form action="{{ route('admin.system-errors.resolve', $error) }}" method="POST">
                    @csrf
                    <div class="modal-header bg-light">
                        <h5 class="modal-title fw-semibold" id="resolveModalLabel{{ $error->id }}">
                            <i class="material-icons-outlined text-success me-2">check_circle</i>
                            Selesaikan Kesalahan
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-4">
                        <div class="alert alert-info d-flex align-items-center mb-4">
                            <i class="material-icons-outlined me-2">info</i>
                            <div>Apakah Anda yakin ingin menandai kesalahan ini sebagai terselesaikan?</div>
                        </div>
                        <div class="mb-3">
                            <label for="notes{{ $error->id }}" class="form-label fw-medium">Catatan (opsional)</label>
                            <textarea name="notes" id="notes{{ $error->id }}" class="form-control" rows="3" placeholder="Tambahkan catatan tentang penyelesaian..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer bg-light">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="material-icons-outlined me-1">close</i> Batal
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="material-icons-outlined me-1">check</i> Selesaikan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal{{ $error->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $error->id }}" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow">
                <form action="{{ route('admin.system-errors.destroy', $error) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <div class="modal-header bg-light">
                        <h5 class="modal-title fw-semibold" id="deleteModalLabel{{ $error->id }}">
                            <i class="material-icons-outlined text-danger me-2">delete</i>
                            Hapus Kesalahan
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-4">
                        <div class="alert alert-warning d-flex align-items-center mb-0">
                            <i class="material-icons-outlined me-2">warning</i>
                            <div>Apakah Anda yakin ingin menghapus kesalahan ini? Tindakan ini tidak dapat dibatalkan.</div>
                        </div>
                    </div>
                    <div class="modal-footer bg-light">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="material-icons-outlined me-1">close</i> Batal
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="material-icons-outlined me-1">delete</i> Hapus
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endforeach

<!-- Bulk Resolve Modal -->
<div class="modal fade" id="bulkResolveModal" tabindex="-1" aria-labelledby="bulkResolveModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <form action="{{ route('admin.system-errors.bulk-resolve') }}" method="POST">
                @csrf
                <div class="modal-header bg-light">
                    <h5 class="modal-title fw-semibold" id="bulkResolveModalLabel">
                        <i class="material-icons-outlined text-success me-2">done_all</i>
                        Selesaikan Semua Kesalahan
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="alert alert-info d-flex align-items-center mb-4">
                        <i class="material-icons-outlined me-2">info</i>
                        <div>Apakah Anda yakin ingin menandai semua kesalahan yang belum terselesaikan sebagai terselesaikan?</div>
                    </div>
                    <div class="mb-3">
                        <label for="bulkNotes" class="form-label fw-medium">Catatan (opsional)</label>
                        <textarea name="notes" id="bulkNotes" class="form-control" rows="3" placeholder="Tambahkan catatan tentang penyelesaian..."></textarea>
                    </div>
                    @foreach($unresolvedIds as $id)
                        <input type="hidden" name="error_ids[]" value="{{ $id }}">
                    @endforeach
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="material-icons-outlined me-1">close</i> Batal
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="material-icons-outlined me-1">done_all</i> Selesaikan Semua
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Resolved Modal -->
<div class="modal fade" id="deleteResolvedModal" tabindex="-1" aria-labelledby="deleteResolvedModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <form action="{{ route('admin.system-errors.destroy-resolved') }}" method="POST">
                @csrf
                @method('DELETE')
                <div class="modal-header bg-light">
                    <h5 class="modal-title fw-semibold" id="deleteResolvedModalLabel">
                        <i class="material-icons-outlined text-danger me-2">delete_sweep</i>
                        Hapus Kesalahan Terselesaikan
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="alert alert-warning d-flex align-items-center mb-0">
                        <i class="material-icons-outlined me-2">warning</i>
                        <div>Apakah Anda yakin ingin menghapus semua kesalahan yang telah terselesaikan? Tindakan ini tidak dapat dibatalkan.</div>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="material-icons-outlined me-1">close</i> Batal
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="material-icons-outlined me-1">delete_sweep</i> Hapus Semua Terselesaikan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
