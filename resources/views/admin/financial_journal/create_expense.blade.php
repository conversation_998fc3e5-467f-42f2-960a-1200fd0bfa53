@extends('layouts.master')

@section('title', 'Add New Expense')

@section('css')
<style>
    .required-field::after {
        content: " *";
        color: red;
    }
</style>
@endsection

@section('content')
<x-page-title title="Add New Expense" pagetitle="Financial Management" />

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #e53935, #ef5350); color: white;">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">add_circle</i>
                    Add New Expense
                </h5>
            </div>
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form action="{{ route('admin.expenses.store') }}" method="POST">
                    @csrf
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="amount" class="form-label required-field">Amount (Rp)</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">payments</i></span>
                                <input type="number" class="form-control" id="amount" name="amount" value="{{ old('amount') }}" min="0" step="0.01" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="date" class="form-label required-field">Date</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">event</i></span>
                                <input type="date" class="form-control" id="date" name="date" value="{{ old('date', now()->format('Y-m-d')) }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label required-field">Description</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">description</i></span>
                            <input type="text" class="form-control" id="description" name="description" value="{{ old('description') }}" required>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="category" class="form-label">Category</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">category</i></span>
                                <input type="text" class="form-control" id="category" name="category" value="{{ old('category') }}" list="category-list">
                                <datalist id="category-list">
                                    @foreach($categories as $category)
                                        <option value="{{ $category }}">
                                    @endforeach
                                </datalist>
                            </div>
                            <div class="form-text">Choose from existing categories or create a new one</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="reference" class="form-label">Reference</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">tag</i></span>
                                <input type="text" class="form-control" id="reference" name="reference" value="{{ old('reference') }}">
                            </div>
                            <div class="form-text">Invoice number, receipt number, etc.</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ route('admin.financial_journal') }}" class="btn btn-outline-secondary">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">arrow_back</i>
                            Back to Journal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">save</i>
                            Save Expense
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
