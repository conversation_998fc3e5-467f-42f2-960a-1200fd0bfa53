@extends('layouts.master')

@section('title', 'Financial Journal')

@section('css')
<style>
    .filter-toggle-icon {
        transition: transform 0.3s ease;
    }

    .collapsed .filter-toggle-icon {
        transform: rotate(180deg);
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .journal-row-in {
        background-color: rgba(25, 135, 84, 0.05);
    }

    .journal-row-out {
        background-color: rgba(220, 53, 69, 0.05);
    }

    .date-input-group .input-group-text {
        background-color: #f8f9fa;
    }
</style>
@endsection

@section('content')
<x-page-title title="Financial Journal" pagetitle="Financial Management" />

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
        <h5 class="card-title mb-0">
            <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
                <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
                <span>Filter Transactions</span>
                <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
            </a>
        </h5>
        <span class="badge bg-white text-primary" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">receipt_long</i>
            {{ number_format(count($journal), 0, ',', '.') }} Transactions
        </span>
    </div>
    <div class="collapse show" id="filterCollapse">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.financial_journal') }}" id="filter-form">
                <div class="row g-3">
                    <!-- Date Range -->
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">Start Date</label>
                        <div class="input-group date-input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">event</i></span>
                            <input type="date" class="form-control" id="start_date" name="start_date"
                                value="{{ request('start_date', now()->startOfMonth()->format('Y-m-d')) }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">End Date</label>
                        <div class="input-group date-input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">event</i></span>
                            <input type="date" class="form-control" id="end_date" name="end_date"
                                value="{{ request('end_date', now()->endOfMonth()->format('Y-m-d')) }}">
                        </div>
                    </div>

                    <!-- Transaction Type -->
                    <div class="col-md-2">
                        <label for="type" class="form-label">Transaction Type</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">swap_vert</i></span>
                            <select name="type" id="type" class="form-select">
                                <option value="">All Types</option>
                                <option value="in" {{ request('type') == 'in' ? 'selected' : '' }}>Money In</option>
                                <option value="out" {{ request('type') == 'out' ? 'selected' : '' }}>Money Out</option>
                            </select>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="col-md-2 d-flex align-items-end">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">search</i>
                                Apply
                            </button>
                            <a href="{{ route('admin.financial_journal') }}" class="btn btn-outline-secondary ms-2">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">refresh</i>
                                Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Summary Section -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-success bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-success">arrow_downward</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Total Money In</h6>
                    <h3 class="mb-0">Rp {{ number_format($totalIn, 0, ',', '.') }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-danger bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-danger">arrow_upward</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Total Money Out</h6>
                    <h3 class="mb-0">Rp {{ number_format($totalOut, 0, ',', '.') }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-{{ $balance >= 0 ? 'primary' : 'warning' }} bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-{{ $balance >= 0 ? 'primary' : 'warning' }}">account_balance</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Balance</h6>
                    <h3 class="mb-0 {{ $balance < 0 ? 'text-danger' : '' }}">Rp {{ number_format($balance, 0, ',', '.') }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Journal Table Section -->
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5 class="card-title mb-0">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">receipt_long</i>
            Transaction History
        </h5>
        <div>
            <a href="{{ route('admin.expenses.create') }}" class="btn btn-sm btn-success me-2">
                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">add_circle</i>
                Add Expense
            </a>
            <a href="#" class="btn btn-sm btn-outline-primary" onclick="exportToExcel()">
                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">file_download</i>
                Export to Excel
            </a>
        </div>
    </div>
    <div class="card-body p-0">
        @if(count($journal) > 0)
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>User</th>
                            <th>Amount</th>
                            <th>Reference</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($journal as $entry)
                            <tr class="journal-row-{{ $entry['type'] }}">
                                <td>
                                    <div class="small">{{ \Carbon\Carbon::parse($entry['date'])->format('d M Y') }}</div>
                                    <div class="small text-muted">{{ \Carbon\Carbon::parse($entry['date'])->format('H:i') }}</div>
                                </td>
                                <td>
                                    @if($entry['type'] === 'in')
                                        <span class="badge status-badge bg-success">Money In</span>
                                    @else
                                        <span class="badge status-badge bg-danger">Money Out</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="fw-medium">{{ $entry['description'] }}</div>
                                    @if(isset($entry['category']))
                                        <span class="badge bg-info">{{ $entry['category'] }}</span>
                                    @endif
                                    @if(isset($entry['notes']) && $entry['notes'])
                                        <div class="small text-muted mt-1">
                                            <i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">notes</i>
                                            {{ Str::limit($entry['notes'], 50) }}
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    <div class="fw-medium">{{ $entry['user'] }}</div>
                                </td>
                                <td class="{{ $entry['type'] === 'in' ? 'text-success' : 'text-danger' }} fw-bold">
                                    {{ $entry['type'] === 'in' ? '+' : '-' }}
                                    Rp {{ number_format($entry['amount'], 0, ',', '.') }}
                                </td>
                                <td>
                                    <div class="small text-muted">{{ $entry['reference'] }}</div>
                                    @if(isset($entry['expense_id']))
                                        <div class="mt-2">
                                            <a href="{{ route('admin.expenses.edit', $entry['expense_id']) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">edit</i>
                                                Edit
                                            </a>
                                        </div>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="empty-state">
                <i class="material-icons-outlined">receipt_long</i>
                <h5>No Transactions Found</h5>
                <p>There are no transactions matching your criteria for the selected period.</p>
            </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        // Toggle filter collapse icon
        const filterCollapse = document.getElementById('filterCollapse');
        const filterToggleIcon = document.querySelector('.filter-toggle-icon');

        filterCollapse.addEventListener('hidden.bs.collapse', function () {
            filterToggleIcon.textContent = 'expand_more';
        });

        filterCollapse.addEventListener('shown.bs.collapse', function () {
            filterToggleIcon.textContent = 'expand_less';
        });

        // Auto-submit form when dates or type change
        document.getElementById('start_date').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('end_date').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('type').addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Export to Excel function
    function exportToExcel() {
        // Get the table
        const table = document.querySelector('table');
        if (!table) return;

        // Create a workbook
        const wb = XLSX.utils.book_new();

        // Get the table data
        const ws = XLSX.utils.table_to_sheet(table);

        // Add the worksheet to the workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Financial Journal');

        // Generate the Excel file
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const fileName = `Financial_Journal_${startDate}_to_${endDate}.xlsx`;

        // Save the file
        XLSX.writeFile(wb, fileName);
    }
</script>

<!-- Add SheetJS library for Excel export -->
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
@endsection
