@extends('layouts.master')

@section('title', 'WhatsApp Monitoring')

@section('css')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
<link href="https://fonts.googleapis.com/css2?family=Material+Icons+Outlined" rel="stylesheet">
<!-- Custom Toastr CSS -->
<style id="toastr-custom-css">
    /* Toastr Styling */
    #toast-container > div {
        opacity: 1;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        padding: 15px 15px 15px 50px;
    }

    .toast-success {
        background-color: #28a745 !important;
    }

    .toast-error {
        background-color: #dc3545 !important;
    }

    .toast-info {
        background-color: #17a2b8 !important;
    }

    .toast-warning {
        background-color: #ffc107 !important;
        color: #212529 !important;
    }

    #toast-container > .toast-success:before {
        content: '\f058';
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
        font-size: 24px;
        color: #fff;
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    #toast-container > .toast-error:before {
        content: '\f057';
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
        font-size: 24px;
        color: #fff;
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    #toast-container > .toast-info:before {
        content: '\f05a';
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
        font-size: 24px;
        color: #fff;
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    #toast-container > .toast-warning:before {
        content: '\f071';
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
        font-size: 24px;
        color: #212529;
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    .toast-message {
        font-size: 14px;
        font-weight: 500;
    }

    .toast-close-button {
        color: inherit;
        opacity: 0.7;
        font-size: 18px;
        font-weight: 400;
    }

    .toast-close-button:hover {
        color: inherit;
        opacity: 1;
    }

    .toast-progress {
        height: 4px;
        opacity: 0.7;
        bottom: 0;
        border-radius: 0 0 8px 8px;
    }

    .toast-success .toast-progress {
        background-color: rgba(255, 255, 255, 0.3);
    }

    .toast-error .toast-progress {
        background-color: rgba(255, 255, 255, 0.3);
    }

    .toast-info .toast-progress {
        background-color: rgba(255, 255, 255, 0.3);
    }

    .toast-warning .toast-progress {
        background-color: rgba(0, 0, 0, 0.1);
    }
</style>
<style>
    /* Card Styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 18px 24px;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
    }

    .card-body {
        padding: 24px;
    }

    /* Status Cards */
    .status-card {
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .status-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .status-card .card-header {
        padding: 15px 20px;
        border-radius: 12px 12px 0 0;
    }

    .status-card .card-body {
        padding: 20px;
    }

    /* Badges */
    .badge {
        padding: 6px 10px;
        font-weight: 500;
        font-size: 0.75rem;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        line-height: 1;
    }

    .badge i {
        font-size: 12px;
        margin-right: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: baseline;
    }

    .badge.bg-success {
        background-color: #198754 !important;
        box-shadow: 0 2px 5px rgba(25, 135, 84, 0.2);
    }

    .badge.bg-danger {
        background-color: #dc3545 !important;
        box-shadow: 0 2px 5px rgba(220, 53, 69, 0.2);
    }

    .badge.bg-warning {
        background-color: #ffc107 !important;
        color: #212529;
        box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
    }

    .badge.bg-info {
        background-color: #0dcaf0 !important;
        box-shadow: 0 2px 5px rgba(13, 202, 240, 0.2);
    }

    .badge.bg-primary {
        background-color: #0d6efd !important;
        box-shadow: 0 2px 5px rgba(13, 110, 253, 0.2);
    }

    /* Buttons */
    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
        color: white;
        box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
    }

    .btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0b5ed7;
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
    }

    .btn-secondary {
        background-color: #f8f9fa;
        border-color: #e9ecef;
        color: #6c757d;
    }

    .btn-secondary:hover {
        background-color: #e9ecef;
        border-color: #dfe2e5;
        color: #495057;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.875rem;
    }

    /* Timeline Styling */
    .timeline {
        position: relative;
        margin: 0 0 30px 0;
        padding: 0;
        list-style: none;
    }

    .timeline:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 4px;
        background: #e9ecef;
        left: 31px;
        margin: 0;
        border-radius: 2px;
    }

    .timeline > div {
        position: relative;
        margin-right: 10px;
        margin-bottom: 15px;
    }

    .timeline > div > i {
        width: 30px;
        height: 30px;
        font-size: 15px;
        line-height: 30px;
        position: absolute;
        color: #fff;
        background: #dc3545;
        border-radius: 50%;
        text-align: center;
        left: 18px;
        top: 0;
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
    }

    .timeline-item {
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        border-radius: 8px;
        margin-top: 0;
        background: #fff;
        color: #444;
        margin-left: 60px;
        margin-right: 15px;
        padding: 0;
        position: relative;
        transition: all 0.3s ease;
    }

    .timeline-item:hover {
        box-shadow: 0 6px 15px rgba(0,0,0,0.08);
        transform: translateY(-2px);
    }

    .timeline-item > .time {
        color: #6c757d;
        float: right;
        padding: 10px;
        font-size: 12px;
    }

    .timeline-item > .timeline-header {
        margin: 0;
        color: #344767;
        border-bottom: 1px solid #f4f4f4;
        padding: 12px 15px;
        font-size: 16px;
        line-height: 1.2;
        font-weight: 500;
    }

    .timeline-item > .timeline-body {
        padding: 12px 15px;
        color: #6c757d;
    }

    /* Animation for refresh icon */
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .spin-animation {
        animation: spin 1s linear infinite;
        display: inline-block;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Title -->
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><i class="fas fa-chart-line me-2"></i>WhatsApp Monitoring</h4>
            <button type="button" class="btn btn-primary" id="refreshStatus">
                <i class="fas fa-sync me-2"></i> Refresh Data
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Status Card -->
        <div class="col-md-4">
            <div class="card status-card h-100">
                <div class="card-header" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
                    <h5 class="card-title text-white mb-0">
                        <i class="material-icons-outlined me-2" style="vertical-align: middle;">signal_cellular_alt</i>
                        Status Koneksi
                    </h5>
                </div>
                <div class="card-body">
                    @if(isset($status['status']['ready']) && $status['status']['ready'])
                        <div class="d-flex align-items-center mb-3">
                            <div class="rounded-circle bg-success p-3 me-3">
                                <i class="fas fa-check text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">WhatsApp Terhubung</h6>
                                <p class="text-muted mb-0 small">Layanan WhatsApp berjalan dengan baik</p>
                            </div>
                        </div>

                        <div class="alert alert-light border-0 shadow-sm">
                            <div class="d-flex align-items-center">
                                <i class="material-icons-outlined text-success me-2">phone_android</i>
                                <div>
                                    <strong>Perangkat Aktif</strong>
                                    <p class="mb-0 small text-muted">Nomor: {{ $status['status']['phone'] ?? 'Tidak diketahui' }}</p>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="d-flex align-items-center mb-3">
                            <div class="rounded-circle bg-danger p-3 me-3">
                                <i class="fas fa-times text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">WhatsApp Tidak Terhubung</h6>
                                <p class="text-muted mb-0 small">Layanan WhatsApp memerlukan autentikasi</p>
                            </div>
                        </div>

                        @if(isset($status['status']['hasQR']) && $status['status']['hasQR'])
                            <div class="alert alert-warning border-0 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="material-icons-outlined text-warning me-2">qr_code_scanner</i>
                                    <div>
                                        <strong>QR Code Tersedia</strong>
                                        <p class="mb-0 small">Silakan scan QR code untuk menghubungkan WhatsApp</p>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-warning" id="showQrCodeBtn">
                                    <i class="material-icons-outlined me-2" style="vertical-align: middle;">qr_code</i>
                                    Scan QR Code
                                </button>
                            </div>
                        @endif
                    @endif

                    <div class="d-grid gap-2 mt-4">
                        <button class="btn btn-primary" id="sendTestMessage">
                            <i class="material-icons-outlined me-2" style="vertical-align: middle;">send</i>
                            Kirim Pesan Test
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Stats Card -->
        <div class="col-md-4">
            <div class="card status-card h-100">
                <div class="card-header" style="background: linear-gradient(90deg, #11998e, #38ef7d); color: white;">
                    <h5 class="card-title text-white mb-0">
                        <i class="material-icons-outlined me-2" style="vertical-align: middle;">analytics</i>
                        Statistik Pesan
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm text-center h-100">
                                <div class="card-body">
                                    <div class="rounded-circle bg-primary p-3 mx-auto mb-3" style="width: 60px; height: 60px;">
                                        <i class="fas fa-envelope text-white" style="font-size: 24px;"></i>
                                    </div>
                                    <h3 class="mb-0">{{ $messageStats['total_sent'] ?? 0 }}</h3>
                                    <p class="text-muted small mb-0">Total Pesan</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm text-center h-100">
                                <div class="card-body">
                                    <div class="rounded-circle bg-success p-3 mx-auto mb-3" style="width: 60px; height: 60px;">
                                        <i class="fas fa-check text-white" style="font-size: 24px;"></i>
                                    </div>
                                    <h3 class="mb-0">{{ $messageStats['success_count'] ?? 0 }}</h3>
                                    <p class="text-muted small mb-0">Sukses</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm text-center h-100">
                                <div class="card-body">
                                    <div class="rounded-circle bg-danger p-3 mx-auto mb-3" style="width: 60px; height: 60px;">
                                        <i class="fas fa-times text-white" style="font-size: 24px;"></i>
                                    </div>
                                    <h3 class="mb-0">{{ $messageStats['error_count'] ?? 0 }}</h3>
                                    <p class="text-muted small mb-0">Error</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if(isset($messageStats['templates']) && count($messageStats['templates']) > 0)
                        <div class="mt-4">
                            <h6 class="fw-bold mb-3">Pesan per Template</h6>
                            <div class="list-group shadow-sm">
                                @foreach($messageStats['templates'] as $template => $count)
                                    <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-file-alt text-primary me-2"></i>
                                            {{ $template }}
                                        </div>
                                        <span class="badge bg-primary rounded-pill">{{ $count }}</span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Errors Card -->
        <div class="col-md-4">
            <div class="card status-card h-100">
                <div class="card-header" style="background: linear-gradient(90deg, #FF416C, #FF4B2B); color: white;">
                    <h5 class="card-title text-white mb-0">
                        <i class="material-icons-outlined me-2" style="vertical-align: middle;">error_outline</i>
                        Error Terbaru (24 Jam)
                    </h5>
                </div>
                <div class="card-body">
                    @if(is_array($recentErrors) && count($recentErrors) > 0)
                        <div class="list-group shadow-sm mb-3">
                            @foreach($recentErrors as $error)
                                <div class="list-group-item border-0 mb-2 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="badge bg-danger">
                                            <i class="fas fa-exclamation-circle me-1"></i> Error
                                        </span>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            @if(isset($error['timestamp']))
                                                @php
                                                    $time = new DateTime($error['timestamp']);
                                                    $now = new DateTime();
                                                    $interval = $time->diff($now);

                                                    if ($interval->days > 0) {
                                                        $timeAgo = "{$interval->days} hari yang lalu";
                                                    } elseif ($interval->h > 0) {
                                                        $timeAgo = "{$interval->h} jam yang lalu";
                                                    } elseif ($interval->i > 0) {
                                                        $timeAgo = "{$interval->i} menit yang lalu";
                                                    } else {
                                                        $timeAgo = "Baru saja";
                                                    }
                                                @endphp
                                                <span title="{{ $error['timestamp'] }}">{{ $timeAgo }}</span>
                                            @else
                                                Unknown
                                            @endif
                                        </small>
                                    </div>
                                    <h6 class="mb-2 fw-bold">{{ $error['message'] ?? 'Unknown Error' }}</h6>
                                    @if(isset($error['error']['message']))
                                        <p class="mb-1 text-muted">{{ $error['error']['message'] }}</p>
                                        @if(isset($error['error']['stack']))
                                            <button class="btn btn-sm btn-outline-secondary mt-2 view-stack" data-stack="{{ htmlspecialchars(json_encode($error['error']['stack'])) }}">
                                                <i class="fas fa-code me-1"></i> Lihat Stack Trace
                                            </button>
                                        @endif
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="rounded-circle bg-success p-4 mx-auto mb-3" style="width: 80px; height: 80px;">
                                <i class="fas fa-check text-white" style="font-size: 36px;"></i>
                            </div>
                            <h5 class="mb-2">Tidak Ada Error</h5>
                            <p class="text-muted mb-0">Tidak ada error yang terdeteksi dalam 24 jam terakhir</p>
                        </div>
                    @endif

                    <div class="d-grid gap-2 mt-4">
                        <a href="{{ route('whatsapp.logs') }}" class="btn btn-outline-primary">
                            <i class="fas fa-list-alt me-2"></i>
                            Lihat Semua Log
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stack Trace Modal -->
<div class="modal fade" id="stackModal" tabindex="-1" aria-labelledby="stackModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stackModalLabel">
                    <i class="material-icons-outlined me-2 text-danger" style="vertical-align: middle;">code</i>
                    Stack Trace
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre id="stackContent" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- QR Code Modal -->
<div class="modal fade" id="qrCodeModal" tabindex="-1" aria-labelledby="qrCodeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
                <h5 class="modal-title text-white" id="qrCodeModalLabel">
                    <i class="material-icons-outlined me-2" style="vertical-align: middle;">qr_code_scanner</i>
                    Scan QR Code WhatsApp
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-4">
                <div id="qrcode-container" class="mx-auto mb-3" style="max-width: 300px;">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <p class="mt-2">Memuat QR Code...</p>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <span>Buka WhatsApp di ponsel Anda, ketuk <strong>Menu</strong> atau <strong>Setelan</strong> dan pilih <strong>Perangkat Tertaut</strong>. Arahkan ponsel Anda ke layar ini untuk memindai kode.</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="refreshQrCodeBtn">
                    <i class="fas fa-sync me-1"></i> Refresh QR Code
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script>
    $(function() {
        // Konfigurasi toastr
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut",
            "preventDuplicates": true,
            "newestOnTop": true,
            "escapeHtml": false,
            "tapToDismiss": false,
            "rtl": false,
            "iconClasses": {
                error: 'toast-error',
                info: 'toast-info',
                success: 'toast-success',
                warning: 'toast-warning'
            }
        };

        // Override toastr's default CSS with our custom CSS
        toastr.options.toastClass = 'toast';
        toastr.options.titleClass = 'toast-title';
        toastr.options.messageClass = 'toast-message';

        // Refresh status button
        $('#refreshStatus').click(function() {
            $(this).html('<i class="fas fa-sync fa-spin me-2"></i> Memuat...');
            location.reload();
        });

        // Send test message button
        $('#sendTestMessage').click(function() {
            $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Mengirim...');

            $.ajax({
                url: '{{ route("whatsapp.test.notification") }}',
                type: 'POST',
                dataType: 'json',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                beforeSend: function(xhr, settings) {
                    // Pastikan URL menggunakan HTTPS
                    if (settings.url.indexOf('http:') === 0) {
                        settings.url = settings.url.replace('http:', 'https:');
                    }
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success('Pesan test berhasil dikirim');
                    } else {
                        toastr.error('Gagal mengirim pesan test: ' + response.error);
                    }
                },
                error: function(xhr) {
                    toastr.error('Terjadi kesalahan saat mengirim pesan test');
                },
                complete: function() {
                    $('#sendTestMessage').prop('disabled', false).html('<i class="material-icons-outlined me-2" style="vertical-align: middle;">send</i> Kirim Pesan Test');
                }
            });
        });

        // View stack trace
        $('.view-stack').click(function() {
            var stack = $(this).data('stack');
            $('#stackContent').text(stack);
            var stackModal = new bootstrap.Modal(document.getElementById('stackModal'));
            stackModal.show();
        });

        // QR Code Modal
        $('#showQrCodeBtn').click(function() {
            // Show modal
            var qrModal = new bootstrap.Modal(document.getElementById('qrCodeModal'));
            qrModal.show();

            // Load QR code
            loadQRCode();
        });

        // Refresh QR Code
        $('#refreshQrCodeBtn').click(function() {
            $(this).prop('disabled', true).html('<i class="fas fa-sync fa-spin me-1"></i> Memuat...');
            loadQRCode();
        });

        // Function to load QR code
        function loadQRCode() {
            $('#qrcode-container').html(`
                <div class="d-flex justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <p class="mt-2">Memuat QR Code...</p>
            `);

            $.ajax({
                url: '{{ route("whatsapp.qr.api") }}',
                type: 'GET',
                dataType: 'json',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                beforeSend: function(xhr, settings) {
                    // Pastikan URL menggunakan HTTPS
                    if (settings.url.indexOf('http:') === 0) {
                        settings.url = settings.url.replace('http:', 'https:');
                    }
                },
                success: function(response) {
                    if (response.success && response.qrCode) {
                        // Pastikan QR code memiliki prefix yang benar
                        let qrCodeData = response.qrCode;
                        if (qrCodeData && !qrCodeData.startsWith('data:image/png;base64,')) {
                            qrCodeData = 'data:image/png;base64,' + qrCodeData;
                        }

                        // Tampilkan QR code
                        $('#qrcode-container').html(`
                            <img src="${qrCodeData}" alt="QR Code" class="img-fluid" style="max-width: 100%;">
                            <p class="mt-3 text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                QR Code siap dipindai
                            </p>
                        `);

                        // Mulai polling untuk memeriksa status login
                        startLoginCheck();
                    } else {
                        $('#qrcode-container').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                QR Code tidak tersedia. Server WhatsApp mungkin sedang tidak aktif.
                            </div>
                            <button class="btn btn-primary mt-3" id="retryQrCode">
                                <i class="fas fa-sync me-1"></i> Coba Lagi
                            </button>
                        `);

                        $('#retryQrCode').click(function() {
                            loadQRCode();
                        });
                    }
                },
                error: function(xhr) {
                    $('#qrcode-container').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            Gagal memuat QR Code. Silakan coba lagi.
                        </div>
                        <button class="btn btn-primary mt-3" id="retryQrCode">
                            <i class="fas fa-sync me-1"></i> Coba Lagi
                        </button>
                    `);

                    $('#retryQrCode').click(function() {
                        loadQRCode();
                    });
                },
                complete: function() {
                    $('#refreshQrCodeBtn').prop('disabled', false).html('<i class="fas fa-sync me-1"></i> Refresh QR Code');
                }
            });
        }

        // Function to check login status
        function startLoginCheck() {
            // Batas maksimum polling (30 detik * 10 = 5 menit)
            var maxPolls = 10;
            var pollCount = 0;

            var loginCheckInterval = setInterval(function() {
                // Hentikan polling jika sudah mencapai batas maksimum
                if (pollCount >= maxPolls) {
                    clearInterval(loginCheckInterval);
                    $('#qrcode-container').append(`
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Waktu pemindaian habis. Silakan refresh QR code jika diperlukan.
                        </div>
                    `);
                    return;
                }

                pollCount++;

                $.ajax({
                    url: '{{ route("whatsapp.status") }}',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 5000, // Timeout setelah 5 detik
                    success: function(response) {
                        if (response.status && response.status.ready) {
                            clearInterval(loginCheckInterval);

                            // Show success message
                            $('#qrcode-container').html(`
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    WhatsApp berhasil terhubung!
                                </div>
                            `);

                            // Reload page after 2 seconds
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        }
                    },
                    error: function(xhr, status, error) {
                        if (status === 'timeout') {
                            // Jika timeout, hentikan polling
                            clearInterval(loginCheckInterval);
                            $('#qrcode-container').append(`
                                <div class="alert alert-danger mt-3">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    Koneksi timeout. Silakan coba lagi nanti.
                                </div>
                            `);
                        }
                        // Jika error lain, lanjutkan polling
                    }
                });
            }, 30000); // Check every 30 seconds instead of 3 seconds

            // Store interval ID in a data attribute to clear it when modal is closed
            $('#qrCodeModal').data('loginCheckInterval', loginCheckInterval);
        }

        // Clear interval when modal is closed
        $('#qrCodeModal').on('hidden.bs.modal', function() {
            var interval = $(this).data('loginCheckInterval');
            if (interval) {
                clearInterval(interval);
            }
        });

        // Auto refresh setiap 15 menit, tapi hanya jika tab aktif
        var autoRefreshTimeout;

        function setupAutoRefresh() {
            // Bersihkan timeout yang ada
            if (autoRefreshTimeout) {
                clearTimeout(autoRefreshTimeout);
            }

            // Set timeout baru
            autoRefreshTimeout = setTimeout(function() {
                // Periksa apakah tab aktif sebelum refresh
                if (!document.hidden) {
                    location.reload();
                } else {
                    // Jika tab tidak aktif, coba lagi nanti
                    setupAutoRefresh();
                }
            }, 15 * 60 * 1000); // 15 menit
        }

        // Mulai auto refresh
        setupAutoRefresh();

        // Reset auto refresh saat tab menjadi aktif
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                setupAutoRefresh();
            }
        });

        // Tampilkan toast notifikasi saat halaman dimuat jika ada status WhatsApp
        @if(isset($status['status']['ready']))
            @if($status['status']['ready'])
                setTimeout(function() {
                    toastr.success('WhatsApp terhubung dan siap digunakan');
                }, 1000);
            @else
                @if(isset($status['status']['hasQR']) && $status['status']['hasQR'])
                    setTimeout(function() {
                        toastr.warning('WhatsApp tidak terhubung. <a href="javascript:void(0);" class="text-white font-weight-bold show-qr-code" style="text-decoration: underline;">Klik di sini untuk scan QR code</a>');

                        // Tambahkan tombol di bawah toast
                        setTimeout(function() {
                            if ($('.toast-warning').length > 0) {
                                var $button = $('<button class="btn btn-light btn-sm mt-2 d-block show-qr-code">Tampilkan QR Code</button>');
                                $('.toast-warning .toast-message').append($button);

                                // Tambahkan event listener untuk tombol dan link
                                $('.show-qr-code').on('click', function() {
                                    var qrModal = new bootstrap.Modal(document.getElementById('qrCodeModal'));
                                    qrModal.show();
                                    loadQRCode();
                                });
                            }
                        }, 300);
                    }, 1000);
                @else
                    setTimeout(function() {
                        toastr.error('WhatsApp tidak terhubung dan QR code tidak tersedia. Server WhatsApp mungkin sedang tidak aktif.');

                        // Tambahkan tombol refresh di bawah toast
                        setTimeout(function() {
                            if ($('.toast-error').length > 0) {
                                var $button = $('<button class="btn btn-light btn-sm mt-2 d-block" id="refreshQR">Coba Dapatkan QR Code</button>');
                                $('.toast-error .toast-message').append($button);

                                // Tambahkan event listener untuk tombol refresh
                                $('#refreshQR').on('click', function() {
                                    var qrModal = new bootstrap.Modal(document.getElementById('qrCodeModal'));
                                    qrModal.show();
                                    loadQRCode();
                                });
                            }
                        }, 300);
                    }, 1000);
                @endif
            @endif
        @else
            setTimeout(function() {
                toastr.error('Tidak dapat memperoleh status WhatsApp. Silakan periksa koneksi ke server WhatsApp.');
            }, 1000);
        @endif
    });
</script>
@endsection
