@extends('layouts.master')

@section('title', 'WhatsApp Logs')

@section('css')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link href="https://fonts.googleapis.com/css2?family=Material+Icons+Outlined" rel="stylesheet">
<style>
    /* Card Styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 18px 24px;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
    }

    .card-body {
        padding: 24px;
    }

    /* Form Controls */
    .form-control, .form-select {
        border-radius: 8px;
        padding: 10px 15px;
        border: 1px solid #e9ecef;
        font-size: 0.9rem;
        transition: all 0.2s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    }

    .form-control:focus, .form-select:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    }

    .input-group-text {
        border-radius: 8px 0 0 8px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .form-label {
        font-weight: 500;
        font-size: 0.85rem;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    /* Buttons */
    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
        color: white;
        box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
    }

    .btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0b5ed7;
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
    }

    .btn-secondary {
        background-color: #f8f9fa;
        border-color: #e9ecef;
        color: #6c757d;
    }

    .btn-secondary:hover {
        background-color: #e9ecef;
        border-color: #dfe2e5;
        color: #495057;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.875rem;
    }

    /* Table Styling */
    .table-responsive {
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }

    .table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table thead th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-top: none;
        padding: 16px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e9ecef;
    }

    .table tbody td {
        padding: 16px;
        vertical-align: middle;
        border-bottom: 1px solid #f8f9fa;
        font-size: 0.9rem;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    .table tbody tr {
        transition: all 0.2s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
    }

    /* Badges */
    .badge {
        padding: 6px 10px;
        font-weight: 500;
        font-size: 0.75rem;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        line-height: 1;
    }

    .badge i {
        font-size: 12px;
        margin-right: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: baseline;
    }

    .badge.bg-success {
        background-color: #198754 !important;
        box-shadow: 0 2px 5px rgba(25, 135, 84, 0.2);
    }

    .badge.bg-danger {
        background-color: #dc3545 !important;
        box-shadow: 0 2px 5px rgba(220, 53, 69, 0.2);
    }

    .badge.bg-warning {
        background-color: #ffc107 !important;
        color: #212529;
        box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
    }

    .badge.bg-info {
        background-color: #0dcaf0 !important;
        box-shadow: 0 2px 5px rgba(13, 202, 240, 0.2);
    }

    .badge.bg-secondary {
        background-color: #6c757d !important;
        box-shadow: 0 2px 5px rgba(108, 117, 125, 0.2);
    }

    /* Code Display */
    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-size: 0.9rem;
        color: #212529;
        border: 1px solid #e9ecef;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
        max-height: 400px;
        overflow-y: auto;
    }

    /* Modal Styling */
    .modal-content {
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .modal-header {
        border-bottom: 1px solid #e9ecef;
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-radius: 12px 12px 0 0;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        border-top: 1px solid #e9ecef;
        padding: 15px 20px;
        border-radius: 0 0 12px 12px;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
                    <h5 class="card-title text-white mb-0">
                        <i class="material-icons-outlined me-2" style="vertical-align: middle;">receipt_long</i>
                        WhatsApp Logs
                    </h5>
                    <a href="{{ route('whatsapp.monitoring') }}" class="btn btn-light">
                        <i class="material-icons-outlined me-2" style="vertical-align: middle;">arrow_back</i>
                        Kembali ke Monitoring
                    </a>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('whatsapp.logs') }}" class="mb-0">
                        <div class="d-flex align-items-center mb-3">
                            <i class="material-icons-outlined me-2 text-primary" style="font-size: 24px;">filter_list</i>
                            <h6 class="mb-0 fw-bold">Filter Log</h6>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="lines" class="form-label">Jumlah Baris</label>
                                <select name="lines" id="lines" class="form-select">
                                    <option value="50" {{ $lines == 50 ? 'selected' : '' }}>50 baris</option>
                                    <option value="100" {{ $lines == 100 ? 'selected' : '' }}>100 baris</option>
                                    <option value="200" {{ $lines == 200 ? 'selected' : '' }}>200 baris</option>
                                    <option value="500" {{ $lines == 500 ? 'selected' : '' }}>500 baris</option>
                                </select>
                                <div class="form-text">Jumlah baris log yang akan ditampilkan</div>
                            </div>
                            <div class="col-md-6">
                                <label for="filter" class="form-label">Filter</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="material-icons-outlined" style="font-size: 18px;">search</i></span>
                                    <input type="text" name="filter" id="filter" class="form-control" value="{{ $filter }}" placeholder="Contoh: error, message, etc.">
                                </div>
                                <div class="form-text">Filter berdasarkan kata kunci dalam log</div>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <i class="material-icons-outlined me-2" style="vertical-align: middle;">filter_alt</i>
                                    Terapkan Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="card-title mb-0">
                        <i class="material-icons-outlined me-2" style="vertical-align: middle;">list_alt</i>
                        Daftar Log
                    </h5>
                    <span class="badge bg-primary">{{ count($logs) }} Entri</span>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>TIMESTAMP</th>
                                    <th>LEVEL</th>
                                    <th>MESSAGE</th>
                                    <th>DETAILS</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($logs as $log)
                                    <tr class="{{ isset($log['level']) && $log['level'] === 'error' ? 'table-danger' : '' }}">
                                        <td style="width: 180px;">{{ $log['timestamp'] ?? 'N/A' }}</td>
                                        <td style="width: 100px;">
                                            @if(isset($log['level']))
                                                @if($log['level'] === 'error')
                                                    <span class="badge bg-danger">
                                                        <i class="material-icons-outlined" style="font-size: 12px;">error</i>
                                                        ERROR
                                                    </span>
                                                @elseif($log['level'] === 'warn' || $log['level'] === 'warning')
                                                    <span class="badge bg-warning">
                                                        <i class="material-icons-outlined" style="font-size: 12px;">warning</i>
                                                        WARNING
                                                    </span>
                                                @elseif($log['level'] === 'info')
                                                    <span class="badge bg-info">
                                                        <i class="material-icons-outlined" style="font-size: 12px;">info</i>
                                                        INFO
                                                    </span>
                                                @else
                                                    <span class="badge bg-secondary">
                                                        <i class="material-icons-outlined" style="font-size: 12px;">help</i>
                                                        {{ strtoupper($log['level']) }}
                                                    </span>
                                                @endif
                                            @else
                                                <span class="badge bg-secondary">
                                                    <i class="material-icons-outlined" style="font-size: 12px;">help</i>
                                                    UNKNOWN
                                                </span>
                                            @endif
                                        </td>
                                        <td>{{ $log['message'] ?? ($log['raw'] ?? 'N/A') }}</td>
                                        <td style="width: 120px;" class="text-center">
                                            @if(isset($log['error']))
                                                <button type="button" class="btn btn-sm btn-outline-info view-details"
                                                    data-type="error"
                                                    data-timestamp="{{ $log['timestamp'] ?? 'N/A' }}"
                                                    data-message="{{ $log['message'] ?? 'N/A' }}"
                                                    data-content="{{ htmlspecialchars(json_encode($log['error'], JSON_PRETTY_PRINT)) }}">
                                                    <i class="material-icons-outlined" style="font-size: 16px;">visibility</i>
                                                    Detail
                                                </button>
                                            @elseif(isset($log['data']))
                                                <button type="button" class="btn btn-sm btn-outline-primary view-details"
                                                    data-type="data"
                                                    data-timestamp="{{ $log['timestamp'] ?? 'N/A' }}"
                                                    data-message="{{ $log['message'] ?? 'N/A' }}"
                                                    data-content="{{ htmlspecialchars(json_encode($log['data'], JSON_PRETTY_PRINT)) }}">
                                                    <i class="material-icons-outlined" style="font-size: 16px;">data_object</i>
                                                    Data
                                                </button>
                                            @else
                                                <span class="badge bg-light text-secondary">Tidak ada detail</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center py-5">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="material-icons-outlined text-muted mb-3" style="font-size: 48px;">search_off</i>
                                                <h5 class="text-muted">Tidak Ada Log Ditemukan</h5>
                                                <p class="text-muted">Coba ubah filter atau tambahkan lebih banyak baris</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Single Reusable Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailsModalLabel">
                    <i class="material-icons-outlined me-2 text-primary" style="vertical-align: middle;" id="modalIcon">data_object</i>
                    <span id="modalTitle">Detail Data</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>Timestamp:</strong> <span id="modalTimestamp"></span>
                </div>
                <div class="mb-3">
                    <strong>Message:</strong> <span id="modalMessage"></span>
                </div>
                <div>
                    <strong id="modalContentLabel">Data:</strong>
                    <pre id="modalContent"></pre>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Handle view details button click
        $('.view-details').on('click', function() {
            var type = $(this).data('type');
            var timestamp = $(this).data('timestamp');
            var message = $(this).data('message');
            var content = $(this).data('content');

            // Set modal content based on type
            if (type === 'error') {
                $('#modalIcon').text('error').removeClass('text-primary').addClass('text-danger');
                $('#modalTitle').text('Detail Error');
                $('#modalContentLabel').text('Error Details:');
            } else {
                $('#modalIcon').text('data_object').removeClass('text-danger').addClass('text-primary');
                $('#modalTitle').text('Detail Data');
                $('#modalContentLabel').text('Data:');
            }

            // Set modal content
            $('#modalTimestamp').text(timestamp);
            $('#modalMessage').text(message);
            $('#modalContent').text(content);

            // Show modal
            var detailsModal = new bootstrap.Modal(document.getElementById('detailsModal'));
            detailsModal.show();
        });
    });
</script>
@endsection
