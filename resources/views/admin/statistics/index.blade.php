@extends('layouts.master')

@section('title', 'Statistik Akses Pengguna')

@section('css')
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A==" crossorigin="" />

<!-- Chart.js dan Leaflet JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<style>
    /* Card styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 16px 24px;
        display: flex;
        align-items: center;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .card-body {
        padding: 24px;
    }

    /* Stats card styling */
    .stats-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .stats-card .card-body {
        padding: 20px;
    }

    .stats-card .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .stats-card h2 {
        font-size: 2rem;
        font-weight: 700;
    }

    /* Chart container */
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }

    /* Map container */
    .map-container {
        height: 400px;
        width: 100%;
        border-radius: 8px;
        overflow: hidden;
    }

    /* Filter form */
    .filter-form {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 24px;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .filter-form label {
        font-weight: 500;
        color: #344767;
        margin-bottom: 8px;
    }

    .filter-form .form-select {
        border-radius: 8px;
        border: 1px solid #e9ecef;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .filter-form .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
    }

    /* Table styling */
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-bottom: 1px solid #e9ecef;
        padding: 12px 16px;
    }

    .table tbody td {
        padding: 12px 16px;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    /* Page header */
    .stats-header {
        margin-bottom: 24px;
    }

    .stats-header h4 {
        font-weight: 700;
        color: #344767;
        margin-bottom: 8px;
    }

    .stats-header p {
        color: #6c757d;
        margin-bottom: 0;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card stats-header">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-2">Statistik Akses Pengguna</h4>
                        <p class="text-muted mb-0">Analisis penggunaan aplikasi dan perilaku pengguna</p>
                    </div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="material-icons-outlined">dashboard</i> Dashboard</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Statistik Akses</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title d-flex align-items-center">
                        <i class="material-icons-outlined me-2 text-primary">filter_list</i>
                        Filter Data
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.statistics.index') }}" method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="period" class="form-label">Periode</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light-primary border-0">
                                    <i class="material-icons-outlined text-primary">date_range</i>
                                </span>
                                <select class="form-select" id="period" name="period">
                                    <option value="daily" {{ $period == 'daily' ? 'selected' : '' }}>Harian</option>
                                    <option value="weekly" {{ $period == 'weekly' ? 'selected' : '' }}>Mingguan</option>
                                    <option value="monthly" {{ $period == 'monthly' ? 'selected' : '' }}>Bulanan</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="days" class="form-label">Rentang Waktu</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light-primary border-0">
                                    <i class="material-icons-outlined text-primary">calendar_today</i>
                                </span>
                                <select class="form-select" id="days" name="days">
                                    <option value="7" {{ $days == 7 ? 'selected' : '' }}>7 Hari Terakhir</option>
                                    <option value="30" {{ $days == 30 ? 'selected' : '' }}>30 Hari Terakhir</option>
                                    <option value="90" {{ $days == 90 ? 'selected' : '' }}>90 Hari Terakhir</option>
                                    <option value="365" {{ $days == 365 ? 'selected' : '' }}>1 Tahun Terakhir</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary d-flex align-items-center">
                                <i class="material-icons-outlined me-2">search</i>
                                Terapkan Filter
                            </button>
                            <div class="dropdown ms-2">
                                <button class="btn btn-success dropdown-toggle d-flex align-items-center" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="material-icons-outlined me-2">file_download</i>
                                    Export Data
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                                    <li>
                                        <a class="dropdown-item" href="{{ route('admin.statistics.export-csv', ['type' => 'page_access', 'days' => $days]) }}">
                                            <i class="material-icons-outlined me-2">insert_drive_file</i>
                                            Data Akses Halaman
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ route('admin.statistics.export-csv', ['type' => 'login', 'days' => $days]) }}">
                                            <i class="material-icons-outlined me-2">login</i>
                                            Data Login Pengguna
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="stats-icon bg-light-primary">
                            <i class="material-icons-outlined text-primary">visibility</i>
                        </div>
                        <span class="badge bg-light-primary text-primary">{{ $period == 'daily' ? 'Harian' : ($period == 'weekly' ? 'Mingguan' : 'Bulanan') }}</span>
                    </div>
                    <h5 class="card-title mb-2">Total Kunjungan</h5>
                    <h2 class="mb-2">{{ number_format($pageStats['totalVisits']) }}</h2>
                    <p class="text-muted mb-0 d-flex align-items-center">
                        <i class="material-icons-outlined me-1 small">date_range</i>
                        {{ $days }} Hari Terakhir
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="stats-icon bg-light-success">
                            <i class="material-icons-outlined text-success">link</i>
                        </div>
                        <span class="badge bg-light-success text-success">Halaman</span>
                    </div>
                    <h5 class="card-title mb-2">URL Unik</h5>
                    <h2 class="mb-2">{{ number_format($pageStats['uniqueUrls']) }}</h2>
                    <p class="text-muted mb-0 d-flex align-items-center">
                        <i class="material-icons-outlined me-1 small">web</i>
                        Halaman yang dikunjungi
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="stats-icon bg-light-info">
                            <i class="material-icons-outlined text-info">login</i>
                        </div>
                        <span class="badge bg-light-info text-info">Autentikasi</span>
                    </div>
                    <h5 class="card-title mb-2">Total Login</h5>
                    <h2 class="mb-2">{{ number_format($loginStats['totalLogins']) }}</h2>
                    <p class="text-muted mb-0 d-flex align-items-center">
                        <i class="material-icons-outlined me-1 small">person</i>
                        Masuk ke sistem
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="stats-icon bg-light-warning">
                            <i class="material-icons-outlined text-warning">people</i>
                        </div>
                        <span class="badge bg-light-warning text-warning">Pengguna</span>
                    </div>
                    <h5 class="card-title mb-2">Pengguna Unik</h5>
                    <h2 class="mb-2">{{ number_format($loginStats['uniqueUsers']) }}</h2>
                    <p class="text-muted mb-0 d-flex align-items-center">
                        <i class="material-icons-outlined me-1 small">verified_user</i>
                        Pengguna aktif
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Grafik Kunjungan dan Login -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title d-flex align-items-center">
                        <i class="material-icons-outlined me-2 text-primary">trending_up</i>
                        Kunjungan per {{ $period == 'daily' ? 'Hari' : ($period == 'weekly' ? 'Minggu' : 'Bulan') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <div class="small text-muted">
                            <i class="material-icons-outlined align-middle small">info</i>
                            Data {{ $days }} hari terakhir
                        </div>
                        <div class="text-end">
                            <span class="badge bg-light-primary text-primary">
                                Rata-rata: {{ count($pageStats['visitsByPeriod']) > 0 ? number_format($pageStats['totalVisits'] / count($pageStats['visitsByPeriod']), 1) : '0' }} kunjungan/{{ $period == 'daily' ? 'hari' : ($period == 'weekly' ? 'minggu' : 'bulan') }}
                            </span>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="visitsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title d-flex align-items-center">
                        <i class="material-icons-outlined me-2 text-info">login</i>
                        Login per {{ $period == 'daily' ? 'Hari' : ($period == 'weekly' ? 'Minggu' : 'Bulan') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <div class="small text-muted">
                            <i class="material-icons-outlined align-middle small">info</i>
                            Data {{ $days }} hari terakhir
                        </div>
                        <div class="text-end">
                            <span class="badge bg-light-info text-info">
                                Rata-rata: {{ count($loginStats['loginsByPeriod']) > 0 ? number_format($loginStats['totalLogins'] / count($loginStats['loginsByPeriod']), 1) : '0' }} login/{{ $period == 'daily' ? 'hari' : ($period == 'weekly' ? 'minggu' : 'bulan') }}
                            </span>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="loginsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Grafik Perangkat dan Browser -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title d-flex align-items-center">
                        <i class="material-icons-outlined me-2 text-success">devices</i>
                        Jenis Perangkat
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <div class="small text-muted">
                            <i class="material-icons-outlined align-middle small">info</i>
                            Distribusi penggunaan perangkat
                        </div>
                        @php
                            $topDevice = collect($deviceStats['deviceTypes'])->sortByDesc('total')->first();
                        @endphp
                        @if($topDevice)
                        <div class="text-end">
                            <span class="badge bg-light-success text-success">
                                Terbanyak: {{ $topDevice->device_type }} ({{ $pageStats['totalVisits'] > 0 ? number_format($topDevice->total / $pageStats['totalVisits'] * 100, 1) : '0' }}%)
                            </span>
                        </div>
                        @endif
                    </div>
                    <div class="chart-container">
                        <canvas id="deviceTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title d-flex align-items-center">
                        <i class="material-icons-outlined me-2 text-warning">language</i>
                        Browser
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <div class="small text-muted">
                            <i class="material-icons-outlined align-middle small">info</i>
                            Distribusi penggunaan browser
                        </div>
                        @php
                            $topBrowser = collect($deviceStats['browsers'])->sortByDesc('total')->first();
                        @endphp
                        @if($topBrowser)
                        <div class="text-end">
                            <span class="badge bg-light-warning text-warning">
                                Terbanyak: {{ $topBrowser->browser }} ({{ $pageStats['totalVisits'] > 0 ? number_format($topBrowser->total / $pageStats['totalVisits'] * 100, 1) : '0' }}%)
                            </span>
                        </div>
                        @endif
                    </div>
                    <div class="chart-container">
                        <canvas id="browserChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabel Data -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title d-flex align-items-center">
                        <i class="material-icons-outlined me-2 text-primary">insert_chart</i>
                        Halaman Paling Banyak Dikunjungi
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-centered table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>URL</th>
                                    <th width="120" class="text-end">Kunjungan</th>
                                    <th width="100" class="text-end">Persentase</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($pageStats['topPages'] as $page)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="wh-40 bg-light-primary rounded-3 d-flex align-items-center justify-content-center me-2">
                                                <i class="material-icons-outlined text-primary">web</i>
                                            </div>
                                            <div class="text-truncate" style="max-width: 300px;" title="{{ $page->url }}">
                                                {{ Str::limit($page->url, 40) }}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-end fw-bold">{{ number_format($page->total) }}</td>
                                    <td class="text-end">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <span class="badge bg-light-primary text-primary">
                                                {{ $pageStats['totalVisits'] > 0 ? number_format($page->total / $pageStats['totalVisits'] * 100, 1) : '0' }}%
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title d-flex align-items-center">
                        <i class="material-icons-outlined me-2 text-info">people</i>
                        Pengguna Paling Aktif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-centered table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Pengguna</th>
                                    <th>Email</th>
                                    <th width="100" class="text-end">Login</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($loginStats['activeUsers'] as $user)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="wh-40 bg-light-info rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <span class="text-info fw-bold">{{ strtoupper(substr($user->user->name ?? 'U', 0, 1)) }}</span>
                                            </div>
                                            <div>{{ $user->user->name ?? 'Tidak diketahui' }}</div>
                                        </div>
                                    </td>
                                    <td>{{ $user->user->email ?? 'Tidak diketahui' }}</td>
                                    <td class="text-end">
                                        <span class="badge bg-light-info text-info">
                                            {{ number_format($user->total) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Peta Lokasi -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title d-flex align-items-center">
                        <i class="material-icons-outlined me-2 text-success">public</i>
                        Distribusi Geografis Pengguna
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <div class="small text-muted">
                                <i class="material-icons-outlined align-middle small">info</i>
                                Peta menunjukkan lokasi pengguna berdasarkan alamat IP. Ukuran lingkaran menunjukkan jumlah kunjungan.
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex justify-content-md-end">
                                @php
                                    $topCountry = collect($locationStats['countries'])->sortByDesc('total')->first();
                                @endphp
                                @if($topCountry)
                                <span class="badge bg-light-success text-success">
                                    <i class="material-icons-outlined me-1">flag</i>
                                    Negara terbanyak: {{ $topCountry->country }} ({{ number_format($topCountry->total) }})
                                </span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="map-container" id="userMap"></div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">Negara Teratas</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-centered table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Negara</th>
                                            <th width="100" class="text-end">Kunjungan</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($locationStats['countries']->take(5) as $country)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="material-icons-outlined text-success me-2">flag</i>
                                                    {{ $country->country ?: 'Tidak diketahui' }}
                                                </div>
                                            </td>
                                            <td class="text-end fw-bold">{{ number_format($country->total) }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">Kota Teratas</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-centered table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Kota</th>
                                            <th>Negara</th>
                                            <th width="100" class="text-end">Kunjungan</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($locationStats['cities']->take(5) as $city)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="material-icons-outlined text-success me-2">location_city</i>
                                                    {{ $city->city ?: 'Tidak diketahui' }}
                                                </div>
                                            </td>
                                            <td>{{ $city->country ?: 'Tidak diketahui' }}</td>
                                            <td class="text-end fw-bold">{{ number_format($city->total) }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM Content Loaded');

        // Inisialisasi grafik langsung
        initCharts();
    });

    function initCharts() {
        // Data untuk grafik
        const visitsData = @json($pageStats['visitsByPeriod']);
        const loginsData = @json($loginStats['loginsByPeriod']);
        const deviceTypes = @json($deviceStats['deviceTypes']);
        const browsers = @json($deviceStats['browsers']);
        const mapData = @json($locationStats['mapData']);

        // Warna tema
        const primaryColor = '#0d6efd';
        const primaryLightColor = 'rgba(13, 110, 253, 0.1)';
        const infoColor = '#0dcaf0';
        const infoLightColor = 'rgba(13, 202, 240, 0.1)';
        const successColor = '#198754';
        const successLightColor = 'rgba(25, 135, 84, 0.1)';
        const warningColor = '#ffc107';
        const warningLightColor = 'rgba(255, 193, 7, 0.1)';
        const dangerColor = '#dc3545';
        const dangerLightColor = 'rgba(220, 53, 69, 0.1)';

        // Konfigurasi umum Chart.js
        Chart.defaults.font.family = "'Poppins', sans-serif";
        Chart.defaults.font.size = 12;
        Chart.defaults.color = '#6c757d';
        Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        Chart.defaults.plugins.tooltip.padding = 10;
        Chart.defaults.plugins.tooltip.cornerRadius = 8;
        Chart.defaults.plugins.tooltip.titleFont = { weight: 'bold', size: 13 };

        // Grafik kunjungan
        console.log('Creating visits chart with data:', visitsData);

        try {
            const visitsCanvas = document.getElementById('visitsChart');
            if (!visitsCanvas) {
                console.error('Visits chart canvas not found!');
                return;
            }

            const visitsCtx = visitsCanvas.getContext('2d');
            if (!visitsCtx) {
                console.error('Could not get 2D context for visits chart!');
                return;
            }

            // Ensure we have data to display
            if (!visitsData || visitsData.length === 0) {
                console.warn('No visits data available');
                return;
            }

            console.log('Creating visits chart...');

            new Chart(visitsCtx, {
                type: 'line',
                data: {
                    labels: visitsData.map(item => item.period),
                    datasets: [{
                        label: 'Kunjungan',
                        data: visitsData.map(item => item.total),
                        backgroundColor: primaryLightColor,
                        borderColor: primaryColor,
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: primaryColor,
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)',
                                drawBorder: false
                            },
                            ticks: {
                                precision: 0
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    return 'Periode: ' + tooltipItems[0].label;
                                },
                                label: function(context) {
                                    return 'Kunjungan: ' + context.raw.toLocaleString('id-ID');
                                }
                            }
                        }
                    }
                }
            });

            console.log('Visits chart created successfully');
        } catch (e) {
            console.error('Error creating visits chart:', e);
        }

        // Grafik login
        console.log('Creating logins chart with data:', loginsData);

        try {
            const loginsCanvas = document.getElementById('loginsChart');
            if (!loginsCanvas) {
                console.error('Logins chart canvas not found!');
                return;
            }

            const loginsCtx = loginsCanvas.getContext('2d');
            if (!loginsCtx) {
                console.error('Could not get 2D context for logins chart!');
                return;
            }

            // Ensure we have data to display
            if (!loginsData || loginsData.length === 0) {
                console.warn('No logins data available');
                return;
            }

            console.log('Creating logins chart...');

            new Chart(loginsCtx, {
                type: 'line',
                data: {
                    labels: loginsData.map(item => item.period),
                    datasets: [{
                        label: 'Login',
                        data: loginsData.map(item => item.total),
                        backgroundColor: infoLightColor,
                        borderColor: infoColor,
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: infoColor,
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)',
                                drawBorder: false
                            },
                            ticks: {
                                precision: 0
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    return 'Periode: ' + tooltipItems[0].label;
                                },
                                label: function(context) {
                                    return 'Login: ' + context.raw.toLocaleString('id-ID');
                                }
                            }
                        }
                    }
                }
            });

            console.log('Logins chart created successfully');
        } catch (e) {
            console.error('Error creating logins chart:', e);
        }

        // Grafik jenis perangkat
        console.log('Creating device type chart with data:', deviceTypes);

        try {
            const deviceTypeCanvas = document.getElementById('deviceTypeChart');
            if (!deviceTypeCanvas) {
                console.error('Device type chart canvas not found!');
                return;
            }

            const deviceTypeCtx = deviceTypeCanvas.getContext('2d');
            if (!deviceTypeCtx) {
                console.error('Could not get 2D context for device type chart!');
                return;
            }

            // Ensure we have data to display
            if (!deviceTypes || deviceTypes.length === 0) {
                console.warn('No device type data available');
                return;
            }

            console.log('Creating device type chart...');

            new Chart(deviceTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: deviceTypes.map(item => item.device_type),
                    datasets: [{
                        data: deviceTypes.map(item => item.total),
                        backgroundColor: [
                            successColor,
                            primaryColor,
                            warningColor,
                            infoColor,
                            '#6f42c1'
                        ],
                        borderWidth: 0,
                        hoverOffset: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '65%',
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                    return `${label}: ${value.toLocaleString('id-ID')} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            console.log('Device type chart created successfully');
        } catch (e) {
            console.error('Error creating device type chart:', e);
        }

        // Grafik browser
        console.log('Creating browser chart with data:', browsers);

        try {
            const browserCanvas = document.getElementById('browserChart');
            if (!browserCanvas) {
                console.error('Browser chart canvas not found!');
                return;
            }

            const browserCtx = browserCanvas.getContext('2d');
            if (!browserCtx) {
                console.error('Could not get 2D context for browser chart!');
                return;
            }

            // Ensure we have data to display
            if (!browsers || browsers.length === 0) {
                console.warn('No browser data available');
                return;
            }

            console.log('Creating browser chart...');

            new Chart(browserCtx, {
                type: 'pie',
                data: {
                    labels: browsers.map(item => item.browser),
                    datasets: [{
                        data: browsers.map(item => item.total),
                        backgroundColor: [
                            '#fd7e14',
                            primaryColor,
                            successColor,
                            warningColor,
                            infoColor,
                            '#6f42c1'
                        ],
                        borderWidth: 0,
                        hoverOffset: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                    return `${label}: ${value.toLocaleString('id-ID')} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            console.log('Browser chart created successfully');
        } catch (e) {
            console.error('Error creating browser chart:', e);
        }

        // Peta distribusi pengguna
        console.log('Initializing map with data:', mapData);

        try {
            if (mapData && mapData.length > 0) {
                console.log('Creating map with', mapData.length, 'locations');

                // Pastikan container peta ada dan memiliki dimensi
                const mapContainer = document.getElementById('userMap');
                if (!mapContainer) {
                    console.error('Map container not found!');
                    return;
                }

                // Set height explicitly to ensure map is visible
                mapContainer.style.height = '400px';

                // Initialize map
                const map = L.map('userMap').setView([0, 0], 2);

                // Add tile layer
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                    maxZoom: 19
                }).addTo(map);

                // Add markers
                const bounds = [];
                mapData.forEach(location => {
                    if (location.latitude && location.longitude) {
                        const lat = parseFloat(location.latitude);
                        const lng = parseFloat(location.longitude);

                        if (!isNaN(lat) && !isNaN(lng)) {
                            console.log('Adding marker at', lat, lng, 'for', location.country);

                            // Calculate circle radius based on visit count
                            const radius = Math.sqrt(location.total) * 15000;

                            // Create circle marker
                            L.circle([lat, lng], {
                                color: successColor,
                                fillColor: successColor,
                                fillOpacity: 0.5,
                                radius: radius,
                                weight: 1
                            }).addTo(map).bindPopup(`
                                <div style="text-align: center; font-family: 'Poppins', sans-serif;">
                                    <strong style="font-size: 14px;">${location.country}</strong>
                                    <br>
                                    <span style="color: #198754; font-weight: 600; font-size: 16px;">${location.total.toLocaleString('id-ID')}</span>
                                    <br>
                                    <span style="font-size: 12px; color: #6c757d;">kunjungan</span>
                                </div>
                            `);

                            bounds.push([lat, lng]);
                        }
                    }
                });

                // Fit map to bounds if we have markers
                if (bounds.length > 0) {
                    console.log('Fitting map to bounds with', bounds.length, 'points');
                    map.fitBounds(bounds);
                }

                // Force map to refresh
                setTimeout(() => {
                    map.invalidateSize();
                }, 100);
            } else {
                console.warn('No map data available');
            }
        } catch (e) {
            console.error('Error initializing map:', e);
        }
    }
</script>
@endsection
