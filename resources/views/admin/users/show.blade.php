@extends('layouts.master')

@section('title', 'User Profile')

@section('css')
<style>
  .company-header {
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
  }

  .user-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #344767;
    margin-bottom: 8px;
  }

  .user-email {
    font-size: 0.95rem;
    color: #6c757d;
  }

  .detail-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
  }

  .detail-info p.fw-bold {
    color: #344767;
    font-size: 0.9rem;
  }

  .detail-info p:not(.fw-bold) {
    color: #495057;
    font-size: 0.95rem;
  }

  .user-info-item {
    border-radius: 10px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    margin-bottom: 16px;
  }

  .user-info-item:hover {
    background-color: #f0f4f8;
    transform: translateY(-2px);
  }

  .subscription-card {
    border-radius: 12px;
    overflow: hidden;
  }

  .subscription-card .card-header {
    background: linear-gradient(90deg, #4481eb, #04befe);
    color: white;
    border-bottom: none;
  }

  .activity-card {
    border-radius: 12px;
    overflow: hidden;
  }

  .activity-card .card-header {
    background: linear-gradient(90deg, #43a047, #66bb6a);
    color: white;
    border-bottom: none;
  }
</style>
@endsection

@section('content')
<x-page-title title="User Profile" pagetitle="Admin" />

<!-- User Header -->
<div class="card company-header">
  <div class="row align-items-center">
    <div class="col-lg-8">
      <div class="d-flex align-items-center">
        <div class="product-box me-3 bg-light-primary" style="width: 70px; height: 70px;">
          @if($user->photo)
            <img src="{{ asset("storage/{$user->photo}") }}" alt="{{ $user->name }}" class="rounded-3" style="width: 100%; height: 100%; object-fit: cover;">
          @else
            <i class="material-icons-outlined text-primary" style="font-size: 2rem;">person</i>
          @endif
        </div>
        <div>
          <h2 class="user-name mb-2">{{ $user->name }}</h2>
          <div class="d-flex align-items-center flex-wrap gap-2">
            <span class="badge bg-light text-dark">
              <i class="material-icons-outlined me-1" style="font-size: 0.75rem;">email</i>
              {{ $user->email }}
            </span>
            @if($user->phone)
            <span class="badge bg-light text-dark">
              <i class="material-icons-outlined me-1" style="font-size: 0.75rem;">phone</i>
              {{ $user->phone }}
            </span>
            @endif

            <span class="badge bg-{{ $user->account_type === 'Super Admin' ? 'danger' : ($user->account_type === 'Individu' ? 'info' : ($user->account_type === 'Perusahaan' ? 'primary' : 'warning')) }}">
              <i class="material-icons-outlined me-1" style="font-size: 0.75rem;">badge</i>
              {{ $user->account_type }}
            </span>

            @if($user->email_verified_at)
            <span class="badge bg-success">
              <i class="material-icons-outlined me-1" style="font-size: 0.75rem;">verified</i>
              Email Verified
            </span>
            @else
            <span class="badge bg-warning">
              <i class="material-icons-outlined me-1" style="font-size: 0.75rem;">pending</i>
              Email Pending
            </span>
            @endif
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-4 mt-4 mt-lg-0">
      <div class="text-lg-end">
        <div class="d-flex justify-content-lg-end gap-2">
          <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-primary">
            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">edit</i>
            Edit User
          </a>
          <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">arrow_back</i>
            Back to List
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <!-- User Information -->
  <div class="col-lg-6">
    <div class="card user-info-card mb-4">
      <div class="card-header py-3">
        <h5 class="card-title d-flex align-items-center mb-0">
          <i class="material-icons-outlined me-2 text-primary">info</i>
          User Information
        </h5>
      </div>
      <div class="card-body">
        @if($user->phone)
        <div class="user-info-item p-3">
          <div class="d-flex align-items-start gap-3">
            <div class="detail-icon">
              <i class="material-icons-outlined">phone</i>
            </div>
            <div class="detail-info">
              <p class="fw-bold mb-1">Nomor HP</p>
              <p class="mb-0">{{ $user->phone }}</p>
            </div>
          </div>
        </div>
        @endif

        @if($user->company_name)
        <div class="user-info-item p-3">
          <div class="d-flex align-items-start gap-3">
            <div class="detail-icon">
              <i class="material-icons-outlined">business</i>
            </div>
            <div class="detail-info">
              <p class="fw-bold mb-1">Company Name</p>
              <p class="mb-0">{{ $user->company_name }}</p>
            </div>
          </div>
        </div>
        @endif

        @if($user->npwp)
        <div class="user-info-item p-3">
          <div class="d-flex align-items-start gap-3">
            <div class="detail-icon">
              <i class="material-icons-outlined">credit_card</i>
            </div>
            <div class="detail-info">
              <p class="fw-bold mb-1">NPWP</p>
              <p class="mb-0">{{ $user->npwp }}</p>
            </div>
          </div>
        </div>
        @endif

        <div class="user-info-item p-3">
          <div class="d-flex align-items-start gap-3">
            <div class="detail-icon">
              <i class="material-icons-outlined">schedule</i>
            </div>
            <div class="detail-info">
              <p class="fw-bold mb-1">Last Seen</p>
              <p class="mb-0">
                @if($user->last_seen instanceof \Carbon\Carbon)
                  {{ $user->last_seen->format('d M Y H:i') }}
                  <span class="badge bg-{{ $user->last_seen->gt(now()->subMinutes(5)) ? 'success' : 'secondary' }} ms-2">
                    {{ $user->last_seen->gt(now()->subMinutes(5)) ? 'Online' : 'Offline' }}
                  </span>
                @else
                  -
                @endif
              </p>
            </div>
          </div>
        </div>

        <div class="user-info-item p-3">
          <div class="d-flex align-items-start gap-3">
            <div class="detail-icon">
              <i class="material-icons-outlined">event</i>
            </div>
            <div class="detail-info">
              <p class="fw-bold mb-1">Created At</p>
              <p class="mb-0">{{ $user->created_at ? $user->created_at->format('d M Y H:i') : '-' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Subscription Information -->
  <div class="col-lg-6">
    <div class="card subscription-card mb-4">
      <div class="card-header py-3">
        <h5 class="card-title d-flex align-items-center mb-0">
          <i class="material-icons-outlined me-2">card_membership</i>
          Subscription Information
        </h5>
      </div>
      <div class="card-body">
        @php
          $activeSubscription = $user->userSubscriptions()
            ->where('status', 'active')
            ->where('is_active', 1)
            ->where('end_date', '>=', now())
            ->first();

          $pendingSubscription = $user->userSubscriptions()
            ->where('status', 'pending')
            ->first();
        @endphp

        @if($activeSubscription)
          <div class="alert alert-success d-flex align-items-center" role="alert">
            <i class="material-icons-outlined me-2">check_circle</i>
            <div>
              User has an active subscription
            </div>
          </div>

          <div class="row g-3 mt-2">
            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body">
                  <h6 class="card-subtitle mb-2 text-muted">Package</h6>
                  <h5 class="card-title">{{ $activeSubscription->subscription->name }}</h5>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body">
                  <h6 class="card-subtitle mb-2 text-muted">Status</h6>
                  <h5 class="card-title">
                    <span class="badge bg-success">Active</span>
                  </h5>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body">
                  <h6 class="card-subtitle mb-2 text-muted">Start Date</h6>
                  <h5 class="card-title">{{ \Carbon\Carbon::parse($activeSubscription->start_date)->format('d M Y') }}</h5>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body">
                  <h6 class="card-subtitle mb-2 text-muted">End Date</h6>
                  <h5 class="card-title">{{ \Carbon\Carbon::parse($activeSubscription->end_date)->format('d M Y') }}</h5>
                  <p class="card-text text-success">{{ (int)now()->diffInDays($activeSubscription->end_date) }} hari tersisa</p>
                </div>
              </div>
            </div>

            <div class="col-md-12 mt-2">
              <a href="{{ route('admin.users.edit', $user->id) }}#subscription-management" class="btn btn-sm btn-primary">
                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">add_circle</i>
                Add Days to Subscription
              </a>
            </div>
          </div>
        @elseif($pendingSubscription)
          <div class="alert alert-warning d-flex align-items-center" role="alert">
            <i class="material-icons-outlined me-2">pending</i>
            <div>
              User has a pending subscription (not yet verified)
            </div>
          </div>

          <div class="row g-3 mt-2">
            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body">
                  <h6 class="card-subtitle mb-2 text-muted">Package</h6>
                  <h5 class="card-title">{{ $pendingSubscription->subscription->name }}</h5>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body">
                  <h6 class="card-subtitle mb-2 text-muted">Status</h6>
                  <h5 class="card-title">
                    <span class="badge bg-warning">Pending</span>
                  </h5>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body">
                  <h6 class="card-subtitle mb-2 text-muted">Start Date</h6>
                  <h5 class="card-title">{{ \Carbon\Carbon::parse($pendingSubscription->start_date)->format('d M Y') }}</h5>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body">
                  <h6 class="card-subtitle mb-2 text-muted">End Date</h6>
                  <h5 class="card-title">{{ \Carbon\Carbon::parse($pendingSubscription->end_date)->format('d M Y') }}</h5>
                </div>
              </div>
            </div>

            <div class="col-md-12 mt-2">
              <a href="{{ route('admin.users.edit', $user->id) }}#subscription-management" class="btn btn-sm btn-success">
                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">check_circle</i>
                Verify Subscription
              </a>
            </div>
          </div>
        @else
          <div class="alert alert-warning d-flex align-items-center" role="alert">
            <i class="material-icons-outlined me-2">warning</i>
            <div>
              User does not have an active subscription
            </div>
          </div>

          <div class="text-center py-4">
            <i class="material-icons-outlined" style="font-size: 4rem; color: #dee2e6;">card_membership</i>
            <h5 class="mt-3">No Active Subscription</h5>
            <p class="text-muted">This user currently doesn't have any active or pending subscription package.</p>
          </div>
        @endif
      </div>
    </div>

    <!-- Activity Information -->
    <div class="card activity-card">
      <div class="card-header py-3">
        <h5 class="card-title d-flex align-items-center mb-0">
          <i class="material-icons-outlined me-2">history</i>
          Recent Activity
        </h5>
      </div>
      <div class="card-body">
        @php
          $recentActivities = \App\Models\PageAccessLog::where('user_id', $user->id)
            ->latest()
            ->take(15)
            ->get();
        @endphp

        @if($recentActivities->count() > 0)
          <div class="table-responsive">
            <table class="table table-sm table-hover">
              <thead class="table-light">
                <tr>
                  <th>URL</th>
                  <th>Method</th>
                  <th>Time</th>
                </tr>
              </thead>
              <tbody>
                @foreach($recentActivities as $activity)
                  <tr>
                    <td>
                      <div class="text-truncate" style="max-width: 300px;" title="{{ $activity->url }}">
                        {{ $activity->url }}
                      </div>
                    </td>
                    <td><span class="badge bg-{{ $activity->method == 'GET' ? 'info' : 'primary' }}">{{ $activity->method }}</span></td>
                    <td>{{ $activity->created_at->diffForHumans() }}</td>
                  </tr>
                @endforeach
              </tbody>
            </table>
          </div>

          <div class="text-center mt-3">
            <a href="{{ route('user.activity-logs') }}?user_id={{ $user->id }}" class="btn btn-sm btn-outline-primary">
              <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">visibility</i>
              View All Activity Logs
            </a>
          </div>
        @else
          <div class="text-center py-4">
            <i class="material-icons-outlined" style="font-size: 4rem; color: #dee2e6;">history</i>
            <h5 class="mt-3">No Recent Activity</h5>
            <p class="text-muted">This user doesn't have any recent activity logs.</p>
          </div>
        @endif
      </div>
    </div>
  </div>
</div>
@endsection
