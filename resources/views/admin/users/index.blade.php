@extends('layouts.master')

@section('title', 'User Management')

@section('css')
<style>
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
  }

  .empty-state i {
    font-size: 4rem;
    color: #e9ecef;
    margin-bottom: 1rem;
  }

  .empty-state h5 {
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .empty-state p {
    color: #adb5bd;
    max-width: 400px;
    margin: 0 auto;
  }

  .filter-toggle-icon {
    transition: transform 0.3s ease;
  }

  .collapsed .filter-toggle-icon {
    transform: rotate(180deg);
  }

  .product-box {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
  }

  .product-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .product-info .product-title {
    font-weight: 500;
    color: #344767;
    text-decoration: none;
    display: block;
    margin-bottom: 0.25rem;
  }

  .product-info .product-category {
    font-size: 0.8125rem;
    color: #6c757d;
  }
</style>
@endsection

@section('content')
<x-page-title title="User Management" pagetitle="Admin" />

<!-- Search and Filter Section -->
<div class="card mb-4">
  <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
    <h5 class="card-title mb-0">
      <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
        <span>Filter Users</span>
        <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
      </a>
    </h5>
    <span class="badge bg-white text-primary" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
      <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">people</i>
      {{ number_format($users->total(), 0, ',', '.') }} Users
    </span>
  </div>
  <div class="collapse show" id="filterCollapse">
    <div class="card-body">
      <form method="GET" action="{{ route('admin.users.index') }}">
        <div class="row g-3">
          <!-- Input Pencarian -->
          <div class="col-md-3">
            <label for="search-input" class="form-label">Search</label>
            <div class="input-group">
              <span class="input-group-text"><i class="material-icons-outlined">search</i></span>
              <input type="text" name="search" id="search-input" class="form-control"
                placeholder="Search by name, email or company..." value="{{ request('search') }}">
            </div>
            <div class="form-text">Search by name, email, or company name</div>
          </div>

          <!-- Filter Account Type -->
          <div class="col-md-3">
            <label for="account-type-filter" class="form-label">Account Type</label>
            <select name="account_type" id="account-type-filter" class="form-select">
              <option value="">All Account Types</option>
              <option value="Super Admin" {{ request('account_type') == 'Super Admin' ? 'selected' : '' }}>Super Admin</option>
              <option value="Individu" {{ request('account_type') == 'Individu' ? 'selected' : '' }}>Individu</option>
              <option value="Perusahaan" {{ request('account_type') == 'Perusahaan' ? 'selected' : '' }}>Perusahaan</option>
              <option value="Bank & Asuransi" {{ request('account_type') == 'Bank & Asuransi' ? 'selected' : '' }}>Bank & Asuransi</option>
            </select>
          </div>

          <!-- Filter Status -->
          <div class="col-md-3">
            <label for="status-filter" class="form-label">User Status</label>
            <select name="status" id="status-filter" class="form-select">
              <option value="">All Statuses</option>
              <option value="online" {{ request('status') == 'online' ? 'selected' : '' }}>Online</option>
              <option value="offline" {{ request('status') == 'offline' ? 'selected' : '' }}>Offline</option>
            </select>
          </div>

          <!-- Filter Subscription Status -->
          <div class="col-md-3">
            <label for="subscription-filter" class="form-label">Subscription</label>
            <select name="subscription_status" id="subscription-filter" class="form-select">
              <option value="">All Subscriptions</option>
              <option value="active" {{ request('subscription_status') == 'active' ? 'selected' : '' }}>Active</option>
              <option value="inactive" {{ request('subscription_status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
            </select>
          </div>

          <!-- Buttons -->
          <div class="col-12 mt-4">
            <button type="submit" class="btn btn-primary">
              <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">search</i>
              Apply Filters
            </button>
            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary ms-2">
              <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">refresh</i>
              Reset
            </a>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Users Table Section -->
<div class="card">
  <div class="card-header d-flex align-items-center justify-content-between">
    <h5 class="card-title mb-0">
      <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">people</i>
      User List
    </h5>
    <div>
      @if(session('success'))
        <div class="alert alert-success py-2 px-3 mb-0">{{ session('success') }}</div>
      @endif
      @if(session('error'))
        <div class="alert alert-danger py-2 px-3 mb-0">{{ session('error') }}</div>
      @endif
    </div>
  </div>
  <div class="card-body p-0">
    @if($users->count() > 0)
      <div class="table-responsive">
        <table class="table align-middle mb-0">
          <thead class="bg-light">
            <tr>
              <th>ID</th>
              <th>User</th>
              <th>Account Type</th>
              <th>Subscription</th>
              <th>Status</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            @foreach($users as $user)
            <tr>
              <td class="fw-semibold text-primary">
                #{{ $user->id }}
              </td>
              <td>
                <div class="d-flex align-items-center gap-3">
                  <div class="product-box bg-light-primary">
                    @if($user->photo)
                      <img src="{{ asset("storage/{$user->photo}") }}" alt="{{ $user->name }}" class="rounded-3">
                    @else
                      <i class="material-icons-outlined text-primary">person</i>
                    @endif
                  </div>
                  <div class="product-info">
                    <a href="{{ route('admin.users.show', $user->id) }}" class="product-title">
                      {{ $user->name }}
                    </a>
                    <p class="mb-0 product-category">
                      {{ $user->email }}
                      @if($user->phone)
                        <span class="d-block">
                          <i class="material-icons-outlined" style="font-size: 0.75rem; vertical-align: middle;">phone</i>
                          {{ $user->phone }}
                        </span>
                      @endif
                      @if($user->company_name)
                        <span class="d-block">{{ $user->company_name }}</span>
                      @endif
                    </p>
                  </div>
                </div>
              </td>
              <td>
                <span class="badge bg-{{ $user->account_type === 'Super Admin' ? 'danger' : ($user->account_type === 'Individu' ? 'info' : ($user->account_type === 'Perusahaan' ? 'primary' : 'warning')) }}">{{ $user->account_type }}</span>
              </td>
              <td>
                @php
                  $activeSubscription = $user->userSubscriptions()
                    ->where('status', 'active')
                    ->where('is_active', 1)
                    ->where('end_date', '>=', now())
                    ->first();
                @endphp
                @if($activeSubscription)
                  <span class="badge bg-success">{{ $activeSubscription->subscription->name }}</span>
                  <small class="d-block mt-1">{{ (int)now()->diffInDays($activeSubscription->end_date) }} hari</small>
                @else
                  <span class="badge bg-danger">Tidak Aktif</span>
                @endif
              </td>
              <td>
                @if($user->last_seen instanceof \Carbon\Carbon)
                  @if($user->last_seen->gt(now()->subMinutes(5)))
                    <span class="badge bg-success">Online</span>
                  @else
                    <span class="badge bg-secondary">Offline</span>
                    <small class="d-block mt-1">{{ $user->last_seen->diffForHumans(['parts' => 1]) }}</small>
                  @endif
                @else
                  <span class="badge bg-secondary">Offline</span>
                @endif
              </td>
              <td>
                <span>{{ $user->created_at ? $user->created_at->format('d M Y') : '-' }}</span>
                @if($user->email_verified_at)
                  <span class="badge bg-success d-block mt-1">Verified</span>
                @else
                  <span class="badge bg-warning d-block mt-1">Pending</span>
                @endif
              </td>
              <td>
                <div class="d-flex gap-2">
                  <a href="{{ route('admin.users.show', $user->id) }}" class="btn btn-sm btn-info d-flex align-items-center">
                    <i class="material-icons-outlined" style="font-size: 1rem;">visibility</i>
                  </a>
                  <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-sm btn-primary d-flex align-items-center">
                    <i class="material-icons-outlined" style="font-size: 1rem;">edit</i>
                  </a>
                  <form action="{{ route('admin.users.destroy', $user->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this user?');">
                    @csrf
                    @method('DELETE')
                    <button class="btn btn-sm btn-danger d-flex align-items-center" type="submit">
                      <i class="material-icons-outlined" style="font-size: 1rem;">delete</i>
                    </button>
                  </form>
                </div>
              </td>
            </tr>
            @endforeach
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="d-flex justify-content-between align-items-center p-3 border-top">
        <div class="text-muted small">
          Menampilkan {{ $users->firstItem() ?? 0 }} - {{ $users->lastItem() ?? 0 }} dari {{ $users->total() }} users
        </div>
        <div>
          {{ $users->links('vendor.pagination.simple') }}
        </div>
      </div>
    @else
      <div class="empty-state">
        <i class="material-icons-outlined">search_off</i>
        <h5>No Users Found</h5>
        <p>Try adjusting your search or filter criteria</p>
      </div>
    @endif
  </div>
</div>
@endsection

@section('scripts')
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Toggle filter collapse icon
    const filterCollapse = document.getElementById('filterCollapse');
    const filterToggleIcon = document.querySelector('.filter-toggle-icon');

    filterCollapse.addEventListener('hidden.bs.collapse', function () {
      filterToggleIcon.textContent = 'expand_more';
    });

    filterCollapse.addEventListener('shown.bs.collapse', function () {
      filterToggleIcon.textContent = 'expand_less';
    });
  });
</script>
@endsection
