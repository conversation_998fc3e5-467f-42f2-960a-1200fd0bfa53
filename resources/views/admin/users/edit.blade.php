@extends('layouts.master')

@section('title', 'Edit User')

@section('css')
<style>
  .form-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
  }

  .form-card .card-header {
    background: linear-gradient(90deg, #4481eb, #04befe);
    color: white;
    border-bottom: none;
    padding: 16px 24px;
  }

  .form-control:focus {
    border-color: #4481eb;
    box-shadow: 0 0 0 0.25rem rgba(68, 129, 235, 0.25);
  }

  .form-label {
    font-weight: 500;
    color: #344767;
  }

  .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
  }

  .form-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
  }

  .form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #344767;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
  }
</style>
@endsection

@section('content')
<x-page-title title="Edit User" pagetitle="Admin" />

<div class="row justify-content-center">
  <div class="col-lg-8">
    <div class="card form-card">
      <div class="card-header py-3">
        <h5 class="card-title d-flex align-items-center mb-0">
          <i class="material-icons-outlined me-2">edit</i>
          Edit User: {{ $user->name }}
        </h5>
      </div>
      <div class="card-body p-4">
        @if ($errors->any())
          <div class="alert alert-danger d-flex align-items-center" role="alert">
            <i class="material-icons-outlined me-2">error</i>
            <div>
              <strong>There were some problems with your input.</strong>
              <ul class="mb-0 mt-1">
                @foreach ($errors->all() as $error)
                  <li>{{ $error }}</li>
                @endforeach
              </ul>
            </div>
          </div>
        @endif

        <form action="{{ route('admin.users.update', $user->id) }}" method="POST">
          @csrf
          @method('PUT')

          <!-- Basic Information Section -->
          <div class="form-section">
            <div class="form-section-title">
              <i class="material-icons-outlined me-2 text-primary">person</i>
              Basic Information
            </div>

            <div class="row g-3">
              <div class="col-md-6">
                <label for="name" class="form-label">Name</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="material-icons-outlined">person</i></span>
                  <input type="text" class="form-control" id="name" name="name" value="{{ old('name', $user->name) }}" required>
                </div>
              </div>

              <div class="col-md-6">
                <label for="email" class="form-label">Email</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="material-icons-outlined">email</i></span>
                  <input type="email" class="form-control" id="email" name="email" value="{{ old('email', $user->email) }}" required>
                </div>
                <div class="form-check mt-2">
                  <input class="form-check-input" type="checkbox" id="verify_email" name="verify_email" value="1" {{ $user->email_verified_at ? 'checked disabled' : '' }}>
                  <label class="form-check-label" for="verify_email">
                    <span class="{{ $user->email_verified_at ? 'text-success' : 'text-warning' }} fw-bold">
                      {{ $user->email_verified_at ? 'Email Verified' : 'Verify Email Manually' }}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Role & Account Section -->
          <div class="form-section">
            <div class="form-section-title">
              <i class="material-icons-outlined me-2 text-primary">admin_panel_settings</i>
              Role & Account Settings
            </div>

            <div class="row g-3">
              <div class="col-md-6">
                <label for="account_type" class="form-label">Account Type</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="material-icons-outlined">badge</i></span>
                  <select class="form-select" id="account_type" name="account_type" required>
                    <option value="">-- Select Account Type --</option>
                    <option value="Super Admin" {{ old('account_type', $user->account_type) == 'Super Admin' ? 'selected' : '' }}>Super Admin</option>
                    <option value="Individu" {{ old('account_type', $user->account_type) == 'Individu' ? 'selected' : '' }}>Individu</option>
                    <option value="Perusahaan" {{ old('account_type', $user->account_type) == 'Perusahaan' ? 'selected' : '' }}>Perusahaan</option>
                    <option value="Bank & Asuransi" {{ old('account_type', $user->account_type) == 'Bank & Asuransi' ? 'selected' : '' }}>Bank & Asuransi</option>
                  </select>
                </div>
              </div>

              <div class="col-md-6">
                <label for="phone" class="form-label">Phone</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="material-icons-outlined">phone</i></span>
                  <input type="text" class="form-control" id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                </div>
              </div>
            </div>
          </div>

          <!-- Company Information Section -->
          <div class="form-section">
            <div class="form-section-title">
              <i class="material-icons-outlined me-2 text-primary">business</i>
              Company Information
            </div>

            <div class="row g-3">
              <div class="col-md-6">
                <label for="company_name" class="form-label">Company Name</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="material-icons-outlined">business</i></span>
                  <input type="text" class="form-control" id="company_name" name="company_name" value="{{ old('company_name', $user->company_name) }}">
                </div>
              </div>

              <div class="col-md-6">
                <label for="npwp" class="form-label">NPWP</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="material-icons-outlined">credit_card</i></span>
                  <input type="text" class="form-control" id="npwp" name="npwp" value="{{ old('npwp', $user->npwp) }}">
                </div>
              </div>
            </div>
          </div>

          <!-- Subscription Section -->
          <div class="form-section" id="subscription-management">
            <div class="form-section-title">
              <i class="material-icons-outlined me-2 text-primary">card_membership</i>
              Subscription Management
            </div>

            <div class="row g-3">
              <div class="col-md-12">
                @php
                  $activeSubscription = $user->userSubscriptions()
                    ->where('status', 'active')
                    ->where('is_active', 1)
                    ->where('end_date', '>=', now())
                    ->first();

                  $pendingSubscription = $user->userSubscriptions()
                    ->where('status', 'pending')
                    ->first();
                @endphp

                @if($activeSubscription)
                  <div class="alert alert-info d-flex align-items-center">
                    <i class="material-icons-outlined me-2">info</i>
                    <div>
                      User currently has an active <strong>{{ $activeSubscription->subscription->name }}</strong> subscription
                      until {{ \Carbon\Carbon::parse($activeSubscription->end_date)->format('d M Y') }}
                      ({{ (int)now()->diffInDays($activeSubscription->end_date) }} hari tersisa)
                    </div>
                  </div>

                  <div class="col-md-6">
                    <label for="add_subscription_days" class="form-label">Add Days to Current Subscription</label>
                    <div class="input-group">
                      <span class="input-group-text"><i class="material-icons-outlined">add_circle</i></span>
                      <input type="number" class="form-control" id="add_subscription_days" name="add_subscription_days" min="1" placeholder="Enter number of days">
                      <input type="hidden" name="subscription_id" value="{{ $activeSubscription->id }}">
                    </div>
                    <div class="form-text">Enter the number of days to add to the current subscription</div>
                  </div>
                @elseif($pendingSubscription)
                  <div class="alert alert-warning d-flex align-items-center">
                    <i class="material-icons-outlined me-2">pending</i>
                    <div>
                      User has a pending <strong>{{ $pendingSubscription->subscription->name }}</strong> subscription
                      (not yet verified)
                    </div>
                  </div>

                  <div class="col-md-12 mt-2">
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="verify_subscription" name="verify_subscription" value="1">
                      <label class="form-check-label" for="verify_subscription">
                        <span class="text-success fw-bold">Verify this subscription manually</span>
                      </label>
                      <input type="hidden" name="pending_subscription_id" value="{{ $pendingSubscription->id }}">
                    </div>
                  </div>
                @else
                  <div class="alert alert-warning d-flex align-items-center">
                    <i class="material-icons-outlined me-2">warning</i>
                    <div>User does not have an active or pending subscription</div>
                  </div>
                @endif
              </div>
            </div>
          </div>

          <div class="d-flex justify-content-end gap-2 mt-4">
            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
              <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">arrow_back</i>
              Cancel
            </a>
            <button type="submit" class="btn btn-primary">
              <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">save</i>
              Update User
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
@endsection
