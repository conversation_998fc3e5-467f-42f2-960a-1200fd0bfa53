@extends('layouts.master')

@section('title') Command Monitoring @endsection

@section('css')
<style>
    .command-card {
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .command-card:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .progress {
        height: 10px;
        margin-bottom: 10px;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .status-running {
        background-color: #3498db;
    }

    .status-completed {
        background-color: #2ecc71;
    }

    .status-failed {
        background-color: #e74c3c;
    }

    .status-pending {
        background-color: #f39c12;
    }

    .command-details {
        display: none;
        margin-top: 15px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }

    .command-message {
        font-style: italic;
        color: #666;
    }

    .refresh-icon {
        cursor: pointer;
        transition: transform 0.3s ease;
    }

    .refresh-icon:hover {
        transform: rotate(180deg);
    }

    .spin-animation {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .additional-data {
        font-family: monospace;
        font-size: 0.9rem;
        max-height: 200px;
        overflow-y: auto;
    }

    .time-info {
        font-size: 0.8rem;
        color: #777;
    }

    .status-updates-section {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 15px;
    }

    .status-updates-section h6 {
        margin-bottom: 10px;
    }

    .status-updates-section table {
        font-size: 0.85rem;
    }

    .status-updates-section th {
        background-color: #e9ecef;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-6">
                <h3>Command Monitoring</h3>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item">Admin</li>
                    <li class="breadcrumb-item active">Command Monitoring</li>
                </ol>
            </div>
            <div class="col-sm-6 text-end">
                <button id="refresh-all" class="btn btn-primary">
                    <i class="material-icons-outlined me-1">refresh</i> Refresh All
                </button>
            </div>
        </div>
    </div>

    <div class="row" id="running-commands-container">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">play_circle</i>
                        Running Commands
                    </h5>
                </div>
                <div class="card-body" id="running-commands">
                    <div class="text-center py-5" id="no-running-commands">
                        <i class="material-icons-outlined" style="font-size: 48px; color: #ccc;">hourglass_empty</i>
                        <h5 class="mt-3">No Running Commands</h5>
                        <p class="text-muted">There are no commands currently running.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">history</i>
                        Command History
                    </h5>
                </div>
                <div class="card-body">
                    @if(count($commands) > 0)
                        <div class="row" id="command-history">
                            @foreach($commands as $command)
                                <div class="col-md-6 command-card-container" data-command="{{ $command->command_name }}">
                                    <div class="card command-card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">{{ $command->command_name }}</h6>
                                            <div>
                                                <span class="badge status-badge status-{{ $command->status }}">{{ ucfirst($command->status) }}</span>
                                                <i class="material-icons-outlined ms-2 refresh-icon" data-command="{{ $command->command_name }}">refresh</i>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="progress">
                                                <div class="progress-bar bg-{{ $command->status === 'running' ? 'primary' : ($command->status === 'completed' ? 'success' : ($command->status === 'failed' ? 'danger' : 'warning')) }}"
                                                     role="progressbar"
                                                     style="width: {{ $command->percentage }}%;"
                                                     aria-valuenow="{{ $command->percentage }}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="100">
                                                    {{ $command->percentage }}%
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>{{ $command->current_step }} / {{ $command->total_steps }}</span>
                                                <span>{{ $command->percentage }}%</span>
                                            </div>
                                            <p class="command-message mt-2">{{ $command->message }}</p>
                                            <div class="time-info">
                                                <div>Started: {{ $command->started_at ? $command->started_at->format('Y-m-d H:i:s') : 'N/A' }}</div>
                                                @if($command->finished_at)
                                                    <div>Finished: {{ $command->finished_at->format('Y-m-d H:i:s') }}</div>
                                                @endif
                                                <div>Last Updated: {{ $command->updated_at->format('Y-m-d H:i:s') }}</div>
                                            </div>
                                            @if(isset($command->additional_data['status_updates_count']) && $command->additional_data['status_updates_count'] > 0)
                                                <div class="alert alert-info mt-2 p-2">
                                                    <small><i class="material-icons-outlined me-1" style="font-size: 14px; vertical-align: middle;">update</i> {{ $command->additional_data['status_updates_count'] }} status paket updates</small>
                                                </div>
                                            @endif
                                            <button class="btn btn-sm btn-outline-primary mt-2 toggle-details">Show Details</button>
                                            <div class="command-details">
                                                <h6>Additional Data:</h6>
                                                @if(isset($command->additional_data['valid_data']))
                                                    <div class="status-updates-section mb-3">
                                                        <h6 class="text-primary">Data Validation Summary</h6>
                                                        <div class="table-responsive">
                                                            <table class="table table-sm table-bordered">
                                                                <thead class="table-light">
                                                                    <tr>
                                                                        <th>Metric</th>
                                                                        <th>Count</th>
                                                                        <th>Percentage</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Total Data</td>
                                                                        <td>{{ $command->additional_data['total_data'] }}</td>
                                                                        <td>100%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Valid Data (All Fields)</td>
                                                                        <td>{{ $command->additional_data['valid_data'] }}</td>
                                                                        <td>{{ round(($command->additional_data['valid_data'] / $command->additional_data['total_data']) * 100, 2) }}%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Missing Nama Produk</td>
                                                                        <td>{{ $command->additional_data['missing_nama_produk'] }}</td>
                                                                        <td>{{ round(($command->additional_data['missing_nama_produk'] / $command->additional_data['total_data']) * 100, 2) }}%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Missing Nama Manufaktur</td>
                                                                        <td>{{ $command->additional_data['missing_nama_manufaktur'] }}</td>
                                                                        <td>{{ round(($command->additional_data['missing_nama_manufaktur'] / $command->additional_data['total_data']) * 100, 2) }}%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Missing Nama Penyedia</td>
                                                                        <td>{{ $command->additional_data['missing_nama_penyedia'] }}</td>
                                                                        <td>{{ round(($command->additional_data['missing_nama_penyedia'] / $command->additional_data['total_data']) * 100, 2) }}%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Missing Nama Pelaksana</td>
                                                                        <td>{{ $command->additional_data['missing_nama_pelaksana'] }}</td>
                                                                        <td>{{ round(($command->additional_data['missing_nama_pelaksana'] / $command->additional_data['total_data']) * 100, 2) }}%</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                @endif

                                                @if(isset($command->additional_data['status_updates_count']) && $command->additional_data['status_updates_count'] > 0)
                                                    <div class="status-updates-section mb-3">
                                                        <h6 class="text-primary">Status Updates ({{ $command->additional_data['status_updates_count'] }})</h6>
                                                        <div class="table-responsive">
                                                            <table class="table table-sm table-bordered">
                                                                <thead class="table-light">
                                                                    <tr>
                                                                        <th>Nomor Paket</th>
                                                                        <th>Status Lama</th>
                                                                        <th>Status Baru</th>
                                                                        <th>Waktu Update</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    @foreach($command->additional_data['status_update_details'] as $update)
                                                                        <tr>
                                                                            <td>{{ $update['nomor_paket'] }}</td>
                                                                            <td>{{ $update['old_status'] }}</td>
                                                                            <td>{{ $update['new_status'] }}</td>
                                                                            <td>{{ $update['updated_at'] }}</td>
                                                                        </tr>
                                                                    @endforeach
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                @endif
                                                <pre class="additional-data">{{ json_encode($command->additional_data, JSON_PRETTY_PRINT) }}</pre>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="material-icons-outlined" style="font-size: 48px; color: #ccc;">history</i>
                            <h5 class="mt-3">No Command History</h5>
                            <p class="text-muted">There is no command history available.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Toggle command details
        $('.toggle-details').on('click', function() {
            const details = $(this).siblings('.command-details');
            details.slideToggle();
            $(this).text(details.is(':visible') ? 'Hide Details' : 'Show Details');
        });

        // Refresh individual command
        $('.refresh-icon').on('click', function() {
            const commandName = $(this).data('command');
            const icon = $(this);

            icon.addClass('spin-animation');

            $.ajax({
                url: "{{ route('admin.commands.progress') }}",
                method: "GET",
                data: { command_name: commandName },
                success: function(data) {
                    updateCommandCard(data);
                },
                error: function(xhr) {
                    console.error(xhr.responseText);
                    alert("Failed to refresh command status.");
                },
                complete: function() {
                    setTimeout(() => {
                        icon.removeClass('spin-animation');
                    }, 500);
                }
            });
        });

        // Refresh all commands
        $('#refresh-all').on('click', function() {
            const btn = $(this);
            const icon = btn.find('i');

            icon.addClass('spin-animation');
            btn.prop('disabled', true);

            $.ajax({
                url: "{{ route('admin.commands.all-progress') }}",
                method: "GET",
                success: function(data) {
                    // Clear existing command cards
                    $('#command-history').empty();

                    // Add updated command cards
                    data.forEach(function(command) {
                        addCommandCard(command);
                    });

                    // Check for running commands
                    checkRunningCommands();
                },
                error: function(xhr) {
                    console.error(xhr.responseText);
                    alert("Failed to refresh commands.");
                },
                complete: function() {
                    setTimeout(() => {
                        icon.removeClass('spin-animation');
                        btn.prop('disabled', false);
                    }, 500);
                }
            });
        });

        // Check for running commands on page load
        checkRunningCommands();

        // Set interval to check for running commands every 10 seconds
        setInterval(checkRunningCommands, 10000);

        function checkRunningCommands() {
            $.ajax({
                url: "{{ route('admin.commands.running') }}",
                method: "GET",
                success: function(data) {
                    // Clear existing running commands
                    $('#running-commands').empty();

                    if (data.length > 0) {
                        $('#no-running-commands').hide();

                        // Add running command cards
                        const runningCommandsHtml = $('<div class="row"></div>');

                        data.forEach(function(command) {
                            const commandCard = $(`
                                <div class="col-md-6 mb-3">
                                    <div class="card command-card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">${command.command_name}</h6>
                                            <span class="badge status-badge status-running">Running</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="progress">
                                                <div class="progress-bar bg-primary progress-bar-striped progress-bar-animated"
                                                     role="progressbar"
                                                     style="width: ${command.percentage}%;"
                                                     aria-valuenow="${command.percentage}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="100">
                                                    ${command.percentage}%
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>${command.current_step} / ${command.total_steps}</span>
                                                <span>${command.percentage}%</span>
                                            </div>
                                            <p class="command-message mt-2">${command.message}</p>
                                            <div class="time-info">
                                                <div>Started: ${command.started_at || 'N/A'}</div>
                                                <div>Last Updated: ${command.updated_at}</div>
                                            </div>
                                            ${command.additional_data && command.additional_data.status_updates_count && command.additional_data.status_updates_count > 0 ? `
                                            <div class="alert alert-info mt-2 p-2">
                                                <small><i class="material-icons-outlined me-1" style="font-size: 14px; vertical-align: middle;">update</i> ${command.additional_data.status_updates_count} status paket updates</small>
                                            </div>
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>
                            `);

                            runningCommandsHtml.append(commandCard);
                        });

                        $('#running-commands').append(runningCommandsHtml);
                    } else {
                        $('#running-commands').append($('#no-running-commands').show());
                    }
                }
            });
        }

        function updateCommandCard(command) {
            const container = $(`.command-card-container[data-command="${command.command_name}"]`);

            if (container.length) {
                container.replaceWith(createCommandCardHtml(command));

                // Reattach event listeners
                $('.toggle-details').off('click').on('click', function() {
                    const details = $(this).siblings('.command-details');
                    details.slideToggle();
                    $(this).text(details.is(':visible') ? 'Hide Details' : 'Show Details');
                });

                $('.refresh-icon').off('click').on('click', function() {
                    const commandName = $(this).data('command');
                    const icon = $(this);

                    icon.addClass('spin-animation');

                    $.ajax({
                        url: "{{ route('admin.commands.progress') }}",
                        method: "GET",
                        data: { command_name: commandName },
                        success: function(data) {
                            updateCommandCard(data);
                        },
                        error: function(xhr) {
                            console.error(xhr.responseText);
                            alert("Failed to refresh command status.");
                        },
                        complete: function() {
                            setTimeout(() => {
                                icon.removeClass('spin-animation');
                            }, 500);
                        }
                    });
                });
            }
        }

        function addCommandCard(command) {
            $('#command-history').append(createCommandCardHtml(command));

            // Reattach event listeners
            $('.toggle-details').off('click').on('click', function() {
                const details = $(this).siblings('.command-details');
                details.slideToggle();
                $(this).text(details.is(':visible') ? 'Hide Details' : 'Show Details');
            });

            $('.refresh-icon').off('click').on('click', function() {
                const commandName = $(this).data('command');
                const icon = $(this);

                icon.addClass('spin-animation');

                $.ajax({
                    url: "{{ route('admin.commands.progress') }}",
                    method: "GET",
                    data: { command_name: commandName },
                    success: function(data) {
                        updateCommandCard(data);
                    },
                    error: function(xhr) {
                        console.error(xhr.responseText);
                        alert("Failed to refresh command status.");
                    },
                    complete: function() {
                        setTimeout(() => {
                            icon.removeClass('spin-animation');
                        }, 500);
                    }
                });
            });
        }

        function createCommandCardHtml(command) {
            const statusClass = command.status === 'running' ? 'primary' :
                              (command.status === 'completed' ? 'success' :
                              (command.status === 'failed' ? 'danger' : 'warning'));

            return `
                <div class="col-md-6 command-card-container" data-command="${command.command_name}">
                    <div class="card command-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">${command.command_name}</h6>
                            <div>
                                <span class="badge status-badge status-${command.status}">${command.status.charAt(0).toUpperCase() + command.status.slice(1)}</span>
                                <i class="material-icons-outlined ms-2 refresh-icon" data-command="${command.command_name}">refresh</i>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="progress">
                                <div class="progress-bar bg-${statusClass} ${command.status === 'running' ? 'progress-bar-striped progress-bar-animated' : ''}"
                                     role="progressbar"
                                     style="width: ${command.percentage}%;"
                                     aria-valuenow="${command.percentage}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                    ${command.percentage}%
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>${command.current_step} / ${command.total_steps}</span>
                                <span>${command.percentage}%</span>
                            </div>
                            <p class="command-message mt-2">${command.message}</p>
                            <div class="time-info">
                                <div>Started: ${command.started_at || 'N/A'}</div>
                                ${command.finished_at ? `<div>Finished: ${command.finished_at}</div>` : ''}
                                <div>Last Updated: ${command.updated_at}</div>
                            </div>
                            <button class="btn btn-sm btn-outline-primary mt-2 toggle-details">Show Details</button>
                            <div class="command-details">
                                <h6>Additional Data:</h6>
                                ${command.additional_data && command.additional_data.valid_data ? `
                                <div class="status-updates-section mb-3">
                                    <h6 class="text-primary">Data Validation Summary</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Metric</th>
                                                    <th>Count</th>
                                                    <th>Percentage</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Total Data</td>
                                                    <td>${command.additional_data.total_data}</td>
                                                    <td>100%</td>
                                                </tr>
                                                <tr>
                                                    <td>Valid Data (All Fields)</td>
                                                    <td>${command.additional_data.valid_data}</td>
                                                    <td>${Math.round((command.additional_data.valid_data / command.additional_data.total_data) * 100 * 100) / 100}%</td>
                                                </tr>
                                                <tr>
                                                    <td>Missing Nama Produk</td>
                                                    <td>${command.additional_data.missing_nama_produk}</td>
                                                    <td>${Math.round((command.additional_data.missing_nama_produk / command.additional_data.total_data) * 100 * 100) / 100}%</td>
                                                </tr>
                                                <tr>
                                                    <td>Missing Nama Manufaktur</td>
                                                    <td>${command.additional_data.missing_nama_manufaktur}</td>
                                                    <td>${Math.round((command.additional_data.missing_nama_manufaktur / command.additional_data.total_data) * 100 * 100) / 100}%</td>
                                                </tr>
                                                <tr>
                                                    <td>Missing Nama Penyedia</td>
                                                    <td>${command.additional_data.missing_nama_penyedia}</td>
                                                    <td>${Math.round((command.additional_data.missing_nama_penyedia / command.additional_data.total_data) * 100 * 100) / 100}%</td>
                                                </tr>
                                                <tr>
                                                    <td>Missing Nama Pelaksana</td>
                                                    <td>${command.additional_data.missing_nama_pelaksana}</td>
                                                    <td>${Math.round((command.additional_data.missing_nama_pelaksana / command.additional_data.total_data) * 100 * 100) / 100}%</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                ` : ''}

                                ${command.additional_data && command.additional_data.status_updates_count && command.additional_data.status_updates_count > 0 ? `
                                <div class="status-updates-section mb-3">
                                    <h6 class="text-primary">Status Updates (${command.additional_data.status_updates_count})</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Nomor Paket</th>
                                                    <th>Status Lama</th>
                                                    <th>Status Baru</th>
                                                    <th>Waktu Update</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${command.additional_data.status_update_details.map(update => `
                                                    <tr>
                                                        <td>${update.nomor_paket}</td>
                                                        <td>${update.old_status}</td>
                                                        <td>${update.new_status}</td>
                                                        <td>${update.updated_at}</td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                ` : ''}
                                <pre class="additional-data">${JSON.stringify(command.additional_data, null, 2)}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    });
</script>
@endsection
