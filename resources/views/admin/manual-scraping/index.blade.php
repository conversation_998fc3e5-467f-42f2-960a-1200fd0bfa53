@extends('layouts.master')

@section('title', 'Manual Scraping')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Manual Scraping</li>
                    </ol>
                </div>
                <h4 class="page-title">Manual Scraping</h4>
            </div>
        </div>
    </div>

    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    @if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0 text-white">Manual Scraping</h5>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs nav-bordered mb-3" id="scrapingTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="tender-details-tab" data-bs-toggle="tab" data-bs-target="#tender-details" type="button" role="tab" aria-controls="tender-details" aria-selected="true">Detail Tender</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="jadwal-tender-tab" data-bs-toggle="tab" data-bs-target="#jadwal-tender" type="button" role="tab" aria-controls="jadwal-tender" aria-selected="false">Jadwal Tender</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="peserta-tender-tab" data-bs-toggle="tab" data-bs-target="#peserta-tender" type="button" role="tab" aria-controls="peserta-tender" aria-selected="false">Peserta Tender</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pemenang-tender-tab" data-bs-toggle="tab" data-bs-target="#pemenang-tender" type="button" role="tab" aria-controls="pemenang-tender" aria-selected="false">Pemenang Tender</button>
                        </li>
                    </ul>

                    <div class="tab-content" id="scrapingTabContent">
                        <!-- Detail Tender Tab -->
                        <div class="tab-pane fade show active" id="tender-details" role="tabpanel" aria-labelledby="tender-details-tab">
                            <form action="{{ route('admin.manual-scraping.process-tender-details') }}" method="POST" class="form-horizontal">
                                @csrf
                                <div class="mb-3">
                                    <label for="url" class="form-label">URL Tender</label>
                                    <input type="url" class="form-control" id="url" name="url" required placeholder="https://lpse.jatimprov.go.id/eproc4/lelang/10019666000/pengumumanlelang">
                                    <small class="text-muted">Masukkan URL halaman detail tender (pengumumanlelang)</small>
                                </div>
                                <div class="mb-3">
                                    <label for="html_content" class="form-label">HTML Content</label>
                                    <textarea class="form-control" id="html_content" name="html_content" rows="10" required></textarea>
                                    <small class="text-muted">Paste HTML dari halaman detail tender (pengumumanlelang)</small>
                                </div>
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-primary">Proses Detail Tender</button>
                                </div>
                            </form>
                        </div>

                        <!-- Jadwal Tender Tab -->
                        <div class="tab-pane fade" id="jadwal-tender" role="tabpanel" aria-labelledby="jadwal-tender-tab">
                            <form action="{{ route('admin.manual-scraping.process-jadwal-tender') }}" method="POST" class="form-horizontal">
                                @csrf
                                <div class="mb-3">
                                    <label for="url_jadwal" class="form-label">URL Jadwal Tender</label>
                                    <input type="url" class="form-control" id="url_jadwal" name="url" required placeholder="https://lpse.jatimprov.go.id/eproc4/lelang/10019666000/jadwal">
                                    <small class="text-muted">Masukkan URL halaman jadwal tender</small>
                                </div>
                                <div class="mb-3">
                                    <label for="html_content_jadwal" class="form-label">HTML Content</label>
                                    <textarea class="form-control" id="html_content_jadwal" name="html_content" rows="10" required></textarea>
                                    <small class="text-muted">Paste HTML dari halaman jadwal tender</small>
                                </div>
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-primary">Proses Jadwal Tender</button>
                                </div>
                            </form>
                        </div>

                        <!-- Peserta Tender Tab -->
                        <div class="tab-pane fade" id="peserta-tender" role="tabpanel" aria-labelledby="peserta-tender-tab">
                            <form action="{{ route('admin.manual-scraping.process-peserta-tender') }}" method="POST" class="form-horizontal">
                                @csrf
                                <div class="mb-3">
                                    <label for="url_peserta" class="form-label">URL Peserta Tender</label>
                                    <input type="url" class="form-control" id="url_peserta" name="url" required placeholder="https://lpse.jatimprov.go.id/eproc4/lelang/10019666000/peserta">
                                    <small class="text-muted">Masukkan URL halaman peserta tender</small>
                                </div>
                                <div class="mb-3">
                                    <label for="html_content_peserta" class="form-label">HTML Content</label>
                                    <textarea class="form-control" id="html_content_peserta" name="html_content" rows="10" required></textarea>
                                    <small class="text-muted">Paste HTML dari halaman peserta tender</small>
                                </div>
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-primary">Proses Peserta Tender</button>
                                </div>
                            </form>
                        </div>

                        <!-- Pemenang Tender Tab -->
                        <div class="tab-pane fade" id="pemenang-tender" role="tabpanel" aria-labelledby="pemenang-tender-tab">
                            <form action="{{ route('admin.manual-scraping.process-pemenang-tender') }}" method="POST" class="form-horizontal">
                                @csrf
                                <div class="mb-3">
                                    <label for="url_pemenang" class="form-label">URL Pemenang Tender</label>
                                    <input type="url" class="form-control" id="url_pemenang" name="url" required placeholder="https://lpse.jatimprov.go.id/eproc4/lelang/10019666000/pemenang">
                                    <small class="text-muted">Masukkan URL halaman pemenang tender</small>
                                </div>
                                <div class="mb-3">
                                    <label for="html_content_pemenang" class="form-label">HTML Content</label>
                                    <textarea class="form-control" id="html_content_pemenang" name="html_content" rows="10" required></textarea>
                                    <small class="text-muted">Paste HTML dari halaman pemenang tender</small>
                                </div>
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-primary">Proses Pemenang Tender</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Panduan Penggunaan -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0 text-white">Panduan Penggunaan</h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="accordionPanduan">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingOne">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                    Cara Menggunakan Manual Scraping
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionPanduan">
                                <div class="accordion-body">
                                    <ol>
                                        <li>Buka halaman tender yang ingin di-scrape di browser</li>
                                        <li>Copy URL halaman tersebut dan paste ke dalam field URL</li>
                                        <li>Klik kanan pada halaman dan pilih "View Page Source" atau "Lihat Sumber Halaman"</li>
                                        <li>Copy seluruh HTML (Ctrl+A lalu Ctrl+C)</li>
                                        <li>Paste HTML ke dalam textarea yang sesuai (Detail Tender, Jadwal, Peserta, atau Pemenang)</li>
                                        <li>Klik tombol "Proses" untuk memproses data</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingTwo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                    Tips Scraping
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionPanduan">
                                <div class="accordion-body">
                                    <ul>
                                        <li>Pastikan HTML yang di-paste lengkap dan tidak terpotong</li>
                                        <li>Untuk Detail Tender, gunakan halaman "pengumumanlelang"</li>
                                        <li>Untuk Jadwal Tender, gunakan halaman "jadwal"</li>
                                        <li>Untuk Peserta Tender, gunakan halaman "peserta"</li>
                                        <li>Untuk Pemenang Tender, gunakan halaman "pemenang" atau "hasil"</li>
                                        <li>Jika terjadi error, periksa kembali HTML yang di-paste</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Auto-resize textareas
        $('textarea').on('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });

        // Keep active tab after form submission
        const activeTab = localStorage.getItem('activeScrapingTab');
        if (activeTab) {
            $(`#scrapingTab button[data-bs-target="${activeTab}"]`).tab('show');
        }

        // Save active tab on change
        $('#scrapingTab button').on('shown.bs.tab', function (e) {
            localStorage.setItem('activeScrapingTab', $(e.target).data('bs-target'));
        });
    });
</script>
@endsection
