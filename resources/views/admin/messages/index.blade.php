@extends('layouts.master')

@section('title', '<PERSON><PERSON> <PERSON> <PERSON><PERSON>')

@section('css')
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
    /* Fix for Select2 inside Bootstrap modal */
    .select2-container {
        z-index: 9999;
    }

    /* Pagination Custom */
    .pagination {
        margin-bottom: 0;
        margin-top: 20px;
    }

    .pagination .page-link {
        color: #4481eb;
        border: none;
        padding: 10px 15px;
        margin: 0 5px;
        border-radius: 8px;
        font-size: 0.9rem;
        transition: all 0.2s ease;
    }

    .pagination .page-link:hover {
        background-color: #f8f9fa;
        color: #4481eb;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .pagination .active .page-link {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        box-shadow: 0 4px 10px rgba(68, 129, 235, 0.3);
    }

    /* Table styling */
    .table-responsive {
        border-radius: 12px;
        overflow: hidden;
    }

    .table thead th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-top: none;
        padding: 16px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e9ecef;
    }

    /* Gradient buttons */
    .btn-gradient-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
        border: none;
        color: white;
        box-shadow: 0 4px 10px rgba(68, 129, 235, 0.3);
        transition: all 0.3s ease;
    }

    .btn-gradient-primary:hover {
        background: linear-gradient(90deg, #3470da, #03aceb);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(68, 129, 235, 0.4);
        color: white;
    }

    /* Card styling */
    .card {
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: none;
        overflow: hidden;
    }

    .bg-gradient-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Kirim Pesan</li>
                    </ol>
                </div>
                <h4 class="page-title">Kirim Pesan ke Pengguna</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="card-title mb-0 text-white">Form Kirim Pesan</h5>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('warning'))
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            {{ session('warning') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form action="{{ route('admin.messages.send') }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <label for="user_ids" class="form-label">Pilih Pengguna</label>
                            <select class="form-control select2" id="user_ids" name="user_ids[]" multiple required data-placeholder="Pilih pengguna...">
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}">
                                        {{ $user->name }} ({{ $user->email }})
                                        @if($user->phone) - {{ $user->phone }} @endif
                                    </option>
                                @endforeach
                            </select>
                            @error('user_ids')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="message_type" class="form-label">Jenis Pesan</label>
                            <select class="form-control" id="message_type" name="message_type" required>
                                <option value="whatsapp">WhatsApp</option>
                                <option value="telegram">Telegram</option>
                                <option value="both">Keduanya (WhatsApp & Telegram)</option>
                            </select>
                            @error('message_type')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Pesan</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required placeholder="Tulis pesan di sini..."></textarea>
                            <small class="text-muted">
                                <span id="char-count">0</span>/1000 karakter
                            </small>
                            @error('message')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-gradient-primary" id="send-button">
                                <span class="button-text"><i class="material-icons-outlined me-1" style="font-size: 16px; vertical-align: text-bottom;">send</i> Kirim Pesan</span>
                                <span class="button-loader d-none">
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span class="ms-1">Mengirim...</span>
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-gradient-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 text-white">Riwayat Pesan</h5>
                    @if(count($messageLogs) > 0)
                    <form action="{{ route('admin.messages.delete-all') }}" method="POST" id="delete-all-form">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-sm btn-danger" id="delete-all-btn">
                            <i class="material-icons-outlined me-1" style="font-size: 16px; vertical-align: text-bottom;">delete_sweep</i>
                            Hapus Semua
                        </button>
                    </form>
                    @endif
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-centered table-hover align-middle mb-0">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Pengguna</th>
                                    <th>Jenis</th>
                                    <th>Status</th>
                                    <th>Pesan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($messageLogs as $log)
                                    <tr>
                                        <td>{{ $log->created_at->format('d M Y H:i') }}</td>
                                        <td>
                                            <a href="{{ route('admin.users.show', $log->user_id) }}">
                                                {{ $log->user->name ?? 'User tidak ditemukan' }}
                                            </a>
                                        </td>
                                        <td>
                                            @if($log->message_type == 'whatsapp')
                                                <span class="badge bg-success">WhatsApp</span>
                                            @elseif($log->message_type == 'telegram')
                                                <span class="badge bg-info">Telegram</span>
                                            @else
                                                <span class="badge bg-primary">Keduanya</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($log->status == 'sent')
                                                <span class="badge bg-success">Terkirim</span>
                                            @else
                                                <span class="badge bg-danger" data-bs-toggle="tooltip" title="{{ $log->error_message }}">
                                                    Gagal
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-primary view-message"
                                                data-bs-toggle="modal" data-bs-target="#messageModal"
                                                data-id="{{ $log->id }}">
                                                <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle;">visibility</i>
                                                Lihat
                                            </button>
                                            <textarea class="d-none message-data" id="message-data-{{ $log->id }}">{{ $log->message }}</textarea>
                                        </td>
                                        <td>
                                            <form action="{{ route('admin.messages.delete', $log->id) }}" method="POST" class="d-inline delete-form">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger delete-btn">
                                                    <i class="material-icons-outlined" style="font-size: 16px; vertical-align: middle;">delete</i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">Belum ada riwayat pesan</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3 pt-3 border-top">
                        <div class="text-muted small">
                            @if(method_exists($messageLogs, 'firstItem') && method_exists($messageLogs, 'lastItem') && method_exists($messageLogs, 'total'))
                                Menampilkan {{ $messageLogs->firstItem() ?? 0 }} - {{ $messageLogs->lastItem() ?? 0 }} dari {{ $messageLogs->total() }} pesan
                            @else
                                Menampilkan {{ $messageLogs->count() }} pesan
                            @endif
                        </div>
                        <div>
                            @if(method_exists($messageLogs, 'links'))
                                {{ $messageLogs->links('vendor.pagination.costum') }}
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk melihat pesan -->
<div class="modal fade" id="messageModal" tabindex="-1" aria-labelledby="messageModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-gradient-primary text-white">
                <h5 class="modal-title text-white" id="messageModalLabel">Detail Pesan</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="message-container p-3 border rounded bg-light">
                    <div id="message-content" class="mb-0" style="white-space: pre-wrap; font-family: inherit; min-height: 100px; overflow-y: auto; max-height: 300px;"></div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle;">info</i>
                        Jika pesan tidak muncul, coba tutup dan buka kembali modal ini.
                    </small>
                </div>
                <!-- Hidden input to store the current message -->
                <input type="hidden" id="current-message-id" value="">
            </div>
            <div class="modal-footer">
                <form id="delete-message-form" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="material-icons-outlined me-1" style="font-size: 16px; vertical-align: text-bottom;">delete</i> Hapus Pesan
                    </button>
                </form>
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                    <i class="material-icons-outlined me-1" style="font-size: 16px; vertical-align: text-bottom;">close</i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            theme: 'bootstrap-5'
        });

        // Character counter for message
        $('#message').on('input', function() {
            var charCount = $(this).val().length;
            $('#char-count').text(charCount);

            if (charCount > 1000) {
                $('#char-count').addClass('text-danger');
                $('#send-button').prop('disabled', true);
            } else {
                $('#char-count').removeClass('text-danger');
                $('#send-button').prop('disabled', false);
            }
        });

        // Handle form submission with loading animation
        $('form').on('submit', function() {
            // Don't show loading for delete forms
            if (!$(this).hasClass('delete-form') && this.id !== 'delete-message-form' && this.id !== 'delete-all-form') {
                var submitButton = $(this).find('button[type="submit"]');

                // Show loading animation
                submitButton.find('.button-text').addClass('d-none');
                submitButton.find('.button-loader').removeClass('d-none');

                // Disable button to prevent double submission
                submitButton.prop('disabled', true);
            }
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        // View message in modal
        $('.view-message').on('click', function() {
            var messageId = $(this).data('id');

            // Show loading indicator
            $('#message-content').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><div class="mt-2">Memuat pesan...</div></div>');

            // Store current message ID
            $('#current-message-id').val(messageId);

            // Set form action for delete button
            var deleteUrl = "{{ route('admin.messages.delete', ':id') }}";
            deleteUrl = deleteUrl.replace(':id', messageId);
            $('#delete-message-form').attr('action', deleteUrl);

            // Get message from hidden textarea
            setTimeout(function() {
                var message = $('#message-data-' + messageId).val();

                if (message && message.trim() !== '') {
                    console.log('Message loaded, length:', message.length);
                    $('#message-content').text(message);
                } else {
                    $('#message-content').html('<em class="text-muted">Tidak dapat menampilkan pesan. Pesan kosong atau tidak valid.</em>');
                }
            }, 300); // Small delay to ensure modal is fully shown
        });

        // Confirm delete for individual message
        $('.delete-btn').on('click', function(e) {
            e.preventDefault();
            var form = $(this).closest('form');

            if (confirm('Apakah Anda yakin ingin menghapus pesan ini?')) {
                form.submit();
            }
        });

        // Confirm delete for message in modal
        $('#delete-message-form').on('submit', function(e) {
            e.preventDefault();

            if (confirm('Apakah Anda yakin ingin menghapus pesan ini?')) {
                this.submit();
            }
        });

        // Confirm delete all messages
        $('#delete-all-btn').on('click', function(e) {
            e.preventDefault();

            if (confirm('Apakah Anda yakin ingin menghapus SEMUA riwayat pesan? Tindakan ini tidak dapat dibatalkan.')) {
                $('#delete-all-form').submit();
            }
        });

        // Ensure modal is fully shown before trying to display message
        $('#messageModal').on('shown.bs.modal', function () {
            var messageId = $('#current-message-id').val();

            if (messageId) {
                // Try to load message again if it's not already loaded
                var currentContent = $('#message-content').text().trim();

                if (!currentContent || currentContent.includes('Memuat pesan...')) {
                    console.log('Modal is fully shown, trying to load message again for ID:', messageId);

                    // Get message from hidden textarea again
                    var message = $('#message-data-' + messageId).val();

                    if (message && message.trim() !== '') {
                        console.log('Message loaded in modal shown event, length:', message.length);
                        $('#message-content').text(message);
                    }
                } else {
                    console.log('Message already loaded, content length:', currentContent.length);
                }
            }
        });
    });
</script>
@endsection
