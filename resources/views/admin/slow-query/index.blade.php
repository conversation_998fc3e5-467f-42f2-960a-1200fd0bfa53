@extends('layouts.master')

@section('title', 'Slow Query Analysis')

@section('content')
<div class="page-wrapper">
    <div class="page-content">
        <!-- Page Header -->
        <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
            <div class="breadcrumb-title pe-3">Admin</div>
            <div class="ps-3">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 p-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="bx bx-home-alt"></i></a></li>
                        <li class="breadcrumb-item active" aria-current="page">Slow Query Analysis</li>
                    </ol>
                </nav>
            </div>
        </div>

        <!-- Analysis Controls -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <i class="material-icons-outlined me-2">analytics</i>
                            <h5 class="mb-0">Query Performance Analysis</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Analysis Period</label>
                                <select class="form-select" id="analysis-days">
                                    <option value="1">Last 1 Day</option>
                                    <option value="3">Last 3 Days</option>
                                    <option value="7" selected>Last 7 Days</option>
                                    <option value="14">Last 14 Days</option>
                                    <option value="30">Last 30 Days</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Threshold (ms)</label>
                                <select class="form-select" id="analysis-threshold">
                                    <option value="50">50ms</option>
                                    <option value="100" selected>100ms</option>
                                    <option value="200">200ms</option>
                                    <option value="500">500ms</option>
                                    <option value="1000">1000ms</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Results Limit</label>
                                <select class="form-select" id="analysis-limit">
                                    <option value="10">Top 10</option>
                                    <option value="20" selected>Top 20</option>
                                    <option value="50">Top 50</option>
                                    <option value="100">Top 100</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary d-block w-100" id="analyze-btn">
                                    <i class="material-icons-outlined me-1">search</i>
                                    Analyze Queries
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loading-state" class="text-center py-5" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Analyzing slow queries...</p>
        </div>

        <!-- Analysis Results -->
        <div id="analysis-results" style="display: none;">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h4 class="mb-0" id="total-queries">0</h4>
                                    <p class="mb-0">Total Slow Queries</p>
                                </div>
                                <i class="material-icons-outlined" style="font-size: 2rem;">query_stats</i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h4 class="mb-0" id="avg-time">0ms</h4>
                                    <p class="mb-0">Average Time</p>
                                </div>
                                <i class="material-icons-outlined" style="font-size: 2rem;">schedule</i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h4 class="mb-0" id="max-time">0ms</h4>
                                    <p class="mb-0">Slowest Query</p>
                                </div>
                                <i class="material-icons-outlined" style="font-size: 2rem;">warning</i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h4 class="mb-0" id="queries-per-day">0</h4>
                                    <p class="mb-0">Queries/Day</p>
                                </div>
                                <i class="material-icons-outlined" style="font-size: 2rem;">trending_up</i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Slow Queries -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="material-icons-outlined me-2">list</i>
                                Top Slow Queries
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="slow-queries-table">
                                    <thead>
                                        <tr>
                                            <th>Query Type</th>
                                            <th>SQL Query</th>
                                            <th>Count</th>
                                            <th>Avg Time</th>
                                            <th>Max Time</th>
                                            <th>Total Impact</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Query Patterns -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="material-icons-outlined me-2">pie_chart</i>
                                Queries by Type
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="query-type-chart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="material-icons-outlined me-2">table_chart</i>
                                Top Affected Tables
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="table-chart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="material-icons-outlined me-2">lightbulb</i>
                                Optimization Recommendations
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="recommendations-list">
                                <!-- Recommendations will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Data State -->
        <div id="no-data-state" class="text-center py-5" style="display: none;">
            <i class="material-icons-outlined text-muted" style="font-size: 4rem;">search_off</i>
            <h4 class="text-muted mt-3">No Slow Queries Found</h4>
            <p class="text-muted">No queries found above the specified threshold in the selected period.</p>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    let queryTypeChart = null;
    let tableChart = null;

    // Auto-load analysis on page load
    loadAnalysis();

    // Analyze button click
    $('#analyze-btn').click(function() {
        loadAnalysis();
    });

    function loadAnalysis() {
        const days = $('#analysis-days').val();
        const threshold = $('#analysis-threshold').val();
        const limit = $('#analysis-limit').val();

        // Show loading state
        $('#loading-state').show();
        $('#analysis-results').hide();
        $('#no-data-state').hide();

        // Make API request
        $.ajax({
            url: '/admin/slow-query/analysis',
            method: 'GET',
            data: {
                days: days,
                threshold: threshold,
                limit: limit
            },
            success: function(response) {
                $('#loading-state').hide();
                
                if (response.queries && response.queries.length > 0) {
                    displayResults(response);
                    $('#analysis-results').show();
                } else {
                    $('#no-data-state').show();
                }
            },
            error: function(xhr, status, error) {
                $('#loading-state').hide();
                console.error('Analysis failed:', error);
                alert('Failed to analyze slow queries. Please try again.');
            }
        });
    }

    function displayResults(data) {
        // Update summary cards
        $('#total-queries').text(data.summary.total_queries.toLocaleString());
        $('#avg-time').text(data.summary.avg_time + 'ms');
        $('#max-time').text(data.summary.max_time + 'ms');
        $('#queries-per-day').text(data.summary.queries_per_day);

        // Populate queries table
        populateQueriesTable(data.queries);

        // Update charts
        updateQueryTypeChart(data.patterns.by_type);
        updateTableChart(data.patterns.by_table);

        // Display recommendations
        displayRecommendations(data.recommendations);
    }

    function populateQueriesTable(queries) {
        const tbody = $('#slow-queries-table tbody');
        tbody.empty();

        queries.forEach(function(query) {
            const impact = (query.count * query.avg_time).toFixed(0);
            const typeClass = getQueryTypeClass(query.type);
            
            const row = `
                <tr>
                    <td><span class="badge ${typeClass}">${query.type}</span></td>
                    <td><code class="text-wrap" style="font-size: 0.8rem; max-width: 400px; display: block;">${query.sql}</code></td>
                    <td><span class="badge bg-info">${query.count}</span></td>
                    <td>${query.avg_time}ms</td>
                    <td>${query.max_time}ms</td>
                    <td><strong>${impact}ms</strong></td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function getQueryTypeClass(type) {
        switch(type) {
            case 'SELECT': return 'bg-primary';
            case 'INSERT': return 'bg-success';
            case 'UPDATE': return 'bg-warning';
            case 'DELETE': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    function updateQueryTypeChart(typeData) {
        const ctx = document.getElementById('query-type-chart').getContext('2d');
        
        if (queryTypeChart) {
            queryTypeChart.destroy();
        }

        const labels = Object.keys(typeData);
        const counts = labels.map(type => typeData[type].count);
        const colors = labels.map(type => getQueryTypeColor(type));

        queryTypeChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: counts,
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    function updateTableChart(tableData) {
        const ctx = document.getElementById('table-chart').getContext('2d');
        
        if (tableChart) {
            tableChart.destroy();
        }

        // Get top 10 tables
        const sortedTables = Object.entries(tableData)
            .sort((a, b) => b[1].count - a[1].count)
            .slice(0, 10);

        const labels = sortedTables.map(item => item[0]);
        const counts = sortedTables.map(item => item[1].count);

        tableChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Query Count',
                    data: counts,
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    function getQueryTypeColor(type) {
        switch(type) {
            case 'SELECT': return '#0d6efd';
            case 'INSERT': return '#198754';
            case 'UPDATE': return '#ffc107';
            case 'DELETE': return '#dc3545';
            default: return '#6c757d';
        }
    }

    function displayRecommendations(recommendations) {
        const container = $('#recommendations-list');
        container.empty();

        if (recommendations.length === 0) {
            container.html('<p class="text-muted">No specific recommendations at this time.</p>');
            return;
        }

        recommendations.forEach(function(rec) {
            const priorityClass = rec.priority === 'high' ? 'danger' : 
                                 rec.priority === 'medium' ? 'warning' : 'info';
            
            const card = `
                <div class="alert alert-${priorityClass} border-0">
                    <div class="d-flex align-items-start">
                        <i class="material-icons-outlined me-3 mt-1">lightbulb</i>
                        <div>
                            <h6 class="alert-heading">${rec.title}</h6>
                            <p class="mb-0">${rec.description}</p>
                            <small class="text-muted">Priority: ${rec.priority.toUpperCase()}</small>
                        </div>
                    </div>
                </div>
            `;
            container.append(card);
        });
    }
});
</script>
@endsection
