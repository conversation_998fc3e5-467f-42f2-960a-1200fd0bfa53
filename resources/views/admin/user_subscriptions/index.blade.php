@extends('layouts.master')

@section('title', 'User Subscriptions Management')

@section('css')
<style>
    .filter-toggle-icon {
        transition: transform 0.3s ease;
    }

    .collapsed .filter-toggle-icon {
        transform: rotate(180deg);
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    .product-box {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        overflow: hidden;
        background-color: #f8f9fa;
    }

    .product-box img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .payment-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 10px;
        border-left: 4px solid #dee2e6;
    }

    .payment-card.confirmed {
        border-left-color: #198754;
    }

    .payment-card.pending {
        border-left-color: #ffc107;
    }

    .payment-card.rejected {
        border-left-color: #dc3545;
    }

    .subscription-row-active {
        background-color: rgba(25, 135, 84, 0.05);
    }

    .subscription-row-pending {
        background-color: rgba(255, 193, 7, 0.05);
    }

    .subscription-row-expired {
        background-color: rgba(108, 117, 125, 0.05);
    }

    .subscription-row-rejected {
        background-color: rgba(220, 53, 69, 0.05);
    }

    .proof-thumbnail {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 4px;
        cursor: pointer;
        transition: transform 0.2s;
    }

    .proof-thumbnail:hover {
        transform: scale(1.05);
    }

    .days-remaining {
        font-size: 0.8rem;
        padding: 2px 6px;
        border-radius: 4px;
    }
</style>
@endsection

@section('content')
<x-page-title title="User Subscriptions" pagetitle="Subscription Management" />

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
        <h5 class="card-title mb-0">
            <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
                <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
                <span>Filter Subscriptions</span>
                <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
            </a>
        </h5>
        <span class="badge bg-white text-primary" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">card_membership</i>
            {{ number_format(count($userSubscriptions), 0, ',', '.') }} Subscriptions
        </span>
    </div>
    <div class="collapse show" id="filterCollapse">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.user_subscriptions.index') }}">
                <div class="row g-3">
                    <!-- User Search -->
                    <div class="col-md-3">
                        <label for="user" class="form-label">User</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">person</i></span>
                            <input type="text" name="user" id="user" class="form-control" placeholder="Search by name or email..." value="{{ request('user') }}">
                        </div>
                    </div>

                    <!-- Status Filter -->
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">info</i></span>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>Expired</option>
                            </select>
                        </div>
                    </div>

                    <!-- Package Filter -->
                    <div class="col-md-2">
                        <label for="package" class="form-label">Package</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">card_membership</i></span>
                            <select name="package" id="package" class="form-select">
                                <option value="">All Packages</option>
                                @foreach(\App\Models\Subscription::all() as $package)
                                    <option value="{{ $package->id }}" {{ request('package') == $package->id ? 'selected' : '' }}>{{ $package->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Date Range -->
                    <div class="col-md-3">
                        <label for="date_range" class="form-label">Date Range</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">date_range</i></span>
                            <select name="date_range" id="date_range" class="form-select">
                                <option value="">All Time</option>
                                <option value="today" {{ request('date_range') == 'today' ? 'selected' : '' }}>Today</option>
                                <option value="yesterday" {{ request('date_range') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                                <option value="this_week" {{ request('date_range') == 'this_week' ? 'selected' : '' }}>This Week</option>
                                <option value="last_week" {{ request('date_range') == 'last_week' ? 'selected' : '' }}>Last Week</option>
                                <option value="this_month" {{ request('date_range') == 'this_month' ? 'selected' : '' }}>This Month</option>
                                <option value="last_month" {{ request('date_range') == 'last_month' ? 'selected' : '' }}>Last Month</option>
                            </select>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="col-md-2 d-flex align-items-end">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">search</i>
                                Apply
                            </button>
                            <a href="{{ route('admin.user_subscriptions.index') }}" class="btn btn-outline-secondary ms-2">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">refresh</i>
                                Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Summary Section -->
<div class="row mb-4">
    @php
        $activeCount = $userSubscriptions->where('status', 'active')->count();
        $pendingCount = $userSubscriptions->where('status', 'pending')->count();
        $rejectedCount = $userSubscriptions->where('status', 'rejected')->count();
        $expiredCount = $userSubscriptions->filter(function($sub) {
            return $sub->status === 'active' && \Carbon\Carbon::parse($sub->end_date)->isPast();
        })->count();

        $totalRevenue = $userSubscriptions->whereIn('status', ['active', 'pending'])->sum('total_amount');
    @endphp

    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-success bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-success">check_circle</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Active Subscriptions</h6>
                    <h3 class="mb-0">{{ $activeCount }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-warning bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-warning">pending</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Pending Verification</h6>
                    <h3 class="mb-0">{{ $pendingCount }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-danger bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-danger">cancel</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Expired/Rejected</h6>
                    <h3 class="mb-0">{{ $expiredCount + $rejectedCount }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <div class="rounded-circle p-3 bg-primary bg-opacity-10 me-3">
                    <i class="material-icons-outlined text-primary">payments</i>
                </div>
                <div>
                    <h6 class="mb-0 text-muted">Total Revenue</h6>
                    <h3 class="mb-0">Rp {{ number_format($totalRevenue, 0, ',', '.') }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Table Section -->
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5 class="card-title mb-0">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">card_membership</i>
            User Subscriptions
        </h5>
        <div>
            @php
                $hasExpiredOrRejected = $userSubscriptions->filter(function($sub) {
                    return $sub->status === 'rejected' ||
                           ($sub->status === 'active' && \Carbon\Carbon::parse($sub->end_date)->isPast());
                })->count() > 0;
            @endphp

            @if($hasExpiredOrRejected)
                <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#bulkDeleteModal">
                    <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">delete_sweep</i>
                    Delete Expired & Rejected
                </button>
            @endif
        </div>
    </div>
    <div class="card-body p-0">
        @if(session('success'))
            <div class="alert alert-success m-3">
                <i class="material-icons-outlined me-2">check_circle</i>
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger m-3">
                <i class="material-icons-outlined me-2">error</i>
                {{ session('error') }}
            </div>
        @endif

        @if(session('info'))
            <div class="alert alert-info m-3">
                <i class="material-icons-outlined me-2">info</i>
                {{ session('info') }}
            </div>
        @endif

        @if(count($userSubscriptions) > 0)
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Package</th>
                            <th>Duration</th>
                            <th>Status</th>
                            <th>Amount</th>
                            <th>Payment</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($userSubscriptions as $subscription)
                            @php
                                $rowClass = '';
                                if ($subscription->status === 'active') {
                                    $rowClass = 'subscription-row-active';
                                } elseif ($subscription->status === 'pending') {
                                    $rowClass = 'subscription-row-pending';
                                } elseif ($subscription->status === 'rejected') {
                                    $rowClass = 'subscription-row-rejected';
                                } elseif (\Carbon\Carbon::parse($subscription->end_date)->isPast()) {
                                    $rowClass = 'subscription-row-expired';
                                }

                                $daysRemaining = \Carbon\Carbon::now()->diffInDays(\Carbon\Carbon::parse($subscription->end_date), false);
                            @endphp
                            <tr class="{{ $rowClass }}">
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="product-box">
                                            @if($subscription->user->photo)
                                                <img src="{{ asset("storage/{$subscription->user->photo}") }}" alt="{{ $subscription->user->name }}" class="rounded-3">
                                            @else
                                                <i class="material-icons-outlined text-primary">person</i>
                                            @endif
                                        </div>
                                        <div>
                                            <a href="{{ route('admin.users.show', $subscription->user->id) }}" class="fw-medium text-dark">
                                                {{ $subscription->user->name }}
                                            </a>
                                            <div class="small text-muted">{{ $subscription->user->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-medium">{{ $subscription->subscription->name }}</span>
                                    <div class="small text-muted">{{ $subscription->subscription->duration_days }} days</div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="small text-muted">Start: {{ \Carbon\Carbon::parse($subscription->start_date)->format('d M Y') }}</div>
                                        <div class="small text-muted">End: {{ \Carbon\Carbon::parse($subscription->end_date)->format('d M Y') }}</div>

                                        @if($subscription->status === 'active' && $daysRemaining > 0)
                                            <span class="badge bg-success days-remaining mt-1">{{ $daysRemaining }} days left</span>
                                        @elseif($subscription->status === 'active' && $daysRemaining <= 0)
                                            <span class="badge bg-danger days-remaining mt-1">Expired</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @if($subscription->status === 'active')
                                        <span class="badge status-badge bg-success">Active</span>
                                    @elseif($subscription->status === 'pending')
                                        <span class="badge status-badge bg-warning">Pending</span>
                                    @elseif($subscription->status === 'rejected')
                                        <span class="badge status-badge bg-danger">Rejected</span>
                                    @else
                                        <span class="badge status-badge bg-secondary">{{ ucfirst($subscription->status) }}</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="fw-medium">Rp {{ number_format($subscription->total_amount, 0, ',', '.') }}</div>
                                    @if($subscription->unique_code)
                                        <div class="small text-muted">Code: {{ $subscription->unique_code }}</div>
                                    @endif
                                </td>
                                <td>
                                    @if(count($subscription->payments) > 0)
                                        @foreach($subscription->payments as $payment)
                                            <div class="payment-card {{ $payment->status }}">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <div class="small fw-medium">Rp {{ number_format($payment->amount, 0, ',', '.') }}</div>
                                                        <div class="small text-muted">{{ $payment->payment_method }}</div>
                                                        <div class="mt-1">
                                                            <span class="badge bg-{{ $payment->status == 'confirmed' ? 'success' : ($payment->status == 'pending' ? 'warning' : 'danger') }}">
                                                                {{ ucfirst($payment->status) }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    @if($payment->confirmation_proof)
                                                        <div>
                                                            <a href="{{ asset('storage/' . $payment->confirmation_proof) }}" target="_blank" data-bs-toggle="tooltip" title="View Payment Proof">
                                                                <img src="{{ asset('storage/' . $payment->confirmation_proof) }}" class="proof-thumbnail" alt="Payment Proof">
                                                            </a>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        @endforeach
                                    @else
                                        <span class="text-muted small">No payment records</span>
                                    @endif
                                </td>
                                <td>
                                    @if($subscription->status === 'pending')
                                        <div class="d-flex flex-column gap-2">
                                            <form action="{{ route('admin.user_subscriptions.approve', $subscription->id) }}" method="POST">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-success w-100">
                                                    <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">check_circle</i>
                                                    Approve
                                                </button>
                                            </form>
                                            <form action="{{ route('admin.user_subscriptions.reject', $subscription->id) }}" method="POST">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-danger w-100">
                                                    <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">cancel</i>
                                                    Reject
                                                </button>
                                            </form>
                                        </div>
                                    @elseif($subscription->status === 'active')
                                        <div class="d-flex flex-column gap-2">
                                            <a href="{{ route('admin.users.edit', $subscription->user->id) }}#subscription-management" class="btn btn-sm btn-primary w-100">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">add_circle</i>
                                                Add Days
                                            </a>
                                            @if(\Carbon\Carbon::parse($subscription->end_date)->isPast())
                                                <form action="{{ route('admin.user_subscriptions.delete', $subscription->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this expired subscription?');">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger w-100">
                                                        <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">delete</i>
                                                        Delete Expired
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    @elseif($subscription->status === 'rejected')
                                        <form action="{{ route('admin.user_subscriptions.delete', $subscription->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this rejected subscription?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger w-100">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">delete</i>
                                                Delete Rejected
                                            </button>
                                        </form>
                                    @else
                                        <span class="text-muted small">No actions available</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if(method_exists($userSubscriptions, 'links'))
                <div class="d-flex justify-content-between align-items-center p-3 border-top">
                    <div class="text-muted small">
                        @if(method_exists($userSubscriptions, 'firstItem') && method_exists($userSubscriptions, 'lastItem') && method_exists($userSubscriptions, 'total'))
                            Menampilkan {{ $userSubscriptions->firstItem() ?? 0 }} - {{ $userSubscriptions->lastItem() ?? 0 }} dari {{ $userSubscriptions->total() }} subscriptions
                        @else
                            Menampilkan {{ count($userSubscriptions) }} subscriptions
                        @endif
                    </div>
                    <div>
                        {{ $userSubscriptions->links('vendor.pagination.simple') }}
                    </div>
                </div>
            @endif
        @else
            <div class="empty-state">
                <i class="material-icons-outlined">card_membership</i>
                <h5>No Subscriptions Found</h5>
                <p>There are no user subscriptions matching your criteria</p>
            </div>
        @endif
    </div>
</div>

<!-- Bulk Delete Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="bulkDeleteModalLabel">
                    <i class="material-icons-outlined me-2">warning</i>
                    Confirm Bulk Delete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete all expired and rejected subscriptions? This action cannot be undone.</p>
                <div class="alert alert-warning">
                    <i class="material-icons-outlined me-2">info</i>
                    This will permanently delete:
                    <ul class="mb-0 mt-2">
                        <li>All rejected subscriptions</li>
                        <li>All expired subscriptions (active but past end date)</li>
                        <li>All related payment records</li>
                        <li>All related commission records</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ route('admin.user_subscriptions.bulk-delete') }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">delete_forever</i>
                        Delete All
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection



@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        // Toggle filter collapse icon
        const filterCollapse = document.getElementById('filterCollapse');
        const filterToggleIcon = document.querySelector('.filter-toggle-icon');

        filterCollapse.addEventListener('hidden.bs.collapse', function () {
            filterToggleIcon.textContent = 'expand_more';
        });

        filterCollapse.addEventListener('shown.bs.collapse', function () {
            filterToggleIcon.textContent = 'expand_less';
        });
    });
</script>
@endsection
