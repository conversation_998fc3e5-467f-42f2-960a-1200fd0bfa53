@extends('layouts.master')

@section('title', 'Riwayat Komisi')

@section('css')
<style>
    /* Card Styles */
    .commission-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s;
        border: none;
    }

    .commission-card:hover {
        transform: translateY(-5px);
    }

    .commission-header {
        background: linear-gradient(90deg, #f57c00, #ffb74d);
        color: white;
        border-bottom: none;
        padding: 16px 20px;
    }

    /* Badge Styles */
    .commission-badge {
        font-size: 0.75rem;
        padding: 5px 10px;
        border-radius: 20px;
        font-weight: 500;
    }

    /* Amount Styles */
    .commission-amount {
        font-weight: 600;
        color: #43a047;
        padding: 4px 8px;
        border-radius: 6px;
        background-color: rgba(67, 160, 71, 0.1);
        display: inline-block;
    }

    /* Empty State Styles */
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    /* User Avatar Styles */
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    /* Table Styles */
    .table th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }

    /* Summary Card Styles */
    .summary-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s;
        height: 100%;
        border: none;
    }

    .summary-card:hover {
        transform: translateY(-5px);
    }

    .summary-card .card-body {
        padding: 20px;
    }

    .summary-card .rounded-circle {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .summary-card h3 {
        font-size: 1.5rem;
        font-weight: 700;
        margin-top: 10px;
        color: #344767;
    }

    /* Filter Section Styles */
    .filter-section {
        background-color: #fff;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .filter-section .input-group-text {
        background-color: #f8f9fa;
        border: 1px solid #ced4da;
        color: #6c757d;
    }

    .filter-section .form-control,
    .filter-section .form-select {
        border: 1px solid #ced4da;
        padding: 8px 12px;
    }

    /* Pagination Styles */
    .pagination {
        margin-top: 20px;
    }

    .pagination .page-link {
        border: 1px solid #dee2e6;
        padding: 8px 12px;
        margin: 0 3px;
        border-radius: 4px;
        color: #344767;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    /* Animation for pulse */
    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.05);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .pulse {
        animation: pulse 2s infinite;
    }

    /* Custom header with diagonal pattern */
    .diagonal-pattern-header {
        background: linear-gradient(135deg, #f57c00, #ffb74d);
        color: white;
        position: relative;
        overflow: hidden;
        padding: 16px 20px;
    }

    .diagonal-pattern-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
            45deg,
            rgba(255, 255, 255, 0.1),
            rgba(255, 255, 255, 0.1) 10px,
            rgba(255, 255, 255, 0) 10px,
            rgba(255, 255, 255, 0) 20px
        );
        z-index: 0;
    }

    .diagonal-pattern-header > * {
        position: relative;
        z-index: 1;
    }
</style>
@endsection

@section('content')

<!-- Commission Summary Section -->
<div class="row mb-4">
    <div class="col-md-4 mb-4 mb-md-0">
        <div class="card rounded-4">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="wh-48 d-flex align-items-center justify-content-center bg-light-warning rounded-circle me-3">
                        <i class="material-icons-outlined text-warning">payments</i>
                    </div>
                    <div>
                        <h6 class="mb-0 text-muted">Total Saldo Komisi</h6>
                    </div>
                </div>
                <h3 class="mb-0 text-center">Rp {{ number_format(auth()->user()->commission_balance, 0, ',', '.') }}</h3>
                <div class="progress mt-3" style="height: 6px;">
                    <div class="progress-bar bg-warning" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4 mb-md-0">
        <div class="card rounded-4">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="wh-48 d-flex align-items-center justify-content-center bg-light-success rounded-circle me-3">
                        <i class="material-icons-outlined text-success">people</i>
                    </div>
                    <div>
                        <h6 class="mb-0 text-muted">Total Referral</h6>
                    </div>
                </div>
                <h3 class="mb-0 text-center">{{ auth()->user()->referrals()->count() }}</h3>
                <div class="progress mt-3" style="height: 6px;">
                    @php
                        $referralCount = auth()->user()->referrals()->count();
                        $referralGoal = max(10, $referralCount);
                        $referralProgress = min(100, ($referralCount / $referralGoal) * 100);
                    @endphp
                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ $referralProgress }}%;" aria-valuenow="{{ $referralProgress }}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card rounded-4">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="wh-48 d-flex align-items-center justify-content-center bg-light-primary rounded-circle me-3">
                        <i class="material-icons-outlined text-primary">account_balance_wallet</i>
                    </div>
                    <div>
                        <h6 class="mb-0 text-muted">Komisi Bulan Ini</h6>
                    </div>
                </div>
                @php
                    $thisMonthCommission = auth()->user()->commissions()
                        ->whereMonth('created_at', now()->month)
                        ->whereYear('created_at', now()->year)
                        ->sum('amount');
                @endphp
                <h3 class="mb-0 text-center">Rp {{ number_format($thisMonthCommission, 0, ',', '.') }}</h3>
                <div class="progress mt-3" style="height: 6px;">
                    <div class="progress-bar bg-primary" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Commission History Section -->
<div class="card rounded-4">
    <div class="card-header diagonal-pattern-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0 text-white">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">history</i>
            Riwayat Komisi
        </h5>
        <a href="{{ route('withdrawals.request') }}" class="btn btn-sm btn-light">
            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">account_balance_wallet</i>
            Ajukan Penarikan
        </a>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success d-flex align-items-center" role="alert">
                <i class="material-icons-outlined me-2">check_circle</i>
                <div>{{ session('success') }}</div>
            </div>
        @endif

        <!-- Filter Section -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('commissions.history') }}">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label fw-medium mb-2">Cari Referral</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">person</i></span>
                                <input type="text" id="search" name="search" class="form-control" placeholder="Cari nama atau email..." value="{{ request('search') }}">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <label for="period" class="form-label fw-medium mb-2">Periode</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">date_range</i></span>
                                <select id="period" name="period" class="form-select">
                                    <option value="">Semua Periode</option>
                                    <option value="this_month" {{ request('period') == 'this_month' ? 'selected' : '' }}>Bulan Ini</option>
                                    <option value="last_month" {{ request('period') == 'last_month' ? 'selected' : '' }}>Bulan Lalu</option>
                                    <option value="this_year" {{ request('period') == 'this_year' ? 'selected' : '' }}>Tahun Ini</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <label for="status" class="form-label fw-medium mb-2">Status</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">info</i></span>
                                <select id="status" name="status" class="form-select">
                                    <option value="">Semua Status</option>
                                    <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>Dikonfirmasi</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Menunggu</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2 d-flex align-items-end">
                            <div class="d-grid gap-2 w-100">
                                <button type="submit" class="btn btn-primary">
                                    <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">search</i>
                                    Filter
                                </button>

                                @if(request('search') || request('period') || request('status'))
                                    <a href="{{ route('commissions.history') }}" class="btn btn-outline-secondary">
                                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">refresh</i>
                                        Reset
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        @if($commissions->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th>Tanggal</th>
                            <th>Pengguna Referral</th>
                            <th>Paket</th>
                            <th>Harga Paket</th>
                            <th>Jumlah Komisi</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($commissions as $commission)
                            <tr>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="fw-medium">{{ $commission->created_at->format('d M Y') }}</span>
                                        <small class="text-muted">{{ $commission->created_at->format('H:i') }} WIB</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar">
                                            @if($commission->referred && $commission->referred->photo)
                                                <img src="{{ asset("storage/{$commission->referred->photo}") }}" alt="{{ $commission->referred->name }}" class="rounded-circle" style="width: 100%; height: 100%; object-fit: cover;">
                                            @else
                                                <i class="material-icons-outlined text-primary">person</i>
                                            @endif
                                        </div>
                                        <div>
                                            <div class="fw-medium">{{ $commission->referred->name ?? 'Pengguna Tidak Ditemukan' }}</div>
                                            <small class="text-muted">{{ $commission->referred->email ?? '-' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($commission->payment && $commission->payment->userSubscription && $commission->payment->userSubscription->subscription)
                                        <div class="d-flex align-items-center">
                                            <i class="material-icons-outlined text-primary me-2">card_membership</i>
                                            <span class="fw-medium">{{ $commission->payment->userSubscription->subscription->name }}</span>
                                        </div>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($commission->payment && $commission->payment->userSubscription && $commission->payment->userSubscription->subscription)
                                        <span class="fw-medium">Rp {{ number_format($commission->payment->userSubscription->subscription->price, 0, ',', '.') }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="commission-amount">
                                        Rp {{ number_format($commission->amount, 0, ',', '.') }}
                                    </span>
                                </td>
                                <td>
                                    @if($commission->payment && $commission->payment->status === 'confirmed')
                                        <span class="badge bg-success">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">check_circle</i>
                                            Dikonfirmasi
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">pending</i>
                                            Menunggu
                                        </span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4 flex-wrap">
                <div class="text-muted small mb-2 mb-md-0">
                    <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">info</i>
                    Menampilkan {{ $commissions->firstItem() ?? 0 }} - {{ $commissions->lastItem() ?? 0 }} dari {{ $commissions->total() }} komisi
                </div>
                <div>
                    {{ $commissions->links('vendor.pagination.simple') }}
                </div>
            </div>
        @else
            <div class="empty-state">
                <i class="material-icons-outlined">monetization_on</i>
                <h5>Belum Ada Riwayat Komisi</h5>
                <p>Anda belum memiliki riwayat komisi. Bagikan kode referral Anda untuk mulai mendapatkan komisi!</p>

                <div class="mt-4">
                    <a href="{{ route('subscriptions.my') }}" class="btn btn-primary">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">share</i>
                        Lihat Kode Referral
                    </a>
                </div>

                <div class="mt-4 p-3 bg-light rounded-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="material-icons-outlined me-2 text-warning">lightbulb</i>
                        <h6 class="mb-0 fw-bold">Cara Mendapatkan Komisi:</h6>
                    </div>
                    <ol class="ps-3 mb-0 text-start">
                        <li>Bagikan kode referral Anda kepada teman</li>
                        <li>Teman Anda mendaftar menggunakan kode referral Anda</li>
                        <li>Teman Anda berlangganan paket premium</li>
                        <li>Anda mendapatkan komisi 10% dari nilai langganan</li>
                    </ol>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        // Add pulse animation to empty state icon
        const emptyStateIcon = document.querySelector('.empty-state i');
        if (emptyStateIcon) {
            emptyStateIcon.classList.add('pulse');
        }
    });
</script>
@endsection
