


@extends('layouts.auth')

@section('title', 'Register')

@section('css')
<style>
    /* Card styling */
    .auth-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .auth-card .card-header {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        border-bottom: none;
        padding: 25px 30px;
    }

    .auth-card .card-body {
        padding: 30px;
    }

    /* Form styling */
    .form-label {
        font-weight: 500;
        color: #344767;
        margin-bottom: 8px;
    }

    .form-control {
        padding: 12px 15px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #4481eb;
        box-shadow: 0 0 0 0.2rem rgba(68, 129, 235, 0.1);
    }

    .input-group-text {
        border-radius: 0 8px 8px 0;
        border: 1px solid #e9ecef;
        border-left: none;
    }

    /* Button styling */
    .btn-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(90deg, #3b72d6, #039fe5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(68, 129, 235, 0.3);
    }

    /* Social login buttons */
    .social-login-btn {
        border-radius: 8px;
        padding: 12px 15px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #e9ecef;
        background-color: white;
        color: #344767;
        font-weight: 500;
    }

    .social-login-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .social-login-btn img {
        margin-right: 10px;
        width: 20px;
    }

    /* Separator */
    .separator {
        display: flex;
        align-items: center;
        margin: 25px 0;
    }

    .separator .line {
        flex: 1;
        height: 1px;
        background-color: #e9ecef;
    }

    .separator p {
        margin: 0 15px;
        color: #6c757d;
        font-size: 0.9rem;
    }

    /* Links */
    a {
        color: #4481eb;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: #04befe;
        text-decoration: underline;
    }

    /* Illustration */
    .auth-illustration {
        background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
        border-radius: 12px;
        padding: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    /* Logo */
    .auth-logo {
        margin-bottom: 20px;
    }

    /* Form check */
    .form-check-input:checked {
        background-color: #4481eb;
        border-color: #4481eb;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100 py-5">
        <div class="col-lg-10">
            <div class="card auth-card">
                <div class="row g-0">
                    <!-- Left side - Register Form -->
                    <div class="col-lg-6">
                        <div class="card-body">
                            <div class="auth-logo">
                                <img src="{{ URL::asset('build/images/logo1.png') }}" width="145" alt="TenderID Logo">
                            </div>

                            <h4 class="fw-bold text-dark mb-2">Buat Akun Baru</h4>
                            <p class="text-muted mb-4">Daftar untuk mendapatkan akses ke semua fitur TenderID</p>

                            <form method="POST" action="{{ route('register') }}" class="mb-4">
                                @csrf
                                <div class="mb-3">
                                    <label for="inputUsername" class="form-label">Nama Lengkap</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">person</i>
                                        </span>
                                        <input type="text" class="form-control border-start-0 @error('name') is-invalid @enderror"
                                            id="inputUsername" name="name" value="{{ old('name') }}"
                                            placeholder="Masukkan nama lengkap Anda">
                                    </div>
                                    @error('name')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="inputEmailAddress" class="form-label">Email</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">email</i>
                                        </span>
                                        <input type="email" class="form-control border-start-0 @error('email') is-invalid @enderror"
                                            id="inputEmailAddress" name="email" value="{{ old('email') }}"
                                            placeholder="Masukkan email Anda">
                                    </div>
                                    @error('email')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="inputPassword" class="form-label">Password</label>
                                    <div class="input-group" id="show_hide_password1">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">lock</i>
                                        </span>
                                        <input type="password" class="form-control border-start-0 border-end-0 @error('password') is-invalid @enderror"
                                            id="inputPassword" name="password"
                                            placeholder="Masukkan password Anda">
                                        <a href="javascript:;" class="input-group-text bg-transparent border-start-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">visibility_off</i>
                                        </a>
                                    </div>
                                    @error('password')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-4">
                                    <label for="inputConfirmPassword" class="form-label">Konfirmasi Password</label>
                                    <div class="input-group" id="show_hide_password2">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">lock</i>
                                        </span>
                                        <input type="password" class="form-control border-start-0 border-end-0"
                                            id="inputConfirmPassword" name="password_confirmation"
                                            placeholder="Konfirmasi password Anda">
                                        <a href="javascript:;" class="input-group-text bg-transparent border-start-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">visibility_off</i>
                                        </a>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="termsCheck" required>
                                        <label class="form-check-label" for="termsCheck">
                                            Saya menyetujui <a href="#" class="fw-medium">Syarat & Ketentuan</a> yang berlaku
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid mb-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="material-icons-outlined me-2" style="font-size: 1.2rem; vertical-align: middle;">how_to_reg</i>
                                        Daftar Sekarang
                                    </button>
                                </div>

                                <div class="text-center">
                                    <p class="mb-0">Sudah punya akun? <a href="{{ route('login') }}" class="fw-medium">Masuk di sini</a></p>
                                </div>
                            </form>

                            <div class="separator">
                                <div class="line"></div>
                                <p>ATAU</p>
                                <div class="line"></div>
                            </div>

                            <div class="social-login mt-4">
                                <button class="social-login-btn w-100 mb-3">
                                    <img src="{{ URL::asset('build/images/apps/05.png') }}" alt="Google">
                                    Daftar dengan Google
                                </button>
                                <button class="social-login-btn w-100">
                                    <img src="{{ URL::asset('build/images/apps/17.png') }}" alt="Facebook">
                                    Daftar dengan Facebook
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Right side - Illustration -->
                    <div class="col-lg-6 d-none d-lg-block">
                        <div class="auth-illustration">
                            <img src="{{ URL::asset('build/images/auth/register1.png') }}" class="img-fluid" alt="Register Illustration">
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <p class="text-muted small">© {{ date('Y') }} TenderID. All rights reserved.</p>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Toggle password visibility for first password field
        $("#show_hide_password1 a").on('click', function(event) {
            event.preventDefault();
            if ($('#show_hide_password1 input').attr("type") == "text") {
                $('#show_hide_password1 input').attr('type', 'password');
                $('#show_hide_password1 a i').text('visibility_off');
            } else if ($('#show_hide_password1 input').attr("type") == "password") {
                $('#show_hide_password1 input').attr('type', 'text');
                $('#show_hide_password1 a i').text('visibility');
            }
        });

        // Toggle password visibility for confirm password field
        $("#show_hide_password2 a").on('click', function(event) {
            event.preventDefault();
            if ($('#show_hide_password2 input').attr("type") == "text") {
                $('#show_hide_password2 input').attr('type', 'password');
                $('#show_hide_password2 a i').text('visibility_off');
            } else if ($('#show_hide_password2 input').attr("type") == "password") {
                $('#show_hide_password2 input').attr('type', 'text');
                $('#show_hide_password2 a i').text('visibility');
            }
        });
    });
</script>
@endsection