

@extends('layouts.auth')

@section('title', 'Lupa Password')

@section('css')
<style>
    /* Card styling */
    .auth-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .auth-card .card-body {
        padding: 30px;
    }

    /* Form styling */
    .form-label {
        font-weight: 500;
        color: #344767;
        margin-bottom: 8px;
    }

    .form-control {
        padding: 12px 15px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #4481eb;
        box-shadow: 0 0 0 0.2rem rgba(68, 129, 235, 0.1);
    }

    .input-group-text {
        border-radius: 0 8px 8px 0;
        border: 1px solid #e9ecef;
        border-left: none;
    }

    /* Button styling */
    .btn-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(90deg, #3b72d6, #039fe5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(68, 129, 235, 0.3);
    }

    .btn-outline-secondary {
        border: 1px solid #6c757d;
        color: #6c757d;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        background-color: transparent;
    }

    .btn-outline-secondary:hover {
        background-color: #f8f9fa;
        color: #343a40;
        transform: translateY(-2px);
    }

    /* Links */
    a {
        color: #4481eb;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: #04befe;
        text-decoration: underline;
    }

    /* Illustration */
    .auth-illustration {
        background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
        border-radius: 12px;
        padding: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    /* Logo */
    .auth-logo {
        margin-bottom: 20px;
    }

    /* Alert */
    .alert-success {
        background-color: rgba(46, 204, 113, 0.1);
        border-color: rgba(46, 204, 113, 0.2);
        color: #2ecc71;
        border-radius: 8px;
        padding: 15px;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100 py-5">
        <div class="col-lg-10">
            <div class="card auth-card">
                <div class="row g-0">
                    <!-- Left side - Forgot Password Form -->
                    <div class="col-lg-6">
                        <div class="card-body">
                            <div class="auth-logo">
                                <img src="{{ URL::asset('build/images/logo1.png') }}" width="145" alt="TenderID Logo">
                            </div>

                            <h4 class="fw-bold text-dark mb-2">Lupa Password?</h4>
                            <p class="text-muted mb-4">Masukkan email terdaftar Anda untuk mendapatkan link reset password</p>

                            @if (session('status'))
                                <div class="alert alert-success mb-4" role="alert">
                                    <i class="material-icons-outlined me-2" style="font-size: 1.2rem; vertical-align: middle;">check_circle</i>
                                    {{ session('status') }}
                                </div>
                            @endif

                            <form method="POST" action="{{ route('password.email') }}" class="mb-4">
                                @csrf
                                <div class="mb-4">
                                    <label for="inputEmailAddress" class="form-label">Email</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">email</i>
                                        </span>
                                        <input type="email" class="form-control border-start-0 @error('email') is-invalid @enderror"
                                            id="inputEmailAddress" name="email" value="{{ old('email') }}"
                                            placeholder="Masukkan email terdaftar Anda">
                                    </div>
                                    @error('email')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="d-grid gap-2 mb-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="material-icons-outlined me-2" style="font-size: 1.2rem; vertical-align: middle;">send</i>
                                        Kirim Link Reset
                                    </button>
                                    <a href="{{ route('login') }}" class="btn btn-outline-secondary">
                                        <i class="material-icons-outlined me-2" style="font-size: 1.2rem; vertical-align: middle;">arrow_back</i>
                                        Kembali ke Login
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Right side - Illustration -->
                    <div class="col-lg-6 d-none d-lg-block">
                        <div class="auth-illustration">
                            <img src="{{ URL::asset('build/images/auth/forgot-password1.png') }}" class="img-fluid" alt="Forgot Password Illustration">
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <p class="text-muted small">© {{ date('Y') }} TenderID. All rights reserved.</p>
            </div>
        </div>
    </div>
</div>
@endsection
