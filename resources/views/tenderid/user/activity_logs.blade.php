@extends('layouts.master')

@section('title', 'User Activity Logs')

@section('css')
<style>
    .filter-toggle-icon {
        transition: transform 0.3s ease;
    }

    .collapsed .filter-toggle-icon {
        transform: rotate(180deg);
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    .product-box {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        overflow: hidden;
        background-color: #f8f9fa;
    }

    .product-box img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .product-info .product-title {
        font-weight: 500;
        color: #344767;
        text-decoration: none;
        display: block;
        margin-bottom: 0.25rem;
    }

    .product-info .product-category {
        font-size: 0.8125rem;
        color: #6c757d;
    }

    .url-cell {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .method-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
    }

    .bg-light-success {
        background-color: rgba(25, 135, 84, 0.1);
    }
</style>
@endsection

@section('content')
<x-page-title title="User Activity Logs" pagetitle="User Management" />

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
        <h5 class="card-title mb-0">
            <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
                <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
                <span>Filter Activity Logs</span>
                <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
            </a>
        </h5>
        <span class="badge bg-white text-primary" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">history</i>
            {{ number_format($logs->total(), 0, ',', '.') }} Logs
        </span>
    </div>
    <div class="collapse show" id="filterCollapse">
        <div class="card-body">
            <form method="GET" action="{{ route('user.activity-logs') }}">
                <div class="row g-3">
                    <!-- User Filter -->
                    <div class="col-md-3">
                        <label for="user_id" class="form-label">Select User</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">person</i></span>
                            <select name="user_id" id="user_id" class="form-select">
                                <option value="">All Users</option>
                                @foreach ($users as $user)
                                    <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Date Filter -->
                    <div class="col-md-3">
                        <label for="date" class="form-label">Date</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">calendar_today</i></span>
                            <input type="date" name="date" id="date" class="form-control" value="{{ request('date') }}">
                        </div>
                    </div>

                    <!-- Method Filter -->
                    <div class="col-md-2">
                        <label for="method" class="form-label">HTTP Method</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">call_made</i></span>
                            <select name="method" id="method" class="form-select">
                                <option value="">All</option>
                                <option value="GET" {{ request('method') == 'GET' ? 'selected' : '' }}>GET</option>
                                <option value="POST" {{ request('method') == 'POST' ? 'selected' : '' }}>POST</option>
                                <option value="PUT" {{ request('method') == 'PUT' ? 'selected' : '' }}>PUT</option>
                                <option value="DELETE" {{ request('method') == 'DELETE' ? 'selected' : '' }}>DELETE</option>
                            </select>
                        </div>
                    </div>

                    <!-- URL Search -->
                    <div class="col-md-2">
                        <label for="url" class="form-label">URL Contains</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">link</i></span>
                            <input type="text" name="url" id="url" class="form-control" placeholder="Search..." value="{{ request('url') }}">
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="col-md-2 d-flex align-items-end">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">search</i>
                                Apply
                            </button>
                            <a href="{{ route('user.activity-logs') }}" class="btn btn-outline-secondary ms-2">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">refresh</i>
                                Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Activity Logs Table Section -->
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5 class="card-title mb-0">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">history</i>
            Activity Logs
        </h5>
    </div>
    <div class="card-body p-0">
        @if($logs->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>URL</th>
                            <th>Method</th>
                            <th>IP Address</th>
                            <th>Time</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($logs as $log)
                            <tr class="{{ $log->created_at->isToday() ? 'bg-light-success' : '' }}">
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="product-box bg-light-primary">
                                            @if($log->user && $log->user->photo)
                                                <img src="{{ asset("storage/{$log->user->photo}") }}" alt="{{ $log->user->name }}" class="rounded-3">
                                            @else
                                                <i class="material-icons-outlined text-primary">person</i>
                                            @endif
                                        </div>
                                        <div>
                                            <span class="fw-medium">{{ $log->user->name ?? 'N/A' }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="url-cell" title="{{ $log->url }}">
                                        {{ $log->url }}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge method-badge bg-{{ $log->method == 'GET' ? 'info' : ($log->method == 'POST' ? 'success' : ($log->method == 'PUT' ? 'warning' : ($log->method == 'DELETE' ? 'danger' : 'secondary'))) }}">
                                        {{ $log->method }}
                                    </span>
                                </td>
                                <td>{{ $log->ip_address }}</td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span>{{ $log->created_at->format('d M Y') }}</span>
                                        <small class="text-muted">{{ $log->created_at->format('H:i:s') }}</small>
                                        @if($log->created_at->isToday())
                                            <span class="badge bg-success mt-1">Today</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#logModal{{ $log->id }}">
                                        <i class="material-icons-outlined" style="font-size: 1rem;">visibility</i>
                                    </button>

                                    <!-- Log Details Modal -->
                                    <div class="modal fade" id="logModal{{ $log->id }}" tabindex="-1" aria-labelledby="logModalLabel{{ $log->id }}" aria-hidden="true">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="logModalLabel{{ $log->id }}">Log Details</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h6 class="fw-bold">User</h6>
                                                            <p>{{ $log->user->name ?? 'N/A' }}</p>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6 class="fw-bold">Time</h6>
                                                            <p>{{ $log->created_at->format('d M Y H:i:s') }}</p>
                                                        </div>
                                                    </div>
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h6 class="fw-bold">Method</h6>
                                                            <p>{{ $log->method }}</p>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6 class="fw-bold">IP Address</h6>
                                                            <p>{{ $log->ip_address }}</p>
                                                        </div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <h6 class="fw-bold">URL</h6>
                                                        <p class="text-break">{{ $log->url }}</p>
                                                    </div>
                                                    <div class="mb-3">
                                                        <h6 class="fw-bold">User Agent</h6>
                                                        <p class="text-break">{{ $log->user_agent }}</p>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <div class="text-muted small">
                    Menampilkan {{ $logs->firstItem() ?? 0 }} - {{ $logs->lastItem() ?? 0 }} dari {{ $logs->total() }} logs
                </div>
                <div>
                    {{ $logs->links('vendor.pagination.simple') }}
                </div>
            </div>
        @else
            <div class="empty-state">
                <i class="material-icons-outlined">search_off</i>
                <h5>No Activity Logs Found</h5>
                <p>Try adjusting your search or filter criteria</p>
            </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle filter collapse icon
        const filterCollapse = document.getElementById('filterCollapse');
        const filterToggleIcon = document.querySelector('.filter-toggle-icon');

        filterCollapse.addEventListener('hidden.bs.collapse', function () {
            filterToggleIcon.textContent = 'expand_more';
        });

        filterCollapse.addEventListener('shown.bs.collapse', function () {
            filterToggleIcon.textContent = 'expand_less';
        });
    });
</script>
@endsection