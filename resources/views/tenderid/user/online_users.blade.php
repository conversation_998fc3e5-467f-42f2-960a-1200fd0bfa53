@extends('layouts.master')

@section('title', 'Pengguna Online')

@section('css')
<style>
    /* Card Styles */
    .card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s;
        border: none;
    }

    /* Header Styles */
    .card-header-blue {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        padding: 16px 20px;
    }

    /* Table Styles */
    .table th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }

    /* User Avatar Styles */
    .product-box {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        overflow: hidden;
    }

    .product-box img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* User Info Styles */
    .product-info .product-title {
        font-weight: 500;
        color: #344767;
        text-decoration: none;
        display: block;
        margin-bottom: 0.25rem;
    }

    .product-info .product-category {
        font-size: 0.8125rem;
        color: #6c757d;
    }

    /* Status Badge Styles */
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* Empty State Styles */
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Animation for pulse */
    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.05);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .pulse {
        animation: pulse 2s infinite;
    }
</style>
@endsection

@section('content')
<!-- Online Users Card -->
<div class="card">
    <div class="card-header card-header-blue d-flex justify-content-between align-items-center">
        <h5 class="mb-0 text-white">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">people</i>
            Pengguna Online
        </h5>
        <span class="badge bg-primary text-white" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">people</i>
            {{ number_format($onlineUsers->total(), 0, ',', '.') }} Pengguna
        </span>
    </div>
    <div class="card-body p-0">
        @if($onlineUsers->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th>ID</th>
                            <th>Pengguna</th>
                            <th>Status</th>
                            <th>Terakhir Dilihat</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($onlineUsers as $user)
                            <tr>
                                <td class="fw-semibold" style="color: #4481eb;">
                                    #{{ $user->id }}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="product-box bg-light-primary">
                                            @if($user->photo)
                                                <img src="{{ asset("storage/{$user->photo}") }}" alt="{{ $user->name }}" class="rounded-3">
                                            @else
                                                <i class="material-icons-outlined" style="color: #4481eb;">person</i>
                                            @endif
                                        </div>
                                        <div class="product-info">
                                            <span class="product-title">{{ $user->name }}</span>
                                            <p class="mb-0 product-category">{{ $user->email }}</p>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($user->last_seen->gt(now()->subMinutes(5)))
                                        <span class="status-badge bg-primary text-white">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">circle</i>
                                            Online
                                        </span>
                                    @else
                                        <span class="status-badge bg-info text-white">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">schedule</i>
                                            Idle
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="fw-medium">{{ $user->last_seen->format('d M Y') }}</span>
                                        <small class="text-muted">{{ $user->last_seen->format('H:i') }} WIB ({{ $user->last_seen->diffForHumans() }})</small>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <div class="text-muted small">
                    Menampilkan {{ $onlineUsers->firstItem() ?? 0 }} - {{ $onlineUsers->lastItem() ?? 0 }} dari {{ $onlineUsers->total() }} pengguna
                </div>
                <div>
                    {{ $onlineUsers->links('vendor.pagination.simple') }}
                </div>
            </div>
        @else
            <div class="empty-state">
                <i class="material-icons-outlined pulse" style="color: #4481eb;">people_off</i>
                <h5 style="color: #4481eb;">Tidak Ada Pengguna Online</h5>
                <p>Saat ini tidak ada pengguna yang aktif dalam 60 menit terakhir.</p>
            </div>
        @endif
    </div>
</div>
@endsection