@extends('layouts.master')

@section('title', '<PERSON><PERSON><PERSON>gguna')

@section('css')
<style>
    /* Card Styles */
    .card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s;
        border: none;
    }

    /* Header Styles */
    .card-header-blue {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        padding: 16px 20px;
    }

    /* Table Styles */
    .table th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }

    /* User Avatar Styles */
    .product-box {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        overflow: hidden;
    }

    .product-box img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* User Info Styles */
    .product-info .product-title {
        font-weight: 500;
        color: #344767;
        text-decoration: none;
        display: block;
        margin-bottom: 0.25rem;
    }

    .product-info .product-category {
        font-size: 0.8125rem;
        color: #6c757d;
    }

    /* Status Badge Styles */
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* Duration Badge Styles */
    .duration-badge {
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 500;
        background-color: rgba(68, 129, 235, 0.1);
        color: #4481eb;
    }

    /* Empty State Styles */
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Animation for pulse */
    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.05);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .pulse {
        animation: pulse 2s infinite;
    }
</style>
@endsection

@section('content')
<x-page-title title="Durasi Login Pengguna" pagetitle="Manajemen Pengguna" />

<!-- Login Durations Card -->
<div class="card">
    <div class="card-header card-header-blue d-flex justify-content-between align-items-center">
        <h5 class="mb-0 text-white">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">timer</i>
            Durasi Login Pengguna
        </h5>
        <span class="badge bg-primary text-white" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">history</i>
            {{ number_format($logs->total(), 0, ',', '.') }} Catatan
        </span>
    </div>
    <div class="card-body p-0">
        @if($logs->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th>Pengguna</th>
                            <th>Waktu Login</th>
                            <th>Waktu Logout</th>
                            <th>Durasi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($logs as $log)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="product-box bg-light-primary">
                                            @if($log->user && $log->user->photo)
                                                <img src="{{ asset("storage/{$log->user->photo}") }}" alt="{{ $log->user->name }}" class="rounded-3">
                                            @else
                                                <i class="material-icons-outlined" style="color: #4481eb;">person</i>
                                            @endif
                                        </div>
                                        <div class="product-info">
                                            <span class="product-title">{{ $log->user ? $log->user->name : 'Pengguna Dihapus' }}</span>
                                            <p class="mb-0 product-category">{{ $log->user ? $log->user->email : '-' }}</p>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="fw-medium">{{ \Carbon\Carbon::parse($log->login_at)->format('d M Y') }}</span>
                                        <small class="text-muted">{{ \Carbon\Carbon::parse($log->login_at)->format('H:i') }} WIB</small>
                                    </div>
                                </td>
                                <td>
                                    @if($log->logout_at)
                                        <div class="d-flex flex-column">
                                            <span class="fw-medium">{{ \Carbon\Carbon::parse($log->logout_at)->format('d M Y') }}</span>
                                            <small class="text-muted">{{ \Carbon\Carbon::parse($log->logout_at)->format('H:i') }} WIB</small>
                                        </div>
                                    @else
                                        <span class="status-badge bg-primary text-white">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.7rem; vertical-align: middle;">circle</i>
                                            Masih Login
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    @if($log->logout_at)
                                        @php
                                            $minutes = \Carbon\Carbon::parse($log->login_at)->diffInMinutes(\Carbon\Carbon::parse($log->logout_at));
                                            $hours = floor($minutes / 60);
                                            $remainingMinutes = $minutes % 60;
                                        @endphp

                                        @if($hours > 0)
                                            <span class="duration-badge">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">schedule</i>
                                                {{ $hours }} jam {{ $remainingMinutes }} menit
                                            </span>
                                        @else
                                            <span class="duration-badge">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: sub;">schedule</i>
                                                {{ $minutes }} menit
                                            </span>
                                        @endif
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <div class="text-muted small">
                    Menampilkan {{ $logs->firstItem() ?? 0 }} - {{ $logs->lastItem() ?? 0 }} dari {{ $logs->total() }} catatan
                </div>
                <div>
                    {{ $logs->links('vendor.pagination.simple') }}
                </div>
            </div>
        @else
            <div class="empty-state">
                <i class="material-icons-outlined pulse" style="color: #4481eb;">timer_off</i>
                <h5 style="color: #4481eb;">Tidak Ada Catatan Login</h5>
                <p>Belum ada catatan durasi login pengguna yang tersedia.</p>
            </div>
        @endif
    </div>
</div>
@endsection