@extends('layouts.master')

@section('title', 'Sync Logs')

@section('css')
<style>
    .table tr.bg-warning-important td {
        background-color: #ffcc00;
    }

    .product-box img {
        transition: transform 0.3s ease;
    }

    .product-box img:hover {
        transform: scale(1.1);
    }

    .product-title {
        font-weight: bold;
        color: #007bff;
    }

    .product-title:hover {
        text-decoration: underline;
    }

    .product-category a {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        color: #007bff;
    }

    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 8px;
    }

    .btn-filter {
        background-color: #007bff;
        color: white;
    }

    .btn-filter:hover {
        background-color: #0056b3;
    }

    /* Pagination Custom */
    .pagination .page-link {
        color: #007bff;
    }

    .pagination .page-link:hover {
        background-color: #f1f1f1;
    }

    .pagination .active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }
</style>
@endsection

@section('content')
<x-page-title title="Sync Logs" pagetitle="Log Sinkronisasi" />

<!-- Search and Filter Section -->
<div class="row g-3 mb-4 align-items-center">
    <!-- Search Field -->
    <div class="col-md-3">
        <form method="GET" action="{{ route('sync_log') }}" class="d-flex align-items-center">
            <input class="form-control me-2" type="search" name="search" placeholder="Search"
                value="{{ request('search') }}">
            <button class="btn btn-primary" type="submit">Search</button>
        </form>
    </div>

    <!-- Filter Actions -->
    <div class="col-md-3">
    <form method="GET" action="{{ route('sync_log') }}">
        <input type="hidden" name="table" value="{{ request('table') }}">
        <select class="form-select" name="action" onchange="this.form.submit()">
            <option value="">All Actions</option>
            @foreach ($actions as $action)
                <option value="{{ $action }}" {{ request('action') == $action ? 'selected' : '' }}>
                    {{ ucfirst($action) }}
                </option>
            @endforeach
        </select>
        </form>
    </div>

    <!-- Filter Tables -->
    <div class="col-md-3">
    <form method="GET" action="{{ route('sync_log') }}">
        <input type="hidden" name="action" value="{{ request('action') }}">
        <select class="form-select" name="table" onchange="this.form.submit()">
            <option value="">All Tables</option>
            @foreach ($tables as $table)
                <option value="{{ $table }}" {{ request('table') == $table ? 'selected' : '' }}>
                    {{ ucfirst($table) }}
                </option>
            @endforeach
        </select>
        </form>
    </div>

    <!-- Reset Button -->
    <div class="col-md-3 text-end">
        <a href="{{ route('sync_log') }}" class="btn btn-secondary">
            Reset Filters
        </a>
    </div>
</div>

<!-- Sync Logs Table Section -->
<div class="card mt-4">
    <div class="card-body">
        <div class="table-responsive white-space-nowrap">
            <table class="table align-middle">
                <thead class="table-light">
                    <tr>
                        <th><a href="{{ route('sync_log', array_merge(request()->all(), ['sort_by' => 'id', 'sort_direction' => request('sort_direction') == 'asc' ? 'desc' : 'asc'])) }}">ID</a></th>
                        <th><a href="{{ route('sync_log', array_merge(request()->all(), ['sort_by' => 'record_id', 'sort_direction' => request('sort_direction') == 'asc' ? 'desc' : 'asc'])) }}">Record ID</a></th>
                        <th>Action</th>
                        <th>Changes</th>
                        <th>Synced At</th>
                        <th>Table</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($syncLogs as $log)
                        <tr>
                            <td>{{ $log->id }}</td>
                            <td>{{ $log->record_id }}</td>
                            <td>{{ $log->action }}</td>
                            <td>{{ $log->changes }}</td>
                            <td>{{ $log->synced_at }}</td>
                            <td>{{ $log->table }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6">No records found.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="d-flex justify-content-center">
            {{ $syncLogs->links('vendor.pagination.costum') }}
            </div>

        </div>
    </div>
</div>
@endsection
