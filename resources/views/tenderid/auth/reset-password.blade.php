@extends('layouts.auth')

@section('title', 'Ubah Password')

@section('css')
<style>
    /* Card styling */
    .auth-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .auth-card .card-header {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        border-bottom: none;
        padding: 25px 30px;
    }

    .auth-card .card-body {
        padding: 30px;
    }

    /* Form styling */
    .form-label {
        font-weight: 500;
        color: #344767;
        margin-bottom: 8px;
    }

    .form-control {
        padding: 12px 15px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #4481eb;
        box-shadow: 0 0 0 0.2rem rgba(68, 129, 235, 0.1);
    }

    /* Error message styling */
    .invalid-feedback {
        color: #ff5a5f;
        font-size: 0.85rem;
        margin-top: 0.5rem;
        padding: 0.5rem;
        border-radius: 6px;
        background-color: rgba(255, 90, 95, 0.08);
        border-left: 3px solid #ff5a5f;
    }

    .alert-success {
        background-color: rgba(25, 135, 84, 0.1);
        border: none;
        border-left: 4px solid #198754;
        color: #198754;
        border-radius: 6px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* Button styling */
    .btn-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-primary:hover {
        background: linear-gradient(90deg, #3b72d6, #039fe5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(68, 129, 235, 0.3);
    }

    .btn-primary:disabled {
        background: linear-gradient(90deg, #4481eb, #04befe);
        opacity: 0.7;
    }

    .btn-light {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-light:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }

    .btn-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        border: 3px solid transparent;
        border-top-color: #ffffff;
        border-radius: 50%;
        animation: button-loading-spinner 1s ease infinite;
    }

    .btn-loading span {
        visibility: hidden;
        opacity: 0;
    }

    @keyframes button-loading-spinner {
        from {
            transform: rotate(0turn);
        }
        to {
            transform: rotate(1turn);
        }
    }

    /* Illustration */
    .auth-illustration {
        background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
        border-radius: 12px;
        padding: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    /* Logo */
    .auth-logo {
        margin-bottom: 20px;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100 py-5">
        <div class="col-lg-10">
            <div class="card auth-card">
                <div class="row g-0">
                    <!-- Left side - Form -->
                    <div class="col-lg-6">
                        <div class="card-body">
                            <div class="auth-logo">
                                <img src="{{ URL::asset('build/images/logo1.png') }}" width="145" alt="TenderID Logo">
                            </div>

                            <h4 class="fw-bold text-dark mb-2">Ubah Password</h4>
                            <p class="text-muted mb-4">Silakan masukkan password baru Anda.</p>

                            <form method="POST" action="{{ route('password.update') }}" class="mb-4">
                                @csrf
                                <input type="hidden" name="token" value="{{ $token ?? request()->route('token') }}">

                                <div class="mb-3">
                                    <label for="Email" class="form-label">Email</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">email</i>
                                        </span>
                                        <input type="email" class="form-control border-start-0 bg-light"
                                            id="Email" value="{{ request()->email ?? request()->query('email') }}"
                                            placeholder="Email Anda" readonly>
                                        <input type="hidden" name="email" value="{{ request()->email ?? request()->query('email') }}">
                                    </div>
                                    <small class="text-muted">Email tidak dapat diubah pada tahap ini.</small>
                                    @error('email')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="NewPassword" class="form-label">Password Baru</label>
                                    <div class="input-group" id="show_hide_password">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">lock</i>
                                        </span>
                                        <input type="password" class="form-control border-start-0 border-end-0 @error('password') is-invalid @enderror"
                                            id="NewPassword" name="password"
                                            placeholder="Masukkan password baru" required>
                                        <a href="javascript:;" class="input-group-text bg-transparent border-start-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">visibility_off</i>
                                        </a>
                                    </div>
                                    @error('password')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-4">
                                    <label for="ConfirmPassword" class="form-label">Konfirmasi Password</label>
                                    <div class="input-group" id="show_hide_password_confirm">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">lock</i>
                                        </span>
                                        <input type="password" class="form-control border-start-0 border-end-0"
                                            id="ConfirmPassword" name="password_confirmation"
                                            placeholder="Konfirmasi password baru" required>
                                        <a href="javascript:;" class="input-group-text bg-transparent border-start-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">visibility_off</i>
                                        </a>
                                    </div>
                                </div>

                                <div class="d-grid mb-4">
                                    <button type="submit" class="btn btn-primary" id="reset-button">
                                        <span>
                                            <i class="material-icons-outlined me-2" style="font-size: 1.2rem; vertical-align: middle;">lock_reset</i>
                                            Ubah Password
                                        </span>
                                    </button>
                                </div>

                                <div class="text-center">
                                    <p class="mb-0">Ingat password Anda? <a href="{{ route('login') }}" class="fw-medium">Masuk di sini</a></p>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Right side - Illustration -->
                    <div class="col-lg-6 d-none d-lg-block">
                        <div class="auth-illustration">
                            <img src="{{ URL::asset('build/images/auth/reset-password1.png') }}" class="img-fluid" alt="Reset Password Illustration">
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <p class="text-muted small">© {{ date('Y') }} TenderID. All rights reserved.</p>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Toggle password visibility for password field
        $("#show_hide_password a").on('click', function(event) {
            event.preventDefault();
            const passwordInput = $('#NewPassword');
            const toggleIcon = $(this).find('i');

            if (passwordInput.attr("type") === "text") {
                passwordInput.attr('type', 'password');
                toggleIcon.text('visibility_off');
            } else {
                passwordInput.attr('type', 'text');
                toggleIcon.text('visibility');
            }
        });

        // Toggle password visibility for confirm password field
        $("#show_hide_password_confirm a").on('click', function(event) {
            event.preventDefault();
            const passwordInput = $('#ConfirmPassword');
            const toggleIcon = $(this).find('i');

            if (passwordInput.attr("type") === "text") {
                passwordInput.attr('type', 'password');
                toggleIcon.text('visibility_off');
            } else {
                passwordInput.attr('type', 'text');
                toggleIcon.text('visibility');
            }
        });

        // Form submission with loading animation
        $('form').on('submit', function() {
            const resetButton = $('#reset-button');

            // Prevent double submission
            if (resetButton.hasClass('btn-loading') || resetButton.prop('disabled')) {
                return false;
            }

            // Add loading state
            resetButton.addClass('btn-loading').prop('disabled', true);

            // Allow form submission
            return true;
        });
    });
</script>
@endsection
