@extends('layouts.auth')

@section('title', '<PERSON><PERSON>')

@section('css')
<style>
    /* Card styling */
    .auth-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .auth-card .card-header {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        border-bottom: none;
        padding: 25px 30px;
    }

    .auth-card .card-body {
        padding: 30px;
    }

    /* Alert styling */
    .alert-success {
        background-color: rgba(25, 135, 84, 0.1);
        border: none;
        border-left: 4px solid #198754;
        color: #198754;
        border-radius: 6px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* Button styling */
    .btn-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-primary:hover {
        background: linear-gradient(90deg, #3b72d6, #039fe5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(68, 129, 235, 0.3);
    }

    /* Illustration */
    .auth-illustration {
        background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
        border-radius: 12px;
        padding: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    /* Logo */
    .auth-logo {
        margin-bottom: 20px;
    }

    /* Links */
    a {
        color: #4481eb;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: #04befe;
        text-decoration: underline;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100 py-5">
        <div class="col-lg-10">
            <div class="card auth-card">
                <div class="row g-0">
                    <!-- Left side - Content -->
                    <div class="col-lg-6">
                        <div class="card-body">
                            <div class="auth-logo">
                                <img src="{{ URL::asset('build/images/logo1.png') }}" width="145" alt="TenderID Logo">
                            </div>

                            <h4 class="fw-bold text-dark mb-2">Email Anda Berhasil Diverifikasi!</h4>
                            <p class="text-muted mb-4">Terima kasih telah memverifikasi email Anda. Akun Anda sekarang aktif, dan Anda dapat melanjutkan ke dashboard kami.</p>

                            <div class="alert alert-success mb-4">
                                <div class="d-flex align-items-center">
                                    <i class="material-icons-outlined me-2" style="font-size: 1.5rem;">check_circle</i>
                                    <div>
                                        <h6 class="mb-1 fw-bold">Berhasil!</h6>
                                        <p class="mb-0">Email Anda telah berhasil diverifikasi.</p>
                                    </div>
                                </div>
                            </div>

                            <p class="mb-4">Jika Anda ingin mengunjungi halaman beranda atau masuk ke akun Anda, silakan klik tombol di bawah ini:</p>

                            <div class="d-grid mb-4">
                                <a href="{{ route('dashboard') }}" class="btn btn-primary">
                                    <i class="material-icons-outlined me-2" style="font-size: 1.2rem; vertical-align: middle;">dashboard</i>
                                    Lanjut ke Dashboard
                                </a>
                            </div>

                            <div class="text-center">
                                <p class="mb-0">Atau, jika Anda ingin <a href="{{ route('login') }}" class="fw-medium">masuk di sini</a>.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Right side - Illustration -->
                    <div class="col-lg-6 d-none d-lg-block">
                        <div class="auth-illustration">
                            <img src="{{ URL::asset('build/images/auth/reset-password1.png') }}" class="img-fluid" alt="Verification Success Illustration">
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <p class="text-muted small">© {{ date('Y') }} TenderID. All rights reserved.</p>
            </div>
        </div>
    </div>
</div>
@endsection
