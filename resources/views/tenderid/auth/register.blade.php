@extends('layouts.auth')

@section('title', 'Register')

@section('css')
<style>
    /* Card styling */
    .auth-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .auth-card .card-body {
        padding: 30px;
    }

    /* Form styling */
    .form-label {
        font-weight: 500;
        color: #344767;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        padding: 12px 15px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4481eb;
        box-shadow: 0 0 0 0.2rem rgba(68, 129, 235, 0.1);
    }

    .input-group-text {
        border-radius: 0 8px 8px 0;
        border: 1px solid #e9ecef;
        border-left: none;
    }

    /* Error message styling */
    .invalid-feedback {
        color: #ff5a5f;
        font-size: 0.85rem;
        margin-top: 0.5rem;
        padding: 0.5rem;
        border-radius: 6px;
        background-color: rgba(255, 90, 95, 0.08);
        border-left: 3px solid #ff5a5f;
    }

    .alert-danger {
        background-color: rgba(255, 90, 95, 0.1);
        border: none;
        border-left: 4px solid #ff5a5f;
        color: #d63939;
        border-radius: 6px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .alert-danger ul {
        padding-left: 1rem;
    }

    /* Button styling */
    .btn-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-primary:hover {
        background: linear-gradient(90deg, #3b72d6, #039fe5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(68, 129, 235, 0.3);
    }

    .btn-primary:disabled {
        background: linear-gradient(90deg, #4481eb, #04befe);
        opacity: 0.7;
    }

    .btn-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        border: 3px solid transparent;
        border-top-color: #ffffff;
        border-radius: 50%;
        animation: button-loading-spinner 1s ease infinite;
    }

    .btn-loading span {
        visibility: hidden;
        opacity: 0;
    }

    @keyframes button-loading-spinner {
        from {
            transform: rotate(0turn);
        }
        to {
            transform: rotate(1turn);
        }
    }

    /* Links */
    a {
        color: #4481eb;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: #04befe;
        text-decoration: underline;
    }

    /* Illustration */
    .auth-illustration {
        background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
        border-radius: 12px;
        padding: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    /* Logo */
    .auth-logo {
        margin-bottom: 20px;
    }

    /* Form check */
    .form-check-input:checked {
        background-color: #4481eb;
        border-color: #4481eb;
    }

    /* Alert styling */
    .alert-danger {
        background-color: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.2);
        color: #dc3545;
        border-radius: 8px;
        padding: 15px;
    }

    .invalid-feedback {
        color: #dc3545;
        display: block;
        margin-top: 5px;
        font-size: 0.85rem;
    }

    /* Additional fields */
    #additional-fields {
        background-color: rgba(68, 129, 235, 0.05);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px dashed rgba(68, 129, 235, 0.2);
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row justify-content-center align-items-center py-5">
        <div class="col-lg-10">
            <div class="card auth-card">
                <div class="row g-0">
                    <!-- Left side - Register Form -->
                    <div class="col-lg-6">
                        <div class="card-body">
                            <div class="auth-logo">
                                <img src="{{ asset('build/images/logo1.png') }}" width="145" alt="TenderID Logo">
                            </div>

                            <h4 class="fw-bold text-dark mb-2">Buat Akun Baru</h4>
                            <p class="text-muted mb-4">Daftar untuk mendapatkan akses ke semua fitur TenderID dan mulai perjalanan berharga bersama kami</p>

                            <!-- Menampilkan pesan error jika ada -->
                            @if ($errors->any())
                                <div class="alert alert-danger mb-4">
                                    <div class="d-flex align-items-center">
                                        <i class="material-icons-outlined me-2" style="font-size: 1.5rem;">error_outline</i>
                                        <div>
                                            <h6 class="mb-1 fw-bold">Terjadi Kesalahan</h6>
                                            <ul class="mb-0">
                                                @foreach ($errors->all() as $error)
                                                    <li>{{ $error }}</li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('register') }}" enctype="multipart/form-data" class="mb-4">
                                @csrf
                                <div class="mb-3">
                                    <label for="inputUsername" class="form-label">Nama Lengkap</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">person</i>
                                        </span>
                                        <input type="text" class="form-control border-start-0 @error('name') is-invalid @enderror"
                                            id="inputUsername" name="name" value="{{ old('name') }}"
                                            placeholder="Masukkan nama lengkap Anda">
                                    </div>
                                    @error('name')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="inputEmailAddress" class="form-label">Email</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">email</i>
                                        </span>
                                        <input type="email" class="form-control border-start-0 @error('email') is-invalid @enderror"
                                            id="inputEmailAddress" name="email" value="{{ old('email') }}"
                                            placeholder="Masukkan email Anda">
                                    </div>
                                    @error('email')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="inputChoosePassword" class="form-label">Password</label>
                                    <div class="input-group" id="show_hide_password">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">lock</i>
                                        </span>
                                        <input type="password" class="form-control border-start-0 border-end-0 @error('password') is-invalid @enderror"
                                            id="inputChoosePassword" name="password"
                                            placeholder="Masukkan password Anda">
                                        <a href="javascript:;" class="input-group-text bg-transparent border-start-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">visibility_off</i>
                                        </a>
                                    </div>
                                    @error('password')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="inputConfirmPassword" class="form-label">Konfirmasi Password</label>
                                    <div class="input-group" id="show_hide_password_confirm">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">lock</i>
                                        </span>
                                        <input type="password" class="form-control border-start-0 border-end-0"
                                            id="inputConfirmPassword" name="password_confirmation"
                                            placeholder="Konfirmasi password Anda">
                                        <a href="javascript:;" class="input-group-text bg-transparent border-start-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">visibility_off</i>
                                        </a>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="account_type" class="form-label">Tipe Akun</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">business</i>
                                        </span>
                                        <select class="form-select border-start-0 @error('account_type') is-invalid @enderror"
                                            id="account_type" name="account_type" required>
                                            <option value="">-- Pilih Tipe Akun --</option>
                                            <option value="Individu" {{ old('account_type') == 'Individu' ? 'selected' : 'selected' }}>Individu</option>
                                            <option value="Perusahaan" {{ old('account_type') == 'Perusahaan' ? 'selected' : '' }}>Perusahaan</option>
                                            <option value="Bank & Asuransi" {{ old('account_type') == 'Bank & Asuransi' ? 'selected' : '' }}>Bank & Asuransi</option>
                                        </select>
                                    </div>
                                    @error('account_type')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div id="additional-fields" style="display: none;" class="mb-3">
                                    <div class="mb-3">
                                        <label for="company_name" class="form-label">Nama Perusahaan</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-transparent border-end-0">
                                                <i class="material-icons-outlined" style="font-size: 1.2rem;">apartment</i>
                                            </span>
                                            <input type="text" class="form-control border-start-0 @error('company_name') is-invalid @enderror"
                                                id="company_name" name="company_name" value="{{ old('company_name') }}"
                                                placeholder="Masukkan nama perusahaan">
                                        </div>
                                        @error('company_name')
                                            <div class="invalid-feedback d-block">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>

                                    <div class="mb-3">
                                        <label for="npwp" class="form-label">NPWP</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-transparent border-end-0">
                                                <i class="material-icons-outlined" style="font-size: 1.2rem;">badge</i>
                                            </span>
                                            <input type="text" class="form-control border-start-0 @error('npwp') is-invalid @enderror"
                                                id="npwp" name="npwp" value="{{ old('npwp') }}"
                                                placeholder="Masukkan nomor NPWP">
                                        </div>
                                        @error('npwp')
                                            <div class="invalid-feedback d-block">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="referral_code" class="form-label">Kode Referral (Opsional)</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">redeem</i>
                                        </span>
                                        <input type="text" class="form-control border-start-0 @error('referral_code') is-invalid @enderror"
                                            id="referral_code" name="referral_code" value="{{ old('referral_code') }}"
                                            placeholder="Masukkan kode referral jika ada">
                                    </div>
                                    @error('referral_code')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-4">
                                    <label for="phone" class="form-label">Nomor Telepon</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">phone</i>
                                        </span>
                                        <input type="text" class="form-control border-start-0 @error('phone') is-invalid @enderror"
                                            id="phone" name="phone" value="{{ old('phone') }}"
                                            placeholder="Masukkan nomor telepon (081234567890)">
                                    </div>
                                    @error('phone')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="termsCheck" name="terms" required>
                                        <label class="form-check-label" for="termsCheck">
                                            Saya menyetujui <a href="#" class="fw-medium">Syarat & Ketentuan</a> yang berlaku
                                        </label>
                                        @error('terms')
                                            <div class="invalid-feedback d-block">
                                                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="d-grid mb-4">
                                    <button type="submit" class="btn btn-primary" id="register-button">
                                        <span>
                                            <i class="material-icons-outlined me-2" style="font-size: 1.2rem; vertical-align: middle;">how_to_reg</i>
                                            Daftar Sekarang
                                        </span>
                                    </button>
                                </div>

                                <div class="text-center">
                                    <p class="mb-0">Sudah punya akun? <a href="{{ route('login') }}" class="fw-medium">Masuk di sini</a></p>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Right side - Illustration -->
                    <div class="col-lg-6 d-none d-lg-block">
                        <div class="auth-illustration">
                            <img src="{{ asset('build/images/auth/register1.png') }}" class="img-fluid" alt="Register Illustration">
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <p class="text-muted small">© {{ date('Y') }} TenderID. All rights reserved.</p>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Toggle password visibility for password field
        $("#show_hide_password a").on('click', function(event) {
            event.preventDefault();
            const passwordInput = $('#inputChoosePassword');
            const toggleIcon = $(this).find('i');

            if (passwordInput.attr("type") === "text") {
                passwordInput.attr('type', 'password');
                toggleIcon.text('visibility_off');
            } else {
                passwordInput.attr('type', 'text');
                toggleIcon.text('visibility');
            }
        });

        // Toggle password visibility for confirm password field
        $("#show_hide_password_confirm a").on('click', function(event) {
            event.preventDefault();
            const passwordInput = $('#inputConfirmPassword');
            const toggleIcon = $(this).find('i');

            if (passwordInput.attr("type") === "text") {
                passwordInput.attr('type', 'password');
                toggleIcon.text('visibility_off');
            } else {
                passwordInput.attr('type', 'text');
                toggleIcon.text('visibility');
            }
        });

        // Show/hide additional fields based on account type
        const accountType = $('#account_type');
        const additionalFields = $('#additional-fields');

        // Check initial value
        if (accountType.val() === 'Perusahaan' || accountType.val() === 'Bank & Asuransi') {
            additionalFields.show();
        } else {
            additionalFields.hide();
        }

        // Add change event listener
        accountType.on('change', function() {
            if (accountType.val() === 'Perusahaan' || accountType.val() === 'Bank & Asuransi') {
                additionalFields.slideDown(300);
            } else {
                additionalFields.slideUp(300);
            }
        });

        // Form submission with loading animation
        $('form').on('submit', function() {
            const registerButton = $('#register-button');

            // Prevent double submission
            if (registerButton.hasClass('btn-loading') || registerButton.prop('disabled')) {
                return false;
            }

            // Add loading state
            registerButton.addClass('btn-loading').prop('disabled', true);

            // Allow form submission
            return true;
        });
    });
</script>
@endsection
