@extends('layouts.auth')

@section('title', '<PERSON><PERSON> Ulang Verifikasi Email')

@section('css')
<style>
    /* Card styling */
    .auth-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .auth-card .card-header {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        border-bottom: none;
        padding: 25px 30px;
    }

    .auth-card .card-body {
        padding: 30px;
    }

    /* Form styling */
    .form-label {
        font-weight: 500;
        color: #344767;
        margin-bottom: 8px;
    }

    .form-control {
        padding: 12px 15px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #4481eb;
        box-shadow: 0 0 0 0.2rem rgba(68, 129, 235, 0.1);
    }

    /* Alert styling */
    .alert-success {
        background-color: rgba(25, 135, 84, 0.1);
        border: none;
        border-left: 4px solid #198754;
        color: #198754;
        border-radius: 6px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .alert-danger {
        background-color: rgba(255, 90, 95, 0.1);
        border: none;
        border-left: 4px solid #ff5a5f;
        color: #d63939;
        border-radius: 6px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* Button styling */
    .btn-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-primary:hover {
        background: linear-gradient(90deg, #3b72d6, #039fe5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(68, 129, 235, 0.3);
    }

    .btn-primary:disabled {
        background: linear-gradient(90deg, #4481eb, #04befe);
        opacity: 0.7;
    }

    .btn-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        border: 3px solid transparent;
        border-top-color: #ffffff;
        border-radius: 50%;
        animation: button-loading-spinner 1s ease infinite;
    }

    .btn-loading span {
        visibility: hidden;
        opacity: 0;
    }

    @keyframes button-loading-spinner {
        from {
            transform: rotate(0turn);
        }
        to {
            transform: rotate(1turn);
        }
    }

    /* Illustration */
    .auth-illustration {
        background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
        border-radius: 12px;
        padding: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    /* Logo */
    .auth-logo {
        margin-bottom: 20px;
    }

    /* Links */
    a {
        color: #4481eb;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: #04befe;
        text-decoration: underline;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100 py-5">
        <div class="col-lg-10">
            <div class="card auth-card">
                <div class="row g-0">
                    <!-- Left side - Form -->
                    <div class="col-lg-6">
                        <div class="card-body">
                            <div class="auth-logo">
                                <img src="{{ URL::asset('build/images/logo1.png') }}" width="145" alt="TenderID Logo">
                            </div>

                            <h4 class="fw-bold text-dark mb-2">Kirim Ulang Tautan Verifikasi Email</h4>
                            <p class="text-muted mb-4">Silakan masukkan alamat email Anda untuk mengirim ulang tautan verifikasi.</p>

                            @if (session('status'))
                                <div class="alert alert-success mb-4">
                                    <div class="d-flex align-items-center">
                                        <i class="material-icons-outlined me-2" style="font-size: 1.5rem;">check_circle</i>
                                        <div>
                                            <h6 class="mb-1 fw-bold">Berhasil!</h6>
                                            <p class="mb-0">{{ session('status') }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if (isset($errors) && $errors->any())
                                <div class="alert alert-danger mb-4">
                                    <div class="d-flex align-items-center">
                                        <i class="material-icons-outlined me-2" style="font-size: 1.5rem;">error_outline</i>
                                        <div>
                                            <h6 class="mb-1 fw-bold">Terjadi Kesalahan</h6>
                                            <ul class="mb-0">
                                                @foreach ($errors->all() as $error)
                                                    <li>{{ $error }}</li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('verification.resend') }}" class="mb-4">
                                @csrf
                                <div class="mb-4">
                                    <label for="email" class="form-label">Alamat Email</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">email</i>
                                        </span>
                                        <input type="email" class="form-control border-start-0"
                                            id="email" name="email"
                                            placeholder="Masukkan email Anda" required>
                                    </div>
                                </div>

                                <div class="d-grid mb-4">
                                    <button type="submit" class="btn btn-primary" id="resend-button">
                                        <span>
                                            <i class="material-icons-outlined me-2" style="font-size: 1.2rem; vertical-align: middle;">send</i>
                                            Kirim Ulang Tautan Verifikasi
                                        </span>
                                    </button>
                                </div>

                                <div class="text-center">
                                    <p class="mb-0">Sudah memiliki akun? <a href="{{ route('login') }}" class="fw-medium">Masuk di sini</a></p>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Right side - Illustration -->
                    <div class="col-lg-6 d-none d-lg-block">
                        <div class="auth-illustration">
                            <img src="{{ URL::asset('build/images/auth/reset-password1.png') }}" class="img-fluid" alt="Email Verification Illustration">
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <p class="text-muted small">© {{ date('Y') }} TenderID. All rights reserved.</p>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Form submission with loading animation
        $('form').on('submit', function() {
            const resendButton = $('#resend-button');

            // Prevent double submission
            if (resendButton.hasClass('btn-loading') || resendButton.prop('disabled')) {
                return false;
            }

            // Add loading state
            resendButton.addClass('btn-loading').prop('disabled', true);

            // Allow form submission
            return true;
        });
    });
</script>
@endsection
