@extends('layouts.auth')

@section('title', 'Login')

@section('css')
<style>
    /* Card styling */
    .auth-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .auth-card .card-header {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        border-bottom: none;
        padding: 25px 30px;
    }

    .auth-card .card-body {
        padding: 30px;
    }

    /* Form styling */
    .form-label {
        font-weight: 500;
        color: #344767;
        margin-bottom: 8px;
    }

    .form-control {
        padding: 12px 15px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #4481eb;
        box-shadow: 0 0 0 0.2rem rgba(68, 129, 235, 0.1);
    }

    /* Error message styling */
    .invalid-feedback {
        color: #ff5a5f;
        font-size: 0.85rem;
        margin-top: 0.5rem;
        padding: 0.5rem;
        border-radius: 6px;
        background-color: rgba(255, 90, 95, 0.08);
        border-left: 3px solid #ff5a5f;
    }

    .alert-danger {
        background-color: rgba(255, 90, 95, 0.1);
        border: none;
        border-left: 4px solid #ff5a5f;
        color: #d63939;
        border-radius: 6px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .alert-danger ul {
        padding-left: 1rem;
    }

    /* Button styling */
    .btn-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-primary:hover {
        background: linear-gradient(90deg, #3b72d6, #039fe5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(68, 129, 235, 0.3);
    }

    .btn-primary:disabled {
        background: linear-gradient(90deg, #4481eb, #04befe);
        opacity: 0.7;
    }

    .btn-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        border: 3px solid transparent;
        border-top-color: #ffffff;
        border-radius: 50%;
        animation: button-loading-spinner 1s ease infinite;
    }

    .btn-loading span {
        visibility: hidden;
        opacity: 0;
    }

    @keyframes button-loading-spinner {
        from {
            transform: rotate(0turn);
        }
        to {
            transform: rotate(1turn);
        }
    }

    /* Links */
    a {
        color: #4481eb;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: #04befe;
        text-decoration: underline;
    }

    /* Illustration */
    .auth-illustration {
        background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
        border-radius: 12px;
        padding: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    /* Logo */
    .auth-logo {
        margin-bottom: 20px;
    }

    /* Form check */
    .form-check-input:checked {
        background-color: #4481eb;
        border-color: #4481eb;
    }

    /* Alert styling */
    .alert-danger {
        background-color: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.2);
        color: #dc3545;
        border-radius: 8px;
        padding: 15px;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100 py-5">
        <div class="col-lg-10">
            <div class="card auth-card">
                <div class="row g-0">
                    <!-- Left side - Login Form -->
                    <div class="col-lg-6">
                        <div class="card-body">
                            <div class="auth-logo">
                                <img src="{{ URL::asset('build/images/logo1.png') }}" width="145" alt="TenderID Logo">
                            </div>

                            <h4 class="fw-bold text-dark mb-2">Selamat Datang Kembali</h4>
                            <p class="text-muted mb-4">Masukkan kredensial Anda untuk login ke akun Anda</p>

                            <!-- Menampilkan pesan error jika ada -->
                            @if ($errors->any())
                                <div class="alert alert-danger mb-4">
                                    <div class="d-flex align-items-center">
                                        <i class="material-icons-outlined me-2" style="font-size: 1.5rem;">error_outline</i>
                                        <div>
                                            <h6 class="mb-1 fw-bold">Terjadi Kesalahan</h6>
                                            <ul class="mb-0">
                                                @foreach ($errors->all() as $error)
                                                    <li>{{ $error }}</li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('login') }}" class="mb-4">
                                @csrf
                                <div class="mb-3">
                                    <label for="inputEmailAddress" class="form-label">Email</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">email</i>
                                        </span>
                                        <input type="email" class="form-control border-start-0"
                                            id="inputEmailAddress" name="email" value="{{ old('email') }}"
                                            placeholder="Masukkan email Anda" required>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <label for="inputChoosePassword" class="form-label mb-0">Password</label>
                                        <a href="{{ route('password.request') }}" class="small">Lupa Password?</a>
                                    </div>
                                    <div class="input-group" id="show_hide_password">
                                        <span class="input-group-text bg-transparent border-end-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">lock</i>
                                        </span>
                                        <input type="password" class="form-control border-start-0 border-end-0"
                                            id="inputChoosePassword" name="password"
                                            placeholder="Masukkan password Anda" required>
                                        <a href="javascript:;" class="input-group-text bg-transparent border-start-0">
                                            <i class="material-icons-outlined" style="font-size: 1.2rem;">visibility_off</i>
                                        </a>
                                    </div>
                                    @error('password')
                                        <div class="invalid-feedback d-block">
                                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">error_outline</i>
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="rememberMe" name="remember" {{ old('remember') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="rememberMe">Ingat Saya</label>
                                    </div>
                                </div>

                                <div class="d-grid mb-4">
                                    <button type="submit" class="btn btn-primary" id="login-button">
                                        <span>
                                            <i class="material-icons-outlined me-2" style="font-size: 1.2rem; vertical-align: middle;">login</i>
                                            Masuk
                                        </span>
                                    </button>
                                </div>

                                <div class="text-center">
                                    <p class="mb-0">Belum punya akun? <a href="{{ route('register') }}" class="fw-medium">Daftar sekarang</a></p>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Right side - Illustration -->
                    <div class="col-lg-6 d-none d-lg-block">
                        <div class="auth-illustration">
                            <img src="{{ URL::asset('build/images/auth/login1.png') }}" class="img-fluid" alt="Login Illustration">
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <p class="text-muted small">© {{ date('Y') }} TenderID. All rights reserved.</p>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Toggle password visibility
        $("#show_hide_password a").on('click', function(event) {
            event.preventDefault();
            const passwordInput = $('#inputChoosePassword');
            const toggleIcon = $(this).find('i');

            if (passwordInput.attr("type") === "text") {
                passwordInput.attr('type', 'password');
                toggleIcon.text('visibility_off');
            } else {
                passwordInput.attr('type', 'text');
                toggleIcon.text('visibility');
            }
        });

        // Form submission with loading animation
        $('form').on('submit', function() {
            const loginButton = $('#login-button');

            // Prevent double submission
            if (loginButton.hasClass('btn-loading') || loginButton.prop('disabled')) {
                return false;
            }

            // Add loading state
            loginButton.addClass('btn-loading').prop('disabled', true);

            // Allow form submission
            return true;
        });
    });
</script>
@endsection