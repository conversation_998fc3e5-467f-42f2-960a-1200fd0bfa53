@extends('layouts.master')

@section('title', 'QR Code')

@section('css')
<style>
    .table tr.bg-warning-important td {
        background-color: #ffcc00;
    }

    .product-box img {
        transition: transform 0.3s ease;
    }

    .product-box img:hover {
        transform: scale(1.1);
    }

    .product-title {
        font-weight: bold;
        color: #007bff;
    }

    .product-title:hover {
        text-decoration: underline;
    }

    .product-category a {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        color: #007bff;
    }

    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 8px;
    }

    .btn-filter {
        background-color: #007bff;
        color: white;
    }

    .btn-filter:hover {
        background-color: #0056b3;
    }

    /* Pagination Custom */
    .pagination .page-link {
        color: #007bff;
    }

    .pagination .page-link:hover {
        background-color: #f1f1f1;
    }

    .pagination .active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    body {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        margin: 0;
        font-family: Arial, sans-serif;
    }

    #qrcode {
        margin: 20px;
    }

    .status {
        color: #666;
        margin-top: 20px;
    }
</style>
@endsection

@section('content')
<x-page-title title="Sync Logs" pagetitle="QR CODE WA" />

<h2>Scan QR Code untuk Login ke WhatsApp</h2>
<p>Silakan scan QR Code dengan WhatsApp di menu <strong>Perangkat Tertaut</strong>.</p>

@if (!empty($qrCodeUrl))
    <div class="card shadow-sm p-4 text-center">
        <div id="qrcode"></div>
        <img src="{{ $qrCodeUrl }}" alt="QR Code" class="img-fluid mx-auto d-block" style="max-width: 300px;" />
        <p class="status mt-3">Status: Menunggu scan...</p>
        <div class="spinner-border text-primary mx-auto mt-2" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <script>
        setInterval(async () => {
            try {
                const response = await fetch('/whatsapp/check-login');
                const data = await response.json();

                if (data.isLoggedIn) {
                    window.location.href = '/whatsapp/success';
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }, 3000);
    </script>
@elseif (!empty($qrCode))
    <div class="card shadow-sm p-4 text-center">
        <div id="qrcode-container" class="mx-auto" style="max-width: 300px;"></div>
        <p class="status mt-3">Status: Menunggu scan...</p>
        <div class="spinner-border text-primary mx-auto mt-2" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <script>
        // Jika QR code adalah data base64, tampilkan langsung
        document.addEventListener('DOMContentLoaded', function() {
            console.log('QR Code loaded');

            // Ambil data QR code dari PHP
            let qrCodeData = {!! json_encode($qrCode) !!};

            // Pastikan QR code memiliki prefix yang benar
            if (qrCodeData && !qrCodeData.startsWith('data:image/png;base64,')) {
                console.log('Adding prefix to QR code data');
                qrCodeData = 'data:image/png;base64,' + qrCodeData;
            }

            // Buat elemen img untuk menampilkan QR code
            const qrImg = document.createElement('img');
            qrImg.src = qrCodeData;
            qrImg.alt = 'QR Code';
            qrImg.className = 'img-fluid';
            qrImg.style.maxWidth = '100%';

            // Tampilkan QR code
            const container = document.getElementById('qrcode-container');
            container.innerHTML = ''; // Bersihkan container
            container.appendChild(qrImg);

            // Log untuk debugging
            console.log('QR Code data prefix:', qrCodeData.substring(0, 50) + '...');

            // Tambahkan handler error untuk gambar
            qrImg.onerror = function() {
                console.error('Failed to load QR code image');
                container.innerHTML = '<div class="alert alert-danger">Gagal memuat QR code. Format data tidak valid.</div>';

                // Tambahkan tombol refresh
                const refreshBtn = document.createElement('button');
                refreshBtn.className = 'btn btn-primary mt-3';
                refreshBtn.innerHTML = '<i class="fas fa-sync me-2"></i> Coba Lagi';
                refreshBtn.onclick = function() {
                    window.location.reload();
                };
                container.appendChild(refreshBtn);
            };
        });

        setInterval(async () => {
            try {
                const response = await fetch('/whatsapp/check-login');
                const data = await response.json();

                if (data.isLoggedIn) {
                    window.location.href = '/whatsapp/success';
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }, 3000);
    </script>
@else
    <div class="alert alert-danger text-center p-4">
        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
        <h4>❌ QR Code tidak tersedia</h4>
        <p>Server WhatsApp mungkin sedang tidak aktif atau sedang dalam proses inisialisasi.</p>
        <a href="{{ route('whatsapp.qr') }}" class="btn btn-primary mt-3">
            <i class="fas fa-sync me-2"></i> Coba Lagi
        </a>
    </div>
@endif

@endsection