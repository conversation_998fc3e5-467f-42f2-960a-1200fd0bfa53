@extends('layouts.master')

@section('title', 'Queue Monitoring')

@section('css')
<style>
    /* Card Styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
        overflow: hidden;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 18px 24px;
    }

    .card-header.gradient-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
        border-bottom: none;
    }

    .card-header.gradient-success {
        background: linear-gradient(90deg, #28a745, #20c997);
        color: white;
        border-bottom: none;
    }

    .card-header.gradient-danger {
        background: linear-gradient(90deg, #dc3545, #fd7e14);
        color: white;
        border-bottom: none;
    }

    .card-header.gradient-warning {
        background: linear-gradient(90deg, #ffc107, #fd7e14);
        color: white;
        border-bottom: none;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
    }

    .card-body {
        padding: 24px;
    }

    /* Buttons */
    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
        color: white;
        box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
    }

    .btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0b5ed7;
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
    }

    .btn-success {
        background-color: #198754;
        border-color: #198754;
        color: white;
        box-shadow: 0 4px 10px rgba(25, 135, 84, 0.2);
    }

    .btn-success:hover {
        background-color: #157347;
        border-color: #146c43;
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(25, 135, 84, 0.25);
    }

    .btn-warning {
        background-color: #ffc107;
        border-color: #ffc107;
        color: #212529;
        box-shadow: 0 4px 10px rgba(255, 193, 7, 0.2);
    }

    .btn-warning:hover {
        background-color: #ffca2c;
        border-color: #ffc720;
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(255, 193, 7, 0.25);
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
        box-shadow: 0 4px 10px rgba(220, 53, 69, 0.2);
    }

    .btn-danger:hover {
        background-color: #bb2d3b;
        border-color: #b02a37;
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(220, 53, 69, 0.25);
    }

    /* List Group Styling */
    .list-group-item {
        border-color: #f1f1f1;
        padding: 16px;
        transition: all 0.2s ease;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
    }

    .list-group-item.running {
        border-left: 4px solid #28a745;
    }

    .list-group-item.failed {
        border-left: 4px solid #dc3545;
    }

    .list-group-item .job-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 5px;
    }

    .list-group-item .job-details {
        color: #6c757d;
        font-size: 0.85rem;
    }

    /* Status Badges */
    .status-badge {
        padding: 6px 12px;
        border-radius: 50px;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .status-badge.running {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .status-badge.stopped {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    /* Animation for status */
    @keyframes pulse {
        0% { opacity: 0.6; }
        50% { opacity: 1; }
        100% { opacity: 0.6; }
    }

    .pulse-animation {
        animation: pulse 1.5s infinite;
    }

    /* Empty state styling */
    .empty-state {
        text-align: center;
        padding: 40px 20px;
    }

    .empty-state i {
        font-size: 48px;
        color: #e9ecef;
        margin-bottom: 15px;
    }

    .empty-state h5 {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 10px;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Spinning animation for refresh icon */
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .spin-animation {
        animation: spin 1s linear infinite;
        display: inline-block;
    }

    .cursor-pointer {
        cursor: pointer;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-6">
                <h3>Queue Monitoring</h3>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item">Monitoring</li>
                    <li class="breadcrumb-item active">Queue Status</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<meta name="csrf-token" content="{{ csrf_token() }}">

<div class="container-fluid">
    <!-- Queue Status Overview -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header gradient-primary d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined me-2">settings</i>
                        <h5 class="card-title mb-0 text-white">Queue Worker Status</h5>
                    </div>
                    <div id="refresh-status" class="d-flex align-items-center cursor-pointer" title="Refresh Status">
                        <i class="material-icons-outlined text-white">refresh</i>
                    </div>
                </div>
                <div class="card-body text-center py-4">
                    <div class="mb-3">
                        <div id="queue-status-badge" class="status-badge stopped pulse-animation d-inline-flex align-items-center">
                            <i class="material-icons-outlined me-1" style="font-size: 16px;">pending</i>
                            <span id="queue-status">Checking...</span>
                        </div>
                    </div>
                    <p class="text-muted mb-4">Queue worker memproses job-job yang dikirim ke sistem secara asynchronous. <br><small class="text-info">Catatan: Halaman ini hanya menampilkan maksimal 50 job terbaru untuk mengoptimalkan kinerja server.</small></p>
                    <button id="start-queue" class="btn btn-primary d-none">
                        <i class="material-icons-outlined me-1">play_arrow</i>
                        Start Queue Worker
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Running and Failed Jobs -->
    <div class="row mt-4">
        <!-- Running Jobs -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header gradient-success d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined me-2">pending_actions</i>
                        <h5 class="card-title mb-0 text-white">Job yang Sedang Berjalan</h5>
                        <small class="ms-2 text-white-50">(max 50 terbaru)</small>
                    </div>
                    <span id="running-jobs-count" class="badge bg-white text-success">0</span>
                </div>
                <div class="card-body">
                    <div id="running-jobs-container">
                        <ul id="running-jobs" class="list-group list-group-flush">
                            <li class="list-group-item d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm text-primary me-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span>Memuat data...</span>
                            </li>
                        </ul>
                    </div>
                    <div id="empty-running-jobs" class="empty-state d-none">
                        <i class="material-icons-outlined">hourglass_empty</i>
                        <h5>Tidak Ada Job yang Berjalan</h5>
                        <p>Saat ini tidak ada job yang sedang diproses oleh queue worker.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Failed Jobs -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header gradient-danger d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined me-2">error_outline</i>
                        <h5 class="card-title mb-0 text-white">Job yang Gagal</h5>
                        <small class="ms-2 text-white-50">(max 50 terbaru)</small>
                    </div>
                    <span id="failed-jobs-count" class="badge bg-white text-danger">0</span>
                </div>
                <div class="card-body">
                    <div id="failed-jobs-container">
                        <ul id="failed-jobs" class="list-group list-group-flush">
                            <li class="list-group-item d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm text-primary me-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span>Memuat data...</span>
                            </li>
                        </ul>
                    </div>
                    <div id="empty-failed-jobs" class="empty-state d-none">
                        <i class="material-icons-outlined">check_circle_outline</i>
                        <h5>Tidak Ada Job yang Gagal</h5>
                        <p>Semua job telah berhasil diproses.</p>
                    </div>
                    <div class="text-center mt-4">
                        <button id="retry-jobs" class="btn btn-warning">
                            <i class="material-icons-outlined me-1">replay</i>
                            Coba Ulang Job yang Gagal
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section('scripts')
<script>
    $(document).ready(function() {
        // Initial load
        checkQueueStatus();
        loadRunningJobs();
        loadFailedJobs();

        // Set up refresh intervals
        setInterval(checkQueueStatus, 10000);
        setInterval(loadRunningJobs, 10000);
        setInterval(loadFailedJobs, 10000);

        // Refresh button click handler
        $("#refresh-status").on("click", function() {
            $(this).find("i").addClass("spin-animation");
            checkQueueStatus();
            loadRunningJobs();
            loadFailedJobs();

            setTimeout(() => {
                $(this).find("i").removeClass("spin-animation");
            }, 1000);
        });

        // Start queue button click handler
        $("#start-queue").click(function() {
            const btn = $(this);
            btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Starting...');

            $.ajax({
                url: "{{ secure_url('/monitor/queue/start') }}",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                },
                success: function() {
                    checkQueueStatus();
                    setTimeout(() => {
                        loadRunningJobs();
                    }, 2000);
                },
                error: function(xhr) {
                    console.error(xhr.responseText);
                    alert("Gagal memulai queue worker. Silakan coba lagi.");
                },
                complete: function() {
                    setTimeout(() => {
                        btn.prop('disabled', false).html('<i class="material-icons-outlined me-1">play_arrow</i>Start Queue Worker');
                    }, 1000);
                }
            });
        });

        // Retry failed jobs button click handler
        $("#retry-jobs").click(function() {
            const btn = $(this);
            btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Mencoba ulang...');

            $.ajax({
                url: "{{ secure_url('/monitor/queue/retry-failed') }}",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                },
                success: function() {
                    loadFailedJobs();
                },
                error: function(xhr) {
                    console.error(xhr.responseText);
                    alert("Gagal mencoba ulang job yang gagal. Silakan coba lagi.");
                },
                complete: function() {
                    setTimeout(() => {
                        btn.prop('disabled', false).html('<i class="material-icons-outlined me-1">replay</i>Coba Ulang Job yang Gagal');
                    }, 1000);
                }
            });
        });
    });

    // Check queue status
    function checkQueueStatus() {
        $.get("{{ secure_url('/monitor/queue/status') }}", function(data) {
            const statusBadge = $("#queue-status-badge");
            const statusText = $("#queue-status");

            if (data.running) {
                statusText.text("Running");
                statusBadge.removeClass("stopped").addClass("running");
                statusBadge.find("i").text("check_circle");
                $("#start-queue").addClass("d-none");
            } else {
                statusText.text("Stopped");
                statusBadge.removeClass("running").addClass("stopped");
                statusBadge.find("i").text("error");
                $("#start-queue").removeClass("d-none");
            }
        })
        .fail(function() {
            const statusBadge = $("#queue-status-badge");
            const statusText = $("#queue-status");

            statusText.text("Connection Error");
            statusBadge.removeClass("running").addClass("stopped");
            statusBadge.find("i").text("wifi_off");
        });
    }

    // Load running jobs
    function loadRunningJobs() {
        $.get("{{ secure_url('/monitor/queue/jobs') }}", function(data) {
            let jobList = $("#running-jobs");
            jobList.empty();

            $("#running-jobs-count").text(data.length || "0");

            if (data.length > 0) {
                $("#running-jobs-container").removeClass("d-none");
                $("#empty-running-jobs").addClass("d-none");

                data.forEach(job => {
                    // Extract job name from payload
                    let jobName = 'Unknown Job';
                    let payloadObj = {};

                    try {
                        if (typeof job.payload === 'string') {
                            payloadObj = JSON.parse(job.payload);
                        } else if (typeof job.payload === 'object') {
                            payloadObj = job.payload;
                        }

                        if (payloadObj.displayName) {
                            jobName = payloadObj.displayName;
                        } else if (payloadObj.job) {
                            jobName = payloadObj.job;
                        } else if (payloadObj.data && payloadObj.data.command) {
                            const command = payloadObj.data.command;
                            if (typeof command === 'string' && command.includes('\\')) {
                                const parts = command.split('\\');
                                jobName = parts[parts.length - 1].replace('"', '');
                            } else {
                                jobName = command;
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing job payload:', e);
                        jobName = 'Job #' + (job.id || 'Unknown');
                    }

                    jobList.append(`
                        <li class="list-group-item running">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="job-title">${jobName}</div>
                                    <div class="job-details">
                                        <span class="badge bg-light text-dark me-2">Queue: ${job.queue}</span>
                                        <span class="text-muted small">ID: ${job.id || 'N/A'}</span>
                                    </div>
                                </div>
                                <span class="badge bg-success">Running</span>
                            </div>
                        </li>
                    `);
                });
            } else {
                $("#running-jobs-container").addClass("d-none");
                $("#empty-running-jobs").removeClass("d-none");
            }
        })
        .fail(function() {
            let jobList = $("#running-jobs");
            jobList.empty();
            jobList.append(`
                <li class="list-group-item">
                    <div class="text-danger">
                        <i class="material-icons-outlined me-2">error</i>
                        Gagal memuat data. Silakan coba lagi.
                    </div>
                </li>
            `);
        });
    }

    // Load failed jobs
    function loadFailedJobs() {
        $.get("{{ secure_url('/monitor/queue/failed-jobs') }}", function(data) {
            let jobList = $("#failed-jobs");
            jobList.empty();

            $("#failed-jobs-count").text(data.length || "0");

            if (data.length > 0) {
                $("#failed-jobs-container").removeClass("d-none");
                $("#empty-failed-jobs").addClass("d-none");

                data.forEach(job => {
                    // Extract job name from payload
                    let jobName = 'Unknown Job';
                    let payloadObj = {};

                    try {
                        if (typeof job.payload === 'string') {
                            payloadObj = JSON.parse(job.payload);
                        } else if (typeof job.payload === 'object') {
                            payloadObj = job.payload;
                        }

                        if (payloadObj.displayName) {
                            jobName = payloadObj.displayName;
                        } else if (payloadObj.job) {
                            jobName = payloadObj.job;
                        } else if (payloadObj.data && payloadObj.data.command) {
                            const command = payloadObj.data.command;
                            if (typeof command === 'string' && command.includes('\\')) {
                                const parts = command.split('\\');
                                jobName = parts[parts.length - 1].replace('"', '');
                            } else {
                                jobName = command;
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing job payload:', e);
                        jobName = 'Job #' + (job.id || 'Unknown');
                    }

                    const failedAt = job.failed_at ? new Date(job.failed_at).toLocaleString() : 'Unknown';

                    jobList.append(`
                        <li class="list-group-item failed">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="job-title">${jobName}</div>
                                    <div class="job-details">
                                        <span class="badge bg-light text-dark me-2">Queue: ${job.queue}</span>
                                        <span class="text-muted small">Failed at: ${failedAt}</span>
                                    </div>
                                    <div class="text-danger small mt-2">
                                        <i class="material-icons-outlined me-1" style="font-size: 14px;">error</i>
                                        ${job.exception ? job.exception.substring(0, 100) + '...' : 'Unknown error'}
                                    </div>
                                </div>
                                <span class="badge bg-danger">Failed</span>
                            </div>
                        </li>
                    `);
                });
            } else {
                $("#failed-jobs-container").addClass("d-none");
                $("#empty-failed-jobs").removeClass("d-none");
            }
        })
        .fail(function() {
            let jobList = $("#failed-jobs");
            jobList.empty();
            jobList.append(`
                <li class="list-group-item">
                    <div class="text-danger">
                        <i class="material-icons-outlined me-2">error</i>
                        Gagal memuat data. Silakan coba lagi.
                    </div>
                </li>
            `);
        });
    }
</script>
@endsection
@endsection