@extends('layouts.master')

@section('title', 'User Profile')

@section('css')
<style>
    .wh-120 {
        width: 120px;
        height: 120px;
    }
    .wh-48 {
        width: 48px;
        height: 48px;
    }
    .wh-40 {
        width: 40px;
        height: 40px;
    }
    .profile-stat {
        border-radius: 10px;
        padding: 15px;
        transition: all 0.3s ease;
    }
    .profile-stat:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>
@endsection

@section('content')
<div class="page-content-wrapper">
    <!-- Breadcrumb -->
    <div class="page-breadcrumb d-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">Profile</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="material-icons-outlined fs-5">home</i></a></li>
                    <li class="breadcrumb-item active" aria-current="page">Profile</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- End Breadcrumb -->

    @if(session('success'))
        <div class="alert alert-success border-0 bg-success alert-dismissible fade show py-2 mb-3">
            <div class="d-flex align-items-center">
                <div class="fs-3 text-white"><i class="material-icons-outlined">check_circle</i></div>
                <div class="ms-3">
                    <div class="text-white">{{ session('success') }}</div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-12 col-lg-4 d-flex">
            <!-- Profile Card -->
            <div class="card rounded-4 w-100">
                <div class="card-body text-center">
                    <div class="position-relative mb-4 mt-3">
                        @if($user->photo)
                            <img src="{{ asset('storage/' . $user->photo) }}" class="rounded-circle border p-1 mx-auto d-block" alt="Profile Photo" style="width: 150px; height: 150px; object-fit: cover;">
                        @else
                            <div class="wh-120 d-flex align-items-center justify-content-center bg-light rounded-circle mx-auto" style="width: 150px; height: 150px;">
                                <span class="material-icons-outlined text-secondary" style="font-size: 64px;">person</span>
                            </div>
                        @endif
                    </div>

                    <h5 class="mb-1 fw-bold">{{ $user->name }}</h5>
                    <p class="text-muted mb-3">{{ $user->account_type }}</p>

                    <div class="d-flex justify-content-center gap-2 mb-3">
                        <a href="{{ route('tenderid.profile.edit') }}" class="btn btn-primary d-flex align-items-center gap-1 px-3">
                            <i class="material-icons-outlined" style="font-size: 18px;">edit</i> Edit Profile
                        </a>
                        <a href="{{ route('profile.change-password') }}" class="btn btn-outline-primary d-flex align-items-center gap-1 px-3">
                            <i class="material-icons-outlined" style="font-size: 18px;">lock</i> Change Password
                        </a>
                    </div>

                    <!-- Notification Status -->
                    <div class="card border shadow-none rounded-4 mb-3">
                        <div class="card-body">
                            <h6 class="mb-3 fw-bold">Notification Settings</h6>

                            <!-- Telegram Status -->
                            <div class="d-flex align-items-center mb-3">
                                <div class="wh-40 d-flex align-items-center justify-content-center {{ $user->telegram_bot ? 'bg-success' : 'bg-warning' }} rounded-circle me-2">
                                    <span class="material-icons-outlined text-white" style="font-size: 20px;">{{ $user->telegram_bot ? 'notifications_active' : 'notifications_off' }}</span>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0 fs-6">Telegram</h6>
                                    <small class="text-muted">{{ $user->telegram_bot ? 'Connected' : 'Not connected' }}</small>
                                </div>
                                <div>
                                    @if($user->telegram_bot)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <a href="{{ route('telegram.link') }}" class="btn btn-sm btn-primary">Connect</a>
                                    @endif
                                </div>
                            </div>

                            <!-- WhatsApp Status -->
                            <div class="d-flex align-items-center">
                                <div class="wh-40 d-flex align-items-center justify-content-center {{ $user->phone ? 'bg-success' : 'bg-warning' }} rounded-circle me-2">
                                    <span class="material-icons-outlined text-white" style="font-size: 20px;">{{ $user->phone ? 'notifications_active' : 'notifications_off' }}</span>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0 fs-6">WhatsApp</h6>
                                    <small class="text-muted">{{ $user->phone ? $user->phone : 'Not connected' }}</small>
                                </div>
                                <div>
                                    @if($user->phone)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <a href="{{ route('tenderid.profile.edit') }}#phone" class="btn btn-sm btn-primary">Add</a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12 col-lg-8 d-flex">
            <div class="card rounded-4 w-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="wh-48 d-flex align-items-center justify-content-center bg-primary rounded-circle me-3">
                            <span class="material-icons-outlined text-white">person</span>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Account Information</h5>
                            <p class="mb-0 text-secondary">Your personal and account details</p>
                        </div>
                    </div>

                    <!-- Account Details -->
                    <div class="card border shadow-none rounded-4 mb-4">
                        <div class="card-body">
                            <h6 class="mb-3 fw-bold">Personal Information</h6>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <p class="text-muted mb-0">Full Name</p>
                                </div>
                                <div class="col-md-8">
                                    <p class="mb-0 fw-medium">{{ $user->name }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <p class="text-muted mb-0">Email Address</p>
                                </div>
                                <div class="col-md-8">
                                    <p class="mb-0 fw-medium">{{ $user->email }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <p class="text-muted mb-0">Phone Number</p>
                                </div>
                                <div class="col-md-8">
                                    <p class="mb-0 fw-medium">{{ $user->phone ?? 'Not provided' }}</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <p class="text-muted mb-0">Account Type</p>
                                </div>
                                <div class="col-md-8">
                                    <p class="mb-0 fw-medium">{{ $user->account_type }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Referral & Commission -->
                    <div class="card border shadow-none rounded-4">
                        <div class="card-body">
                            <h6 class="mb-3 fw-bold">Referral & Commission</h6>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <p class="text-muted mb-0">Referral Code</p>
                                </div>
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center">
                                        <p class="mb-0 fw-medium me-2">{{ $user->referral_code }}</p>
                                        <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('{{ $user->referral_code }}')">
                                            <i class="material-icons-outlined" style="font-size: 16px;">content_copy</i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3 mt-2">
                                <div class="col-md-6">
                                    <div class="profile-stat bg-light-success">
                                        <div class="d-flex align-items-center">
                                            <div class="wh-40 d-flex align-items-center justify-content-center bg-success rounded-circle me-2">
                                                <span class="material-icons-outlined text-white" style="font-size: 20px;">payments</span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fs-6">Total Commissions</h6>
                                                <h5 class="mb-0 fw-bold">Rp {{ number_format($user->total_commissions, 0, ',', '.') }}</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="profile-stat bg-light-primary">
                                        <div class="d-flex align-items-center">
                                            <div class="wh-40 d-flex align-items-center justify-content-center bg-primary rounded-circle me-2">
                                                <span class="material-icons-outlined text-white" style="font-size: 20px;">account_balance_wallet</span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fs-6">Commission Balance</h6>
                                                <h5 class="mb-0 fw-bold">Rp {{ number_format($user->commission_balance, 0, ',', '.') }}</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @if($user->commission_balance > 0)
                            <div class="mt-3 text-end">
                                <a href="{{ route('withdrawals.request') }}" class="btn btn-success d-inline-flex align-items-center gap-1">
                                    <span class="material-icons-outlined" style="font-size: 18px;">monetization_on</span>
                                    <span>Withdraw Funds</span>
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Create a temporary toast notification
            const toast = document.createElement('div');
            toast.className = 'position-fixed bottom-0 end-0 p-3';
            toast.style.zIndex = '5';
            toast.innerHTML = `
                <div class="toast show align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="material-icons-outlined align-middle me-1">check_circle</i>
                            Referral code copied to clipboard!
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;
            document.body.appendChild(toast);

            // Remove the toast after 3 seconds
            setTimeout(function() {
                document.body.removeChild(toast);
            }, 3000);
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
        });
    }
</script>
@endsection
