@extends('layouts.master')

@section('title', 'Edit Profile')

@section('css')
<style>
    .wh-40 {
        width: 40px;
        height: 40px;
    }
    .wh-48 {
        width: 48px;
        height: 48px;
    }
    .wh-120 {
        width: 120px;
        height: 120px;
    }
</style>
@endsection

@section('content')
<div class="page-content-wrapper">
    <!-- Breadcrumb -->
    <div class="page-breadcrumb d-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">Profile</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="material-icons-outlined fs-5">home</i></a></li>
                    <li class="breadcrumb-item"><a href="{{ route('tenderid.profile.index') }}">Profile</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Edit Profile</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- End Breadcrumb -->

    <div class="row">
        <div class="col-12">
            <div class="card rounded-4">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="wh-48 d-flex align-items-center justify-content-center bg-primary rounded-circle me-3">
                            <span class="material-icons-outlined text-white">person</span>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Edit Profile</h5>
                            <p class="mb-0 text-secondary">Update your personal information</p>
                        </div>
                    </div>

                    @if(session('success'))
                        <div class="alert alert-success border-0 bg-success alert-dismissible fade show py-2">
                            <div class="d-flex align-items-center">
                                <div class="fs-3 text-white"><i class="material-icons-outlined">check_circle</i></div>
                                <div class="ms-3">
                                    <div class="text-white">{{ session('success') }}</div>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="alert alert-danger border-0 bg-danger alert-dismissible fade show py-2">
                            <div class="d-flex align-items-center">
                                <div class="fs-3 text-white"><i class="material-icons-outlined">error</i></div>
                                <div class="ms-3">
                                    <div class="text-white">
                                        <ul class="mb-0">
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form action="{{ route('tenderid.profile.update') }}" method="POST" enctype="multipart/form-data" class="mt-4">
                        @csrf

                        <div class="row">
                            <div class="col-12 col-lg-8">
                                <!-- Basic Information -->
                                <div class="card rounded-4 border shadow-none mb-4">
                                    <div class="card-body">
                                        <h6 class="mb-3 fw-bold">Basic Information</h6>

                                        <div class="mb-3">
                                            <label for="name" class="form-label">Name</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-transparent"><i class="material-icons-outlined">person</i></span>
                                                <input type="text"
                                                    class="form-control border-start-0 @error('name') is-invalid @enderror"
                                                    id="name"
                                                    name="name"
                                                    value="{{ old('name', $user->name) }}"
                                                    required>
                                                @error('name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-transparent"><i class="material-icons-outlined">email</i></span>
                                                <input type="email"
                                                    class="form-control border-start-0 @error('email') is-invalid @enderror"
                                                    id="email"
                                                    name="email"
                                                    value="{{ old('email', $user->email) }}"
                                                    required>
                                                @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="account_type" class="form-label">Account Type</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-transparent"><i class="material-icons-outlined">badge</i></span>
                                                <input type="text"
                                                    class="form-control border-start-0 @error('account_type') is-invalid @enderror"
                                                    id="account_type"
                                                    name="account_type"
                                                    value="{{ old('account_type', $user->account_type) }}"
                                                    readonly>
                                                @error('account_type')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Notification Settings -->
                                <div class="card rounded-4 border shadow-none mb-4" id="phone">
                                    <div class="card-body">
                                        <h6 class="mb-3 fw-bold">Notification Settings</h6>

                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Nomor WhatsApp</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-transparent"><i class="material-icons-outlined">phone</i></span>
                                                <input type="text"
                                                    class="form-control border-start-0 @error('phone') is-invalid @enderror"
                                                    id="phone"
                                                    name="phone"
                                                    placeholder="Contoh: 628123456789 (tanpa tanda + atau awalan 0)"
                                                    value="{{ old('phone', $user->phone) }}">
                                                @error('phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            <small class="text-muted">Masukkan nomor WhatsApp Anda untuk menerima notifikasi. Format: 628xxx (tanpa tanda + atau awalan 0)</small>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label d-block">Telegram</label>
                                            @if($user->telegram_bot)
                                                <div class="d-flex align-items-center">
                                                    <div class="wh-40 d-flex align-items-center justify-content-center bg-success rounded-circle me-2">
                                                        <span class="material-icons-outlined text-white" style="font-size: 20px;">notifications_active</span>
                                                    </div>
                                                    <span class="me-2">Terhubung</span>
                                                    <span class="badge bg-success">Aktif</span>
                                                </div>
                                            @else
                                                <div class="d-flex align-items-center">
                                                    <div class="wh-40 d-flex align-items-center justify-content-center bg-warning rounded-circle me-2">
                                                        <span class="material-icons-outlined text-white" style="font-size: 20px;">notifications_off</span>
                                                    </div>
                                                    <span class="me-2">Belum terhubung</span>
                                                    <a href="{{ route('telegram.link') }}" class="btn btn-sm btn-primary d-flex align-items-center gap-1">
                                                        <span class="material-icons-outlined" style="font-size: 16px;">link</span>
                                                        <span>Hubungkan</span>
                                                    </a>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 col-lg-4">
                                <!-- Profile Photo -->
                                <div class="card rounded-4 border shadow-none mb-4">
                                    <div class="card-body">
                                        <h6 class="mb-3 fw-bold">Profile Photo</h6>

                                        <div class="text-center mb-3">
                                            @if($user->photo)
                                                <img src="{{ asset('storage/' . $user->photo) }}" alt="Profile Photo" class="rounded-circle border p-1" style="width: 120px; height: 120px; object-fit: cover;">
                                            @else
                                                <div class="wh-120 d-flex align-items-center justify-content-center bg-light rounded-circle mx-auto">
                                                    <span class="material-icons-outlined text-secondary" style="font-size: 48px;">person</span>
                                                </div>
                                            @endif
                                        </div>

                                        <div class="mb-3">
                                            <label for="photo" class="form-label">Change Photo</label>
                                            <input type="file"
                                                class="form-control @error('photo') is-invalid @enderror"
                                                id="photo"
                                                name="photo"
                                                accept="image/*">
                                            <small class="text-muted">Allowed JPG, JPEG or PNG. Max size of 2MB</small>
                                            @error('photo')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2 mt-3">
                            <a href="{{ route('tenderid.profile.index') }}" class="btn btn-light px-4">
                                <i class="material-icons-outlined me-1">arrow_back</i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary px-4">
                                <i class="material-icons-outlined me-1">save</i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if URL has hash
        if (window.location.hash) {
            const targetElement = document.querySelector(window.location.hash);
            if (targetElement) {
                // Scroll to the element with a slight delay to ensure page is fully loaded
                setTimeout(function() {
                    targetElement.scrollIntoView({ behavior: 'smooth' });

                    // Add highlight effect
                    targetElement.classList.add('border-primary');
                    setTimeout(function() {
                        targetElement.classList.remove('border-primary');
                    }, 2000);
                }, 500);
            }
        }
    });
</script>
@endsection