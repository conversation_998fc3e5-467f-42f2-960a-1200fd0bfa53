@extends('layouts.master')

@section('title', 'For You')

@section('css')
<style>
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    .animate-spin {
        animation: spin 1s linear infinite;
    }

    /* Card styling */
    .card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        margin-bottom: 25px;
        border: none;
    }

    .card:hover {
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }

    .card-header {
        background: linear-gradient(90deg, #007bff, #6f42c1, #fd7e14);
        color: white;
        border-bottom: none;
        padding: 18px 25px;
        position: relative;
    }

    .card-header:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: rgba(255, 255, 255, 0.2);
    }

    .card-header h5 {
        margin-bottom: 0;
        font-weight: 600;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        color: white !important;
    }

    .card-header h5 i {
        margin-right: 12px;
        font-size: 1.3rem;
        background: rgba(255, 255, 255, 0.2);
        padding: 8px;
        border-radius: 50%;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .card-body {
        padding: 25px;
    }

    /* Table styling */
    .table-responsive {
        border: none;
        border-radius: 12px;
        margin-top: 10px;
        overflow: hidden; /* Ensure the border-radius is applied properly */
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .table {
        margin-bottom: 0;
    }

    .table thead {
        background-color: #f8f9fa;
    }

    .table thead th {
        color: #344767 !important;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        padding: 16px;
        vertical-align: middle;
        letter-spacing: 0.5px;
        white-space: nowrap;
        border-bottom: 1px solid #e9ecef;
    }

    .table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #f8f9fa;
    }

    .table tbody tr:last-child {
        border-bottom: none;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
    }

    .table tbody td {
        padding: 16px;
        vertical-align: middle;
        font-size: 0.9rem;
        color: #495057;
    }

    /* Highlight first column */
    .table tbody td:first-child a {
        font-weight: 600;
        color: #007bff;
        transition: color 0.2s ease;
    }

    .table tbody td:first-child a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    /* Product styling */
    .product-box {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        overflow: hidden;
        background-color: #f8f9fa;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
        border: 2px solid rgba(0, 123, 255, 0.1);
        padding: 2px;
    }

    .product-box img {
        transition: transform 0.3s ease;
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .product-box img:hover {
        transform: scale(1.1);
    }

    .product-info {
        flex: 1;
    }

    .product-title {
        font-weight: 600;
        color: #007bff;
        transition: color 0.2s ease;
        display: block;
        margin-bottom: 5px;
        line-height: 1.3;
        font-size: 0.95rem;
    }

    .product-title:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .product-category {
        color: #6c757d;
        font-size: 0.85rem;
        margin-bottom: 0;
        line-height: 1.4;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }

    .product-category a {
        color: #6c757d;
        transition: color 0.2s ease;
        display: inline-flex;
        align-items: center;
    }

    .product-category a:hover {
        color: #495057;
    }

    .product-category i {
        font-size: 14px;
        margin-right: 4px;
        color: #6c757d;
        opacity: 0.8;
    }

    /* Button styling */
    .btn-sm {
        padding: 0.35rem 0.85rem;
        border-radius: 50px;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-weight: 500;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    }

    .btn-primary, .btn-warning {
        color: white !important;
    }

    .btn-sm:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    }

    .btn-sm:active {
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn-sm i {
        font-size: 0.9rem;
    }

    .follow-btn, .unfollow-btn {
        min-width: 42px;
    }

    .follow-btn {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border-color: #007bff;
        color: white !important;
    }

    .follow-btn:hover {
        background: linear-gradient(45deg, #0069d9, #0062cc);
        border-color: #0062cc;
        color: white !important;
    }

    .unfollow-btn {
        background: linear-gradient(45deg, #fd7e14, #e65c00);
        border-color: #fd7e14;
        color: white !important;
    }

    .unfollow-btn:hover {
        background: linear-gradient(45deg, #e65c00, #fd7e14);
        border-color: #e65c00;
        color: white !important;
    }

    /* Badge styling */
    .badge {
        padding: 0.5em 0.9em;
        font-weight: 500;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .badge i {
        margin-right: 5px;
    }

    /* Loading animation */
    .spinner-grow {
        margin-right: 5px;
    }
</style>
@endsection

@section('content')

<div class="row">
    <!-- Followed Tenders -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="material-icons-outlined">gavel</i> Followed Tenders</h5>
            </div>
            <div class="card-body">
                <div class="product-table">
                    <div class="table-responsive white-space-nowrap">
                        <table class="table align-middle">
                            <thead>
                                <tr>
                                    <th>Kode Tender</th>
                                    <th>Nama Paket</th>
                                    <th>Tahap Tender</th>
                                    <th>Pagu</th>
                                    <th>HPS</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($followedTenders as $tender)
                                    <tr>
                                        <td>
                                            <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}" class="fw-semibold text-primary">
                                                #{{ $tender->kode_tender }}
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center gap-3">
                                                <div class="product-box">
                                                    @if ($tender->lpse->logo_path)
                                                        <img src="{{ asset(str_replace('http://proyeklpse.test', '', $tender->lpse->logo_path)) }}" alt="Logo" class="rounded-3">
                                                    @else
                                                        <img src="{{ asset('build/images/logo-default.png') }}" alt="Logo" class="rounded-3">
                                                    @endif
                                                </div>
                                                <div class="product-info">
                                                    <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}" class="product-title">{{ $tender->nama_paket }}</a>
                                                    <p class="mb-0 product-category">
                                                        <a href="{{ route('lpse.dashboard', ['kode_lpse' => $tender->lpse->kd_lpse]) }}">
                                                            <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle;">business</i>
                                                            {{ $tender->lpse->nama_lpse }}
                                                        </a>
                                                    </p>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle; margin-right: 3px;">flag</i>
                                                {{ $tender->tahap_tender }}
                                            </span>
                                        </td>
                                        <td class="fw-semibold">{{ format_number_short($tender->pagu) }}</td>
                                        <td class="fw-semibold">{{ format_number_short($tender->hps) }}</td>
                                        <td>
                                            @php
                                                $followed = json_decode(auth()->user()->tender_followed) ?? [];
                                            @endphp
                                            <a href="javascript:;" class="btn btn-sm {{ in_array($tender->kode_tender, $followed) ? 'btn-warning unfollow-btn' : 'btn-primary follow-btn' }}"
                                               data-type="tender"
                                               data-id="{{ $tender->kode_tender }}">
                                                <i class="bi {{ in_array($tender->kode_tender, $followed) ? 'bi-bell-slash-fill' : 'bi-bell-fill' }}"></i>
                                                <span class="d-none d-md-inline">{{ in_array($tender->kode_tender, $followed) ? 'Unfollow' : 'Follow' }}</span>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="material-icons-outlined text-muted mb-2" style="font-size: 48px;">notifications_off</i>
                                                <p class="mb-0 text-muted">No followed tenders yet</p>
                                                <a href="{{ route('tender_lpse.index') }}" class="btn btn-sm btn-primary mt-3">
                                                    <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle; margin-right: 3px;">search</i>
                                                    Browse Tenders
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Followed LPSE -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="material-icons-outlined">business</i> Followed LPSE</h5>
            </div>
            <div class="card-body">
                <div class="product-table">
                    <div class="table-responsive white-space-nowrap">
                        <table class="table align-middle">
                            <thead>
                                <tr>
                                    <th>LPSE</th>
                                    <th>Website</th>
                                    <th>Email</th>
                                    <th>Helpdesk</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($followedLpse as $lpse)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center gap-3">
                                                <div class="product-box">
                                                    @if ($lpse->logo_path)
                                                        <img src="{{ asset(str_replace('http://proyeklpse.test', '', $lpse->logo_path)) }}" alt="Logo" class="rounded-3">
                                                    @else
                                                        <img src="{{ asset('build/images/logo-default.png') }}" alt="Logo" class="rounded-3">
                                                    @endif
                                                </div>
                                                <div class="product-info">
                                                    <a href="{{ route('lpse.dashboard', ['kode_lpse' => $lpse->kd_lpse]) }}" class="product-title">{{ $lpse->nama_lpse }}</a>
                                                    <p class="mb-0 product-category">
                                                        <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle;">location_on</i>
                                                        {{ $lpse->provinsi }}
                                                    </p>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{{ $lpse->url }}" target="_blank" class="d-flex align-items-center text-primary">
                                                <i class="material-icons-outlined me-1" style="font-size: 16px;">language</i>
                                                <span>{{ $lpse->url }}</span>
                                            </a>
                                        </td>
                                        <td>
                                            <a href="mailto:{{ $lpse->email }}" class="d-flex align-items-center text-primary">
                                                <i class="material-icons-outlined me-1" style="font-size: 16px;">email</i>
                                                <span>{{ $lpse->email }}</span>
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="material-icons-outlined me-1 text-success" style="font-size: 16px;">phone</i>
                                                <span>{{ $lpse->helpdesk }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                $followed = json_decode(auth()->user()->lpse_followed) ?? [];
                                            @endphp
                                            <a href="javascript:;" class="btn btn-sm {{ in_array($lpse->kd_lpse, $followed) ? 'btn-warning unfollow-btn' : 'btn-primary follow-btn' }}"
                                               data-type="lpse"
                                               data-id="{{ $lpse->kd_lpse }}">
                                                <i class="bi {{ in_array($lpse->kd_lpse, $followed) ? 'bi-bell-slash-fill' : 'bi-bell-fill' }}"></i>
                                                <span class="d-none d-md-inline">{{ in_array($lpse->kd_lpse, $followed) ? 'Unfollow' : 'Follow' }}</span>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="material-icons-outlined text-muted mb-2" style="font-size: 48px;">notifications_off</i>
                                                <p class="mb-0 text-muted">No followed LPSE yet</p>
                                                <a href="{{ route('lpse.index') }}" class="btn btn-sm btn-primary mt-3">
                                                    <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle; margin-right: 3px;">search</i>
                                                    Browse LPSE
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Followed SIRUP -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="material-icons-outlined">description</i> Followed SIRUP</h5>
            </div>
            <div class="card-body">
                <div class="product-table">
                    <div class="table-responsive white-space-nowrap">
                        <table class="table align-middle">
                            <thead>
                                <tr>
                                    <th>Kode RUP</th>
                                    <th>Nama Paket</th>
                                    <th>Pagu</th>
                                    <th>Metode Pemilihan</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($followedSirup as $sirup)
                                    <tr data-kode-rup="{{ $sirup->kode_rup }}">
                                        <td>
                                            <a href="{{ route('sirup.show', ['id' => $sirup->id]) }}" class="fw-semibold text-primary">
                                                {{ $sirup->kode_rup }}
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center gap-3">
                                                <div class="product-box">
                                                    @php
                                                        $nama_klpd = $sirup->nama_klpd;
                                                        if (strpos($nama_klpd, "Kab.") !== false) {
                                                            $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
                                                        }
                                                        $nama_klpd = "LPSE {$nama_klpd}";
                                                        $lpse = DB::table('lpse')
                                                                ->where('nama_lpse', 'like', "%{$nama_klpd}%")
                                                                ->first();
                                                        if ($lpse) {
                                                            $lpse->logo_path = str_replace('http://proyeklpse.test', '', $lpse->logo_path);
                                                        }
                                                    @endphp
                                                    @if($lpse)
                                                        <img src="{{ asset($lpse->logo_path) }}" alt="Logo" class="rounded-3">
                                                    @else
                                                        <img src="{{ asset('build/images/logo-default.png') }}" alt="Logo" class="rounded-3">
                                                    @endif
                                                </div>
                                                <div class="product-info">
                                                    <a href="{{ route('sirup.show', ['id' => $sirup->id]) }}" class="product-title">{{ $sirup->nama_paket }}</a>
                                                    <p class="mb-0 product-category">
                                                        <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle;">account_balance</i>
                                                        {{ $sirup->satuan_kerja }} -
                                                        <a href="{{ route('sirup.index', ['nama_klpd' => $sirup->nama_klpd]) }}">{{ $sirup->nama_klpd }}</a>
                                                    </p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="fw-semibold">{{ format_number_short($sirup->total_pagu) }}</td>
                                        <td>
                                            <span class="badge bg-info">
                                                <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle; margin-right: 3px;">settings</i>
                                                {{ $sirup->metode_pemilihan }}
                                            </span>
                                        </td>
                                        <td id="status-{{ $sirup->kode_rup }}">
                                            <div class="d-flex align-items-center">
                                                <span class="spinner-grow spinner-grow-sm text-primary me-2" role="status" aria-hidden="true"></span>
                                                <span>Loading...</span>
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                $followed = json_decode(auth()->user()->sirup_followed) ?? [];
                                            @endphp
                                            <a href="javascript:;" class="btn btn-sm {{ in_array($sirup->kode_rup, $followed) ? 'btn-warning unfollow-btn' : 'btn-primary follow-btn' }}"
                                               data-type="sirup"
                                               data-id="{{ $sirup->kode_rup }}">
                                                <i class="bi {{ in_array($sirup->kode_rup, $followed) ? 'bi-bell-slash-fill' : 'bi-bell-fill' }}"></i>
                                                <span class="d-none d-md-inline">{{ in_array($sirup->kode_rup, $followed) ? 'Unfollow' : 'Follow' }}</span>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="material-icons-outlined text-muted mb-2" style="font-size: 48px;">notifications_off</i>
                                                <p class="mb-0 text-muted">No followed SIRUP yet</p>
                                                <a href="{{ route('sirup.index') }}" class="btn btn-sm btn-primary mt-3">
                                                    <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle; margin-right: 3px;">search</i>
                                                    Browse SIRUP
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Load status for each SIRUP row
    $('tr[data-kode-rup]').each(function() {
        var kodeRup = $(this).data('kode-rup');
        if (kodeRup) {
            loadStatus(kodeRup);
        }
    });

    // Handle follow/unfollow buttons
    $('.follow-btn, .unfollow-btn').click(function() {
        var btn = $(this);
        var type = btn.data('type');
        var id = btn.data('id');
        var action = btn.hasClass('follow-btn') ? 'follow' : 'unfollow';
        var url = `/${action}/${type}/${id}`;

        // Prevent double clicks
        if (btn.hasClass('disabled')) return;

        // Add loading state
        btn.addClass('disabled');
        var icon = btn.find('i');
        var originalClass = icon.attr('class');
        icon.attr('class', 'bi bi-hourglass-split animate-spin');

        $.post(url, {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.status === 'followed' || response.status === 'unfollowed') {
                setTimeout(function() {
                    if (action === 'follow') {
                        btn.removeClass('btn-primary follow-btn')
                           .addClass('btn-warning unfollow-btn')
                           .find('i')
                           .attr('class', 'bi bi-bell-slash-fill');

                        // Update text if it exists
                        if (btn.find('span').length > 0) {
                            btn.find('span').text('Unfollow');
                        }
                    } else {
                        btn.removeClass('btn-warning unfollow-btn')
                           .addClass('btn-primary follow-btn')
                           .find('i')
                           .attr('class', 'bi bi-bell-fill');

                        // Update text if it exists
                        if (btn.find('span').length > 0) {
                            btn.find('span').text('Follow');
                        }
                    }
                    btn.removeClass('disabled');
                }, 500);
            }
        })
        .fail(function(xhr, status, error) {
            setTimeout(function() {
                btn.removeClass('disabled');
                icon.attr('class', originalClass);
                alert('Failed to update follow status. Please try again.');
            }, 500);
        });
    });
});

function loadStatus(kodeRup) {
    $.ajax({
        url: '/sirup/status/' + kodeRup,
        type: 'GET',
        success: function(response) {
            if (response.status) {
                if (response.status === 'Sudah Tayang' || response.status === 'Sudah Klik') {
                    $('#status-' + kodeRup).html(`
                        <span class="badge bg-success">
                            <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle; margin-right: 3px;">check_circle</i>
                            ${response.status}
                        </span>
                    `);
                } else {
                    $('#status-' + kodeRup).html(`
                        <span class="badge bg-danger">
                            <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle; margin-right: 3px;">cancel</i>
                            ${response.status}
                        </span>
                    `);
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('Error fetching status:', error);
            $('#status-' + kodeRup).html(`
                <span class="badge bg-danger">
                    <i class="material-icons-outlined" style="font-size: 14px; vertical-align: middle; margin-right: 3px;">error</i>
                    Error
                </span>
            `);
        }
    });
}
</script>
@endsection
