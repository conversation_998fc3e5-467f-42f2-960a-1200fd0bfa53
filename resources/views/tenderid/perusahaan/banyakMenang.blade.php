@extends('layouts.master')

@section('title', '<PERSON><PERSON><PERSON>')
@section('css')
<style>
    .table tr.bg-warning-important td {
        background-color: #ffcc00;
        /* Menggunakan warna kuning yang lebih lembut */
    }

    .product-box img {
        transition: transform 0.3s ease;
    }

    .product-box img:hover {
        transform: scale(1.1);
        /* Animasi zoom ketika hover gambar */
    }

    .product-title {
        font-weight: bold;
        color: #007bff;
    }

    .product-title:hover {
        text-decoration: underline;
    }

    .product-category a {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        color: #007bff;
    }

    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 8px;
    }

    .btn-filter {
        background-color: #007bff;
        color: white;
    }

    .btn-filter:hover {
        background-color: #0056b3;
    }

    /* Pagination Custom */
    .pagination .page-link {
        color: #007bff;
    }

    .pagination .page-link:hover {
        background-color: #f1f1f1;
    }

    .pagination .active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }
</style>
@endsection

@section('content')
<x-page-title title="Perusahaan" pagetitle="Semua Perusahaan" />


<!-- Search Section -->
<div class="row g-3">
    <div class="col-auto">
        <div class="position-relative">
            <form method="GET" action="{{ route('perusahaan.banyak_menang') }}" class="mb-4">
                <input class="form-control px-5" type="search" name="search" placeholder="Cari Nama Perusahaan"
                    value="{{ request('search') }}">
                <span
                    class="material-icons-outlined position-absolute ms-3 translate-middle-y start-0 top-50 fs-5">search</span>
            </form>
        </div>
    </div>
</div>

<!-- Tender Table Section -->
<div class="card mt-4">
    <div class="card-body">
        <div class="product-table">
            <div class="table-responsive white-space-nowrap">
                <table class="table align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>Nama Perusahaan</th>
                            <th>NPWP</th>
                            <th>Alamat</th>
                            <th>Jumlah Tender Dimenangkan</th>
                            <th>Nilai Kontrak Dimenangkan</th>
                        </tr>
                    </thead>
                    <tbody>
                    @if($perusahaan->isEmpty())
                <tr>
                    <td colspan="6">Tidak ada perusahaan yang ditemukan</td>
                </tr>
            @else
                @foreach ($perusahaan as $p)
                <tr>
        <td><a href="{{ route('perusahaan.show', ['npwp' => encrypt($p->npwp)]) }}">{{ $p->nama_peserta }}</a></td>
        <td>{{ $p->npwp }}</td>
        <td>{{ $p->alamat }}</td>
        <td>{{ $p->jumlah_tender_dimenangkan }} Tender</td>
        <td>{{ convert_to_rupiah($p->total_nilai_kontrak) }}</td>
    </tr>
                @endforeach
            @endif
                    </tbody>
                </table>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $perusahaan->links('vendor.pagination.costum') }}
                </div>
            </div>
        </div>
    </div>
</div>

@endsection