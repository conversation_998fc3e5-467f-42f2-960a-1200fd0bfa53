@php
use App\Models\Lpse;
use App\Helpers\TenderHelper;
@endphp
@extends('layouts.master')

@section('title', "$perusahaan->nama_peserta - Detail <PERSON>")
@section('css')
<style>
    /* Card styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 16px 24px;
        display: flex;
        align-items: center;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .card-body {
        padding: 24px;
    }

    .card-body .card-body-title {
        margin-bottom: 20px;
    }

    /* Table styling */
    .table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table thead th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-top: none;
        padding: 16px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e9ecef;
    }

    .table tbody td {
        padding: 16px;
        vertical-align: middle;
        border-bottom: 1px solid #f8f9fa;
        font-size: 0.9rem;
        color: #495057;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    .table tbody tr {
        transition: all 0.2s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .table-responsive {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        overflow: hidden;
    }

    /* Row status styling */
    .table tr.bg-warning-important td {
        background-color: rgba(255, 193, 7, 0.15);
    }

    .table tr.bg-success td {
        background-color: rgba(25, 135, 84, 0.1);
        color: #155724;
    }

    .table tr.bg-danger-subtle td {
        background-color: rgba(220, 53, 69, 0.1);
        color: #721c24;
    }

    /* Product styling */
    .product-box {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .product-box img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        transition: transform 0.3s ease;
    }

    .product-box img:hover {
        transform: scale(1.15);
    }

    .product-info {
        flex: 1;
    }

    .product-title {
        font-weight: 600;
        color: #0d6efd;
        display: block;
        margin-bottom: 5px;
        font-size: 0.95rem;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .product-title:hover {
        color: #0b5ed7;
        text-decoration: underline;
    }

    .product-category {
        color: #6c757d;
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .product-category a {
        color: #6c757d;
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .product-category a:hover {
        color: #0d6efd;
        text-decoration: underline;
    }

    /* Badge styling */
    .badge {
        padding: 6px 10px;
        font-weight: 500;
        font-size: 0.75rem;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        line-height: 1;
    }

    .badge i {
        font-size: 12px;
        margin-right: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: baseline;
    }

    .badge.bg-primary {
        background-color: #0d6efd !important;
        box-shadow: 0 2px 5px rgba(13, 110, 253, 0.2);
    }

    .badge.bg-success {
        background-color: #198754 !important;
        box-shadow: 0 2px 5px rgba(25, 135, 84, 0.2);
    }

    .badge.bg-danger {
        background-color: #dc3545 !important;
        box-shadow: 0 2px 5px rgba(220, 53, 69, 0.2);
    }

    .badge.bg-warning {
        background-color: #ffc107 !important;
        color: #212529;
        box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
    }

    .badge.bg-info {
        background-color: #0dcaf0 !important;
        box-shadow: 0 2px 5px rgba(13, 202, 240, 0.2);
    }

    /* Company info card styling */
    .company-info-card {
        border-radius: 12px;
        transition: all 0.3s ease;
        margin-bottom: 16px;
    }

    .company-info-item {
        border-radius: 10px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .company-info-item:hover {
        background-color: #f0f4f8;
        transform: translateY(-2px);
    }

    .detail-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .detail-info p.fw-bold {
        color: #344767;
        font-size: 0.9rem;
    }

    .detail-info p:not(.fw-bold) {
        color: #495057;
        font-size: 0.95rem;
    }

    /* Summary card styling */
    .summary-card {
        border-radius: 12px;
    }

    .summary-card .card-title {
        color: #344767;
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f8f9fa;
    }

    .summary-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .summary-label {
        color: #6c757d;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .summary-value {
        color: #344767;
        font-weight: 600;
        font-size: 0.9rem;
    }

    /* Chart styling */
    #charthpskontrak {
        margin: 0 auto;
    }

    /* Pagination styling */
    .pagination {
        margin-top: 20px;
        margin-bottom: 0;
    }

    .pagination .page-link {
        color: #0d6efd;
        border-radius: 6px;
        margin: 0 3px;
        border: 1px solid #dee2e6;
        transition: all 0.2s ease;
    }

    .pagination .page-link:hover {
        background-color: #f1f1f1;
        border-color: #dee2e6;
    }

    .pagination .active .page-link {
        background-color: #0d6efd;
        border-color: #0d6efd;
        color: white;
        box-shadow: 0 2px 5px rgba(13, 110, 253, 0.2);
    }

    /* Company header styling */
    .company-header {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
    }

    .company-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: #344767;
        margin-bottom: 8px;
    }

    .company-npwp {
        font-size: 0.95rem;
        color: #6c757d;
    }

    .company-total {
        font-size: 2.5rem;
        font-weight: 700;
        color: #0d6efd;
        text-align: right;
    }
</style>
@apexchartsScripts
@endsection

@section('content')

<div class="card company-header">
    <div class="row align-items-center">
        <div class="col-lg-8">
            <div class="d-flex align-items-center">
                <div class="product-box me-3" style="width: 70px; height: 70px; background-color: rgba(13, 110, 253, 0.1);">
                    <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                </div>
                <div>
                    <h2 class="company-name mb-2">{{ $perusahaan->nama_peserta }}</h2>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-light text-dark me-3">
                            <i class="bi bi-credit-card-2-front me-1"></i>
                            NPWP: {{ $perusahaan->npwp ?? $perusahaan->npwp_blur }}
                        </span>

                        @if($perusahaan->alamat)
                        <span class="badge bg-light text-dark">
                            <i class="bi bi-geo-alt me-1"></i>
                            {{ Str::limit($perusahaan->alamat, 50) }}
                        </span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mt-4 mt-lg-0">
            <div class="text-lg-end">
                <p class="text-muted mb-1">Total Nilai Kontrak</p>
                @php
                    $total = $totalNilaiKontrak + $totalNilaiPaketPengadaan;
                @endphp
                <h2 class="company-total mb-0">Rp{{ format_number_short($total) }}</h2>
                <div class="d-flex justify-content-lg-end mt-2">
                    <span class="badge bg-success me-2 pt-2">
                        <i class="bi bi-trophy me-1"></i>
                        {{ $totalTenderDimenangkan }} Tender
                    </span>
                    <span class="badge bg-primary me-2 pt-2">
                        <i class="bi bi-cart-check me-1"></i>
                        {{ $totalPaketPengadaan }} E-Katalog
                    </span>
                    <a href="{{ route('perusahaan.analysis', ['pembeda' => encrypt($perusahaan->pembeda)]) }}" class="btn btn-sm btn-light">
                        <i class="material-icons-outlined text-dark" style="font-size: 14px; vertical-align: text-bottom;">analytics</i> <span class="text-dark">Analisis</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12 col-lg-8">
        @if ($totalPaketPengadaan > 0)
        <div class="card mb-4 w-100">
            <div class="card-header py-2">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-cart-check text-success me-2"></i>
                        <h5 class="card-title mb-0">Paket E-Katalog</h5>
                        <span class="badge bg-success ms-2 rounded-pill">{{ $totalPaketPengadaan }}</span>
                    </div>
                    <div>
                        <span class="badge bg-primary me-1 rounded-0">E-Katalog 5</span>
                        <span class="badge bg-success rounded-0">E-Katalog 6</span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="product-table">
                    <div class="table-responsive">
                        <table class="table align-middle">
                            <thead>
                                <tr>
                                    <th>Nama Paket</th>
                                    <th>Tanggal Klik</th>
                                    <th>Nilai Kontrak</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($paketPengadaan as $paket)
                                @php
                                $nama_klpd = $paket->instansi_pembeli;
                                if (strpos($nama_klpd, "Kab.") !== false) {
                                    $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
                                }
                                $nama_lpse = "LPSE {$nama_klpd}";
                                $lpse = Lpse::where('nama_lpse', $nama_lpse)->first();
                                if (!$lpse) {
                                    $lpse = Lpse::where('nama_lpse', 'LIKE', "%{$nama_klpd}%")->first();
                                }
                                @endphp
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center gap-3">
                                            <div class="product-box">
                                                @if($lpse)
                                                <img src="{{ asset(str_replace('http://proyeklpse.test', '', $lpse->logo_path)) }}" alt="Logo" class="rounded-3">
                                                @else
                                                <img src="{{ asset('build/images/logo-default.png') }}" alt="Logo" class="rounded-3">
                                                @endif
                                            </div>
                                            <div class="product-info">
                                                <a href="{{ route('ekatalog.show', ['id' => $paket->id_paket]) }}" class="product-title">
                                                    {{ $paket->nama_paket }}
                                                </a>
                                                <div class="d-flex align-items-center mt-1">
                                                    @if(isset($paket->katalog_version) && $paket->katalog_version === 'E-Katalog 6')
                                                        <span class="badge bg-success me-2 rounded-0">E-Katalog 6</span>
                                                    @else
                                                        <span class="badge bg-primary me-2 rounded-0">{{ $paket->jenis_katalog ?? 'E-Katalog 5' }}</span>
                                                    @endif
                                                    <p class="mb-0 product-category">
                                                        {{ $paket->pengelola }} - {{$paket->instansi_pembeli }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-calendar-event me-2 text-muted"></i>
                                            <span>{{ format_date_indonesian($paket->tanggal_paket) }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-semibold text-success">{{ convert_to_rupiah($paket->nilai_kontrak) }}</span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>

                        <div class="d-flex justify-content-center mt-4">
                            {{ $paketPengadaan->links('vendor.pagination.costum') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <div class="card mb-4 w-100">
            <div class="card-header py-2">
                <div class="d-flex align-items-center">
                    <i class="bi bi-trophy text-warning me-2"></i>
                    <h5 class="card-title mb-0">Tender Dimenangkan</h5>
                    <span class="badge bg-success ms-2 rounded-pill">{{ $totalTenderDimenangkan }}</span>
                </div>
            </div>
            <div class="card-body">
                <div class="product-table">
                    <div class="table-responsive">
                        <table class="table align-middle">
                            <thead>
                                <tr>
                                    <th>Nama Tender</th>
                                    <th>HPS</th>
                                    <th>Nilai Kontrak</th>
                                    <th>Skor Penawaran</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($tenderDimenangkan as $tender)
                                @php
                                $tender->status_tender = TenderHelper::normalizeTenderStatus($tender->status_tender);
                                $nilaiKontrak = $tender->pemenang->first()->harga_penawaran;
                                if ($nilaiKontrak < 1) {
                                    $nilaiKontrak = $tender->pemenang->first()->harga_terkoreksi;
                                }

                                // Hitung skor penawaran (rasio nilai kontrak terhadap HPS)
                                $skorPenawaran = 0;
                                if ($tender->hps > 0) {
                                    $skorPenawaran = ($nilaiKontrak / $tender->hps) * 100;
                                }

                                // Tentukan warna skor
                                if ($skorPenawaran >= 95) {
                                    $skorClass = 'text-success';
                                } elseif ($skorPenawaran >= 85) {
                                    $skorClass = 'text-primary';
                                } else {
                                    $skorClass = 'text-warning';
                                }
                                @endphp
                                <tr class="{{ $tender->status_tender !== 'Aktif' ? 'bg-warning-important' : '' }}">
                                    <td>
                                        <div class="d-flex align-items-center gap-3">
                                            <div class="product-box">
                                                <img src="{{ asset(str_replace('http://proyeklpse.test', '', $tender->lpse->logo_path)) }}" alt="Logo" class="rounded-3">
                                            </div>
                                            <div class="product-info">
                                                <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}" class="product-title">
                                                    {{ $tender->nama_paket }}
                                                </a>
                                                <div class="d-flex align-items-center mt-1">
                                                    <span class="badge bg-light text-dark me-2">
                                                        <i class="bi bi-hash me-1"></i>
                                                        {{ $tender->kode_tender }}
                                                    </span>
                                                    <p class="mb-0 product-category">
                                                        <a href="{{ route('lpse.dashboard', ['kode_lpse' => $tender->lpse->kd_lpse]) }}">
                                                            {{ $tender->lpse->nama_lpse }}
                                                        </a>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-semibold">{{ format_number_short($tender->hps) }}</span>
                                    </td>
                                    <td>
                                        <span class="fw-semibold text-success">{{ format_number_short($nilaiKontrak) }}</span>
                                    </td>
                                    <td>
                                        <span class="fw-semibold {{ $skorClass }}">{{ number_format($skorPenawaran, 1) }}%</span>
                                        @if($skorPenawaran >= 95)
                                            <i class="bi bi-star-fill text-warning ms-1" data-bs-toggle="tooltip" title="Sangat Baik"></i>
                                        @elseif($skorPenawaran >= 85)
                                            <i class="bi bi-star-half text-warning ms-1" data-bs-toggle="tooltip" title="Baik"></i>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>

                        <div class="d-flex justify-content-center mt-4">
                            {{ $tenderDimenangkan->links('vendor.pagination.costum') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-4 w-100">
            <div class="card-header py-2">
                <div class="d-flex align-items-center">
                    <i class="bi bi-list-check text-primary me-2"></i>
                    <h5 class="card-title mb-0">Tender Diikuti</h5>
                    <span class="badge bg-primary ms-2 rounded-pill">{{ $totalTenderDiikuti }}</span>
                </div>
            </div>
            <div class="card-body">
                <div class="product-table">
                    <div class="table-responsive">
                        <table class="table align-middle">
                            <thead>
                                <tr>
                                    <th>Nama Tender</th>
                                    <th>Pagu</th>
                                    <th>HPS</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($tenderDiikuti as $tender)
                                @php
                                $tender->status_tender = TenderHelper::normalizeTenderStatus($tender->status_tender);
                                $isWinner = $tenderDimenangkan->contains('kode_tender', $tender->kode_tender);
                                $rowClass = '';
                                if ($isWinner) {
                                    $rowClass = 'bg-success';
                                } elseif ($tender->status_tender !== 'Aktif') {
                                    $rowClass = 'bg-danger-subtle';
                                }
                                @endphp
                                <tr class="{{ $rowClass }}">
                                    <td>
                                        <div class="d-flex align-items-center gap-3">
                                            <div class="product-box">
                                                <img src="{{ asset(str_replace('http://proyeklpse.test', '', $tender->lpse->logo_path)) }}" alt="Logo" class="rounded-3">
                                            </div>
                                            <div class="product-info">
                                                <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}" class="product-title">
                                                    {{ $tender->nama_paket }}
                                                </a>
                                                <div class="d-flex align-items-center mt-1">
                                                    <span class="badge bg-light text-dark me-2">
                                                        <i class="bi bi-hash me-1"></i>
                                                        {{ $tender->kode_tender }}
                                                    </span>
                                                    <p class="mb-0 product-category">
                                                        <a href="{{ route('lpse.dashboard', ['kode_lpse' => $tender->lpse->kd_lpse]) }}">
                                                            {{ $tender->lpse->nama_lpse }}
                                                        </a>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-semibold">{{ format_number_short($tender->pagu) }}</span>
                                    </td>
                                    <td>
                                        <span class="fw-semibold">{{ format_number_short($tender->hps) }}</span>
                                    </td>
                                    <td>
                                        @if($isWinner)
                                            <span class="badge bg-success">
                                                <i class="bi bi-trophy-fill me-1"></i>
                                                <span>Menang</span>
                                            </span>
                                        @elseif($tender->status_tender === 'Aktif')
                                            <span class="badge bg-primary">
                                                <i class="bi bi-play-circle-fill me-1"></i>
                                                <span>Aktif</span>
                                            </span>
                                        @else
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle-fill me-1"></i>
                                                <span>Batal</span>
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                        <div class="d-flex justify-content-center mt-4">
                            {{ $tenderDiikuti->links('vendor.pagination.costum') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-lg-4">
        <div class="w-100">
            <div class="card company-info-card">
                <div class="card-header py-3">
                    <h5 class="card-title d-flex align-items-center mb-0">
                        <i class="bi bi-info-circle me-2 text-primary"></i>
                        Informasi Perusahaan
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="company-info-item p-3">
                            <div class="d-flex align-items-start gap-3">
                                <div class="detail-icon">
                                    <i class="bi bi-building"></i>
                                </div>
                                <div class="detail-info">
                                    <p class="fw-bold mb-1">Nama Perusahaan</p>
                                    <p class="mb-0">{{ $perusahaan->nama_peserta }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="company-info-item p-3">
                            <div class="d-flex align-items-start gap-3">
                                <div class="detail-icon">
                                    <i class="bi bi-credit-card-2-front"></i>
                                </div>
                                <div class="detail-info">
                                    <p class="fw-bold mb-1">NPWP</p>
                                    <p class="mb-0">{{ $perusahaan->npwp ?? $perusahaan->npwp_blur }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($perusahaan->alamat)
                    <div class="mb-0">
                        <div class="company-info-item p-3">
                            <div class="d-flex align-items-start gap-3">
                                <div class="detail-icon">
                                    <i class="bi bi-geo-alt"></i>
                                </div>
                                <div class="detail-info">
                                    <p class="fw-bold mb-1">Alamat</p>
                                    <p class="mb-0">{{ $perusahaan->alamat }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            @if ($totalPaketPengadaan > 0)
            <div class="card summary-card">
                <div class="card-header py-3">
                    <h5 class="card-title d-flex align-items-center mb-0">
                        <i class="bi bi-cart-check me-2 text-success"></i>
                        Ringkasan E-Katalog
                    </h5>
                </div>
                <div class="card-body">
                    <div class="summary-item">
                        <span class="summary-label">
                            <i class="bi bi-box-seam me-1 text-primary"></i>
                            Total Paket Diklik
                        </span>
                        <span class="summary-value badge bg-primary">{{ $totalPaketPengadaan }}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">
                            <i class="bi bi-cash-stack me-1 text-success"></i>
                            Total Nilai Kontrak
                        </span>
                        <span class="summary-value">{{ convert_to_rupiah($totalNilaiPaketPengadaan)}}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">
                            <i class="bi bi-percent me-1 text-warning"></i>
                            Persentase dari Total
                        </span>
                        <span class="summary-value">
                            @php
                                $total = $totalNilaiKontrak + $totalNilaiPaketPengadaan;
                                $percentage = $total > 0 ? ($totalNilaiPaketPengadaan / $total) * 100 : 0;
                            @endphp
                            {{ number_format($percentage, 1) }}%
                        </span>
                    </div>
                </div>
            </div>
            @endif

            <div class="card summary-card">
                <div class="card-header py-3">
                    <h5 class="card-title d-flex align-items-center mb-0">
                        <i class="bi bi-trophy me-2 text-warning"></i>
                        Ringkasan Tender
                    </h5>
                </div>
                <div class="card-body">
                    <div class="summary-item">
                        <span class="summary-label">
                            <i class="bi bi-list-check me-1 text-primary"></i>
                            Total Tender Diikuti
                        </span>
                        <span class="summary-value badge bg-primary">{{ $totalTenderDiikuti }}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">
                            <i class="bi bi-trophy me-1 text-success"></i>
                            Tender Dimenangkan
                        </span>
                        <span class="summary-value badge bg-success">{{ $totalTenderDimenangkan }}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">
                            <i class="bi bi-graph-up-arrow me-1 text-info"></i>
                            Tingkat Keberhasilan
                        </span>
                        <span class="summary-value">
                            @php
                                $successRate = $totalTenderDiikuti > 0 ? ($totalTenderDimenangkan / $totalTenderDiikuti) * 100 : 0;
                            @endphp
                            {{ number_format($successRate, 1) }}%
                        </span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">
                            <i class="bi bi-cash-coin me-1 text-primary"></i>
                            Total Nilai HPS
                        </span>
                        <span class="summary-value">{{ convert_to_rupiah($totalHps) }}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">
                            <i class="bi bi-cash-stack me-1 text-success"></i>
                            Total Nilai Kontrak
                        </span>
                        <span class="summary-value">{{ convert_to_rupiah($totalNilaiKontrak) }}</span>
                    </div>
                </div>
            </div>

            <div class="card summary-card">
                <div class="card-header py-3">
                    <h5 class="card-title d-flex align-items-center mb-0">
                        <i class="bi bi-graph-up-arrow me-2 text-info"></i>
                        Skor Penawaran Proyek
                    </h5>
                </div>
                <div class="card-body">
                    <div id="charthpskontrak" class="mt-3"></div>
                    <div class="text-center mt-3">
                        <p class="text-muted mb-1">Skor Penawaran Proyek</p>
                        @php
                            $percentage = ($totalHps > 0) ? ($totalNilaiKontrak / $totalHps) * 100 : 0;

                            if ($percentage == 0) {
                                $scoreClass = 'text-muted';
                                $scoreText = 'Belum Ada Penawaran';
                            } elseif ($percentage >= 95) {
                                $scoreClass = 'text-success';
                                $scoreText = 'Sangat Baik';
                            } elseif ($percentage >= 85) {
                                $scoreClass = 'text-primary';
                                $scoreText = 'Baik';
                            } else {
                                $scoreClass = 'text-warning';
                                $scoreText = 'Cukup';
                            }
                        @endphp
                        <h4 class="{{ $scoreClass }} fw-bold">{{ $scoreText }}</h4>
                        <p class="small text-muted">
                            @if($percentage > 0)
                                Semakin tinggi persentase, semakin baik skor penawaran
                            @else
                                Perusahaan belum memiliki data penawaran yang cukup
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="{{ URL::asset('build/plugins/apexchart/apexcharts.min.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var totalNilaiKontrak = {{ $totalNilaiKontrak }};
        var totalHps = {{ $totalHps }};

        function formatRupiah(angka) {
            return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(angka);
        }

        var percentage = totalHps > 0 ? ((totalNilaiKontrak / totalHps) * 100).toFixed(2) : 0;

        // Determine color based on percentage
        var color;
        if (percentage == 0) {
            color = '#6c757d'; // muted
        } else if (percentage >= 95) {
            color = '#198754'; // success
        } else if (percentage >= 85) {
            color = '#0d6efd'; // primary
        } else {
            color = '#ffc107'; // warning
        }

        var options = {
            series: [parseFloat(percentage)],
            chart: {
                height: 280,
                type: 'radialBar',
                offsetY: -10,
                fontFamily: 'inherit',
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800,
                    animateGradually: {
                        enabled: true,
                        delay: 150
                    },
                    dynamicAnimation: {
                        enabled: true,
                        speed: 350
                    }
                }
            },
            plotOptions: {
                radialBar: {
                    startAngle: -135,
                    endAngle: 135,
                    hollow: {
                        margin: 0,
                        size: '70%',
                        background: '#fff',
                        image: undefined,
                        imageOffsetX: 0,
                        imageOffsetY: 0,
                        position: 'front',
                        dropShadow: {
                            enabled: true,
                            top: 3,
                            left: 0,
                            blur: 4,
                            opacity: 0.1
                        }
                    },
                    track: {
                        background: '#f2f2f2',
                        strokeWidth: '67%',
                        margin: 0,
                        dropShadow: {
                            enabled: true,
                            top: -3,
                            left: 0,
                            blur: 4,
                            opacity: 0.1
                        }
                    },
                    dataLabels: {
                        show: true,
                        name: {
                            offsetY: -10,
                            show: true,
                            color: '#888',
                            fontSize: '14px'
                        },
                        value: {
                            formatter: function(val) {
                                return val + "%";
                            },
                            color: '#111',
                            fontSize: '30px',
                            fontWeight: 'bold',
                            show: true,
                            offsetY: 5
                        }
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shade: 'dark',
                    type: 'horizontal',
                    shadeIntensity: 0.5,
                    gradientToColors: [color],
                    inverseColors: false,
                    opacityFrom: 1,
                    opacityTo: 1,
                    stops: [0, 100]
                }
            },
            stroke: {
                lineCap: 'round',
                dashArray: 4
            },
            labels: ['Rasio Kontrak/HPS'],
            tooltip: {
                enabled: true,
                y: {
                    formatter: function(val) {
                        if (val == 0) {
                            return "Belum ada data penawaran";
                        } else {
                            return "Skor: " + val + "% - Nilai Kontrak: " + formatRupiah(totalNilaiKontrak) + " dari HPS: " + formatRupiah(totalHps);
                        }
                    }
                }
            }
        };

        var chart = new ApexCharts(document.querySelector("#charthpskontrak"), options);
        chart.render();
    });
</script>
@endsection
