@extends('layouts.master')

@section('title', "<PERSON><PERSON><PERSON> - {$perusahaan->nama_pese<PERSON>}")

@section('css')
<style>
    /* Card styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 16px 24px;
        display: flex;
        align-items: center;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .card-body {
        padding: 24px;
    }

    .company-header {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
    }

    .company-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: #344767;
        margin-bottom: 8px;
    }

    .company-npwp {
        font-size: 0.95rem;
        color: #6c757d;
    }

    /* Competitor styling */
    .competitor-item {
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 0;
        transition: all 0.2s ease;
    }

    .competitor-item:hover {
        background-color: #f8f9fa;
    }

    .competitor-item:last-child {
        border-bottom: none;
    }

    .competitor-name {
        font-weight: 600;
        color: #344767;
        font-size: 0.9rem;
    }

    .competitor-name a:hover {
        color: #0d6efd !important;
    }

    /* Badge styling */
    .badge {
        padding: 6px 10px;
        font-weight: 500;
        font-size: 0.75rem;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        line-height: 1;
    }

    .badge i {
        font-size: 12px;
        margin-right: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: baseline;
    }

    .badge.bg-primary {
        background-color: #0d6efd !important;
        box-shadow: 0 2px 5px rgba(13, 110, 253, 0.2);
    }

    .badge.bg-success {
        background-color: #198754 !important;
        box-shadow: 0 2px 5px rgba(25, 135, 84, 0.2);
    }

    .badge.bg-danger {
        background-color: #dc3545 !important;
        box-shadow: 0 2px 5px rgba(220, 53, 69, 0.2);
    }

    .badge.bg-warning {
        background-color: #ffc107 !important;
        color: #212529;
        box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
    }

    .badge.bg-info {
        background-color: #0dcaf0 !important;
        box-shadow: 0 2px 5px rgba(13, 202, 240, 0.2);
    }

    .badge-count {
        background-color: #0d6efd;
        color: white;
        padding: 5px 10px;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
        box-shadow: 0 2px 5px rgba(13, 110, 253, 0.2);
    }

    .badge-win {
        background-color: #198754;
        color: white;
        padding: 5px 10px;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
        box-shadow: 0 2px 5px rgba(25, 135, 84, 0.2);
    }

    /* Project type styling */
    .project-type-item {
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.2s ease;
    }

    .project-type-item:hover {
        background-color: #f8f9fa;
    }

    .project-type-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .project-type-name {
        font-weight: 600;
        color: #344767;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .project-type-name a:hover {
        color: #0d6efd !important;
    }

    .project-type-stats {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 12px;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .stat-value {
        font-weight: 600;
        color: #344767;
        font-size: 0.85rem;
    }

    /* Tender item styling */
    .tender-item {
        padding: 0;
        border-radius: 12px;
        margin-bottom: 24px;
        background-color: #ffffff;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .tender-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        border-color: #d1d9e6;
    }

    .tender-item.winner {
        border-top: 3px solid #198754;
    }

    .tender-item:not(.winner) {
        border-top: 3px solid #6c757d;
    }

    .tender-header {
        padding: 20px 20px 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .tender-title {
        font-weight: 600;
        color: #344767;
        font-size: 1rem;
        line-height: 1.4;
    }

    .tender-title a:hover {
        color: #0d6efd !important;
    }

    .tender-instansi {
        font-size: 0.85rem;
        color: #6c757d;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .lpse-logo-container {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        overflow: hidden;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #e9ecef;
        flex-shrink: 0;
    }

    .lpse-logo {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .lpse-logo-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #e9ecef;
        color: #6c757d;
    }

    .lpse-logo-placeholder i {
        font-size: 24px;
    }

    .status-badge {
        padding: 6px 10px;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .tender-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        padding: 15px 20px;
        background-color: #ffffff;
        border-bottom: 1px solid #e9ecef;
    }

    .tender-meta-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 0.85rem;
        color: #6c757d;
    }

    .tender-budget {
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .budget-item {
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;
    }

    .budget-label {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .budget-value {
        font-weight: 700;
        color: #344767;
        font-size: 1.1rem;
    }

    .budget-short {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 2px;
    }

    .tender-actions {
        padding: 15px 20px;
        background-color: #ffffff;
    }

    /* Button styling */
    .btn-with-icon {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        transition: all 0.3s ease;
    }

    .btn-with-icon i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-with-icon:hover i {
        transform: translateY(-1px);
    }

    .btn-outline-secondary.btn-with-icon:hover {
        background-color: #6c757d;
        color: white;
    }

    .btn-primary.btn-with-icon {
        background: linear-gradient(135deg, #4481eb, #04befe);
        border: none;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn-primary.btn-with-icon:hover {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
    }

    @media (max-width: 767.98px) {
        .tender-actions .row > div {
            margin-bottom: 10px;
            text-align: center;
        }

        .tender-actions .row > div:last-child {
            margin-bottom: 0;
            text-align: center;
        }

        .btn-with-icon {
            width: 100%;
            justify-content: center;
        }
    }

    .success-rate-card {
        text-align: center;
        padding: 20px;
    }

    .success-rate-value {
        font-size: 3rem;
        font-weight: 700;
        color: #0d6efd;
    }

    .success-rate-label {
        font-size: 1rem;
        color: #6c757d;
        margin-top: 5px;
    }

    .success-rate-stats {
        display: flex;
        justify-content: space-around;
        margin-top: 20px;
    }

    .success-rate-stat {
        text-align: center;
    }

    .success-rate-stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #344767;
    }

    .success-rate-stat-label {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .chart-container {
        height: 300px;
    }

    .back-button {
        margin-bottom: 20px;
    }

    .pagination .page-link {
        color: #0d6efd;
    }

    .pagination .page-link:hover {
        background-color: #f1f1f1;
        border-color: #dee2e6;
    }

    .pagination .active .page-link {
        background-color: #0d6efd;
        border-color: #0d6efd;
        color: white;
        box-shadow: 0 2px 5px rgba(13, 110, 253, 0.2);
    }
</style>
@apexchartsScripts
@endsection

@section('content')
<div class="back-button text-end mb-3">
    <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($perusahaan->pembeda)]) }}" class="btn btn-light">
        <i class="material-icons-outlined text-dark" style="font-size: 16px; vertical-align: text-bottom;">arrow_back</i> <span class="text-dark">Kembali ke Detail Perusahaan</span>
    </a>
</div>

<div class="card company-header">
    <div class="row align-items-center">
        <div class="col-lg-8">
            <div class="d-flex align-items-center">
                <div class="product-box me-3 d-flex align-items-center justify-content-center" style="width: 70px; height: 70px; background-color: rgba(13, 110, 253, 0.1); border-radius: 12px;">
                    <i class="material-icons-outlined text-primary" style="font-size: 2.2rem;">business</i>
                </div>
                <div>
                    <h2 class="company-name mb-2">{{ $perusahaan->nama_peserta }}</h2>
                    <div class="d-flex align-items-center flex-wrap gap-2">
                        <span class="badge bg-light text-dark">
                            <i class="material-icons-outlined" style="font-size: 14px;">credit_card</i>
                            NPWP: {{ $perusahaan->npwp ?? $perusahaan->npwp_blur }}
                        </span>

                        @if($perusahaan->alamat)
                        <span class="badge bg-light text-dark">
                            <i class="material-icons-outlined" style="font-size: 14px;">location_on</i>
                            {{ Str::limit($perusahaan->alamat, 50) }}
                        </span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mt-4 mt-lg-0">
            <div class="text-lg-end">
                <p class="text-muted mb-1">Tingkat Keberhasilan Tender</p>
                <h2 class="company-total mb-0">{{ is_object($successRate) ? number_format($successRate->success_rate, 1) : '0.0' }}%</h2>
                <div class="d-flex justify-content-lg-end mt-2 gap-2">
                    <span class="badge bg-light text-dark">
                        <i class="material-icons-outlined text-success" style="font-size: 14px; vertical-align: text-bottom;">emoji_events</i>
                        {{ is_object($successRate) ? $successRate->total_menang : 0 }} Menang
                    </span>
                    <span class="badge bg-light text-dark">
                        <i class="material-icons-outlined text-primary" style="font-size: 14px; vertical-align: text-bottom;">assignment</i>
                        {{ is_object($successRate) ? $successRate->total_tender : 0 }} Diikuti
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Grafik Tren Partisipasi -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">trending_up</i>
                <h5 class="card-title mb-0">Tren Partisipasi Tender (12 Bulan Terakhir)</h5>
            </div>
            <div class="card-body">
                <div id="participation-chart" class="chart-container"></div>
            </div>
        </div>

        <!-- Tender yang Diikuti -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">assignment</i>
                <h5 class="card-title mb-0">Tender Terbaru yang Diikuti</h5>
            </div>
            <div class="card-body">
                @foreach($tenderParticipation as $tender)
                <div class="tender-item {{ $tender->is_winner ? 'winner' : '' }}">
                    <div class="tender-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="d-flex align-items-start">
                                <div class="lpse-logo-container me-3">
                                    @if($tender->logo_lpse)
                                    <img src="{{ asset(str_replace('http://proyeklpse.test', '', $tender->logo_lpse)) }}" alt="{{ $tender->nama_lpse ?? 'LPSE' }}" class="lpse-logo">
                                    @else
                                    <div class="lpse-logo-placeholder">
                                        <i class="material-icons-outlined">account_balance</i>
                                    </div>
                                    @endif
                                </div>
                                <div>
                                    <div class="tender-title">
                                        <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}" class="text-decoration-none text-dark">
                                            {{ $tender->nama_paket }}
                                        </a>
                                    </div>
                                    <div class="tender-instansi mt-1">
                                        <i class="material-icons-outlined" style="font-size: 14px; vertical-align: text-bottom;">business</i>
                                        <span>{{ $tender->nama_lpse ?? 'Tidak tersedia' }}</span>
                                    </div>
                                </div>
                            </div>
                            <span class="badge {{ $tender->is_winner ? 'bg-success' : 'bg-secondary' }} status-badge">
                                <i class="material-icons-outlined" style="font-size: 14px; vertical-align: text-bottom;">{{ $tender->is_winner ? 'emoji_events' : 'cancel' }}</i>
                                {{ $tender->is_winner ? 'Menang' : 'Tidak Menang' }}
                            </span>
                        </div>
                    </div>

                    <div class="tender-meta">
                        <div class="tender-meta-item">
                            <i class="material-icons-outlined text-primary" style="font-size: 14px;">tag</i>
                            <span>{{ $tender->kode_tender }}</span>
                        </div>
                        <div class="tender-meta-item">
                            <i class="material-icons-outlined text-info" style="font-size: 14px;">category</i>
                            <span>{{ $tender->kategori_pekerjaan }}</span>
                        </div>
                        <div class="tender-meta-item">
                            <i class="material-icons-outlined text-warning" style="font-size: 14px;">event</i>
                            <span>{{ \Carbon\Carbon::parse($tender->tanggal_paket_tayang)->format('d M Y') }}</span>
                        </div>
                        <div class="tender-meta-item">
                            <i class="material-icons-outlined text-secondary" style="font-size: 14px;">people</i>
                            <span>{{ $tender->jumlah_peserta }} Peserta</span>
                        </div>
                    </div>

                    <div class="tender-budget">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="budget-item">
                                    <div class="budget-label">
                                        <i class="material-icons-outlined text-success" style="font-size: 14px;">account_balance</i>
                                        <span>Pagu Anggaran</span>
                                    </div>
                                    <div class="budget-value">Rp{{ number_format($tender->pagu, 0, ',', '.') }}</div>
                                    <div class="budget-short">{{ format_number_short($tender->pagu) }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="budget-item">
                                    <div class="budget-label">
                                        <i class="material-icons-outlined text-primary" style="font-size: 14px;">payments</i>
                                        <span>HPS</span>
                                    </div>
                                    <div class="budget-value">
                                        @if(isset($tender->hps) && $tender->hps > 0)
                                            Rp{{ number_format($tender->hps, 0, ',', '.') }}
                                            <div class="budget-short">{{ format_number_short($tender->hps) }}</div>
                                        @else
                                            <span class="text-muted">Tidak tersedia</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tender-actions">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                @if($tender->url_tender)
                                <a href="{{ $tender->url_tender }}/lelang/{{ $tender->kode_tender }}" target="_blank" class="btn btn-sm btn-outline-secondary me-2 btn-with-icon">
                                    <i class="material-icons-outlined">open_in_new</i>
                                    <span>Lihat di LPSE</span>
                                </a>
                                @endif
                            </div>
                            <div class="col-md-6 text-md-end">
                                <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}" class="btn btn-sm btn-primary btn-with-icon">
                                    <i class="material-icons-outlined">visibility</i>
                                    <span>Lihat Detail</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Statistik Keberhasilan -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">emoji_events</i>
                <h5 class="card-title mb-0">Statistik Keberhasilan</h5>
            </div>
            <div class="card-body success-rate-card">
                <div class="success-rate-value">{{ is_object($successRate) ? number_format($successRate->success_rate, 1) : '0.0' }}%</div>
                <div class="success-rate-label">Tingkat Keberhasilan</div>
                <div class="success-rate-stats">
                    <div class="success-rate-stat">
                        <div class="success-rate-stat-value">{{ is_object($successRate) ? $successRate->total_tender : 0 }}</div>
                        <div class="success-rate-stat-label">Total Tender</div>
                    </div>
                    <div class="success-rate-stat">
                        <div class="success-rate-stat-value">{{ is_object($successRate) ? $successRate->total_menang : 0 }}</div>
                        <div class="success-rate-stat-label">Tender Dimenangkan</div>
                    </div>
                </div>
                <hr>
                <div class="success-rate-stats">
                    <div class="success-rate-stat">
                        <div class="success-rate-stat-value">Rp{{ is_object($successRate) ? number_format($successRate->total_pagu / 1000000000, 1) : '0.0' }}M</div>
                        <div class="success-rate-stat-label">Total Pagu</div>
                    </div>
                    <div class="success-rate-stat">
                        <div class="success-rate-stat-value">Rp{{ is_object($successRate) ? number_format($successRate->total_nilai_kontrak / 1000000000, 1) : '0.0' }}M</div>
                        <div class="success-rate-stat-label">Total Nilai Kontrak</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kompetitor Utama -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">people</i>
                <h5 class="card-title mb-0">Kompetitor Utama</h5>
            </div>
            <div class="card-body">
                @foreach($competitors as $competitor)
                <div class="competitor-item p-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="competitor-name">
                            <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($competitor->pembeda)]) }}" class="text-decoration-none text-dark">
                                {{ $competitor->nama_peserta }}
                            </a>
                        </div>
                        <div class="d-flex gap-2">
                            <span class="badge bg-light text-dark pt-2" title="Jumlah tender yang sama">
                                <i class="material-icons-outlined text-primary" style="font-size: 14px; vertical-align: text-bottom;">assignment</i>
                                {{ $competitor->jumlah_tender_sama }}
                            </span>
                            <span class="badge bg-light text-dark pt-2" title="Jumlah tender yang dimenangkan">
                                <i class="material-icons-outlined text-success" style="font-size: 14px; vertical-align: text-bottom;">emoji_events</i>
                                {{ $competitor->jumlah_menang }}
                            </span>
                            <a href="{{ route('perusahaan.analysis', ['pembeda' => encrypt($competitor->pembeda)]) }}" class="btn btn-sm btn-light" title="Analisis Perusahaan">
                                <i class="material-icons-outlined text-dark" style="font-size: 14px; vertical-align: text-bottom;">analytics</i>
                            </a>
                        </div>
                    </div>
                    <div class="small text-muted">
                        <i class="material-icons-outlined" style="font-size: 12px; vertical-align: text-bottom;">credit_card</i>
                        NPWP: {{ $competitor->npwp ?? $competitor->npwp_blur }}
                    </div>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Jenis Proyek -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">category</i>
                <h5 class="card-title mb-0">Jenis Proyek yang Diikuti</h5>
            </div>
            <div class="card-body">
                @foreach($projectTypes as $projectType)
                <div class="project-type-item p-2">
                    <div class="project-type-name">
                        <i class="material-icons-outlined me-1" style="font-size: 16px; vertical-align: text-bottom;">category</i>
                        <a href="{{ route('tender_lpse.index', ['kategori' => $projectType->kategori_pekerjaan]) }}" class="text-decoration-none text-dark">
                            {{ $projectType->kategori_pekerjaan }}
                        </a>
                    </div>
                    <div class="project-type-stats">
                        <div class="stat-item">
                            <i class="material-icons-outlined text-primary" style="font-size: 14px;">assignment</i>
                            <span class="stat-label">Diikuti:</span>
                            <span class="stat-value">{{ $projectType->jumlah }}</span>
                        </div>
                        <div class="stat-item">
                            <i class="material-icons-outlined text-success" style="font-size: 14px;">emoji_events</i>
                            <span class="stat-label">Menang:</span>
                            <span class="stat-value">{{ $projectType->jumlah_menang }}</span>
                        </div>
                        <div class="stat-item">
                            <i class="material-icons-outlined text-info" style="font-size: 14px;">payments</i>
                            <span class="stat-label">Rata-rata Pagu:</span>
                            <span class="stat-value">Rp{{ number_format($projectType->rata_rata_pagu, 0, ',', '.') }}</span>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="{{ URL::asset('build/plugins/apexchart/apexcharts.min.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Data untuk grafik partisipasi
        const participationData = @json($participationTrend);

        // Memformat data untuk chart
        const months = participationData.map(item => {
            const [year, month] = item.bulan.split('-');
            return new Date(year, month - 1).toLocaleString('id-ID', { month: 'short', year: 'numeric' });
        });

        const participated = participationData.map(item => item.jumlah_tender);
        const won = participationData.map(item => item.jumlah_menang);

        // Membuat chart
        const options = {
            series: [{
                name: 'Tender Diikuti',
                data: participated
            }, {
                name: 'Tender Dimenangkan',
                data: won
            }],
            chart: {
                type: 'bar',
                height: 300,
                stacked: false,
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    borderRadius: 5
                },
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                categories: months,
            },
            yaxis: {
                title: {
                    text: 'Jumlah Tender'
                }
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + " tender"
                    }
                }
            },
            colors: ['#0d6efd', '#198754']
        };

        const chart = new ApexCharts(document.querySelector("#participation-chart"), options);
        chart.render();
    });
</script>
@endsection
