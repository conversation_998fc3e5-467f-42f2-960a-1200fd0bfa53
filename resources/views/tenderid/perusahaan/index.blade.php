@extends('layouts.master')

@section('title', '<PERSON><PERSON><PERSON>')
@section('css')
<style>
    .table tr.bg-warning-important td {
        background-color: #ffcc00;
    }

    .product-box img {
        transition: transform 0.3s ease;
    }

    .product-box img:hover {
        transform: scale(1.1);
    }

    .product-title {
        font-weight: bold;
        color: #007bff;
    }

    .product-title:hover {
        text-decoration: underline;
    }

    .product-category a {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        color: #007bff;
    }

    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 8px;
    }

    .btn-filter {
        background-color: #007bff;
        color: white;
    }

    .btn-filter:hover {
        background-color: #0056b3;
    }

    /* Pagination Custom */
    .pagination .page-link {
        color: #007bff;
    }

    .pagination .page-link:hover {
        background-color: #f1f1f1;
    }

    .pagination .active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    /* Custom style for smaller icons */
    .small-icon {
        font-size: 0.9rem;
        vertical-align: middle;
        margin-right: 0.3rem;
    }

    /* Filter card styles */
    .filter-card {
        border-radius: 10px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .filter-card:hover {
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    }

    .filter-card .card-header {
        background: linear-gradient(90deg, #007bff, #6f42c1);
        color: white;
        border-radius: 10px 10px 0 0;
        padding: 15px 20px;
    }

    .filter-card .card-header h5 {
        margin: 0;
        font-weight: 600;
    }

    .filter-card .card-body {
        padding: 20px;
    }

    /* Table styles */
    .data-table {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .data-table .table {
        margin-bottom: 0;
    }

    .data-table thead {
        background: linear-gradient(90deg, #f8f9fa, #e9ecef);
    }

    .data-table th {
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
    }

    .data-table th a {
        color: #495057;
        text-decoration: none;
        display: flex;
        align-items: center;
    }

    .data-table th a:hover {
        color: #007bff;
    }

    .data-table tbody tr {
        transition: all 0.2s ease;
    }

    .data-table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    .data-table tbody tr td {
        vertical-align: middle;
        padding: 12px 15px;
    }

    .company-link {
        color: #007bff;
        font-weight: 500;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
    }

    .company-link:hover {
        color: #0056b3;
        text-decoration: none;
        transform: translateX(3px);
    }

    .company-link i {
        opacity: 0;
        margin-right: 5px;
        transition: all 0.2s ease;
    }

    .company-link:hover i {
        opacity: 1;
    }

    /* Badge styles */
    .badge-ekatalog {
        background-color: #28a745;
        color: white;
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
    }

    .badge-tender {
        background-color: #007bff;
        color: white;
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
    }

    /* Select2 custom styles */
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 38px;
        padding-left: 12px;
        color: #495057;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #007bff;
    }

    /* Filter toggle styling */
    .filter-toggle-icon {
        transition: transform 0.3s ease;
    }

    .collapsed .filter-toggle-icon {
        transform: rotate(180deg);
    }

    /* Responsive adjustments */
    @media (max-width: 767.98px) {
        .filter-card .card-body {
            padding: 15px;
        }

        .data-table th, .data-table td {
            padding: 10px;
        }
    }
</style>
@endsection

@section('content')
@php
    // Ambil parameter filter dan sort dari request, default sort = asc
    $currentFilter = request('filter');
    $currentSort = request('sort', 'asc');
@endphp

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
        <h5 class="card-title mb-0">
            <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
                <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
                <span>Filter Perusahaan</span>
                <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
            </a>
        </h5>
        <span class="badge bg-white text-primary">
            @if(method_exists($perusahaan, 'total'))
                {{ number_format($perusahaan->total(), 0, ',', '.') }} Data
            @else
                {{ number_format($perusahaan->count(), 0, ',', '.') }} Data
            @endif
        </span>
    </div>
    <div class="collapse show" id="filterCollapse">
        <div class="card-body">
            <form method="GET" action="{{ route('perusahaan.index') }}">
                <div class="row g-3">
                    <!-- Input Pencarian -->
                    <div class="col-md-4">
                        <label for="search-input" class="form-label">Pencarian</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">search</i></span>
                            <input type="text" name="search" id="search-input" class="form-control"
                                placeholder="Cari nama perusahaan atau NPWP" value="{{ request('search') }}">
                        </div>
                        <div class="form-text">Cari berdasarkan nama perusahaan, NPWP, atau alamat</div>
                    </div>

                    <!-- Filter Berdasarkan -->
                    <div class="col-md-3">
                        <label for="filter-select" class="form-label">Urutkan Berdasarkan</label>
                        <select class="form-select" id="filter-select" name="filter">
                            <option value="">Pilih Urutan</option>
                            <option value="name" {{ request('filter') == 'name' ? 'selected' : '' }}>Nama Perusahaan</option>
                            <option value="npwp" {{ request('filter') == 'npwp' ? 'selected' : '' }}>NPWP</option>
                            <option value="tender" {{ request('filter') == 'tender' ? 'selected' : '' }}>Tender Dimenangkan</option>
                            <option value="nilai_tender" {{ request('filter') == 'nilai_tender' ? 'selected' : '' }}>Nilai Kontrak Tender</option>
                            <option value="paket_ekatalog" {{ request('filter') == 'paket_ekatalog' ? 'selected' : '' }}>Paket E-Katalog</option>
                            <option value="nilai_ekatalog" {{ request('filter') == 'nilai_ekatalog' ? 'selected' : '' }}>Nilai Kontrak Ekatalog</option>
                            <option value="total" {{ request('filter') == 'total' ? 'selected' : '' }}>Total Nilai Kontrak</option>
                        </select>
                    </div>

                    <!-- Urutan -->
                    <div class="col-md-3">
                        <label for="sort-select" class="form-label">Urutan</label>
                        <select class="form-select" id="sort-select" name="sort">
                            <option value="asc" {{ request('sort', 'asc') == 'asc' ? 'selected' : '' }}>Naik (A-Z, Terkecil-Terbesar)</option>
                            <option value="desc" {{ request('sort', 'asc') == 'desc' ? 'selected' : '' }}>Turun (Z-A, Terbesar-Terkecil)</option>
                        </select>
                    </div>

                    <!-- Tombol Filter & Reset -->
                    <div class="col-md-2 d-flex align-items-end">
                        <div class="d-grid gap-2 w-100">
                            <button type="submit" class="btn btn-primary">
                                <i class="material-icons-outlined align-middle me-1">filter_alt</i> Terapkan Filter
                            </button>
                            <a href="{{ route('perusahaan.index') }}" class="btn btn-outline-secondary">
                                <i class="material-icons-outlined align-middle me-1">refresh</i> Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Perusahaan Table Section -->
<div class="card data-table mt-4">
    <div class="card-header py-3 bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="material-icons-outlined me-2">business</i>
                Daftar Perusahaan
            </h5>
            <div>
                @if(request('search'))
                    <span class="badge bg-primary">
                        <i class="material-icons-outlined small-icon">search</i>
                        Pencarian: "{{ request('search') }}"
                    </span>
                @endif
                @if(request('filter'))
                    <span class="badge bg-info">
                        <i class="material-icons-outlined small-icon">filter_list</i>
                        Filter: {{ ucfirst(str_replace('_', ' ', request('filter'))) }}
                    </span>
                @endif
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table align-middle mb-0">
                <thead>
                    <tr>
                        <!-- Nama Perusahaan -->
                        <th class="px-3 py-3">
                            <a href="{{ route('perusahaan.index', array_merge(request()->all(), [
                                    'filter' => 'name',
                                    'sort' => ($currentFilter == 'name' && $currentSort == 'asc') ? 'desc' : 'asc'
                                ])) }}" class="d-flex align-items-center">
                                <i class="material-icons-outlined small-icon">business</i>
                                <span>Nama Perusahaan</span>
                                @if($currentFilter == 'name')
                                    <i class="material-icons-outlined ms-1 small-icon">
                                        {{ $currentSort == 'asc' ? 'arrow_upward' : 'arrow_downward' }}
                                    </i>
                                @endif
                            </a>
                        </th>
                        <!-- NPWP -->
                        <th class="px-3 py-3">
                            <a href="{{ route('perusahaan.index', array_merge(request()->all(), [
                                    'filter' => 'npwp',
                                    'sort' => ($currentFilter == 'npwp' && $currentSort == 'asc') ? 'desc' : 'asc'
                                ])) }}" class="d-flex align-items-center">
                                <i class="material-icons-outlined small-icon">article</i>
                                <span>NPWP</span>
                                @if($currentFilter == 'npwp')
                                    <i class="material-icons-outlined ms-1 small-icon">
                                        {{ $currentSort == 'asc' ? 'arrow_upward' : 'arrow_downward' }}
                                    </i>
                                @endif
                            </a>
                        </th>
                        <!-- Tender Dimenangkan -->
                        <th class="px-3 py-3">
                            <a href="{{ route('perusahaan.index', array_merge(request()->all(), [
                                    'filter' => 'tender',
                                    'sort' => ($currentFilter == 'tender' && $currentSort == 'asc') ? 'desc' : 'asc'
                                ])) }}" class="d-flex align-items-center">
                                <i class="material-icons-outlined small-icon">military_tech</i>
                                <span>Tender</span>
                                @if($currentFilter == 'tender')
                                    <i class="material-icons-outlined ms-1 small-icon">
                                        {{ $currentSort == 'asc' ? 'arrow_upward' : 'arrow_downward' }}
                                    </i>
                                @endif
                            </a>
                        </th>
                        <!-- Nilai Kontrak Tender -->
                        <th class="px-3 py-3">
                            <a href="{{ route('perusahaan.index', array_merge(request()->all(), [
                                    'filter' => 'nilai_tender',
                                    'sort' => ($currentFilter == 'nilai_tender' && $currentSort == 'asc') ? 'desc' : 'asc'
                                ])) }}" class="d-flex align-items-center">
                                <i class="material-icons-outlined small-icon">monetization_on</i>
                                <span>Nilai Tender</span>
                                @if($currentFilter == 'nilai_tender')
                                    <i class="material-icons-outlined ms-1 small-icon">
                                        {{ $currentSort == 'asc' ? 'arrow_upward' : 'arrow_downward' }}
                                    </i>
                                @endif
                            </a>
                        </th>
                        <!-- Paket E-Katalog -->
                        <th class="px-3 py-3">
                            <a href="{{ route('perusahaan.index', array_merge(request()->all(), [
                                    'filter' => 'paket_ekatalog',
                                    'sort' => ($currentFilter == 'paket_ekatalog' && $currentSort == 'asc') ? 'desc' : 'asc'
                                ])) }}" class="d-flex align-items-center">
                                <i class="material-icons-outlined small-icon">shopping_cart</i>
                                <span>E-Katalog</span>
                                @if($currentFilter == 'paket_ekatalog')
                                    <i class="material-icons-outlined ms-1 small-icon">
                                        {{ $currentSort == 'asc' ? 'arrow_upward' : 'arrow_downward' }}
                                    </i>
                                @endif
                            </a>
                        </th>
                        <!-- Nilai Kontrak Ekatalog -->
                        <th class="px-3 py-3">
                            <a href="{{ route('perusahaan.index', array_merge(request()->all(), [
                                    'filter' => 'nilai_ekatalog',
                                    'sort' => ($currentFilter == 'nilai_ekatalog' && $currentSort == 'asc') ? 'desc' : 'asc'
                                ])) }}" class="d-flex align-items-center">
                                <i class="material-icons-outlined small-icon">attach_money</i>
                                <span>Nilai E-Katalog</span>
                                @if($currentFilter == 'nilai_ekatalog')
                                    <i class="material-icons-outlined ms-1 small-icon">
                                        {{ $currentSort == 'asc' ? 'arrow_upward' : 'arrow_downward' }}
                                    </i>
                                @endif
                            </a>
                        </th>
                        <!-- Total Nilai Kontrak -->
                        <th class="px-3 py-3">
                            <a href="{{ route('perusahaan.index', array_merge(request()->all(), [
                                    'filter' => 'total',
                                    'sort' => ($currentFilter == 'total' && $currentSort == 'asc') ? 'desc' : 'asc'
                                ])) }}" class="d-flex align-items-center">
                                <i class="material-icons-outlined small-icon">summarize</i>
                                <span>Total Nilai</span>
                                @if($currentFilter == 'total')
                                    <i class="material-icons-outlined ms-1 small-icon">
                                        {{ $currentSort == 'asc' ? 'arrow_upward' : 'arrow_downward' }}
                                    </i>
                                @endif
                            </a>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @if(method_exists($perusahaan, 'isEmpty') ? $perusahaan->isEmpty() : count($perusahaan) === 0)
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="material-icons-outlined" style="font-size: 3rem; color: #ccc;">search_off</i>
                                    <p class="mt-2 mb-0">Tidak ada perusahaan yang ditemukan</p>
                                    <small class="text-muted">Coba ubah filter pencarian Anda</small>
                                </div>
                            </td>
                        </tr>
                    @else
                        @foreach ($perusahaan as $p)
                            <tr>
                                <td class="px-3 py-3">
                                    <div class="d-flex align-items-center">
                                        <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($p->pembeda)]) }}" class="company-link me-2">
                                            <i class="material-icons-outlined">arrow_forward</i>
                                            <span class="fw-medium">{{ $p->nama_peserta }}</span>
                                        </a>
                                        <a href="{{ route('perusahaan.analysis', ['pembeda' => encrypt($p->pembeda)]) }}" class="btn btn-sm btn-light" title="Analisis Perusahaan">
                                            <i class="material-icons-outlined text-dark" style="font-size: 16px; vertical-align: text-bottom;">analytics</i>
                                        </a>
                                    </div>
                                </td>
                                <td class="px-3 py-3">
                                    <span class="text-muted">{{ $p->npwp ?? $p->npwp_blur }}</span>
                                </td>
                                <td class="px-3 py-3">
                                    <div class="d-flex align-items-center">
                                        <span class="badge-tender me-2">{{ number_format($p->jumlah_tender_dimenangkan,0,',','.') }}</span>
                                        <small class="text-muted">Menang dari {{ $p->jumlah_tender_diikuti }} diikuti</small>
                                    </div>
                                </td>
                                <td class="px-3 py-3 fw-medium">{{ convert_to_rupiah($p->nilai_tender_dimenangkan) }}</td>
                                <td class="px-3 py-3">
                                    <div class="d-flex align-items-center">
                                        <span class="badge-ekatalog me-2">{{ number_format($p->jumlah_ekatalog_didapatkan,0,',','.') }}</span>
                                        <small class="text-muted">Paket</small>
                                    </div>
                                </td>
                                <td class="px-3 py-3 fw-medium">{{ convert_to_rupiah($p->nilai_ekatalog_didapatkan) }}</td>
                                <td class="px-3 py-3 fw-medium">{{ convert_to_rupiah($p->total_tender_ekatalog) }}</td>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
            <div>
                @if(method_exists($perusahaan, 'hasPages') && $perusahaan->hasPages())
                    <small class="text-muted">
                        Menampilkan
                        {{ method_exists($perusahaan, 'firstItem') ? ($perusahaan->firstItem() ?? 0) : 0 }} -
                        {{ method_exists($perusahaan, 'lastItem') ? ($perusahaan->lastItem() ?? 0) : 0 }}
                        @if(method_exists($perusahaan, 'total'))
                            dari total {{ $perusahaan->total() }}
                        @endif
                        perusahaan
                    </small>
                @else
                    <small class="text-muted">Menampilkan {{ $perusahaan->count() }} perusahaan</small>
                @endif
            </div>
            <div>
                @if(method_exists($perusahaan, 'links'))
                    {{ $perusahaan->links('vendor.pagination.simple') }}
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    $(document).ready(function () {
        // Initialize Select2 for filter dropdown
        $('#filter-select').select2({
            width: '100%',
            placeholder: "Pilih Urutan",
            allowClear: true,
            dropdownCssClass: "select2-dropdown-filter",
            templateResult: formatFilterOption
        });

        // Initialize Select2 for sort dropdown
        $('#sort-select').select2({
            width: '100%',
            minimumResultsForSearch: Infinity, // Hide search box
            dropdownCssClass: "select2-dropdown-sort"
        });

        // Format filter options with icons
        function formatFilterOption(option) {
            if (!option.id) {
                return option.text;
            }

            let icon = '';

            switch(option.id) {
                case 'name':
                    icon = 'business';
                    break;
                case 'npwp':
                    icon = 'article';
                    break;
                case 'tender':
                    icon = 'military_tech';
                    break;
                case 'nilai_tender':
                    icon = 'monetization_on';
                    break;
                case 'paket_ekatalog':
                    icon = 'shopping_cart';
                    break;
                case 'nilai_ekatalog':
                    icon = 'attach_money';
                    break;
                case 'total':
                    icon = 'summarize';
                    break;
                default:
                    icon = 'sort';
            }

            return $(`<span><i class="material-icons-outlined small-icon align-middle me-2">${icon}</i> ${option.text}</span>`);
        }

        // Add hover effect to table rows
        $('.data-table tbody tr').hover(
            function() {
                $(this).find('.company-link i').css('opacity', '1');
            },
            function() {
                $(this).find('.company-link i').css('opacity', '0');
            }
        );

        // Handle filter collapse toggle
        $('#filterCollapse').on('hide.bs.collapse', function () {
            $('.filter-toggle-icon').addClass('collapsed');
        });

        $('#filterCollapse').on('show.bs.collapse', function () {
            $('.filter-toggle-icon').removeClass('collapsed');
        });
    });
</script>
@endsection