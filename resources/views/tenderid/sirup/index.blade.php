{{-- resources/views/data_sirup/index.blade.php --}}
@php
    use App\Models\Lpse;
@endphp
@extends('layouts.master')

@section('title', 'Semua Data SIRUP')

@section('css')
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <style>
        /* Card Styling */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            margin-bottom: 24px;
        }

        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 12px 12px 0 0 !important;
            padding: 18px 24px;
        }

        .card-title {
            font-weight: 600;
            color: #344767;
            margin-bottom: 0;
            font-size: 1.1rem;
        }

        .card-body {
            padding: 24px;
        }

        /* Form Controls */
        .form-control, .form-select {
            border-radius: 8px;
            padding: 10px 15px;
            border: 1px solid #e9ecef;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
        }

        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
        }

        .input-group-text {
            border-radius: 8px 0 0 8px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .form-label {
            font-weight: 500;
            font-size: 0.85rem;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        /* Buttons */
        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
            box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
        }

        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0b5ed7;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
        }

        .btn-secondary {
            background-color: #f8f9fa;
            border-color: #e9ecef;
            color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #e9ecef;
            border-color: #dfe2e5;
            color: #495057;
        }

        /* Table Styling */
        .table-responsive {
            border: none;
            border-radius: 12px;
            overflow: hidden;
        }

        .table {
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background-color: #f8f9fa;
            color: #344767;
            font-weight: 600;
            border-top: none;
            padding: 16px;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e9ecef;
        }

        .table tbody td {
            padding: 16px;
            vertical-align: middle;
            border-bottom: 1px solid #f8f9fa;
            font-size: 0.9rem;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
        }

        /* Product Box */
        .product-box {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .product-box img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .product-box img:hover {
            transform: scale(1.15);
        }

        /* Product Info */
        .product-info {
            flex: 1;
        }

        .product-title {
            font-weight: 600;
            color: #0d6efd;
            display: block;
            margin-bottom: 5px;
            font-size: 0.95rem;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .product-title:hover {
            color: #0b5ed7;
            text-decoration: underline;
        }

        .product-category {
            color: #6c757d;
            font-size: 0.85rem;
            margin-bottom: 0;
        }

        .product-category a {
            color: #6c757d;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .product-category a:hover {
            color: #0d6efd;
            text-decoration: underline;
        }

        /* Badges */
        .badge {
            padding: 6px 10px;
            font-weight: 500;
            font-size: 0.75rem;
            border-radius: 6px;
            display: inline-flex;
            align-items: center;
            line-height: 1;
        }

        .badge i {
            font-size: 12px;
            margin-right: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            vertical-align: baseline;
        }

        .badge.bg-success {
            background-color: #198754 !important;
            box-shadow: 0 2px 5px rgba(25, 135, 84, 0.2);
        }

        .badge.bg-danger {
            background-color: #dc3545 !important;
            box-shadow: 0 2px 5px rgba(220, 53, 69, 0.2);
        }

        .badge.bg-warning {
            background-color: #ffc107 !important;
            color: #212529;
            box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
        }

        .badge.bg-info {
            background-color: #0dcaf0 !important;
            box-shadow: 0 2px 5px rgba(13, 202, 240, 0.2);
        }

        .badge.bg-secondary {
            background-color: #6c757d !important;
            box-shadow: 0 2px 5px rgba(108, 117, 125, 0.2);
        }

        .badge.bg-light {
            background-color: #f8f9fa !important;
            color: #212529;
            box-shadow: 0 2px 5px rgba(248, 249, 250, 0.2);
        }

        /* Pagination */
        .pagination {
            margin-top: 24px;
            justify-content: center;
            gap: 5px;
        }

        .pagination .page-link {
            border-radius: 8px;
            margin: 0 3px;
            color: #0d6efd;
            border: 1px solid #e9ecef;
            padding: 8px 15px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .pagination .page-link:hover {
            background-color: #f8f9fa;
            color: #0b5ed7;
            border-color: #e9ecef;
            z-index: 1;
        }

        .pagination .active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
            box-shadow: 0 4px 8px rgba(13, 110, 253, 0.2);
        }

        /* Select2 Customization - Minimal */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--single {
            height: 38px !important;
            border-radius: 4px;
            border: 1px solid #ced4da;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 36px;
            padding-left: 12px;
            color: #495057;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 6px 12px;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #007bff;
        }

        .select2-container--default .select2-selection--single .select2-selection__clear {
            margin-right: 10px;
            font-weight: bold;
        }

        .select2-container--default .select2-selection--single .select2-selection__placeholder {
            color: #6c757d;
        }

        /* Animation for status loading */
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        .status-loading {
            animation: pulse 1.5s infinite;
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 6px 10px;
            display: inline-block;
        }

        /* Empty state styling */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
        }

        .empty-state i {
            font-size: 48px;
            color: #e9ecef;
            margin-bottom: 15px;
        }

        .empty-state h5 {
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: #adb5bd;
            max-width: 400px;
            margin: 0 auto;
        }

        /* Spinner customization */
        .spinner-grow-sm {
            width: 0.8rem;
            height: 0.8rem;
        }

        /* Spinning animation for refresh icon */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .spin-animation {
            animation: spin 1s linear infinite;
            display: inline-block;
        }

        /* Filter toggle styling */
        .filter-toggle-icon {
            transition: transform 0.3s ease;
        }

        .collapsed .filter-toggle-icon {
            transform: rotate(180deg);
        }
    </style>
@endsection

@section('content')

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
            <h5 class="card-title mb-0">
                <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
                    <span>Filter Data SIRUP</span>
                    <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
                </a>
            </h5>
            <span class="badge bg-white text-primary">
                @if(method_exists($data_sirup, 'total'))
                    {{ number_format($data_sirup->total(), 0, ',', '.') }} Data
                @else
                    {{ number_format($data_sirup->count(), 0, ',', '.') }} Data
                @endif
            </span>
        </div>
        <div class="collapse show" id="filterCollapse">
            <div class="card-body">
                <form method="GET" action="{{ route('sirup.index') }}">
                <div class="row g-3">
                    <!-- Pencarian -->
                    <div class="col-md-3">
                        <label for="search-input" class="form-label">Pencarian</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">search</i></span>
                            <input type="text" name="search" id="search-input" class="form-control"
                                placeholder="Cari Nama Paket atau Nomor RUP..." value="{{ request('search') }}">
                        </div>
                        <div class="form-text">Cari berdasarkan nama paket atau kode RUP</div>
                    </div>

                    <!-- Filter Tahun Anggaran -->
                    <div class="col-md-2">
                        <label for="tahun-anggaran" class="form-label">Tahun Anggaran</label>
                        <select class="form-select" id="tahun-anggaran" name="tahun_anggaran">
                            <option value="">Semua Tahun</option>
                            @foreach($tahun_anggaran as $tahun)
                                <option value="{{ $tahun }}" {{ request('tahun_anggaran') == $tahun ? 'selected' : '' }}>
                                    {{ $tahun }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Filter Rentang Nilai Pagu -->
                    <div class="col-md-2">
                        <label for="filter-pagu" class="form-label">Rentang Nilai Pagu</label>
                        <select class="form-select" id="filter-pagu" name="filter_pagu">
                            <option value="">Semua Nilai</option>
                            <option value="0-200000000" {{ request('filter_pagu') == '0-200000000' ? 'selected' : '' }}>0 - 200jt</option>
                            <option value="200000000-1000000000" {{ request('filter_pagu') == '200000000-1000000000' ? 'selected' : '' }}>200jt - 1M</option>
                            <option value="1000000000-10000000000" {{ request('filter_pagu') == '1000000000-10000000000' ? 'selected' : '' }}>1M - 10M</option>
                            <option value="10000000000-50000000000" {{ request('filter_pagu') == '10000000000-50000000000' ? 'selected' : '' }}>10M - 50M</option>
                            <option value="50000000000-" {{ request('filter_pagu') == '50000000000-' ? 'selected' : '' }}>Di atas 50M</option>
                        </select>
                    </div>

                    <!-- Filter Metode Pemilihan -->
                    <div class="col-md-2">
                        <label for="metode-pemilihan" class="form-label">Metode Pemilihan</label>
                        <select class="form-select" id="metode-pemilihan" name="metode_pemilihan">
                            <option value="">Semua Metode</option>
                            @foreach($metode_pemilihan as $metode)
                                @php
                                    if ($metode == '') {
                                        continue;
                                    }
                                @endphp
                                <option value="{{ $metode }}" {{ request('metode_pemilihan') == $metode ? 'selected' : '' }}>
                                    {{ $metode }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Filter Nama KLPD -->
                    <div class="col-md-3">
                        <label for="nama-klpd-select" class="form-label">Nama Instansi</label>
                        <select class="form-control" id="nama-klpd-select" name="nama_klpd">
                            <option value="">Semua Instansi</option>
                            @foreach($nama_klpd as $klpd)
                                <option value="{{ $klpd }}" {{ request('nama_klpd') == $klpd ? 'selected' : '' }}>
                                    {{ $klpd }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Tombol Filter & Reset -->
                    <div class="col-12 mt-4">
                        <div class="d-flex justify-content-end gap-2">
                            <button type="submit" class="btn btn-primary d-inline-flex align-items-center">
                                <i class="material-icons-outlined me-2">filter_alt</i>
                                <span>Terapkan Filter</span>
                            </button>
                            <a href="{{ route('sirup.index') }}" class="btn btn-outline-secondary d-inline-flex align-items-center">
                                <i class="material-icons-outlined me-2">refresh</i>
                                <span>Reset Filter</span>
                            </a>
                        </div>
                    </div>
                </div>
            </form>
            </div>
        </div>
    </div>


    <!-- Data SIRUP Table Section -->
    <div class="card">
        <div class="card-header d-flex align-items-center justify-content-between">
            <h5 class="card-title mb-0">
                <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">list_alt</i>
                Daftar Data SIRUP
            </h5>
            <div>
                <button class="btn btn-sm btn-outline-primary me-2 d-inline-flex align-items-center" id="btn-refresh-data">
                    <i class="material-icons-outlined me-1">refresh</i>
                    <span>Refresh Data</span>
                </button>
                <div class="dropdown d-inline-block">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle d-inline-flex align-items-center" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="material-icons-outlined me-1">tune</i>
                        <span>Tampilan</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
                        <li><a class="dropdown-item d-flex align-items-center" href="#" id="toggle-compact-view">
                            <i class="material-icons-outlined me-2">view_compact</i>
                            <span>Tampilan Kompak</span>
                        </a></li>
                        <li><a class="dropdown-item d-flex align-items-center" href="#" id="toggle-full-view">
                            <i class="material-icons-outlined me-2">view_agenda</i>
                            <span>Tampilan Lengkap</span>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item d-flex align-items-center" href="#" id="toggle-status-column">
                            <i class="material-icons-outlined me-2">toggle_on</i>
                            <span>Tampilkan Status</span>
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if($data_sirup->count() > 0)
                <div class="table-responsive">
                    <table class="table align-middle mb-0">
                        <thead>
                            <tr>
                                <th style="width: 10%;">Kode RUP</th>
                                <th style="width: 30%;">Nama Paket</th>
                                <th style="width: 10%;">Tahun</th>
                                <th style="width: 12%;">Pagu</th>
                                <th style="width: 15%;">Metode Pemilihan</th>
                                <th style="width: 13%;">Tanggal Diumumkan</th>
                                <th style="width: 10%;">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($data_sirup as $sirup)
                                @php
                                    $nama_klpd = $sirup->nama_klpd;

                                    // Ubah "Kab." menjadi "Kabupaten" jika ditemukan
                                    if (strpos($nama_klpd, "Kab.") !== false) {
                                        $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
                                    }

                                    // Tambahkan "LPSE" di depan nama KLPD
                                    $nama_klpd = "LPSE {$nama_klpd}";

                                    // Query pencarian langsung berdasarkan nama_klpd
                                    $lpse = DB::table('lpse')
                                            ->where('nama_lpse', 'like', "%{$nama_klpd}%")
                                            ->first();

                                    if ($lpse) {
                                        $lpse->logo_path = str_replace('http://proyeklpse.test', '', $lpse->logo_path);
                                    }
                                @endphp
                                <tr data-kode-rup="{{ $sirup->kode_rup }}" class="sirup-row">
                                    <td>
                                        <a href="{{ route('sirup.show', ['id' => $sirup->id]) }}" class="fw-semibold text-primary">
                                            #{{ $sirup->kode_rup }}
                                        </a>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center gap-3">
                                            <div class="product-box">
                                                @if($lpse)
                                                    <img src="{{ asset($lpse->logo_path) }}" alt="Logo" class="rounded-3">
                                                @else
                                                    <img src="{{ asset('build/images/logo-default.png') }}" alt="Logo" class="rounded-3">
                                                @endif
                                            </div>
                                            <div class="product-info">
                                                <a href="{{ route('sirup.show', ['id' => $sirup->id]) }}" class="product-title">
                                                    {{ \Illuminate\Support\Str::limit($sirup->nama_paket, 70) }}
                                                </a>
                                                <p class="mb-0 product-category">
                                                    {{ \Illuminate\Support\Str::limit($sirup->satuan_kerja, 40) }} -
                                                    <a href="{{ route('sirup.index', ['nama_klpd' => $sirup->nama_klpd]) }}">
                                                        {{ \Illuminate\Support\Str::limit($sirup->nama_klpd, 30) }}
                                                    </a>
                                                </p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ $sirup->tahun_anggaran }}</span>
                                    </td>
                                    <td id="sirup-{{ $sirup->kode_rup }}">
                                        <span class="fw-semibold">Rp {{ format_number_short($sirup->total_pagu) }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-dark">{{ $sirup->metode_pemilihan }}</span>
                                    </td>
                                    <td>
                                        @if($sirup->tanggal_paket_diumumkan !== null)
                                            <span class="text-muted small d-flex align-items-center">
                                                <i class="material-icons-outlined me-1" style="font-size: 14px;">event</i>
                                                <span>{{ format_date_time($sirup->tanggal_paket_diumumkan) }}</span>
                                            </span>
                                        @else
                                            <span class="badge bg-danger">Data Tidak Ada</span>
                                        @endif
                                    </td>
                                    <td id="status-{{ $sirup->kode_rup }}" class="status-cell">
                                        <div class="d-flex align-items-center">
                                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <span class="small">Memuat...</span>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center p-3 border-top">
                    <div class="text-muted small">
                        @if(method_exists($data_sirup, 'firstItem') && method_exists($data_sirup, 'lastItem') && method_exists($data_sirup, 'total'))
                            Menampilkan {{ $data_sirup->firstItem() ?? 0 }} - {{ $data_sirup->lastItem() ?? 0 }} dari {{ $data_sirup->total() }} data
                        @else
                            Menampilkan {{ $data_sirup->count() }} data
                        @endif
                    </div>
                    <div>
                        @if(method_exists($data_sirup, 'links'))
                            {{ $data_sirup->links('vendor.pagination.simple') }}
                        @endif
                    </div>
                </div>
            @else
                <div class="empty-state">
                    <i class="material-icons-outlined">search_off</i>
                    <h5>Tidak Ada Data SIRUP</h5>
                    <p>Tidak ada data SIRUP yang sesuai dengan filter yang Anda pilih. Silakan ubah filter atau coba kata kunci lain.</p>
                </div>
            @endif
        </div>
    </div>
@endsection

@section('scripts')
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function () {
            // Inisialisasi Select2 dengan konfigurasi sederhana
            $('#nama-klpd-select').select2({
                width: '100%',
                placeholder: "Pilih Nama Instansi",
                allowClear: true,
                minimumInputLength: 2,
                language: {
                    inputTooShort: function() {
                        return "Ketik minimal 2 karakter untuk mencari";
                    },
                    noResults: function() {
                        return "Tidak ada instansi yang ditemukan";
                    },
                    searching: function() {
                        return "Mencari...";
                    }
                }
            });

            // Memuat status untuk setiap baris
            loadAllStatus();

            // Refresh data button
            $('#btn-refresh-data').on('click', function() {
                $(this).html(`
                    <i class="material-icons-outlined spin-animation me-1">refresh</i>
                    <span>Memuat...</span>
                `);
                $(this).prop('disabled', true);

                // Reset semua status cell ke loading
                $('.status-cell').each(function() {
                    $(this).html(`
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="small">Memuat...</span>
                        </div>
                    `);
                });

                // Reload status
                loadAllStatus();

                // Reset button setelah 2 detik
                setTimeout(function() {
                    $('#btn-refresh-data').html(`
                        <i class="material-icons-outlined me-1">refresh</i>
                        <span>Refresh Data</span>
                    `);
                    $('#btn-refresh-data').prop('disabled', false);
                }, 2000);
            });

            // Toggle compact view
            $('#toggle-compact-view').on('click', function(e) {
                e.preventDefault();
                $('.sirup-row').addClass('compact-view');
                $('.product-category').hide();
                $('.table').addClass('table-sm');
                localStorage.setItem('sirup_view_mode', 'compact');

                // Notifikasi
                showToast('Tampilan kompak diaktifkan');
            });

            // Toggle full view
            $('#toggle-full-view').on('click', function(e) {
                e.preventDefault();
                $('.sirup-row').removeClass('compact-view');
                $('.product-category').show();
                $('.table').removeClass('table-sm');
                localStorage.setItem('sirup_view_mode', 'full');

                // Notifikasi
                showToast('Tampilan lengkap diaktifkan');
            });

            // Toggle status column
            let statusColumnVisible = true;
            $('#toggle-status-column').on('click', function(e) {
                e.preventDefault();
                statusColumnVisible = !statusColumnVisible;

                if (statusColumnVisible) {
                    $('th:nth-child(7), td:nth-child(7)').show();
                    $(this).html(`
                        <i class="material-icons-outlined me-2">toggle_on</i>
                        <span>Tampilkan Status</span>
                    `);
                    localStorage.setItem('sirup_status_column', 'visible');
                    showToast('Kolom status ditampilkan');
                } else {
                    $('th:nth-child(7), td:nth-child(7)').hide();
                    $(this).html(`
                        <i class="material-icons-outlined me-2">toggle_off</i>
                        <span>Sembunyikan Status</span>
                    `);
                    localStorage.setItem('sirup_status_column', 'hidden');
                    showToast('Kolom status disembunyikan');
                }
            });

            // Load saved view preferences
            loadViewPreferences();

            // Handle filter collapse toggle
            $('#filterCollapse').on('show.bs.collapse', function () {
                $('.filter-toggle-icon').text('expand_less');
                localStorage.setItem('sirup_filter_collapsed', 'false');
            });

            $('#filterCollapse').on('hide.bs.collapse', function () {
                $('.filter-toggle-icon').text('expand_more');
                localStorage.setItem('sirup_filter_collapsed', 'true');
            });

            // Load filter collapse state from localStorage
            if (localStorage.getItem('sirup_filter_collapsed') === 'true') {
                $('#filterCollapse').collapse('hide');
            }
        });

        // Function to load all status
        function loadAllStatus() {
            $('.sirup-row').each(function () {
                var kodeRup = $(this).data('kode-rup');
                if (kodeRup) {
                    loadStatus(kodeRup);
                }
            });
        }

        // Function to load status for a single row
        function loadStatus(kodeRup) {
            console.log('Loading status for kode_rup:', kodeRup);

            // Gunakan URL absolut dengan HTTPS
            const baseUrl = window.location.protocol + '//' + window.location.host;

            $.ajax({
                url: baseUrl + '/sirup/status/' + kodeRup,
                type: 'GET',
                dataType: 'json',
                cache: false,
                crossDomain: true,
                xhrFields: {
                    withCredentials: true
                },
                success: function (response) {
                    console.log('Response for kode_rup ' + kodeRup + ':', response);

                    if (response && response.status) {
                        let badgeClass = 'bg-danger';
                        let icon = 'pending';
                        let statusText = response.status;

                        if (statusText === 'Sudah Tayang' || statusText === 'Sudah Klik') {
                            badgeClass = 'bg-success';
                            icon = statusText === 'Sudah Tayang' ? 'check_circle' : 'shopping_cart';
                        } else if (statusText === 'Data Tidak Ditemukan') {
                            badgeClass = 'bg-secondary';
                            icon = 'help_outline';
                        } else if (statusText.startsWith('Error:')) {
                            badgeClass = 'bg-danger';
                            icon = 'error';
                        }

                        // Tambahkan sumber jika ada
                        let sourceText = '';
                        if (response.sumber) {
                            sourceText = `<small class="d-block mt-1">${response.sumber}</small>`;
                        }

                        $('#status-' + kodeRup).html(`
                            <span class="badge ${badgeClass}">
                                <i class="material-icons-outlined">${icon}</i>
                                <span>${statusText}</span>
                                ${sourceText}
                            </span>
                        `);
                    } else {
                        console.error('Invalid response format:', response);
                        $('#status-' + kodeRup).html(`
                            <span class="badge bg-secondary">
                                <i class="material-icons-outlined">help_outline</i>
                                <span>Tidak Diketahui</span>
                            </span>
                        `);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching status for kode_rup ' + kodeRup + ':', error);
                    console.error('XHR:', xhr);

                    $('#status-' + kodeRup).html(`
                        <span class="badge bg-danger">
                            <i class="material-icons-outlined">error</i>
                            <span>Error: ${error || 'Unknown'}</span>
                        </span>
                    `);
                }
            });
        }

        // Function to sync pagu
        function syncPagu(kodeRup) {
            console.log('Syncing pagu for kode_rup:', kodeRup);

            var $td = $('#sirup-' + kodeRup);

            // Show loading indicator
            $td.html(`
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="small ms-2">Memperbarui...</span>
            `);

            // Gunakan URL absolut dengan HTTPS
            const baseUrl = window.location.protocol + '//' + window.location.host;

            // Send AJAX request
            $.ajax({
                url: baseUrl + '/sirup/pagu/' + kodeRup,
                type: 'GET',
                dataType: 'json',
                cache: false,
                crossDomain: true,
                xhrFields: {
                    withCredentials: true
                },
                success: function (response) {
                    console.log('Response for pagu sync kode_rup ' + kodeRup + ':', response);

                    if (response && response.total_pagu) {
                        $td.html(`
                            <span class="fw-semibold">Rp ${response.total_pagu}</span>
                            <i class="material-icons-outlined text-success ms-1" style="font-size: 14px; vertical-align: middle;">verified</i>
                        `);

                        // Show success notification
                        showToast('Pagu berhasil diperbarui', 'success');
                    } else if (response && response.message) {
                        $td.html(`
                            <span class="fw-semibold text-warning">Rp ${response.total_pagu || '0'}</span>
                            <i class="material-icons-outlined text-warning ms-1" style="font-size: 14px; vertical-align: middle;">warning</i>
                        `);

                        // Show warning notification
                        showToast(response.message, 'warning');
                    } else {
                        $td.html(`
                            <span class="fw-semibold text-secondary">Rp 0</span>
                            <i class="material-icons-outlined text-secondary ms-1" style="font-size: 14px; vertical-align: middle;">help_outline</i>
                        `);

                        // Show info notification
                        showToast('Tidak ada data pagu yang ditemukan', 'info');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error syncing pagu for kode_rup ' + kodeRup + ':', error);
                    console.error('XHR:', xhr);

                    var message = xhr.responseJSON ? xhr.responseJSON.message : 'Terjadi kesalahan: ' + error;
                    $td.html(`
                        <span class="fw-semibold text-danger">Error</span>
                        <i class="material-icons-outlined text-danger ms-1" style="font-size: 14px; vertical-align: middle;">error</i>
                    `);

                    // Show error notification
                    showToast('Gagal memperbarui pagu: ' + message, 'error');
                }
            });
        }

        // Function to load view preferences
        function loadViewPreferences() {
            // Load view mode
            const viewMode = localStorage.getItem('sirup_view_mode');
            if (viewMode === 'compact') {
                $('.sirup-row').addClass('compact-view');
                $('.product-category').hide();
                $('.table').addClass('table-sm');
            }

            // Load status column visibility
            const statusColumn = localStorage.getItem('sirup_status_column');
            if (statusColumn === 'hidden') {
                $('th:nth-child(7), td:nth-child(7)').hide();
                $('#toggle-status-column').html(`
                    <i class="material-icons-outlined me-2">toggle_off</i>
                    <span>Sembunyikan Status</span>
                `);
            }
        }

        // Function to show toast notification
        function showToast(message, type = 'success') {
            // Create toast container if it doesn't exist
            if ($('#toast-container').length === 0) {
                $('body').append('<div id="toast-container" class="position-fixed bottom-0 end-0 p-3" style="z-index: 9999;"></div>');
            }

            // Set toast color based on type
            let bgColor = 'bg-success';
            let icon = 'check_circle';

            if (type === 'error') {
                bgColor = 'bg-danger';
                icon = 'error';
            } else if (type === 'warning') {
                bgColor = 'bg-warning text-dark';
                icon = 'warning';
            } else if (type === 'info') {
                bgColor = 'bg-info text-dark';
                icon = 'info';
            }

            // Create toast
            const toastId = 'toast-' + Date.now();
            const toast = `
                <div id="${toastId}" class="toast align-items-center ${bgColor} text-white border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="material-icons-outlined me-2" style="vertical-align: middle;">${icon}</i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            // Add toast to container
            $('#toast-container').append(toast);

            // Initialize and show toast
            const toastElement = new bootstrap.Toast(document.getElementById(toastId), {
                delay: 3000
            });
            toastElement.show();

            // Remove toast after it's hidden
            $(`#${toastId}`).on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>


@endsection