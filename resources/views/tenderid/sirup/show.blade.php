@php
  use App\Models\Lpse;
@endphp
@extends('layouts.master')

@section('title', $dataSirup->nama_paket)

@section('css')
  <!-- Optional CSS -->
  <style>
    /* Card styling */
    .card {
      border: none;
      border-radius: 12px;
      box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      margin-bottom: 24px;
    }

    .card:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      border-radius: 12px 12px 0 0 !important;
      padding: 18px 24px;
    }

    .card-title {
      font-weight: 600;
      color: #344767;
      margin-bottom: 0;
      font-size: 1.1rem;
    }

    .card-body {
      padding: 24px;
    }

    .card-footer {
      background-color: #f8f9fa;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      border-radius: 0 0 12px 12px !important;
      padding: 16px 24px;
    }

    /* Table styling */
    .table {
      margin-bottom: 0;
      border-collapse: separate;
      border-spacing: 0;
    }

    .table thead th {
      background-color: #f8f9fa;
      color: #344767;
      font-weight: 600;
      border-top: none;
      padding: 16px;
      font-size: 0.85rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 1px solid #e9ecef;
    }

    .table tbody td {
      padding: 16px;
      vertical-align: middle;
      border-bottom: 1px solid #f8f9fa;
      font-size: 0.9rem;
      color: #495057;
    }

    .table tbody tr:last-child td {
      border-bottom: none;
    }

    .table tbody tr {
      transition: all 0.2s ease;
    }

    .table tbody tr:hover {
      background-color: #f8f9fa;
    }

    .table-bordered {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      overflow: hidden;
    }

    .table-bordered th,
    .table-bordered td {
      border: 1px solid #e9ecef;
    }

    .table-bordered thead th {
      border-bottom-width: 1px;
    }

    .table thead th.text-center {
      background-color: #0d6efd;
      color: white;
      font-weight: 600;
      padding: 16px;
      font-size: 1rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    /* Membungkus teks di dalam tabel */
    .table td {
      word-wrap: break-word;
      word-break: normal;
      white-space: normal;
    }

    .table tr.bg-warning-important td {
      background-color: rgba(255, 193, 7, 0.15);
    }

    /* Product styling */
    .product-box {
      width: 50px;
      height: 50px;
      border-radius: 10px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .product-box img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      transition: transform 0.3s ease;
    }

    .product-box img:hover {
      transform: scale(1.15);
    }

    .product-info {
      flex: 1;
    }

    .product-title {
      font-weight: 600;
      color: #0d6efd;
      display: block;
      margin-bottom: 5px;
      font-size: 0.95rem;
      text-decoration: none;
      transition: all 0.2s ease;
    }

    .product-title:hover {
      color: #0b5ed7;
      text-decoration: underline;
    }

    .product-category {
      color: #6c757d;
      font-size: 0.85rem;
      margin-bottom: 0;
    }

    .product-category a {
      color: #6c757d;
      text-decoration: none;
      transition: color 0.2s ease;
    }

    .product-category a:hover {
      color: #0d6efd;
      text-decoration: underline;
    }

    /* Badge styling */
    .badge {
      padding: 6px 10px;
      font-weight: 500;
      font-size: 0.75rem;
      border-radius: 6px;
      display: inline-flex;
      align-items: center;
      line-height: 1;
    }

    .badge i {
      font-size: 12px;
      margin-right: 4px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      vertical-align: baseline;
    }

    .badge.bg-primary {
      background-color: #0d6efd !important;
      box-shadow: 0 2px 5px rgba(13, 110, 253, 0.2);
    }

    .badge.bg-success {
      background-color: #198754 !important;
      box-shadow: 0 2px 5px rgba(25, 135, 84, 0.2);
    }

    .badge.bg-danger {
      background-color: #dc3545 !important;
      box-shadow: 0 2px 5px rgba(220, 53, 69, 0.2);
    }

    .badge.bg-warning {
      background-color: #ffc107 !important;
      color: #212529;
      box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
    }

    .badge.bg-info {
      background-color: #0dcaf0 !important;
      box-shadow: 0 2px 5px rgba(13, 202, 240, 0.2);
    }

    /* Button styling */
    .btn {
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 500;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 0.875rem;
    }

    .btn-primary {
      background-color: #0d6efd;
      border-color: #0d6efd;
      color: white;
      box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
    }

    .btn-primary:hover {
      background-color: #0b5ed7;
      border-color: #0b5ed7;
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
    }

    .btn-warning {
      background-color: #ffc107;
      border-color: #ffc107;
      color: #212529;
      box-shadow: 0 4px 10px rgba(255, 193, 7, 0.2);
    }

    .btn-warning:hover {
      background-color: #ffca2c;
      border-color: #ffca2c;
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(255, 193, 7, 0.25);
    }

    .btn-dark {
      background-color: #212529;
      border-color: #212529;
      color: white;
      box-shadow: 0 4px 10px rgba(33, 37, 41, 0.2);
    }

    .btn-dark:hover {
      background-color: #424649;
      border-color: #424649;
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(33, 37, 41, 0.25);
    }

    /* Section title styling */
    .section-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #344767;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      align-items: center;
    }

    .section-title i {
      margin-right: 8px;
      font-size: 1.2rem;
      color: #0d6efd;
    }

    /* Summary box styling */
    .summary-box {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
    }

    .summary-box-title {
      font-size: 0.85rem;
      font-weight: 500;
      color: #6c757d;
      margin-bottom: 4px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .summary-box-value {
      font-size: 1.25rem;
      font-weight: 600;
      color: #344767;
      margin-bottom: 0;
    }

    /* Print styling */
    @media print {
      /* Hide elements that shouldn't be printed */
      .top-header,
      .sidebar-wrapper,
      .page-footer,
      footer,
      .footer,
      .page-footer-wrapper,
      button,
      .btn,
      .no-print {
        display: none !important;
      }

      /* Main content styling */
      #content {
        display: block !important;
        position: static !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      body {
        background-color: white !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
      }

      /* Card styling for print */
      .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
        break-inside: avoid !important;
        page-break-inside: avoid !important;
      }

      /* Badge styling for print */
      .badge {
        border: 1px solid #dee2e6 !important;
        color: #000 !important;
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
      }

      /* Ensure tables don't break across pages */
      table {
        page-break-inside: avoid !important;
      }

      /* Add page breaks where needed */
      .page-break-after {
        page-break-after: always !important;
      }

      /* Remove background colors from specific elements */
      .card-header,
      .card-footer {
        background-color: white !important;
      }
    }
  </style>
@endsection

@section('content')

  <div class="card">
    <div class="card-header">
      <div class="row align-items-center">
        <div class="col-12 col-lg-6">
          <h5 class="card-title d-flex align-items-center">
            <i class="bi bi-file-earmark-text me-2"></i>
            Detail Paket SIRUP
          </h5>
        </div>
        <div class="col-12 col-lg-6 text-lg-end mt-3 mt-lg-0">
          @auth
            @php
              $followed = json_decode(auth()->user()->sirup_followed) ?? [];
            @endphp
            @if (in_array($dataSirup->kode_rup, $followed))
              <button class="btn btn-warning btn-sm unfollow-btn me-2" data-sirup-id="{{ $dataSirup->kode_rup }}">
                <i class="bi bi-bell-slash-fill"></i>
                <span>Unfollow</span>
              </button>
            @else
              <button class="btn btn-primary btn-sm follow-btn me-2" data-sirup-id="{{ $dataSirup->kode_rup }}">
                <i class="bi bi-bell-fill"></i>
                <span>Follow</span>
              </button>
            @endif
          @endauth

          <a href="javascript:;" onclick="window.print()" class="btn btn-dark btn-sm no-print">
            <i class="bi bi-printer-fill"></i>
            <span>Print</span>
          </a>
        </div>
      </div>
    </div>

    <div class="card-body">
      <!-- Status Badge -->
      <div class="mb-4">
        <div class="d-flex flex-wrap align-items-center justify-content-between">
          <h6 class="text-uppercase fw-bold text-muted mb-0">
            <i class="bi bi-hash me-1"></i>Kode RUP: {{ $dataSirup->kode_rup }}
          </h6>

          @if($dataSirup->status_paket)
            @php
              $statusClass = 'bg-secondary';
              $statusIcon = 'bi-question-circle';

              if($dataSirup->status_paket === 'Sudah Tayang') {
                $statusClass = 'bg-success';
                $statusIcon = 'bi-check-circle';
              } elseif($dataSirup->status_paket === 'Sudah Klik') {
                $statusClass = 'bg-success';
                $statusIcon = 'bi-cart-check';
              }
            @endphp
            <span class="badge {{ $statusClass }} mb-2 mb-md-0">
              <i class="bi {{ $statusIcon }} me-1"></i>
              <span>{{ $dataSirup->status_paket }}</span>
            </span>
          @endif
        </div>
      </div>

      <!-- Nama Paket -->
      <div class="mb-4">
        <div class="d-flex align-items-start mb-3">
          @php
            // Cari logo LPSE berdasarkan nama KLPD
            $nama_klpd = $dataSirup->nama_klpd;
            // Ubah "Kab." menjadi "Kabupaten" jika ditemukan
            if (strpos($nama_klpd, "Kab.") !== false) {
              $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
            }
            // Tambahkan "LPSE" di depan nama KLPD
            $nama_lpse = "LPSE {$nama_klpd}";
            // Query pencarian langsung berdasarkan nama_klpd
            $lpse = DB::table('lpse')
              ->where('nama_lpse', 'like', "%{$nama_klpd}%")
              ->first();

            $logo_path = $lpse && $lpse->logo_path
              ? str_replace('http://proyeklpse.test', '', $lpse->logo_path)
              : 'build/images/logo-default.png';
          @endphp

          <div class="product-box me-3" style="width: 60px; height: 60px;">
            <img src="{{ asset($logo_path) }}" alt="Logo KLPD" class="rounded-3">
          </div>

          <div>
            <h4 class="fw-bold text-primary mb-1">{{ $dataSirup->nama_paket }}</h4>
            <div class="d-flex flex-wrap align-items-center text-muted">
              <div class="me-4 mb-2">
                <i class="bi bi-building me-1"></i>
                <span>{{ $dataSirup->nama_klpd }}</span>
              </div>
              <div class="me-4 mb-2">
                <i class="bi bi-briefcase me-1"></i>
                <span>{{ $dataSirup->satuan_kerja }}</span>
              </div>
              <div class="mb-2">
                <i class="bi bi-calendar me-1"></i>
                <span>{{ $dataSirup->tahun_anggaran }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagu Anggaran -->
      <div class="mb-4">
        <div class="summary-box">
          <div class="row">
            <div class="col-md-6">
              <p class="summary-box-title">Pagu Anggaran</p>
              <h4 class="summary-box-value">{{ convert_to_rupiah($dataSirup->total_pagu) }}</h4>
            </div>
            <div class="col-md-6">
              <p class="summary-box-title">Metode Pemilihan</p>
              <h4 class="summary-box-value">{{ $dataSirup->metode_pemilihan }}</h4>
            </div>
          </div>
        </div>
      </div>

      <!-- Detail Informasi -->
      <div class="row mb-4">
        <div class="col-md-12">
          <h6 class="section-title">
            <i class="bi bi-info-circle"></i>
            Informasi Detail
          </h6>

          <div class="table-responsive">
            <table class="table table-bordered">
              <tbody>
                <tr>
                  <td width="30%"><strong>Uraian Pekerjaan</strong></td>
                  <td>{{ $dataSirup->uraian_pekerjaan }}</td>
                </tr>
                <tr>
                  <td><strong>Spesifikasi Pekerjaan</strong></td>
                  <td>{{ $dataSirup->spesifikasi_pekerjaan }}</td>
                </tr>
                <tr>
                  <td><strong>Volume Pekerjaan</strong></td>
                  <td>{{ $dataSirup->volume_pekerjaan }}</td>
                </tr>
                <tr>
                  <td><strong>Lokasi Pekerjaan</strong></td>
                  <td>
                    @if($dataSirup->lokasi_pekerjaan && is_array($dataSirup->lokasi_pekerjaan))
                      @foreach($dataSirup->lokasi_pekerjaan as $lokasi)
                        <div class="mb-1">
                          <i class="bi bi-geo-alt me-1 text-primary"></i>
                          {{ $lokasi['detail_lokasi'] ?? 'Not specified' }} - {{ $lokasi['kabupaten_kota'] ?? 'Not specified' }} -
                          {{ $lokasi['provinsi'] ?? 'Not specified' }}
                        </div>
                      @endforeach
                    @else
                      <span class="text-muted">Not specified</span>
                    @endif
                  </td>
                </tr>
                <tr>
                  <td><strong>Jenis Pengadaan</strong></td>
                  <td>
                    @if($dataSirup->jenis_pengadaan && is_array($dataSirup->jenis_pengadaan))
                      @foreach($dataSirup->jenis_pengadaan as $pengadaan)
                        <span class="badge bg-info me-1 mb-1">{{ $pengadaan['jenis_pengadaan'] ?? 'Not specified' }}</span>
                      @endforeach
                    @else
                      <span class="text-muted">Not specified</span>
                    @endif
                  </td>
                </tr>
                <tr>
                  <td><strong>Produk Dalam Negeri</strong></td>
                  <td>
                    @if($dataSirup->produk_dalam_negeri)
                      <span class="badge bg-success">Ya</span>
                    @else
                      <span class="badge bg-secondary">Tidak</span>
                    @endif
                  </td>
                </tr>
                <tr>
                  <td><strong>UMKM</strong></td>
                  <td>
                    @if($dataSirup->umkm)
                      <span class="badge bg-success">Ya</span>
                    @else
                      <span class="badge bg-secondary">Tidak</span>
                    @endif
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Sumber Dana -->
      <div class="row mb-4">
        <div class="col-md-12">
          <h6 class="section-title">
            <i class="bi bi-cash-coin"></i>
            Sumber Dana
          </h6>

          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th width="5%">No</th>
                  <th width="35%">Sumber Dana</th>
                  <th width="30%">Pagu</th>
                  <th width="30%">Kode Rekening</th>
                </tr>
              </thead>
              <tbody>
                @if($dataSirup->sumber_dana && is_array($dataSirup->sumber_dana))
                  @foreach($dataSirup->sumber_dana as $dana)
                    <tr>
                      <td class="text-center">{{ $dana['no'] ?? '-' }}</td>
                      <td>{{ $dana['sumber_dana'] ?? 'Not specified' }} - {{ $dana['ta'] ?? 'Not specified' }}</td>
                      <td>{{ $dana['pagu'] ? convert_to_rupiah($dana['pagu']) : 'Not specified' }}</td>
                      <td style="word-break: break-all;">{{ $dana['mak'] ?? 'Not specified' }}</td>
                    </tr>
                  @endforeach
                @else
                  <tr>
                    <td colspan="4" class="text-center">Tidak ada data sumber dana</td>
                  </tr>
                @endif
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Jadwal -->
      <div class="row">
        <div class="col-md-12">
          <h6 class="section-title">
            <i class="bi bi-calendar-event"></i>
            Jadwal
          </h6>

          <div class="row">
            <!-- Jadwal Pemilihan Penyedia -->
            <div class="col-md-4 mb-4">
              <div class="card h-100 border">
                <div class="card-header bg-light py-2">
                  <h6 class="mb-0 fw-bold">Pemilihan Penyedia</h6>
                </div>
                <div class="card-body p-0">
                  <div class="table-responsive">
                    <table class="table mb-0">
                      <thead>
                        <tr>
                          <th>Mulai</th>
                          <th>Akhir</th>
                        </tr>
                      </thead>
                      <tbody>
                        @if($dataSirup->jadwal_pemilihan_penyedia && is_array($dataSirup->jadwal_pemilihan_penyedia))
                          @foreach($dataSirup->jadwal_pemilihan_penyedia as $penyedia)
                            <tr>
                              <td>{{ $penyedia['mulai'] ?? 'Not specified' }}</td>
                              <td>{{ $penyedia['akhir'] ?? 'Not specified' }}</td>
                            </tr>
                          @endforeach
                        @else
                          <tr>
                            <td colspan="2" class="text-center">Tidak ada data</td>
                          </tr>
                        @endif
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            <!-- Jadwal Pelaksanaan Kontrak -->
            <div class="col-md-4 mb-4">
              <div class="card h-100 border">
                <div class="card-header bg-light py-2">
                  <h6 class="mb-0 fw-bold">Pelaksanaan Kontrak</h6>
                </div>
                <div class="card-body p-0">
                  <div class="table-responsive">
                    <table class="table mb-0">
                      <thead>
                        <tr>
                          <th>Mulai</th>
                          <th>Akhir</th>
                        </tr>
                      </thead>
                      <tbody>
                        @if($dataSirup->jadwal_pelaksanaan_kontrak && is_array($dataSirup->jadwal_pelaksanaan_kontrak))
                          @foreach($dataSirup->jadwal_pelaksanaan_kontrak as $jadwal)
                            <tr>
                              <td>{{ $jadwal['mulai'] ?? 'Not specified' }}</td>
                              <td>{{ $jadwal['akhir'] ?? 'Not specified' }}</td>
                            </tr>
                          @endforeach
                        @else
                          <tr>
                            <td colspan="2" class="text-center">Tidak ada data</td>
                          </tr>
                        @endif
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            <!-- Pemanfaatan Barang Jasa -->
            <div class="col-md-4 mb-4">
              <div class="card h-100 border">
                <div class="card-header bg-light py-2">
                  <h6 class="mb-0 fw-bold">Pemanfaatan Barang/Jasa</h6>
                </div>
                <div class="card-body p-0">
                  <div class="table-responsive">
                    <table class="table mb-0">
                      <thead>
                        <tr>
                          <th>Mulai</th>
                          <th>Akhir</th>
                        </tr>
                      </thead>
                      <tbody>
                        @if($dataSirup->pemanfaatan_barang_jasa && is_array($dataSirup->pemanfaatan_barang_jasa))
                          @foreach($dataSirup->pemanfaatan_barang_jasa as $pemanfaatan)
                            <tr>
                              <td>{{ $pemanfaatan['mulai'] ?? 'Not specified' }}</td>
                              <td>{{ $pemanfaatan['akhir'] ?? 'Not specified' }}</td>
                            </tr>
                          @endforeach
                        @else
                          <tr>
                            <td colspan="2" class="text-center">Tidak ada data</td>
                          </tr>
                        @endif
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-footer py-3">
      <div class="d-flex align-items-center justify-content-center flex-column">
        <p class="text-uppercase text-muted mb-1 small">Paket ini diumumkan pada</p>
        <p class="d-flex align-items-center mb-0 fw-bold">
          <i class="bi bi-clock me-2 text-primary"></i>
          <span>{{ format_date_time($dataSirup->tanggal_paket_diumumkan) ?? 'Not specified' }} WIB</span>
        </p>
      </div>
    </div>
  </div>


  @if($dataSirup->status_paket === 'Sudah Tayang' && $dataSirup->tender_data->isNotEmpty())
    <div class="card">
      <div class="card-header">
        <div class="d-flex align-items-center justify-content-between">
          <h5 class="card-title d-flex align-items-center mb-0">
            <i class="bi bi-check-circle-fill me-2 text-success"></i>
            Realisasi Tender
          </h5>

          <div class="d-flex align-items-center">
            <span class="badge bg-light text-dark me-2">
              <i class="bi bi-info-circle me-1"></i>
              <span>LPSE</span>
            </span>

            @php
              // Ambil semua LPSE yang terkait dengan tender
              $lpseLogos = [];
              foreach($dataSirup->tender_data as $tender) {
                $kodeLpse = $tender->kd_lpse;
                $lpse = Lpse::where('kd_lpse', $kodeLpse)->first();
                if ($lpse && !in_array($lpse->kd_lpse, array_column($lpseLogos, 'kd_lpse'))) {
                  $lpseLogos[] = [
                    'kd_lpse' => $lpse->kd_lpse,
                    'nama_lpse' => $lpse->nama_lpse,
                    'logo_path' => str_replace('http://proyeklpse.test', '', $lpse->logo_path)
                  ];
                }
              }
            @endphp

            <div class="d-flex">
              @foreach($lpseLogos as $logo)
                <div class="product-box me-1" style="width: 30px; height: 30px;">
                  <a href="{{ route('lpse.dashboard', ['kode_lpse' => $logo['kd_lpse']]) }}" data-bs-toggle="tooltip" title="{{ $logo['nama_lpse'] }}">
                    <img src="{{ asset($logo['logo_path']) }}" alt="{{ $logo['nama_lpse'] }}" class="rounded-3">
                  </a>
                </div>
              @endforeach
            </div>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table align-middle">
            <thead>
              <tr>
                <th>Kode Tender</th>
                <th>Nama Paket</th>
                <th>Kategori</th>
                <th>Tahapan</th>
                <th>Pagu</th>
                <th>HPS</th>
                <th>Tanggal Tayang</th>
              </tr>
            </thead>
            <tbody>
              @foreach($dataSirup->tender_data as $tender)
                @php
                  $kodeLpse = $tender->kd_lpse;
                  $lpse = Lpse::where('kd_lpse', $kodeLpse)->first();

                  if ($tender->status_tender === 'Tender Sedang Berjalan') {
                    $tender->status_tender = 'Aktif';
                    $statusClass = '';
                  } elseif ($tender->status_tender === 'Tender Ditutup') {
                    $tender->status_tender = 'Tidak Aktif';
                    $statusClass = 'bg-warning-important';
                  } else {
                    $statusClass = $tender->status_tender !== 'Aktif' ? 'bg-warning-important' : '';
                  }
                @endphp

                <tr class="{{ $statusClass }}">
                  <td>
                    <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}" class="fw-medium text-primary">
                      #{{ $tender->kode_tender }}
                    </a>
                  </td>
                  <td>
                    <div class="d-flex align-items-center gap-3">
                      <div class="product-box">
                        <img src="{{ asset(str_replace('http://proyeklpse.test', '', $lpse->logo_path)) }}" alt="Logo" class="rounded-3">
                      </div>
                      <div class="product-info">
                        <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}" class="product-title">
                          {{ $tender->nama_paket }}
                        </a>
                        <p class="mb-0 product-category">
                          <a href="{{ route('lpse.dashboard', ['kode_lpse' => $tender->lpse->kd_lpse]) }}">
                            {{ $tender->lpse->nama_lpse }}
                          </a>
                        </p>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info">{{ $tender->kategori_pekerjaan }}</span>
                  </td>
                  <td>
                    <span class="badge {{ $tender->status_tender === 'Aktif' ? 'bg-success' : 'bg-warning text-dark' }}">
                      <i class="bi {{ $tender->status_tender === 'Aktif' ? 'bi-play-circle-fill' : 'bi-pause-circle-fill' }} me-1"></i>
                      <span>{{ $tender->tahap_tender }}</span>
                    </span>
                  </td>
                  <td>
                    <span class="fw-semibold">{{ format_number_short($tender->pagu) }}</span>
                  </td>
                  <td>
                    <span class="fw-semibold">{{ format_number_short($tender->hps) }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <i class="bi bi-calendar-event me-1 text-muted"></i>
                      <span>{{ format_date_indonesian($tender->tanggal_paket_tayang) }}</span>
                    </div>
                  </td>
                </tr>
              @endforeach
            </tbody>
          </table>
        </div>
      </div>
    </div>
  @endif

  @if($dataSirup->status_paket === 'Sudah Klik' && $dataSirup->tender_data->isNotEmpty())
    @php
      $totalPaket = 0;
      $totalNilaiKontrak = 0;
    @endphp

    <div class="card">
      <div class="card-header">
        <div class="d-flex align-items-center justify-content-between">
          <h5 class="card-title d-flex align-items-center mb-0">
            <i class="bi bi-cart-check-fill me-2 text-success"></i>
            Realisasi E-Katalog
          </h5>

          <div class="d-flex align-items-center">
            <span class="badge bg-light text-dark me-2">
              <i class="bi bi-info-circle me-1"></i>
              <span>Instansi</span>
            </span>

            @php
              // Ambil semua LPSE yang terkait dengan paket E-Katalog
              $instansiLogos = [];
              foreach($dataSirup->tender_data as $paket) {
                $nama_klpd = $paket->instansi_pembeli;

                // Ubah "Kab." menjadi "Kabupaten" jika ditemukan
                if (strpos($nama_klpd, "Kab.") !== false) {
                  $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
                }
                // Tambahkan "LPSE" di depan nama KLPD
                $nama_lpse = "LPSE {$nama_klpd}";

                // Query pencarian langsung berdasarkan nama_klpd
                $lpse = DB::table('lpse')
                  ->where('nama_lpse', 'like', "%{$nama_klpd}%")
                  ->first();

                if ($lpse && !in_array($lpse->kd_lpse, array_column($instansiLogos, 'kd_lpse'))) {
                  $instansiLogos[] = [
                    'kd_lpse' => $lpse->kd_lpse,
                    'nama_lpse' => $lpse->nama_lpse,
                    'logo_path' => str_replace('http://proyeklpse.test', '', $lpse->logo_path)
                  ];
                }
              }
            @endphp

            <div class="d-flex">
              @foreach($instansiLogos as $logo)
                <div class="product-box me-1" style="width: 30px; height: 30px;">
                  <img src="{{ asset($logo['logo_path']) }}" alt="{{ $logo['nama_lpse'] }}" class="rounded-3" data-bs-toggle="tooltip" title="{{ $logo['nama_lpse'] }}">
                </div>
              @endforeach
            </div>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table align-middle">
            <thead>
              <tr>
                <th>Nomor Paket</th>
                <th>Nama Paket</th>
                <th>Instansi Pembeli</th>
                <th>Pelaksana</th>
                <th>Nilai Kontrak</th>
                <th>Versi</th>
              </tr>
            </thead>
            <tbody>
              @forelse($dataSirup->tender_data as $paket)
                @php
                  $totalPaket++;
                  $totalNilaiKontrak += $paket->nilai_kontrak;
                  $nama_klpd = $paket->instansi_pembeli;

                  // Ubah "Kab." menjadi "Kabupaten" jika ditemukan
                  if (strpos($nama_klpd, "Kab.") !== false) {
                    $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
                  }
                  // Tambahkan "LPSE" di depan nama KLPD
                  $nama_klpd = "LPSE {$nama_klpd}";
                  // Query pencarian langsung berdasarkan nama_klpd
                  $lpse = DB::table('lpse')
                    ->where('nama_lpse', 'like', "%{$nama_klpd}%")
                    ->first();
                  if ($lpse) {
                    $lpse->logo_path = str_replace('http://proyeklpse.test', '', $lpse->logo_path);
                  }
                @endphp

                <tr>
                  <td>
                    @if($paket->katalog_version === 'E-Katalog 5')
                      <a href="{{ route('ekatalog.show', ['id' => $paket->id_paket]) }}" class="fw-medium text-primary">
                        #{{ $paket->nomor_paket }}
                      </a>
                    @else
                      <a href="#" class="fw-medium text-primary">
                        #{{ $paket->nomor_paket }}
                      </a>
                    @endif
                  </td>
                  <td>
                    <div class="d-flex align-items-center gap-3">
                      <div class="product-box">
                        @if($lpse)
                          <img src="{{ asset($lpse->logo_path) }}" alt="Logo" class="rounded-3">
                        @else
                          <img src="{{ asset('build/images/logo-default.png') }}" alt="Logo" class="rounded-3">
                        @endif
                      </div>
                      <div class="product-info">
                        @if($paket->katalog_version === 'E-Katalog 5')
                          <a href="{{ route('ekatalog.show', ['id' => $paket->id_paket]) }}" class="product-title">
                            {{ $paket->nama_paket }}
                          </a>
                        @else
                          <a href="#" class="product-title">
                            {{ $paket->nama_paket }}
                          </a>
                        @endif
                        <p class="mb-0 product-category">
                          {{ $paket->pengelola }}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="d-flex flex-column">
                      <span class="fw-semibold">{{ $paket->instansi_pembeli }}</span>
                      <small class="text-muted">Satker: {{ $paket->satuan_kerja }}</small>
                    </div>
                  </td>
                  <td>
                    @if($paket->katalog_version === 'E-Katalog 5')
                      <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($paket->pelaksana->pembeda)]) }}" class="text-primary">
                        {{ $paket->pelaksana->nama_peserta }}
                      </a>
                    @else
                      <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($paket->penyedia->pembeda)]) }}" class="text-primary">
                        {{ $paket->penyedia->nama_peserta }}
                      </a>
                    @endif
                  </td>
                  <td>
                    <span class="fw-semibold">{{ convert_to_rupiah($paket->nilai_kontrak) }}</span>
                  </td>
                  <td>
                    <span class="badge bg-success">
                      <i class="bi bi-tag-fill me-1"></i>
                      <span>{{ $paket->katalog_version }}</span>
                    </span>
                  </td>
                </tr>
              @empty
                <tr>
                  <td colspan="6" class="text-center py-4">
                    <i class="bi bi-info-circle text-muted d-block mb-2" style="font-size: 1.5rem;"></i>
                    <span class="text-muted">Tidak ada data ditemukan</span>
                  </td>
                </tr>
              @endforelse
            </tbody>
          </table>
        </div>

        <div class="row mt-4">
          <div class="col-md-4 mb-3">
            <div class="summary-box">
              <p class="summary-box-title">Banyak Paket Diklik</p>
              <h4 class="summary-box-value d-flex align-items-center">
                <i class="bi bi-box-seam me-2 text-primary"></i>
                {{ $totalPaket }} Paket
              </h4>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <div class="summary-box">
              <p class="summary-box-title">Total Anggaran Diklik</p>
              <h4 class="summary-box-value d-flex align-items-center">
                <i class="bi bi-cash-stack me-2 text-success"></i>
                {{ convert_to_rupiah($totalNilaiKontrak) }}
              </h4>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <div class="summary-box">
              <p class="summary-box-title">Sisa Pagu Anggaran</p>
              @php
                $sisaPagu = $dataSirup->total_pagu - $totalNilaiKontrak;
              @endphp
              <h4 class="summary-box-value d-flex align-items-center">
                <i class="bi bi-wallet2 me-2 text-warning"></i>
                {{ convert_to_rupiah($sisaPagu) }}
              </h4>
            </div>
          </div>
        </div>
      </div>
    </div>
  @endif
@endsection

@section('scripts')
<script>
  $(document).ready(function () {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl, {
        placement: 'top',
        boundary: 'window'
      });
    });

    // Handle follow/unfollow button click
    $('.follow-btn, .unfollow-btn').click(function () {
      var btn = $(this);
      var id = btn.data('sirup-id');
      var action = btn.hasClass('follow-btn') ? 'follow' : 'unfollow';
      var url = `/${action}/sirup/${id}`;

      // Show loading state
      var originalHtml = btn.html();
      btn.prop('disabled', true);
      btn.html(`<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...`);

      $.post(url, {
        _token: '{{ csrf_token() }}'
      }, function (response) {
        if (response.status === 'followed' || response.status === 'unfollowed') {
          if (action === 'follow') {
            // Change to unfollow button
            btn.removeClass('btn-primary follow-btn')
               .addClass('btn-warning unfollow-btn')
               .html(`
                 <i class="bi bi-bell-slash-fill"></i>
                 <span>Unfollow</span>
               `);

            // Show success notification
            showNotification('Success', 'Anda akan menerima notifikasi untuk paket ini', 'success');
          } else {
            // Change to follow button
            btn.removeClass('btn-warning unfollow-btn')
               .addClass('btn-primary follow-btn')
               .html(`
                 <i class="bi bi-bell-fill"></i>
                 <span>Follow</span>
               `);

            // Show info notification
            showNotification('Info', 'Anda tidak akan menerima notifikasi untuk paket ini', 'info');
          }
        } else {
          // Restore original button state on error
          btn.html(originalHtml);

          // Show error notification
          showNotification('Error', 'Terjadi kesalahan, silakan coba lagi', 'danger');
        }

        // Re-enable button
        btn.prop('disabled', false);
      }).fail(function() {
        // Restore original button state on error
        btn.html(originalHtml);
        btn.prop('disabled', false);

        // Show error notification
        showNotification('Error', 'Terjadi kesalahan, silakan coba lagi', 'danger');
      });
    });

    // Function to show notification
    function showNotification(title, message, type) {
      // Check if Bootstrap toast is available
      if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        // Create toast element
        var toastHtml = `
          <div class="toast align-items-center text-white bg-${type} border-0 position-fixed top-0 end-0 m-3" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
              <div class="toast-body">
                <strong>${title}:</strong> ${message}
              </div>
              <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
          </div>
        `;

        // Append toast to body
        $('body').append(toastHtml);

        // Initialize and show toast
        var toastElement = document.querySelector('.toast:last-child');
        var toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();

        // Remove toast after it's hidden
        $(toastElement).on('hidden.bs.toast', function() {
          $(this).remove();
        });
      } else {
        // Fallback to alert if Bootstrap toast is not available
        alert(`${title}: ${message}`);
      }
    }
  });
</script>
@endsection
