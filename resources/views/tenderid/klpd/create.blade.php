@extends('layouts.master')

@section('title', 'Tambah KLPD Baru')

@section('css')
<style>
    .required-field::after {
        content: " *";
        color: red;
    }

    .preview-container {
        width: 150px;
        height: 150px;
        border: 1px dashed #ccc;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 10px;
        overflow: hidden;
    }

    .preview-container img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .preview-placeholder {
        color: #aaa;
        text-align: center;
    }
</style>
@endsection

@section('content')
<x-page-title title="Tambah KLPD Baru" pagetitle="Master Data" />

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">add_circle</i>
                    Tambah KLPD Baru
                </h5>
            </div>
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form action="{{ route('klpd.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="kd_klpd" class="form-label required-field">Kode KLPD</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">code</i></span>
                                <input type="text" class="form-control" id="kd_klpd" name="kd_klpd" value="{{ old('kd_klpd') }}" required>
                            </div>
                            <div class="form-text">Contoh: D123, K456, dll.</div>
                        </div>

                        <div class="col-md-6">
                            <label for="jenis_klpd" class="form-label required-field">Jenis KLPD</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">category</i></span>
                                <select class="form-select" id="jenis_klpd" name="jenis_klpd" required>
                                    <option value="">Pilih Jenis KLPD</option>
                                    @foreach($jenisKlpd as $jenis)
                                        <option value="{{ $jenis }}" {{ old('jenis_klpd') == $jenis ? 'selected' : '' }}>{{ $jenis }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="nama_klpd" class="form-label required-field">Nama KLPD</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">business</i></span>
                            <input type="text" class="form-control" id="nama_klpd" name="nama_klpd" value="{{ old('nama_klpd') }}" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="kd_provinsi" class="form-label required-field">Provinsi</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">location_on</i></span>
                                <select class="form-select" id="kd_provinsi" name="kd_provinsi" required>
                                    <option value="">Pilih Provinsi</option>
                                    @foreach($provinces as $code => $name)
                                        <option value="{{ $code }}" {{ old('kd_provinsi') == $code ? 'selected' : '' }}>{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label for="kd_kabupaten" class="form-label">Kabupaten/Kota</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">location_city</i></span>
                                <input type="text" class="form-control" id="kd_kabupaten" name="kd_kabupaten" value="{{ old('kd_kabupaten') }}">
                            </div>
                            <div class="form-text">Opsional. Masukkan kode kabupaten/kota jika ada.</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="logo" class="form-label">Logo</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">image</i></span>
                            <input type="file" class="form-control" id="logo" name="logo" accept="image/*" onchange="previewImage(this)">
                        </div>
                        <div class="form-text">Opsional. Format: JPG, PNG, GIF. Maks: 2MB.</div>

                        <div class="preview-container" id="logo-preview">
                            <div class="preview-placeholder">
                                <i class="material-icons-outlined" style="font-size: 48px;">image</i>
                                <p>Preview Logo</p>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ route('klpd.index') }}" class="btn btn-outline-secondary">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">arrow_back</i>
                            Kembali
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">save</i>
                            Simpan KLPD
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function previewImage(input) {
        const preview = document.getElementById('logo-preview');

        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" alt="Logo Preview">`;
            }

            reader.readAsDataURL(input.files[0]);
        } else {
            preview.innerHTML = `
                <div class="preview-placeholder">
                    <i class="material-icons-outlined" style="font-size: 48px;">image</i>
                    <p>Preview Logo</p>
                </div>
            `;
        }
    }
</script>
@endsection
