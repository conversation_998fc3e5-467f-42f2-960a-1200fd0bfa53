@extends('layouts.master')

@section('title', 'Data KLPD')
@section('css')
    <style>
        .filter-toggle-icon {
            transition: transform 0.3s ease;
        }

        .collapsed .filter-toggle-icon {
            transform: rotate(180deg);
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 3rem 1rem;
            text-align: center;
        }

        .empty-state i {
            font-size: 4rem;
            color: #e9ecef;
            margin-bottom: 1rem;
        }

        .empty-state h5 {
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .empty-state p {
            color: #adb5bd;
            max-width: 400px;
            margin: 0 auto;
        }

        .product-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            overflow: hidden;
            background-color: #f8f9fa;
        }

        .product-box img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .klpd-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .klpd-table tr:hover {
            background-color: rgba(68, 129, 235, 0.05);
        }

        .pagination {
            margin-bottom: 0;
        }

        .pagination .page-link {
            color: #4481eb;
            border-radius: 4px;
            margin: 0 2px;
        }

        .pagination .page-item.active .page-link {
            background-color: #4481eb;
            border-color: #4481eb;
        }
    </style>
@endsection
@section('content')
    <x-page-title title="Master Data" pagetitle="Data KLPD" />

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
            <h5 class="card-title mb-0">
                <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
                    <span>Filter KLPD</span>
                    <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
                </a>
            </h5>
            <span class="badge bg-white text-primary" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">business</i>
                {{ number_format(count($dataKlpd), 0, ',', '.') }} KLPD
            </span>
        </div>
        <div class="collapse show" id="filterCollapse">
            <div class="card-body">
                <form method="GET" action="{{ route('klpd.index') }}" id="filter-form">
                    <div class="row g-3">
                        <!-- Search -->
                        <div class="col-md-4">
                            <label for="search" class="form-label">Cari KLPD</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">search</i></span>
                                <input type="text" class="form-control" id="search" name="search" placeholder="Cari berdasarkan kode atau nama..." value="{{ request('search') }}">
                            </div>
                        </div>

                        <!-- Jenis KLPD -->
                        <div class="col-md-3">
                            <label for="jenis_klpd" class="form-label">Jenis KLPD</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">category</i></span>
                                <select name="jenis_klpd" id="jenis_klpd" class="form-select">
                                    <option value="">Semua Jenis</option>
                                    @php
                                        $jenisKlpd = \App\Models\Klpd::select('jenis_klpd')
                                            ->whereNotNull('jenis_klpd')
                                            ->distinct()
                                            ->pluck('jenis_klpd')
                                            ->toArray();
                                    @endphp
                                    @foreach($jenisKlpd as $jenis)
                                        <option value="{{ $jenis }}" {{ request('jenis_klpd') == $jenis ? 'selected' : '' }}>{{ $jenis }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Provinsi -->
                        <div class="col-md-3">
                            <label for="provinsi" class="form-label">Provinsi</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">location_on</i></span>
                                <select name="provinsi" id="provinsi" class="form-select">
                                    <option value="">Semua Provinsi</option>
                                    @php
                                        // Perbaikan query: gunakan where dengan kondisi yang benar
                                        $provinces = \App\Models\Klpd::where(function($query) {
                                                $query->where('jenis_klpd', 'PROVINSI')
                                                      ->orWhere('jenis_klpd', 'Provinsi');
                                            })
                                            ->orderBy('nama_klpd')
                                            ->get();

                                        // Konversi ke array dengan kd_provinsi sebagai key dan nama_klpd sebagai value
                                        $provincesArray = [];
                                        foreach ($provinces as $province) {
                                            $key = $province->kd_provinsi ?? $province->kd_klpd;
                                            $provincesArray[$key] = $province->nama_klpd;
                                        }

                                        // Jika tidak ada provinsi di database, gunakan fallback
                                        if (empty($provincesArray)) {
                                            $provincesArray = [
                                                '11' => 'ACEH',
                                                '12' => 'SUMATERA UTARA',
                                                '13' => 'SUMATERA BARAT',
                                                '14' => 'RIAU',
                                                '15' => 'JAMBI',
                                                '16' => 'SUMATERA SELATAN',
                                                '17' => 'BENGKULU',
                                                '18' => 'LAMPUNG',
                                                '19' => 'KEPULAUAN BANGKA BELITUNG',
                                                '21' => 'KEPULAUAN RIAU',
                                                '31' => 'DKI JAKARTA',
                                                '32' => 'JAWA BARAT',
                                                '33' => 'JAWA TENGAH',
                                                '34' => 'DI YOGYAKARTA',
                                                '35' => 'JAWA TIMUR',
                                                '36' => 'BANTEN',
                                                '51' => 'BALI',
                                                '52' => 'NUSA TENGGARA BARAT',
                                                '53' => 'NUSA TENGGARA TIMUR',
                                                '61' => 'KALIMANTAN BARAT',
                                                '62' => 'KALIMANTAN TENGAH',
                                                '63' => 'KALIMANTAN SELATAN',
                                                '64' => 'KALIMANTAN TIMUR',
                                                '65' => 'KALIMANTAN UTARA',
                                                '71' => 'SULAWESI UTARA',
                                                '72' => 'SULAWESI TENGAH',
                                                '73' => 'SULAWESI SELATAN',
                                                '74' => 'SULAWESI TENGGARA',
                                                '75' => 'GORONTALO',
                                                '76' => 'SULAWESI BARAT',
                                                '81' => 'MALUKU',
                                                '82' => 'MALUKU UTARA',
                                                '91' => 'PAPUA BARAT',
                                                '94' => 'PAPUA',
                                            ];
                                        }
                                    @endphp
                                    @foreach($provincesArray as $code => $name)
                                        <option value="{{ $code }}" {{ request('provinsi') == $code ? 'selected' : '' }}>{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="col-md-2 d-flex align-items-end">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">search</i>
                                    Terapkan
                                </button>
                                <a href="{{ route('klpd.index') }}" class="btn btn-outline-secondary ms-2">
                                    <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">refresh</i>
                                    Reset
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show">
            <i class="material-icons-outlined me-2">check_circle</i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- KLPD Table Section -->
    <div class="card">
        <div class="card-header d-flex align-items-center justify-content-between">
            <h5 class="card-title mb-0">
                <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">business</i>
                Daftar KLPD
            </h5>
            @auth
                @if(auth()->user()->account_type === 'Super Admin')
                    <div class="d-flex gap-2">
                        <a href="{{ route('klpd.fix-data') }}" class="btn btn-warning" onclick="return confirm('Apakah Anda yakin ingin memperbaiki data KLPD yang rusak? Ini akan mengubah kd_provinsi menjadi NULL untuk KLPD yang bukan provinsi tetapi memiliki kd_provinsi = 11 (ACEH).')">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">build</i>
                            Perbaiki Data
                        </a>
                        <a href="{{ route('klpd.create') }}" class="btn btn-primary">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">add_circle</i>
                            Tambah KLPD
                        </a>
                    </div>
                @endif
            @endauth
        </div>
        <div class="card-body p-0">
            @if(count($dataKlpd) > 0)
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0 klpd-table">
                        <thead>
                            <tr>
                                <th>KLPD</th>
                                <th>Jenis</th>
                                <th>Provinsi</th>
                                <th>Kabupaten</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($dataKlpd as $klpd)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="product-box">
                                                @if($klpd->logo_path)
                                                    <img src="{{ asset($klpd->logo_path) }}" alt="{{ $klpd->nama_klpd }}" class="rounded-3">
                                                @else
                                                    <i class="material-icons-outlined text-primary">business</i>
                                                @endif
                                            </div>
                                            <div>
                                                <div class="fw-medium text-dark">{{ $klpd->nama_klpd }}</div>
                                                <div class="small text-muted">Kode: {{ $klpd->kd_klpd }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $klpd->jenis_klpd ?: 'Tidak Ada' }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $provinsiName = '';
                                            if ($klpd->kd_provinsi) {
                                                // Perbaikan query: gunakan where dengan kondisi yang benar
                                                $provinsi = \App\Models\Klpd::where(function($query) {
                                                        $query->where('jenis_klpd', 'PROVINSI')
                                                              ->orWhere('jenis_klpd', 'Provinsi');
                                                    })
                                                    ->where('kd_provinsi', $klpd->kd_provinsi)
                                                    ->first();

                                                // Jika provinsi ditemukan, gunakan nama provinsi
                                                // Jika tidak, gunakan kode provinsi dari fallback
                                                if ($provinsi) {
                                                    $provinsiName = $provinsi->nama_klpd;
                                                } else {
                                                    // Coba cari dari daftar provinsi fallback
                                                    $fallbackProvinces = [
                                                        '11' => 'ACEH',
                                                        '12' => 'SUMATERA UTARA',
                                                        '13' => 'SUMATERA BARAT',
                                                        '14' => 'RIAU',
                                                        '15' => 'JAMBI',
                                                        '16' => 'SUMATERA SELATAN',
                                                        '17' => 'BENGKULU',
                                                        '18' => 'LAMPUNG',
                                                        '19' => 'KEPULAUAN BANGKA BELITUNG',
                                                        '21' => 'KEPULAUAN RIAU',
                                                        '31' => 'DKI JAKARTA',
                                                        '32' => 'JAWA BARAT',
                                                        '33' => 'JAWA TENGAH',
                                                        '34' => 'DI YOGYAKARTA',
                                                        '35' => 'JAWA TIMUR',
                                                        '36' => 'BANTEN',
                                                        '51' => 'BALI',
                                                        '52' => 'NUSA TENGGARA BARAT',
                                                        '53' => 'NUSA TENGGARA TIMUR',
                                                        '61' => 'KALIMANTAN BARAT',
                                                        '62' => 'KALIMANTAN TENGAH',
                                                        '63' => 'KALIMANTAN SELATAN',
                                                        '64' => 'KALIMANTAN TIMUR',
                                                        '65' => 'KALIMANTAN UTARA',
                                                        '71' => 'SULAWESI UTARA',
                                                        '72' => 'SULAWESI TENGAH',
                                                        '73' => 'SULAWESI SELATAN',
                                                        '74' => 'SULAWESI TENGGARA',
                                                        '75' => 'GORONTALO',
                                                        '76' => 'SULAWESI BARAT',
                                                        '81' => 'MALUKU',
                                                        '82' => 'MALUKU UTARA',
                                                        '91' => 'PAPUA BARAT',
                                                        '94' => 'PAPUA',
                                                    ];

                                                    $provinsiName = $fallbackProvinces[$klpd->kd_provinsi] ?? $klpd->kd_provinsi;
                                                }
                                            }
                                        @endphp
                                        {{ $provinsiName ?: 'Tidak Ada' }}
                                    </td>
                                    <td>{{ $klpd->kd_kabupaten ?: 'Tidak Ada' }}</td>
                                    <td>
                                        @auth
                                            @if(auth()->user()->account_type === 'Super Admin')
                                                <div class="d-flex gap-1">
                                                    <a href="{{ route('klpd.edit', $klpd->kd_klpd) }}" class="btn btn-primary btn-sm">
                                                        <i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">edit</i>
                                                        Edit
                                                    </a>
                                                    <button type="button" class="btn btn-info btn-sm search-logo-btn"
                                                        data-kdklpd="{{ $klpd->kd_klpd }}" data-namaklpd="{{ $klpd->nama_klpd }}">
                                                        <i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">search</i>
                                                        Search Logo
                                                    </button>
                                                </div>
                                            @endif
                                        @endauth
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if(method_exists($dataKlpd, 'links'))
                    <div class="d-flex justify-content-between align-items-center p-3 border-top">
                        <div class="text-muted small">
                            @if(method_exists($dataKlpd, 'firstItem') && method_exists($dataKlpd, 'lastItem') && method_exists($dataKlpd, 'total'))
                                Menampilkan {{ $dataKlpd->firstItem() ?? 0 }} - {{ $dataKlpd->lastItem() ?? 0 }} dari {{ $dataKlpd->total() }} KLPD
                            @else
                                Menampilkan {{ count($dataKlpd) }} KLPD
                            @endif
                        </div>
                        <div>
                            {{ $dataKlpd->links('vendor.pagination.simple') }}
                        </div>
                    </div>
                @endif
            @else
                <div class="empty-state">
                    <i class="material-icons-outlined">business</i>
                    <h5>Tidak Ada KLPD</h5>
                    <p>Tidak ada data KLPD yang sesuai dengan kriteria pencarian Anda.</p>
                </div>
            @endif
        </div>
    </div>
@endsection
@section('scripts')
    <!-- Modal for Logo Search -->
    <div class="modal fade" id="logoSearchModal" tabindex="-1" aria-labelledby="logoSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
                    <h5 class="modal-title" id="logoSearchModalLabel">
                        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">image_search</i>
                        Search Logo
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="logoResults" class="d-flex flex-wrap gap-2"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">close</i>
                        Tutup
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle filter collapse icon
            const filterCollapse = document.getElementById('filterCollapse');
            const filterToggleIcon = document.querySelector('.filter-toggle-icon');

            if (filterCollapse && filterToggleIcon) {
                filterCollapse.addEventListener('hidden.bs.collapse', function () {
                    filterToggleIcon.textContent = 'expand_more';
                });

                filterCollapse.addEventListener('shown.bs.collapse', function () {
                    filterToggleIcon.textContent = 'expand_less';
                });
            }

            // Auto-submit form when select fields change
            const jenisKlpdSelect = document.getElementById('jenis_klpd');
            if (jenisKlpdSelect) {
                jenisKlpdSelect.addEventListener('change', function() {
                    this.form.submit();
                });
            }

            const provinsiSelect = document.getElementById('provinsi');
            if (provinsiSelect) {
                provinsiSelect.addEventListener('change', function() {
                    this.form.submit();
                });
            }
        });

        document.addEventListener('DOMContentLoaded', function () {
            const logoSearchModal = new bootstrap.Modal(document.getElementById('logoSearchModal'));
            const logoResults = document.getElementById('logoResults');
            let currentKdKlpd = null;

            document.querySelectorAll('.search-logo-btn').forEach(button => {
                button.addEventListener('click', () => {
                    currentKdKlpd = button.getAttribute('data-kdklpd');
                    const namaKlpd = button.getAttribute('data-namaklpd');
                    document.getElementById('logoSearchModalLabel').innerHTML = '<i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">image_search</i> Cari Logo untuk ' + namaKlpd;

                    logoResults.innerHTML = `
                        <div class="d-flex justify-content-center w-100 py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Mencari gambar...</span>
                        </div>
                    `;
                    logoSearchModal.show();

                    fetch('/api/logo-search?query=PNG Logo ' + encodeURIComponent(namaKlpd))
                        .then(response => response.json())
                        .then(data => {
                            logoResults.innerHTML = '';
                            if (data.length === 0) {
                                logoResults.innerHTML = `
                                    <div class="empty-state w-100">
                                        <i class="material-icons-outlined">image_not_supported</i>
                                        <h5>Tidak Ada Gambar</h5>
                                        <p>Tidak ada gambar yang ditemukan untuk "${namaKlpd}".</p>
                                    </div>
                                `;
                                return;
                            }

                            const imageGrid = document.createElement('div');
                            imageGrid.className = 'd-flex flex-wrap gap-3 justify-content-center';

                            data.forEach(imgUrl => {
                                const imgContainer = document.createElement('div');
                                imgContainer.className = 'card p-2 border-0 shadow-sm';
                                imgContainer.style.width = '150px';
                                imgContainer.style.cursor = 'pointer';

                                const img = document.createElement('img');
                                img.src = imgUrl;
                                img.alt = 'Logo option';
                                img.className = 'img-fluid rounded mb-2';
                                img.style.height = '100px';
                                img.style.objectFit = 'contain';

                                const useButton = document.createElement('button');
                                useButton.className = 'btn btn-sm btn-primary w-100';
                                useButton.innerHTML = '<i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">check</i> Gunakan';

                                imgContainer.appendChild(img);
                                imgContainer.appendChild(useButton);
                                imageGrid.appendChild(imgContainer);

                                useButton.addEventListener('click', () => {
                                    if (confirm('Upload gambar ini sebagai logo?')) {
                                        uploadLogo(currentKdKlpd, imgUrl);
                                    }
                                });

                                img.addEventListener('click', () => {
                                    if (confirm('Upload gambar ini sebagai logo?')) {
                                        uploadLogo(currentKdKlpd, imgUrl);
                                    }
                                });
                            });

                            logoResults.appendChild(imageGrid);
                        })
                        .catch(() => {
                            logoResults.innerHTML = `
                                <div class="alert alert-danger w-100">
                                    <i class="material-icons-outlined me-2">error</i>
                                    Error saat memuat gambar. Silakan coba lagi.
                                </div>
                            `;
                        });
                });
            });

        function uploadLogo(kdKlpd, imageUrl) {
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Show loading state
            logoResults.innerHTML = `
                <div class="d-flex justify-content-center w-100 py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">Mengupload logo...</span>
                </div>
            `;

            fetch(`/klpd/${kdKlpd}/update-logo`, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({ kd_klpd: kdKlpd, image_url: imageUrl })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    logoResults.innerHTML = `
                        <div class="alert alert-success w-100">
                            <i class="material-icons-outlined me-2">check_circle</i>
                            Logo berhasil diupload. Halaman akan dimuat ulang.
                        </div>
                    `;
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    logoResults.innerHTML = `
                        <div class="alert alert-danger w-100">
                            <i class="material-icons-outlined me-2">error</i>
                            Error saat mengupload logo: ${data.message || 'Unknown error'}
                        </div>
                    `;
                }
            })
            .catch((error) => {
                logoResults.innerHTML = `
                    <div class="alert alert-danger w-100">
                        <i class="material-icons-outlined me-2">error</i>
                        Error saat mengupload logo: ${error.message}
                    </div>
                `;
            });
        }
        });
    </script>
@endsection