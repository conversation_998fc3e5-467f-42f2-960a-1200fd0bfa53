@extends('layouts.master')

@section('title', 'Edit KLPD')

@section('css')
<style>
    .required-field::after {
        content: " *";
        color: red;
    }

    .preview-container {
        width: 150px;
        height: 150px;
        border: 1px dashed #ccc;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 10px;
        overflow: hidden;
    }

    .preview-container img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .preview-placeholder {
        color: #aaa;
        text-align: center;
    }
</style>
@endsection

@section('content')
<x-page-title title="Edit KLPD" pagetitle="Master Data" />

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">edit</i>
                    Edit KLPD: {{ $klpd->nama_klpd }}
                </h5>
            </div>
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form action="{{ route('klpd.update', $klpd->kd_klpd) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="kd_klpd" class="form-label">Kode KLPD</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">code</i></span>
                                <input type="text" class="form-control" id="kd_klpd" value="{{ $klpd->kd_klpd }}" readonly disabled>
                            </div>
                            <div class="form-text">Kode KLPD tidak dapat diubah.</div>
                        </div>

                        <div class="col-md-6">
                            <label for="jenis_klpd" class="form-label required-field">Jenis KLPD</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">category</i></span>
                                <select class="form-select" id="jenis_klpd" name="jenis_klpd" required>
                                    <option value="">Pilih Jenis KLPD</option>
                                    @foreach($jenisKlpd as $jenis)
                                        <option value="{{ $jenis }}" {{ old('jenis_klpd', $klpd->jenis_klpd) == $jenis ? 'selected' : '' }}>{{ $jenis }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="nama_klpd" class="form-label required-field">Nama KLPD</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">business</i></span>
                            <input type="text" class="form-control" id="nama_klpd" name="nama_klpd" value="{{ old('nama_klpd', $klpd->nama_klpd) }}" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="kd_provinsi" class="form-label required-field">Provinsi</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">location_on</i></span>
                                <select class="form-select" id="kd_provinsi" name="kd_provinsi" required>
                                    <option value="">Pilih Provinsi</option>
                                    @foreach($provinces as $code => $name)
                                        @php
                                            $selected = old('kd_provinsi', $klpd->kd_provinsi) == $code;
                                        @endphp
                                        <option value="{{ $code }}" {{ $selected ? 'selected' : '' }}>{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label for="kd_kabupaten" class="form-label">Kabupaten/Kota</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="material-icons-outlined">location_city</i></span>
                                <input type="text" class="form-control" id="kd_kabupaten" name="kd_kabupaten" value="{{ old('kd_kabupaten', $klpd->kd_kabupaten) }}">
                            </div>
                            <div class="form-text">Opsional. Masukkan kode kabupaten/kota jika ada.</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="logo" class="form-label">Logo</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">image</i></span>
                            <input type="file" class="form-control" id="logo" name="logo" accept="image/*" onchange="previewImage(this)">
                        </div>
                        <div class="form-text">Opsional. Format: JPG, PNG, GIF. Maks: 2MB. Biarkan kosong jika tidak ingin mengubah logo.</div>

                        <div class="preview-container" id="logo-preview">
                            @if($klpd->logo_path)
                                <img src="{{ asset($klpd->logo_path) }}" alt="Logo Preview">
                            @else
                                <div class="preview-placeholder">
                                    <i class="material-icons-outlined" style="font-size: 48px;">image</i>
                                    <p>Tidak ada logo</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            <a href="{{ route('klpd.index') }}" class="btn btn-outline-secondary">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">arrow_back</i>
                                Kembali
                            </a>

                            <button type="button" class="btn btn-outline-danger ms-2" data-bs-toggle="modal" data-bs-target="#deleteKlpdModal">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">delete</i>
                                Hapus
                            </button>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">save</i>
                            Perbarui KLPD
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteKlpdModal" tabindex="-1" aria-labelledby="deleteKlpdModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteKlpdModalLabel">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">warning</i>
                    Konfirmasi Hapus
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus KLPD ini? Tindakan ini tidak dapat dibatalkan.</p>
                <div class="alert alert-warning">
                    <strong>Kode KLPD:</strong> {{ $klpd->kd_klpd }}<br>
                    <strong>Nama KLPD:</strong> {{ $klpd->nama_klpd }}<br>
                    <strong>Jenis KLPD:</strong> {{ $klpd->jenis_klpd }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
                <form action="{{ route('klpd.destroy', $klpd->kd_klpd) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Hapus KLPD</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Debug: Log selected values
        console.log('KLPD Data:', {
            kd_klpd: '{{ $klpd->kd_klpd }}',
            nama_klpd: '{{ $klpd->nama_klpd }}',
            jenis_klpd: '{{ $klpd->jenis_klpd }}',
            kd_provinsi: '{{ $klpd->kd_provinsi }}',
            kd_kabupaten: '{{ $klpd->kd_kabupaten }}'
        });

        // Ensure select elements have the correct values selected
        const jenisKlpdSelect = document.getElementById('jenis_klpd');
        const kdProvinsiSelect = document.getElementById('kd_provinsi');

        // Set jenis_klpd value
        if (jenisKlpdSelect) {
            const jenisKlpdValue = '{{ old('jenis_klpd', $klpd->jenis_klpd) }}';
            console.log('Setting jenis_klpd to:', jenisKlpdValue);

            for (let i = 0; i < jenisKlpdSelect.options.length; i++) {
                if (jenisKlpdSelect.options[i].value === jenisKlpdValue) {
                    jenisKlpdSelect.options[i].selected = true;
                    break;
                }
            }
        }

        // Set kd_provinsi value
        if (kdProvinsiSelect) {
            const kdProvinsiValue = '{{ old('kd_provinsi', $klpd->kd_provinsi) }}';
            console.log('Setting kd_provinsi to:', kdProvinsiValue);
            console.log('Available options:');

            for (let i = 0; i < kdProvinsiSelect.options.length; i++) {
                console.log(`Option ${i}: value="${kdProvinsiSelect.options[i].value}", text="${kdProvinsiSelect.options[i].text}"`);

                // Try both string and number comparison
                if (kdProvinsiSelect.options[i].value === kdProvinsiValue ||
                    kdProvinsiSelect.options[i].value === String(kdProvinsiValue) ||
                    Number(kdProvinsiSelect.options[i].value) === Number(kdProvinsiValue)) {
                    console.log(`Match found at option ${i}`);
                    kdProvinsiSelect.options[i].selected = true;
                    break;
                }
            }

            // If no match found, try to find by text
            if (!kdProvinsiSelect.selectedIndex || kdProvinsiSelect.selectedIndex === 0) {
                console.log('No match found by value, trying to find by province name');
                // This is a fallback if the value doesn't match
                const provinceName = '{{ $provinces[$klpd->kd_provinsi] ?? "" }}';
                if (provinceName) {
                    for (let i = 0; i < kdProvinsiSelect.options.length; i++) {
                        if (kdProvinsiSelect.options[i].text === provinceName) {
                            console.log(`Match found by name at option ${i}`);
                            kdProvinsiSelect.options[i].selected = true;
                            break;
                        }
                    }
                }
            }
        }
    });

    function previewImage(input) {
        const preview = document.getElementById('logo-preview');

        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" alt="Logo Preview">`;
            }

            reader.readAsDataURL(input.files[0]);
        } else {
            @if($klpd->logo_path)
                preview.innerHTML = `<img src="{{ asset($klpd->logo_path) }}" alt="Logo Preview">`;
            @else
                preview.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="material-icons-outlined" style="font-size: 48px;">image</i>
                        <p>Tidak ada logo</p>
                    </div>
                `;
            @endif
        }
    }
</script>
@endsection
