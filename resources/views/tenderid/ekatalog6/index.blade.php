{{-- resources/views/tenderid/ekatalog6/index.blade.php --}}
@php
  use App\Models\Lpse;
@endphp
@extends('layouts.master')

@section('title', 'Semua Paket Ekatalog 6')

@section('css')
  <!-- Select2 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

  <style>
    /* Card Styling */
    .card {
      border: none;
      border-radius: 12px;
      box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      margin-bottom: 24px;
    }

    .card:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      border-radius: 12px 12px 0 0 !important;
      padding: 18px 24px;
    }

    .card-title {
      font-weight: 600;
      color: #344767;
      margin-bottom: 0;
      font-size: 1.1rem;
    }

    .card-body {
      padding: 24px;
    }

    /* Form Controls */
    .form-control, .form-select {
      border-radius: 8px;
      padding: 10px 15px;
      border: 1px solid #e9ecef;
      font-size: 0.9rem;
      transition: all 0.2s ease;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    }

    .form-control:focus, .form-select:focus {
      border-color: #4481eb;
      box-shadow: 0 0 0 0.25rem rgba(68, 129, 235, 0.1);
    }

    .form-label {
      font-weight: 500;
      font-size: 0.9rem;
      color: #495057;
      margin-bottom: 8px;
    }

    .form-text {
      font-size: 0.75rem;
      color: #6c757d;
    }

    .input-group-text {
      background-color: #f8f9fa;
      border-color: #e9ecef;
      color: #6c757d;
    }

    /* Table Styling */
    .table {
      margin-bottom: 0;
      table-layout: auto;
      width: 100%;
    }

    .table thead th {
      font-weight: 500;
      text-transform: uppercase;
      font-size: 0.8rem;
      letter-spacing: 0.5px;
      padding: 15px;
      border: none;
    }

    .table tbody td {
      padding: 16px;
      vertical-align: middle;
      border-bottom: 1px solid #f8f9fa;
      font-size: 0.9rem;
      word-wrap: break-word;
      white-space: normal;
    }

    .table tbody tr:last-child td {
      border-bottom: none;
    }

    .table tbody tr {
      transition: all 0.2s ease;
    }

    .table tbody tr:hover {
      background-color: #f8f9fa;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
    }

    /* Product Box */
    .product-box {
      width: 50px;
      height: 50px;
      border-radius: 10px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .product-box img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      transition: transform 0.3s ease;
    }

    .product-box img:hover {
      transform: scale(1.15);
    }

    .product-info {
      flex: 1;
    }

    .product-title {
      font-weight: 600;
      color: #344767;
      margin-bottom: 5px;
      display: block;
      text-decoration: none;
    }

    .product-title:hover {
      color: #4481eb;
      text-decoration: underline;
    }

    .product-category {
      color: #6c757d;
      font-size: 0.85rem;
      margin-bottom: 0;
    }

    /* Badge Styling */
    .badge {
      padding: 6px 12px;
      font-weight: 500;
      font-size: 0.75rem;
      border-radius: 30px;
    }

    .badge.tenderid_header {
      background: linear-gradient(90deg, #4caf50, #2e7d32);
      color: white;
    }

    /* Pagination Custom */
    .pagination {
      margin-bottom: 0;
      margin-top: 20px;
    }

    .pagination .page-link {
      color: #4481eb;
      border: none;
      padding: 10px 15px;
      margin: 0 5px;
      border-radius: 8px;
      font-size: 0.9rem;
      transition: all 0.2s ease;
    }

    .pagination .page-link:hover {
      background-color: #f8f9fa;
      color: #4481eb;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .pagination .active .page-link {
      background: linear-gradient(90deg, #4481eb, #04befe);
      color: white;
      box-shadow: 0 4px 10px rgba(68, 129, 235, 0.3);
    }

    /* Empty State */
    .empty-state {
      text-align: center;
      padding: 40px 20px;
    }

    .empty-state i {
      font-size: 48px;
      color: #e9ecef;
      margin-bottom: 15px;
    }

    .empty-state h5 {
      color: #6c757d;
      font-weight: 500;
      margin-bottom: 10px;
    }

    .empty-state p {
      color: #adb5bd;
      max-width: 400px;
      margin: 0 auto;
    }

    /* Filter toggle styling */
    .filter-toggle-icon {
      transition: transform 0.3s ease;
    }

    .collapsed .filter-toggle-icon {
      transform: rotate(180deg);
    }

    /* Compact view styling */
    .compact-view td {
      padding-top: 10px !important;
      padding-bottom: 10px !important;
    }

    /* Select2 customization */
    .select2-container .select2-selection--single {
      height: 42px !important;
      padding: 8px 12px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      background-color: #fff;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
      height: 42px;
    }

    .select2-dropdown {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
      background-color: #4481eb;
    }

    /* Saat fokus, gunakan efek Bootstrap */
    .select2-container--default .select2-selection--single:focus {
    border-color: #86b7fe !important;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    /* Dropdown harus mirip dengan dropdown Bootstrap */
    .select2-container--default .select2-dropdown {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Ukuran teks dalam dropdown */
    .select2-container--default .select2-results__option {
    padding: 8px 12px;
    font-size: 14px;
    }

    /* Efek hover seperti dropdown Bootstrap */
    .select2-container--default .select2-results__option--highlighted {
    background-color: #0d6efd !important;
    color: white !important;
    }
  </style>
@endsection

@section('content')

  <!-- Search and Filter Section -->
  <div class="card mb-4">
    <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
      <h5 class="card-title mb-0">
        <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
          <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
          <span>Filter Paket e-Katalog 6</span>
          <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
        </a>
      </h5>
      <span class="badge bg-white text-primary" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
        <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">shopping_bag</i>
        @if(method_exists($ekatalog6List, 'total'))
          {{ number_format($ekatalog6List->total(), 0, ',', '.') }} Paket
        @else
          {{ number_format($ekatalog6List->count(), 0, ',', '.') }} Paket
        @endif
      </span>
    </div>
    <div class="collapse show" id="filterCollapse">
      <div class="card-body">
        <form method="GET" action="{{ route('ekatalog6.index') }}">
          <div class="row g-3">
            <!-- Input Pencarian -->
            <div class="col-md-3">
              <label for="search-input" class="form-label">Pencarian</label>
              <div class="input-group">
                <span class="input-group-text"><i class="material-icons-outlined">search</i></span>
                <input type="text" name="search" id="search-input" class="form-control" placeholder="Cari nama paket atau kode..."
                  value="{{ request('search') }}">
              </div>
              <div class="form-text">Cari berdasarkan nama paket, kode, atau satker</div>
            </div>

            <!-- Dropdown Instansi -->
            <div class="col-md-3">
              <label for="instansi-select" class="form-label">Instansi</label>
              <select name="instansi" id="instansi-select" class="form-control select2">
                <option value="">Pilih Instansi</option>
                @foreach($instansi_list as $instansi)
                  <option value="{{ $instansi }}" {{ request('instansi') == $instansi ? 'selected' : '' }}>
                    {{ $instansi }}
                  </option>
                @endforeach
              </select>
              <div class="form-text">Pilih instansi pembeli</div>
            </div>

            <!-- Filter Tahun Anggaran -->
            <div class="col-md-4">
              <label class="form-label">Rentang Tahun Anggaran</label>
              <div class="row g-2">
                <div class="col-md-6">
                  <div class="input-group">
                    <span class="input-group-text"><i class="material-icons-outlined">event</i></span>
                    <input type="number" name="tanggal_mulai" id="tanggal-mulai" class="form-control" min="2000" max="{{ date('Y') + 1 }}"
                      placeholder="Tahun Mulai" value="{{ request('tanggal_mulai') }}">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="input-group">
                    <span class="input-group-text"><i class="material-icons-outlined">event</i></span>
                    <input type="number" name="tanggal_akhir" id="tanggal-akhir" class="form-control" min="2000" max="{{ date('Y') + 1 }}"
                      placeholder="Tahun Akhir" value="{{ request('tanggal_akhir') }}">
                  </div>
                </div>
              </div>
              <div class="form-text">Masukkan tahun awal dan akhir</div>
            </div>

            <!-- Tombol Filter & Reset -->
            <div class="col-12 mt-4">
              <div class="d-flex justify-content-end gap-2">
                <button type="submit" class="btn btn-primary d-inline-flex align-items-center">
                  <i class="material-icons-outlined me-2">filter_alt</i>
                  <span>Terapkan Filter</span>
                </button>
                <a href="{{ route('ekatalog6.index') }}" class="btn btn-outline-secondary d-inline-flex align-items-center">
                  <i class="material-icons-outlined me-2">refresh</i>
                  <span>Reset Filter</span>
                </a>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Paket Ekatalog6 Table Section -->
  <div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
      <h5 class="card-title mb-0">
        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">shopping_cart</i>
        Daftar Paket e-Katalog 6
      </h5>
      <div>
        <button class="btn btn-sm btn-outline-primary me-2 d-inline-flex align-items-center" id="btn-refresh-data">
          <i class="material-icons-outlined me-1">refresh</i>
          <span>Refresh Data</span>
        </button>
        <div class="dropdown d-inline-block">
          <button class="btn btn-sm btn-outline-secondary dropdown-toggle d-inline-flex align-items-center" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="material-icons-outlined me-1">tune</i>
            <span>Tampilan</span>
          </button>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
            <li><a class="dropdown-item d-flex align-items-center" href="#" id="toggle-compact-view">
              <i class="material-icons-outlined me-2">view_compact</i>
              <span>Tampilan Kompak</span>
            </a></li>
            <li><a class="dropdown-item d-flex align-items-center" href="#" id="toggle-full-view">
              <i class="material-icons-outlined me-2">view_agenda</i>
              <span>Tampilan Lengkap</span>
            </a></li>
          </ul>
        </div>
      </div>
    </div>
    <div class="card-body p-0">
      @if($ekatalog6List->count() > 0)
        <div class="table-responsive">
          <table class="table align-middle mb-0">
            <thead>
              <tr class="tenderid_header">
                <th style="width: 10%;">Kode Paket</th>
                <th style="width: 35%;">Nama Paket</th>
                <th style="width: 25%;">Instansi Pembeli & SatKer</th>
                <th style="width: 20%;">Pelaksana</th>
                <th style="width: 10%;">Tanggal Sync</th>
              </tr>
            </thead>
            <tbody>
              @foreach($ekatalog6List as $item)
                <tr class="ekatalog-row">
                  <td>
                    <a href="#" class="fw-semibold text-primary">
                      #{{ $item->kode }}
                    </a>
                  </td>
                  <td>
                    <div class="d-flex align-items-center gap-3">
                      <div class="product-box">
                        @php
                          $nama_klpd = $item->klpd ? $item->klpd->nama_klpd : '';
                          $nama_klpd_normalized = \App\Helpers\CompanyHelper::normalizeKlpdName($nama_klpd);
                          $lpse = Lpse::where('nama_lpse', 'LIKE', "%{$nama_klpd_normalized}%")->first();
                          if ($lpse) {
                            $lpse->logo_path = str_replace('http://proyeklpse.test', '', $lpse->logo_path);
                          }
                        @endphp
                        @if($lpse)
                          <img src="{{ asset($lpse->logo_path) }}" alt="Logo" class="rounded-3">
                        @else
                          <img src="{{ asset('build/images/logo-default.png') }}" alt="Logo" class="rounded-3">
                        @endif
                      </div>
                      <div class="product-info">
                        <a href="#" class="product-title">
                          {{ \Illuminate\Support\Str::limit($item->nama_paket, 70) }}
                        </a>
                        <p class="mb-0 product-category">
                          <span class="badge tenderid_header">{{ convert_to_rupiah($item->total_pelaksanaan) }}</span>
                        </p>
                      </div>
                    </div>
                  </td>
                  <td>
                    <a href="{{ route('ekatalog6.index', ['instansi' => $item->klpd->nama_klpd]) }}" class="text-primary">
                      <strong>{{ $nama_klpd_normalized }}</strong>
                    </a>
                    <div class="text-muted small">
                      Satker: {{ \Illuminate\Support\Str::limit($item->nama_satker, 50) }}
                    </div>
                  </td>
                  <td>
                    <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($item->penyedia->pembeda)]) }}" class="text-primary">
                      {{ \Illuminate\Support\Str::limit($item->penyedia->nama_peserta, 40) }}
                    </a>
                  </td>
                  <td>
                    <span class="text-muted small d-flex align-items-center">
                      <i class="material-icons-outlined me-1" style="font-size: 14px;">event</i>
                      <span>{{ $item->created_at ? \Carbon\Carbon::parse($item->created_at)->format('j F Y') : '-' }}</span>
                    </span>
                  </td>
                </tr>
              @endforeach
            </tbody>
          </table>

          <!-- Pagination -->
          <div class="d-flex justify-content-between align-items-center p-3 border-top">
            <div class="text-muted small">
              @if(method_exists($ekatalog6List, 'firstItem') && method_exists($ekatalog6List, 'lastItem') && method_exists($ekatalog6List, 'total'))
                Menampilkan {{ $ekatalog6List->firstItem() ?? 0 }} - {{ $ekatalog6List->lastItem() ?? 0 }} dari {{ $ekatalog6List->total() }} paket
              @else
                Menampilkan {{ $ekatalog6List->count() }} paket
              @endif
            </div>
            <div>
              @if(method_exists($ekatalog6List, 'links'))
                {{ $ekatalog6List->links('vendor.pagination.simple') }}
              @endif
            </div>
          </div>
        </div>
      @else
        <div class="empty-state">
          <i class="material-icons-outlined">search_off</i>
          <h5>Tidak Ada Data e-Katalog 6</h5>
          <p>Tidak ada data e-Katalog 6 yang sesuai dengan filter yang Anda pilih. Silakan ubah filter atau coba kata kunci lain.</p>
        </div>
      @endif
    </div>
  </div>
@endsection

@section('scripts')
  <!-- Select2 JS -->
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

  <script>
    $(document).ready(function () {
      // Inisialisasi Select2
      $('#instansi-select').select2({
        width: '100%',
        placeholder: "Pilih Instansi",
        allowClear: true,
        minimumInputLength: 2,
        language: {
          inputTooShort: function() {
            return "Ketik minimal 2 karakter untuk mencari";
          },
          noResults: function() {
            return "Tidak ada instansi yang ditemukan";
          },
          searching: function() {
            return "Mencari...";
          }
        }
      });

      // Refresh button
      $('#btn-refresh-data').on('click', function(e) {
        e.preventDefault();

        // Tampilkan animasi loading
        $(this).html(`
          <i class="material-icons-outlined me-1 spin-animation">refresh</i>
          <span>Memuat...</span>
        `);
        $(this).prop('disabled', true);

        // Reload halaman
        setTimeout(function() {
          window.location.reload();
        }, 500);
      });

      // Toggle compact view
      $('#toggle-compact-view').on('click', function(e) {
        e.preventDefault();
        $('.ekatalog-row').addClass('compact-view');
        $('.product-category').hide();
        $('.table').addClass('table-sm');
        localStorage.setItem('ekatalog6_view_mode', 'compact');

        // Notifikasi
        showToast('Tampilan kompak diaktifkan');
      });

      // Toggle full view
      $('#toggle-full-view').on('click', function(e) {
        e.preventDefault();
        $('.ekatalog-row').removeClass('compact-view');
        $('.product-category').show();
        $('.table').removeClass('table-sm');
        localStorage.setItem('ekatalog6_view_mode', 'full');

        // Notifikasi
        showToast('Tampilan lengkap diaktifkan');
      });

      // Load saved view preferences
      loadViewPreferences();

      // Handle filter collapse toggle
      $('#filterCollapse').on('show.bs.collapse', function () {
        $('.filter-toggle-icon').text('expand_less');
        localStorage.setItem('ekatalog6_filter_collapsed', 'false');
      });

      $('#filterCollapse').on('hide.bs.collapse', function () {
        $('.filter-toggle-icon').text('expand_more');
        localStorage.setItem('ekatalog6_filter_collapsed', 'true');
      });

      // Load filter collapse state from localStorage
      if (localStorage.getItem('ekatalog6_filter_collapsed') === 'true') {
        $('#filterCollapse').collapse('hide');
      }
    });

    // Function to load view preferences
    function loadViewPreferences() {
      // Load view mode
      const viewMode = localStorage.getItem('ekatalog6_view_mode');
      if (viewMode === 'compact') {
        $('.ekatalog-row').addClass('compact-view');
        $('.product-category').hide();
        $('.table').addClass('table-sm');
      }
    }

    // Function to show toast notification
    function showToast(message) {
      // Check if toast container exists
      let toastContainer = document.getElementById('toast-container');
      if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
      }

      // Create toast element
      const toastId = 'toast-' + Date.now();
      const toast = document.createElement('div');
      toast.className = 'toast align-items-center text-white bg-primary border-0';
      toast.id = toastId;
      toast.setAttribute('role', 'alert');
      toast.setAttribute('aria-live', 'assertive');
      toast.setAttribute('aria-atomic', 'true');

      toast.innerHTML = `
        <div class="d-flex">
          <div class="toast-body">
            <i class="material-icons-outlined me-2">info</i>
            ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      `;

      toastContainer.appendChild(toast);

      // Initialize and show toast
      const bsToast = new bootstrap.Toast(toast, {
        animation: true,
        autohide: true,
        delay: 3000
      });

      bsToast.show();

      // Remove toast after it's hidden
      toast.addEventListener('hidden.bs.toast', function () {
        toast.remove();
      });
    }
  </script>
@endsection
