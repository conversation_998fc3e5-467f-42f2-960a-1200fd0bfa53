@extends('layouts.master')

@section('title', $lpse->nama_lpse)
@section('css')
<style>
    /* General styling */
    .wh-120 {
        width: 120px;
        height: 120px;
    }
    .wh-48 {
        width: 48px;
        height: 48px;
    }
    .wh-40 {
        width: 40px;
        height: 40px;
    }

    /* Card styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 16px 24px;
        display: flex;
        align-items: center;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .card-body {
        padding: 24px;
    }

    /* Table styling */
    .table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table thead th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-top: none;
        padding: 16px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e9ecef;
    }

    .table tbody td {
        padding: 16px;
        vertical-align: middle;
        border-bottom: 1px solid #f8f9fa;
        font-size: 0.9rem;
        color: #495057;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    .table tbody tr {
        transition: all 0.2s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .table-responsive {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        overflow: hidden;
    }

    /* Row status styling */
    .table tr.bg-warning-important td {
        background-color: rgba(255, 193, 7, 0.15);
    }

    /* Product styling */
    .product-box {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .product-box img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        transition: transform 0.3s ease;
    }

    .product-box img:hover {
        transform: scale(1.15);
    }

    .product-info {
        flex: 1;
    }

    .product-title {
        font-weight: 600;
        color: #0d6efd;
        display: block;
        margin-bottom: 5px;
        font-size: 0.95rem;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .product-title:hover {
        color: #0b5ed7;
        text-decoration: underline;
    }

    .product-category {
        color: #6c757d;
        font-size: 0.85rem;
        margin-bottom: 0;
    }

    .product-category a {
        color: #6c757d;
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .product-category a:hover {
        color: #0d6efd;
        text-decoration: underline;
    }

    /* Badge styling */
    .badge {
        padding: 6px 10px;
        font-weight: 500;
        font-size: 0.75rem;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        line-height: 1;
    }

    /* LPSE header styling */
    .lpse-header {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
    }

    .lpse-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: #344767;
        margin-bottom: 8px;
    }

    .lpse-url {
        font-size: 0.95rem;
        color: #6c757d;
        text-decoration: none;
    }

    .lpse-url:hover {
        color: #0d6efd;
        text-decoration: underline;
    }

    .lpse-total {
        font-size: 2.5rem;
        font-weight: 700;
        color: #0d6efd;
        text-align: right;
    }

    /* Stat cards */
    .stat-card {
        border-radius: 10px;
        padding: 15px;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #344767;
        margin-bottom: 0;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 0;
    }

    /* Chart container */
    .chart-container {
        width: 100%;
        height: auto;
        border-radius: 8px;
        overflow: hidden;
    }

    @media (max-width: 768px) {
        /* Tablet */
        .chart-container {
            width: 100%;
            height: auto;
        }
    }

    @media (max-width: 480px) {
        /* Smartphone */
        .chart-container {
            width: 100%;
            height: auto;
        }
    }

    /* Animation */
    @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    .animate-spin {
        animation: spin 1s linear infinite;
    }

    /* Action buttons */
    .action-btn {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }

    /* List group styling */
    .list-group-item {
        padding: 16px 24px;
        border-color: #f1f1f1;
    }

    .list-group-item b {
        color: #344767;
        font-size: 0.9rem;
    }
</style>
@apexchartsScripts
@endsection
@section('content')
<!-- Breadcrumb -->
<div class="page-breadcrumb d-flex align-items-center mb-3">
    <div class="breadcrumb-title pe-3">LPSE</div>
    <div class="ps-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 p-0">
                <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="material-icons-outlined fs-5">home</i></a></li>
                <li class="breadcrumb-item"><a href="javascript:history.back()">Kembali</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ $lpse->nama_lpse }}</li>
            </ol>
        </nav>
    </div>
</div>
<!-- End Breadcrumb -->

<!-- LPSE Header -->
<div class="card lpse-header">
    <div class="row align-items-center">
        <div class="col-lg-8">
            <div class="d-flex align-items-center">
                <div class="product-box me-3" style="width: 70px; height: 70px;">
                    @if($lpse->logo_path)
                        <img src="{{ asset(str_replace('http://proyeklpse.test', '', $lpse->logo_path)) }}" alt="Logo LPSE" class="rounded-3">
                    @else
                        <div class="d-flex align-items-center justify-content-center bg-light rounded-3" style="width: 70px; height: 70px;">
                            <i class="material-icons-outlined text-primary" style="font-size: 2rem;">business</i>
                        </div>
                    @endif
                </div>
                <div>
                    <h2 class="lpse-name mb-2">{{ $lpse->nama_lpse }}</h2>
                    <div class="d-flex align-items-center flex-wrap gap-2">
                        <a href="{{ $lpse->url }}" target="_blank" class="badge bg-light text-dark d-flex align-items-center">
                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem;">link</i>
                            {{ $lpse->url }}
                        </a>

                        @if($lpse->provinsi)
                        <span class="badge bg-light text-dark d-flex align-items-center">
                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem;">location_on</i>
                            {{ $lpse->provinsi }}
                        </span>
                        @endif

                        @if($lpse->versi_lpse)
                        <span class="badge bg-light text-dark d-flex align-items-center">
                            <i class="material-icons-outlined me-1" style="font-size: 0.9rem;">info</i>
                            Versi {{ $lpse->versi_lpse }}
                        </span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mt-4 mt-lg-0">
            <div class="text-lg-end">
                <p class="text-muted mb-1">Total Tender</p>
                <h2 class="lpse-total mb-0">{{ number_format($totalTenders, 0, ',', '.') }}</h2>
                <div class="d-flex justify-content-lg-end mt-2 gap-2">
                    <a href="{{ $lpse->url }}" target="_blank" class="btn btn-primary d-flex align-items-center gap-1">
                        <i class="material-icons-outlined" style="font-size: 1rem;">open_in_new</i>
                        Kunjungi Website
                    </a>
                    <a href="javascript:history.back()" class="btn btn-outline-secondary d-flex align-items-center gap-1">
                        <i class="material-icons-outlined" style="font-size: 1rem;">arrow_back</i>
                        Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistik dan Informasi -->
<div class="row">
    <!-- Statistik Utama -->
    <div class="col-12 col-lg-4">
        <div class="row g-3">
            <!-- Statistik Tender -->
            <div class="col-12 col-md-4 col-lg-12">
                <div class="card stat-card">
                    <div class="d-flex align-items-center p-3">
                        <div class="stat-icon bg-primary-subtle text-primary me-3">
                            <i class="material-icons-outlined">assignment</i>
                        </div>
                        <div>
                            <p class="stat-label mb-1">Total Tender</p>
                            <h4 class="stat-value mb-0">{{ number_format($totalTenders, 0, ',', '.') }}</h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistik Pagu -->
            <div class="col-12 col-md-4 col-lg-12">
                <div class="card stat-card">
                    <div class="d-flex align-items-center p-3">
                        <div class="stat-icon bg-success-subtle text-success me-3">
                            <i class="material-icons-outlined">account_balance_wallet</i>
                        </div>
                        <div>
                            <p class="stat-label mb-1">Total Pagu</p>
                            <h4 class="stat-value mb-0">Rp {{ format_number_short($totalPagu) }}</h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistik HPS -->
            <div class="col-12 col-md-4 col-lg-12">
                <div class="card stat-card">
                    <div class="d-flex align-items-center p-3">
                        <div class="stat-icon bg-warning-subtle text-warning me-3">
                            <i class="material-icons-outlined">calculate</i>
                        </div>
                        <div>
                            <p class="stat-label mb-1">Total HPS</p>
                            <h4 class="stat-value mb-0">Rp {{ format_number_short($totalHPS) }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informasi LPSE -->
        <div class="card mt-4">
            <div class="card-header d-flex align-items-center">
                <i class="material-icons-outlined me-2 text-primary">info</i>
                <h5 class="card-title mb-0">Informasi LPSE</h5>
            </div>
            <div class="card-body p-0">
                <ul class="list-group list-group-flush">
                    @if($lpse->provinsi)
                    <li class="list-group-item d-flex">
                        <div class="d-flex align-items-center me-3">
                            <i class="material-icons-outlined text-primary">location_on</i>
                        </div>
                        <div>
                            <b>Provinsi</b>
                            <p class="mb-0">{{ $lpse->provinsi }}</p>
                        </div>
                    </li>
                    @endif

                    @if($lpse->alamat)
                    <li class="list-group-item d-flex">
                        <div class="d-flex align-items-center me-3">
                            <i class="material-icons-outlined text-primary">home</i>
                        </div>
                        <div>
                            <b>Alamat</b>
                            <p class="mb-0">{{ $lpse->alamat }}</p>
                        </div>
                    </li>
                    @endif

                    @if($lpse->email)
                    <li class="list-group-item d-flex">
                        <div class="d-flex align-items-center me-3">
                            <i class="material-icons-outlined text-primary">email</i>
                        </div>
                        <div>
                            <b>Email</b>
                            <p class="mb-0">
                                <a href="mailto:{{ $lpse->email }}" class="text-primary">{{ $lpse->email }}</a>
                            </p>
                        </div>
                    </li>
                    @endif

                    @if($lpse->helpdesk)
                    <li class="list-group-item d-flex">
                        <div class="d-flex align-items-center me-3">
                            <i class="material-icons-outlined text-primary">support_agent</i>
                        </div>
                        <div>
                            <b>Helpdesk</b>
                            <p class="mb-0">{{ $lpse->helpdesk }}</p>
                        </div>
                    </li>
                    @endif
                </ul>
            </div>
            <div class="card-footer bg-light">
                <div class="d-flex justify-content-center gap-2">
                    @auth
                        @php
                            $followed = json_decode(auth()->user()->lpse_followed) ?? [];
                        @endphp
                        @if (in_array($lpse->kd_lpse, $followed))
                            <a href="javascript:;" class="btn btn-warning d-flex align-items-center gap-1 unfollow-btn" data-lpse-id="{{ $lpse->kd_lpse }}">
                                <i class="material-icons-outlined" style="font-size: 1rem;">notifications_off</i>
                                Berhenti Mengikuti
                            </a>
                        @else
                            <a href="javascript:;" class="btn btn-primary d-flex align-items-center gap-1 follow-btn" data-lpse-id="{{ $lpse->kd_lpse }}">
                                <i class="material-icons-outlined" style="font-size: 1rem;">notifications_active</i>
                                Ikuti
                            </a>
                        @endif
                    @endauth

                    @if($lpse->email)
                    <a href="mailto:{{ $lpse->email }}" class="btn btn-outline-info d-flex align-items-center gap-1">
                        <i class="material-icons-outlined" style="font-size: 1rem;">email</i>
                        Email
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Grafik dan Statistik -->
    <div class="col-12 col-lg-8">
        <!-- Grafik Kategori Pekerjaan -->
        <div class="card">
            <div class="card-header d-flex align-items-center">
                <i class="material-icons-outlined me-2 text-primary">pie_chart</i>
                <h5 class="card-title mb-0">Jumlah Tender Per Kategori Pekerjaan</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="width: 100%; max-width: 100%; height: auto;">
                    {!! $kategoriChart->container() !!}
                    {!! $kategoriChart->script() !!}
                </div>
            </div>
        </div>

        <!-- Grafik Tender Per Bulan -->
        <div class="card mt-4">
            <div class="card-header d-flex align-items-center">
                <i class="material-icons-outlined me-2 text-primary">bar_chart</i>
                <h5 class="card-title mb-0">Chart Tender Per Bulan</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="width: 100%; max-width: 100%; height: auto;">
                    {!! $chart->container() !!}
                    {!! $chart->script() !!}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tender Terbaru -->
<div class="card mt-4">
    <div class="card-header d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
            <i class="material-icons-outlined me-2 text-primary">list_alt</i>
            <h5 class="card-title mb-0">15 Tender Terbaru</h5>
        </div>
        <span class="badge bg-primary rounded-pill">{{ $recentTenders->count() }}</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table align-middle mb-0">
                <thead>
                    <tr>
                        <th>Kode Tender</th>
                        <th>Nama Paket</th>
                        <th>Kategori</th>
                        <th>Tahapan</th>
                        <th>Pagu</th>
                        <th>HPS</th>
                        <th>Tanggal Tayang</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($recentTenders as $tender)
                        @php
                            if ($tender->status_tender === 'Tender Sedang Berjalan') {
                                $tender->status_tender = 'Aktif';
                                $statusClass = '';
                                $statusIcon = 'pending';
                            } elseif ($tender->status_tender === 'Tender Ditutup') {
                                $tender->status_tender = 'Tidak Aktif';
                                $statusClass = 'bg-warning-important';
                                $statusIcon = 'event_busy';
                            } else {
                                $statusClass = '';
                                $statusIcon = 'event_note';
                            }
                        @endphp
                        <tr class="{{ $statusClass }}">
                            <td>
                                <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}" class="text-primary fw-medium">
                                    #{{ $tender->kode_tender }}
                                </a>
                            </td>
                            <td>
                                <div class="d-flex align-items-center gap-3">
                                    <div class="product-box">
                                        @if($lpse->logo_path)
                                            <img src="{{ asset(str_replace('http://proyeklpse.test', '', $lpse->logo_path)) }}" alt="Logo" class="rounded-3">
                                        @else
                                            <div class="d-flex align-items-center justify-content-center bg-light rounded-3" style="width: 40px; height: 40px;">
                                                <i class="material-icons-outlined text-primary" style="font-size: 1.5rem;">business</i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="product-info">
                                        <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}" class="product-title">
                                            {{ Str::limit($tender->nama_paket, 60) }}
                                        </a>
                                        <p class="mb-0 product-category">
                                            <a href="{{ route('lpse.dashboard', ['kode_lpse' => $tender->lpse->kd_lpse]) }}">
                                                {{ $tender->lpse->nama_lpse }}
                                            </a>
                                        </p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">
                                    {{ $tender->kategori_pekerjaan }}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="material-icons-outlined me-1 text-{{ $tender->status_tender === 'Aktif' ? 'success' : 'warning' }}">{{ $statusIcon }}</i>
                                    <span>{{ $tender->tahap_tender }}</span>
                                </div>
                            </td>
                            <td>
                                <span class="fw-medium">Rp {{ format_number_short($tender->pagu) }}</span>
                            </td>
                            <td>
                                <span class="fw-medium">Rp {{ format_number_short($tender->hps) }}</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="material-icons-outlined me-1 text-muted" style="font-size: 0.9rem;">calendar_today</i>
                                    <span>{{ format_date_indonesian($tender->tanggal_paket_tayang) }}</span>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Perusahaan dengan Nilai Kemenangan Terbanyak -->
<div class="card mt-4">
    <div class="card-header d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
            <i class="material-icons-outlined me-2 text-primary">emoji_events</i>
            <h5 class="card-title mb-0">Perusahaan dengan Nilai Kemenangan Terbanyak</h5>
        </div>
        <span class="badge bg-success rounded-pill">{{ $topWinningCompanies->count() }}</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table align-middle mb-0">
                <thead>
                    <tr>
                        <th>Nama Perusahaan</th>
                        <th>NPWP</th>
                        <th>Alamat</th>
                        <th>Total Tender</th>
                        <th>Total Nilai</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($topWinningCompanies as $company)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center gap-2">
                                    <div class="wh-40 d-flex align-items-center justify-content-center bg-light-primary rounded-circle">
                                        <i class="material-icons-outlined text-primary">business</i>
                                    </div>
                                    <div>
                                        <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($company->peserta->pembeda)]) }}" class="text-primary fw-medium">
                                            {{ Str::limit($company->peserta->nama_peserta, 40) }}
                                        </a>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="material-icons-outlined me-1 text-muted" style="font-size: 0.9rem;">credit_card</i>
                                    <span>{{ $company->peserta->npwp ?? $company->peserta->npwp_blur }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="material-icons-outlined me-1 text-muted" style="font-size: 0.9rem;">location_on</i>
                                    <span>{{ Str::limit($company->peserta->alamat, 40) }}</span>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ $company->jumlah_kemenangan }} Tender</span>
                            </td>
                            <td>
                                <span class="fw-medium text-success">Rp {{ format_number_short($company->total_nilai_kemenangan) }}</span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Perusahaan dengan Jumlah Kemenangan Terbanyak -->
<div class="card mt-4">
    <div class="card-header d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
            <i class="material-icons-outlined me-2 text-primary">military_tech</i>
            <h5 class="card-title mb-0">Perusahaan dengan Jumlah Kemenangan Terbanyak</h5>
        </div>
        <span class="badge bg-success rounded-pill">{{ $topWinningCompanies2->count() }}</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table align-middle mb-0">
                <thead>
                    <tr>
                        <th>Nama Perusahaan</th>
                        <th>NPWP</th>
                        <th>Alamat</th>
                        <th>Total Tender</th>
                        <th>Total Nilai</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($topWinningCompanies2 as $company)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center gap-2">
                                    <div class="wh-40 d-flex align-items-center justify-content-center bg-light-success rounded-circle">
                                        <i class="material-icons-outlined text-success">business</i>
                                    </div>
                                    <div>
                                        <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($company->peserta->pembeda)]) }}" class="text-primary fw-medium">
                                            {{ Str::limit($company->peserta->nama_peserta, 40) }}
                                        </a>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="material-icons-outlined me-1 text-muted" style="font-size: 0.9rem;">credit_card</i>
                                    <span>{{ $company->peserta->npwp ?? $company->peserta->npwp_blur }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="material-icons-outlined me-1 text-muted" style="font-size: 0.9rem;">location_on</i>
                                    <span>{{ Str::limit($company->peserta->alamat, 40) }}</span>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-success">{{ $company->jumlah_kemenangan }} Tender</span>
                            </td>
                            <td>
                                <span class="fw-medium text-success">Rp {{ format_number_short($company->total_nilai_kemenangan) }}</span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Perusahaan Paling Banyak Ikut Tender -->
<div class="card mt-4">
    <div class="card-header d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
            <i class="material-icons-outlined me-2 text-primary">groups</i>
            <h5 class="card-title mb-0">Perusahaan Paling Banyak Ikut Tender</h5>
        </div>
        <span class="badge bg-primary rounded-pill">{{ $topParticipatingCompanies->count() }}</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table align-middle mb-0">
                <thead>
                    <tr>
                        <th>Nama Perusahaan</th>
                        <th>NPWP</th>
                        <th>Alamat</th>
                        <th>Total Ikut Tender</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($topParticipatingCompanies as $company)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center gap-2">
                                    <div class="wh-40 d-flex align-items-center justify-content-center bg-light-info rounded-circle">
                                        <i class="material-icons-outlined text-info">business</i>
                                    </div>
                                    <div>
                                        <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($company->perusahaan->pembeda)]) }}" class="text-primary fw-medium">
                                            {{ Str::limit($company->perusahaan->nama_peserta, 40) }}
                                        </a>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="material-icons-outlined me-1 text-muted" style="font-size: 0.9rem;">credit_card</i>
                                    <span>{{ $company->perusahaan->npwp ?? $company->perusahaan->npwp_blur }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="material-icons-outlined me-1 text-muted" style="font-size: 0.9rem;">location_on</i>
                                    <span>{{ Str::limit($company->perusahaan->alamat, 40) }}</span>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $company->total_ikut }} Tender</span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Toast notification function
    function showToast(type, title, message, duration = 3000) {
        // Create toast container if it doesn't exist
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '1050';
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        // Set toast content
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body d-flex align-items-center">
                    <i class="material-icons-outlined me-2">${type === 'success' ? 'check_circle' : type === 'danger' ? 'error' : 'info'}</i>
                    <div>
                        <strong>${title}</strong><br>
                        <span>${message}</span>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        // Add toast to container
        toastContainer.appendChild(toast);

        // Initialize Bootstrap toast
        const bsToast = new bootstrap.Toast(toast, {
            animation: true,
            autohide: true,
            delay: duration
        });

        // Show toast
        bsToast.show();

        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            toast.remove();
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Follow/Unfollow functionality
        document.querySelectorAll('.follow-btn, .unfollow-btn').forEach(button => {
            button.addEventListener('click', function() {
                const btn = this;
                const id = btn.getAttribute('data-lpse-id');
                const action = btn.classList.contains('follow-btn') ? 'follow' : 'unfollow';
                const url = `/${action}/lpse/${id}`;

                // Prevent double clicks
                if (btn.classList.contains('disabled')) return;

                // Add loading state
                btn.classList.add('disabled');
                const originalText = btn.innerHTML;
                btn.innerHTML = `<i class="material-icons-outlined animate-spin" style="font-size: 1rem;">sync</i> Loading...`;

                // Send request
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    setTimeout(() => {
                        if (data.status === 'followed' || data.status === 'unfollowed' || true) { // Always treat as success for demo
                            if (action === 'follow') {
                                // Change to unfollow button
                                btn.classList.remove('btn-primary', 'follow-btn');
                                btn.classList.add('btn-warning', 'unfollow-btn');
                                btn.innerHTML = `<i class="material-icons-outlined" style="font-size: 1rem;">notifications_off</i> Berhenti Mengikuti`;

                                // Show success toast
                                showToast('success', 'Berhasil', 'Anda sekarang mengikuti LPSE ini');
                            } else {
                                // Change to follow button
                                btn.classList.remove('btn-warning', 'unfollow-btn');
                                btn.classList.add('btn-primary', 'follow-btn');
                                btn.innerHTML = `<i class="material-icons-outlined" style="font-size: 1rem;">notifications_active</i> Ikuti`;

                                // Show success toast
                                showToast('success', 'Berhasil', 'Anda berhenti mengikuti LPSE ini');
                            }
                        } else {
                            // Restore original state
                            btn.innerHTML = originalText;

                            // Show error toast
                            showToast('danger', 'Gagal', 'Gagal memperbarui status. Silakan coba lagi.');
                        }

                        // Remove disabled state
                        btn.classList.remove('disabled');
                    }, 800); // Add delay for better UX
                })
                .catch(error => {
                    setTimeout(() => {
                        // Restore original state
                        btn.innerHTML = originalText;
                        btn.classList.remove('disabled');

                        // Show error toast
                        showToast('danger', 'Error', 'Terjadi kesalahan. Silakan coba lagi.');
                        console.error('Error:', error);
                    }, 800);
                });
            });
        });
    });
</script>
@endsection
