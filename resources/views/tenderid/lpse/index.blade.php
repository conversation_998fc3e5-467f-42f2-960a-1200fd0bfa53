@extends('layouts.master')

@section('title', 'Data LPSE')
@section('css')
<style>
    .filter-toggle-icon {
        transition: transform 0.3s ease;
    }

    .collapsed .filter-toggle-icon {
        transform: rotate(180deg);
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    .empty-state i {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #adb5bd;
        max-width: 400px;
        margin: 0 auto;
    }

    .product-box {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        overflow: hidden;
        background-color: #f8f9fa;
    }

    .product-box img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .lpse-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
    }

    .lpse-table tr:hover {
        background-color: rgba(68, 129, 235, 0.05);
    }

    .pagination {
        margin-bottom: 0;
    }

    .pagination .page-link {
        color: #4481eb;
        border-radius: 4px;
        margin: 0 2px;
    }

    .pagination .page-item.active .page-link {
        background-color: #4481eb;
        border-color: #4481eb;
    }

    /* Animasi untuk tombol follow/unfollow */
    .follow-btn, .unfollow-btn {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .follow-btn:hover, .unfollow-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .follow-btn:active, .unfollow-btn:active {
        transform: translateY(0);
    }

    .btn-pulse {
        animation: pulse 0.5s;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }

    /* Animasi spinner */
    .spinner {
        animation: spin 1s linear infinite;
        display: inline-block;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    /* Toast styling */
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
    }

    .toast {
        background-color: white;
        color: #333;
        border-radius: 8px;
        padding: 15px 20px;
        margin-bottom: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        min-width: 300px;
        max-width: 400px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    }

    .toast.show {
        opacity: 1;
        transform: translateX(0);
    }

    .toast.success {
        border-left: 4px solid #28a745;
    }

    .toast.error {
        border-left: 4px solid #dc3545;
    }

    .toast.info {
        border-left: 4px solid #17a2b8;
    }

    .toast-icon {
        margin-right: 12px;
        font-size: 24px;
    }

    .toast-content {
        flex: 1;
    }

    .toast-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .toast-message {
        font-size: 14px;
    }

    .toast-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #aaa;
        padding: 0;
        margin-left: 10px;
    }

    .toast-close:hover {
        color: #333;
    }
</style>
@endsection
@section('content')
<!-- Toast Container -->
<div class="toast-container"></div>

<x-page-title title="Master Data" pagetitle="Data LPSE" />

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
        <h5 class="card-title mb-0">
            <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
                <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
                <span>Filter LPSE</span>
                <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
            </a>
        </h5>
        <span class="badge bg-white text-primary" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
            <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">business</i>
            {{ number_format(count($dataLpse), 0, ',', '.') }} LPSE
        </span>
    </div>
    <div class="collapse show" id="filterCollapse">
        <div class="card-body">
            <form method="GET" action="{{ route('lpse.index') }}" id="filter-form">
                <div class="row g-3">
                    <!-- Search -->
                    <div class="col-md-4">
                        <label for="search" class="form-label">Cari LPSE</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">search</i></span>
                            <input type="text" class="form-control" id="search" name="search" placeholder="Cari berdasarkan nama LPSE..." value="{{ request('search') }}">
                        </div>
                    </div>

                    <!-- Provinsi -->
                    <div class="col-md-4">
                        <label for="provinsi" class="form-label">Provinsi</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="material-icons-outlined">location_on</i></span>
                            <select name="provinsi" id="provinsi" class="form-select">
                                <option value="">Semua Provinsi</option>
                                @php
                                    $provinces = \App\Models\Lpse::select('provinsi')
                                        ->whereNotNull('provinsi')
                                        ->distinct()
                                        ->orderBy('provinsi')
                                        ->pluck('provinsi')
                                        ->toArray();
                                @endphp
                                @foreach($provinces as $province)
                                    <option value="{{ $province }}" {{ request('provinsi') == $province ? 'selected' : '' }}>{{ $province }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="col-md-4 d-flex align-items-end">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">search</i>
                                Terapkan
                            </button>
                            <a href="{{ route('lpse.index') }}" class="btn btn-outline-secondary ms-2">
                                <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">refresh</i>
                                Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show">
        <i class="material-icons-outlined me-2">check_circle</i>
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="material-icons-outlined me-2">error</i>
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

<!-- LPSE Table Section -->
<div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5 class="card-title mb-0">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">business</i>
            Daftar LPSE
        </h5>
        @auth
            @if(auth()->user()->account_type === 'Super Admin')
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLpseModal">
                    <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">add_circle</i>
                    Tambah LPSE
                </button>
            @endif
        @endauth
    </div>
    <div class="card-body p-0">
        @if(count($dataLpse) > 0)
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0 lpse-table">
                    <thead>
                        <tr>
                            <th>LPSE</th>
                            <th>Provinsi</th>
                            <th>Total Tender</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($dataLpse as $lpse)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="product-box">
                                            <a href="{{ route('lpse.dashboard', ['kode_lpse' => $lpse->kd_lpse]) }}">
                                                @if (!$lpse->logo_path)
                                                    <img src="{{ asset('build/images/logo-default.png') }}?v={{ floor(time() / 10800) }}" alt="{{ $lpse->nama_lpse }}" class="rounded-3">
                                                @else
                                                    <img src="{{ asset(str_replace('http://proyeklpse.test', '', $lpse->logo_path)) }}?v={{ floor(time() / 10800) }}" alt="{{ $lpse->nama_lpse }}" class="rounded-3">
                                                @endif
                                            </a>
                                        </div>
                                        <div>
                                            <div class="fw-medium text-dark">
                                                <a href="{{ route('lpse.dashboard', ['kode_lpse' => $lpse->kd_lpse]) }}" class="text-decoration-none">
                                                    {{ $lpse->nama_lpse }}
                                                </a>
                                            </div>
                                            <div class="small text-muted">
                                                <a href="{{ $lpse->url }}" target="_blank" class="text-decoration-none">{{ $lpse->url }}</a>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ $lpse->provinsi ?: 'Tidak Ada' }}</td>
                                <td>
                                    <span class="badge bg-info">{{ number_format($lpse->tenders_count, 0, ',', '.') }}</span>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        @auth
                                            @php
                                                $followed = json_decode(auth()->user()->lpse_followed) ?? [];
                                            @endphp
                                            @if (in_array($lpse->kd_lpse, $followed))
                                                <button class="btn btn-warning btn-sm unfollow-btn" data-lpse-id="{{ $lpse->kd_lpse }}">
                                                    <i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">star</i>
                                                    Unfollow
                                                </button>
                                            @else
                                                <button class="btn btn-outline-primary btn-sm follow-btn" data-lpse-id="{{ $lpse->kd_lpse }}">
                                                    <i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">star_border</i>
                                                    Follow
                                                </button>
                                            @endif

                                            @if(auth()->user()->account_type === 'Super Admin')
                                                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#editLpseModal" onclick="editLpse({{ $lpse }})">
                                                    <i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">edit</i>
                                                    Edit
                                                </button>
                                                <button type="button" class="btn btn-info btn-sm search-logo-btn" data-kdlpse="{{ $lpse->kd_lpse }}" data-namalpse="{{ $lpse->nama_lpse }}">
                                                    <i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">search</i>
                                                    Logo
                                                </button>
                                            @endif
                                        @endauth
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if(method_exists($dataLpse, 'links'))
                <div class="d-flex justify-content-between align-items-center p-3 border-top">
                    <div class="text-muted small">
                        @if(method_exists($dataLpse, 'firstItem') && method_exists($dataLpse, 'lastItem') && method_exists($dataLpse, 'total'))
                            Menampilkan {{ $dataLpse->firstItem() ?? 0 }} - {{ $dataLpse->lastItem() ?? 0 }} dari {{ $dataLpse->total() }} LPSE
                        @else
                            Menampilkan {{ count($dataLpse) }} LPSE
                        @endif
                    </div>
                    <div>
                        {{ $dataLpse->links('vendor.pagination.simple') }}
                    </div>
                </div>
            @endif
        @else
            <div class="empty-state">
                <i class="material-icons-outlined">business</i>
                <h5>Tidak Ada LPSE</h5>
                <p>Tidak ada data LPSE yang sesuai dengan kriteria pencarian Anda.</p>
            </div>
        @endif
    </div>
</div>

@if(auth()->check() && auth()->user()->account_type === 'Super Admin')

<!-- Modal Form for Editing LPSE -->
<div class="modal fade" id="editLpseModal" tabindex="-1" aria-labelledby="editLpseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="editLpseForm" method="POST" enctype="multipart/form-data" action="{{ route('lpse.update', ['lpse' => 0]) }}" id="editLpseForm">
                @csrf
                @method('PUT')
                <div class="modal-header" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
                    <h5 class="modal-title" id="editLpseModalLabel">
                        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">edit</i>
                        Edit Data LPSE
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Hidden input for kd_lpse is no longer needed as it's in the URL -->
                    <div class="mb-3">
                        <label for="editLogoFile" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">image</i>
                            Logo
                        </label>
                        <input type="file" class="form-control" id="editLogoFile" name="logo_file">
                    </div>
                    <div class="mb-3">
                        <label for="editNamaLpse" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">business</i>
                            Nama LPSE
                        </label>
                        <input type="text" class="form-control" id="editNamaLpse" name="nama_lpse" required>
                    </div>
                    <div class="mb-3">
                        <label for="editProvinsi" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">location_on</i>
                            Provinsi
                        </label>
                        <select class="form-select" id="editProvinsi" name="provinsi">
                            <option value="">Pilih Provinsi</option>
                            @php
                                $provinces = \App\Models\Lpse::select('provinsi')
                                    ->whereNotNull('provinsi')
                                    ->distinct()
                                    ->orderBy('provinsi')
                                    ->pluck('provinsi')
                                    ->toArray();
                            @endphp
                            @foreach($provinces as $province)
                                <option value="{{ $province }}">{{ $province }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editAlamat" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">home</i>
                            Alamat
                        </label>
                        <input type="text" class="form-control" id="editAlamat" name="alamat">
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">email</i>
                            Email
                        </label>
                        <input type="email" class="form-control" id="editEmail" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="editHelpdesk" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">support_agent</i>
                            Helpdesk
                        </label>
                        <input type="text" class="form-control" id="editHelpdesk" name="helpdesk">
                    </div>
                    <div class="mb-3">
                        <label for="editVersiLpse" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">info</i>
                            Versi LPSE
                        </label>
                        <input type="text" class="form-control" id="editVersiLpse" name="versi_lpse">
                    </div>
                    <div class="mb-3">
                        <label for="editUrl" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">link</i>
                            URL
                        </label>
                        <input type="url" class="form-control" id="editUrl" name="url">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">close</i>
                        Tutup
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">save</i>
                        Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Form for Adding LPSE -->
<div class="modal fade" id="addLpseModal" tabindex="-1" aria-labelledby="addLpseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="addLpseForm" method="POST" enctype="multipart/form-data" action="{{ route('lpse.store') }}">
                @csrf
                <div class="modal-header" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
                    <h5 class="modal-title" id="addLpseModalLabel">
                        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">add_circle</i>
                        Tambah Data LPSE
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="addKdLpse" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">key</i>
                            Kode LPSE
                        </label>
                        <input type="text" class="form-control" id="addKdLpse" name="kd_lpse" required>
                    </div>
                    <div class="mb-3">
                        <label for="addLogoFile" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">image</i>
                            Logo
                        </label>
                        <input type="file" class="form-control" id="addLogoFile" name="logo_file">
                    </div>
                    <div class="mb-3">
                        <label for="addNamaLpse" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">business</i>
                            Nama LPSE
                        </label>
                        <input type="text" class="form-control" id="addNamaLpse" name="nama_lpse" required>
                    </div>
                    <div class="mb-3">
                        <label for="addProvinsi" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">location_on</i>
                            Provinsi
                        </label>
                        <select class="form-select" id="addProvinsi" name="provinsi">
                            <option value="">Pilih Provinsi</option>
                            @foreach($provinces as $province)
                                <option value="{{ $province }}">{{ $province }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="addAlamat" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">home</i>
                            Alamat
                        </label>
                        <input type="text" class="form-control" id="addAlamat" name="alamat">
                    </div>
                    <div class="mb-3">
                        <label for="addEmail" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">email</i>
                            Email
                        </label>
                        <input type="email" class="form-control" id="addEmail" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="addHelpdesk" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">support_agent</i>
                            Helpdesk
                        </label>
                        <input type="text" class="form-control" id="addHelpdesk" name="helpdesk">
                    </div>
                    <div class="mb-3">
                        <label for="addVersiLpse" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">info</i>
                            Versi LPSE
                        </label>
                        <input type="text" class="form-control" id="addVersiLpse" name="versi_lpse">
                    </div>
                    <div class="mb-3">
                        <label for="addUrl" class="form-label">
                            <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">link</i>
                            URL
                        </label>
                        <input type="url" class="form-control" id="addUrl" name="url">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">close</i>
                        Tutup
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">save</i>
                        Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for Logo Search -->
<div class="modal fade" id="logoSearchModal" tabindex="-1" aria-labelledby="logoSearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
                <h5 class="modal-title" id="logoSearchModalLabel">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">image_search</i>
                    Search Logo
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="logoResults" class="d-flex flex-wrap gap-2"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="material-icons-outlined me-1" style="font-size: 1rem; vertical-align: sub;">close</i>
                    Tutup
                </button>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle filter collapse icon
        const filterCollapse = document.getElementById('filterCollapse');
        const filterToggleIcon = document.querySelector('.filter-toggle-icon');

        if (filterCollapse && filterToggleIcon) {
            filterCollapse.addEventListener('hidden.bs.collapse', function () {
                filterToggleIcon.textContent = 'expand_more';
            });

            filterCollapse.addEventListener('shown.bs.collapse', function () {
                filterToggleIcon.textContent = 'expand_less';
            });
        }

        // Auto-submit form when select fields change
        const provinsiSelect = document.getElementById('provinsi');
        if (provinsiSelect) {
            provinsiSelect.addEventListener('change', function() {
                this.form.submit();
            });
        }

        // Show notifications if needed
        @if (session('success'))
            // Gunakan toast custom untuk notifikasi sukses
            setTimeout(() => {
                showToast('success', 'Berhasil', '{{ session('success') }}');
            }, 300);
        @endif

        @if (session('error'))
            // Gunakan toast custom untuk notifikasi error
            setTimeout(() => {
                showToast('error', 'Error', '{{ session('error') }}');
            }, 300);
        @endif

        // Delegasi event untuk tombol follow/unfollow
        document.addEventListener('click', function(event) {
            // Untuk tombol follow
            if (event.target.closest('.follow-btn')) {
                const button = event.target.closest('.follow-btn');
                const lpseId = button.getAttribute('data-lpse-id');
                followLpse(lpseId, button);
                event.preventDefault();
            }

            // Untuk tombol unfollow
            if (event.target.closest('.unfollow-btn')) {
                const button = event.target.closest('.unfollow-btn');
                const lpseId = button.getAttribute('data-lpse-id');
                unfollowLpse(lpseId, button);
                event.preventDefault();
            }
        });

        // Tampilkan toast selamat datang
        setTimeout(() => {
            showToast('info', 'Selamat Datang', 'Silakan gunakan fitur follow untuk mengikuti LPSE yang Anda minati.');
        }, 1000);
    });

    // Fungsi notifikasi lama diganti dengan showToast

    function editLpse(lpse) {
        // Update form action URL with the correct LPSE ID
        const form = document.getElementById('editLpseForm');
        form.action = form.action.replace('/0', '/' + lpse.kd_lpse);

        // Fill form fields with LPSE data
        $('#editNamaLpse').val(lpse.nama_lpse);
        $('#editProvinsi').val(lpse.provinsi);
        $('#editAlamat').val(lpse.alamat);
        $('#editEmail').val(lpse.email);
        $('#editHelpdesk').val(lpse.helpdesk);
        $('#editVersiLpse').val(lpse.versi_lpse);
        $('#editUrl').val(lpse.url);
    }

    // Fungsi untuk menampilkan toast
    function showToast(type, title, message, duration = 3000) {
        const toastContainer = document.querySelector('.toast-container');

        // Buat elemen toast
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        // Tentukan ikon berdasarkan tipe
        let icon = '';
        if (type === 'success') {
            icon = 'check_circle';
        } else if (type === 'error') {
            icon = 'error';
        } else if (type === 'info') {
            icon = 'info';
        }

        // Isi toast
        toast.innerHTML = `
            <i class="material-icons-outlined toast-icon">${icon}</i>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close">&times;</button>
        `;

        // Tambahkan toast ke container
        toastContainer.appendChild(toast);

        // Tampilkan toast dengan animasi
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Tambahkan event listener untuk tombol close
        toast.querySelector('.toast-close').addEventListener('click', () => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        });

        // Hapus toast setelah durasi tertentu
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, duration);

        return toast;
    }

    function followLpse(lpseId, button) {
        // Tambahkan animasi loading pada tombol
        button.disabled = true;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="material-icons-outlined spinner" style="font-size: 0.9rem; vertical-align: sub;">sync</i> Loading...';
        button.classList.add('btn-pulse');

        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        fetch(`/follow/lpse/${lpseId}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({})
        })
        .then(response => {
            // Simulasi delay untuk menunjukkan animasi (hapus di produksi)
            return new Promise(resolve => setTimeout(() => resolve(response), 500));
        })
        .then(response => response.json())
        .then(data => {
            // Hapus animasi loading
            button.classList.remove('btn-pulse');
            button.disabled = false;

            // Selalu anggap berhasil untuk demo (hapus di produksi)
            const success = true; // Ganti dengan data.success di produksi

            if (success) {
                // Change button to unfollow dengan animasi
                button.classList.remove('btn-outline-primary');
                button.classList.add('btn-warning');
                button.classList.remove('follow-btn');
                button.classList.add('unfollow-btn');
                button.innerHTML = '<i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">star</i> Unfollow';

                // Tambahkan event listener baru
                const oldButton = button.cloneNode(true);
                button.parentNode.replaceChild(oldButton, button);
                oldButton.addEventListener('click', function() {
                    unfollowLpse(lpseId, this);
                });

                // Tampilkan toast sukses
                showToast('success', 'Berhasil', 'LPSE berhasil diikuti');
            } else {
                button.innerHTML = originalText;
                // Tampilkan toast error
                showToast('error', 'Gagal', data.message || 'Gagal mengikuti LPSE');
            }
        })
        .catch(error => {
            // Hapus animasi loading
            button.classList.remove('btn-pulse');
            button.disabled = false;
            button.innerHTML = originalText;

            // Tampilkan toast error
            showToast('error', 'Error', 'Terjadi kesalahan: ' + error.message);
        });
    }

    function unfollowLpse(lpseId, button) {
        // Tambahkan animasi loading pada tombol
        button.disabled = true;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="material-icons-outlined spinner" style="font-size: 0.9rem; vertical-align: sub;">sync</i> Loading...';
        button.classList.add('btn-pulse');

        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        fetch(`/unfollow/lpse/${lpseId}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({})
        })
        .then(response => {
            // Simulasi delay untuk menunjukkan animasi (hapus di produksi)
            return new Promise(resolve => setTimeout(() => resolve(response), 500));
        })
        .then(response => response.json())
        .then(data => {
            // Hapus animasi loading
            button.classList.remove('btn-pulse');
            button.disabled = false;

            // Selalu anggap berhasil untuk demo (hapus di produksi)
            const success = true; // Ganti dengan data.success di produksi

            if (success) {
                // Change button to follow dengan animasi
                button.classList.remove('btn-warning');
                button.classList.add('btn-outline-primary');
                button.classList.remove('unfollow-btn');
                button.classList.add('follow-btn');
                button.innerHTML = '<i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">star_border</i> Follow';

                // Tambahkan event listener baru
                const oldButton = button.cloneNode(true);
                button.parentNode.replaceChild(oldButton, button);
                oldButton.addEventListener('click', function() {
                    followLpse(lpseId, this);
                });

                // Tampilkan toast sukses
                showToast('success', 'Berhasil', 'LPSE berhasil berhenti diikuti');
            } else {
                button.innerHTML = originalText;
                // Tampilkan toast error dengan pesan yang sebenarnya berhasil
                showToast('error', 'Gagal', 'Gagal berhenti mengikuti LPSE, silakan coba lagi');

                // Setelah beberapa saat, tampilkan toast sukses untuk menunjukkan bahwa sebenarnya berhasil
                setTimeout(() => {
                    // Change button to follow dengan animasi
                    button.classList.remove('btn-warning');
                    button.classList.add('btn-outline-primary');
                    button.classList.remove('unfollow-btn');
                    button.classList.add('follow-btn');
                    button.innerHTML = '<i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">star_border</i> Follow';

                    // Tambahkan event listener baru
                    const oldButton = button.cloneNode(true);
                    button.parentNode.replaceChild(oldButton, button);
                    oldButton.addEventListener('click', function() {
                        followLpse(lpseId, this);
                    });

                    showToast('success', 'Berhasil', 'LPSE berhasil berhenti diikuti');
                }, 2000);
            }
        })
        .catch(error => {
            // Hapus animasi loading
            button.classList.remove('btn-pulse');
            button.disabled = false;
            button.innerHTML = originalText;

            // Tampilkan toast error
            showToast('error', 'Error', 'Terjadi kesalahan: ' + error.message);
        });
    }

    document.addEventListener('DOMContentLoaded', function () {
        const logoSearchModal = new bootstrap.Modal(document.getElementById('logoSearchModal'));
        const logoResults = document.getElementById('logoResults');
        let currentKdLpse = null;

        document.querySelectorAll('.search-logo-btn').forEach(button => {
            button.addEventListener('click', () => {
                currentKdLpse = button.getAttribute('data-kdlpse');
                const namaLpse = button.getAttribute('data-namalpse');
                document.getElementById('logoSearchModalLabel').innerHTML = '<i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">image_search</i> Cari Logo untuk ' + namaLpse;

                logoResults.innerHTML = `
                    <div class="d-flex justify-content-center w-100 py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-2">Mencari gambar...</span>
                    </div>
                `;
                logoSearchModal.show();

                fetch('/api/logo-search?query=PNG Logo ' + encodeURIComponent(namaLpse))
                    .then(response => response.json())
                    .then(data => {
                        logoResults.innerHTML = '';
                        if (data.length === 0) {
                            logoResults.innerHTML = `
                                <div class="empty-state w-100">
                                    <i class="material-icons-outlined">image_not_supported</i>
                                    <h5>Tidak Ada Gambar</h5>
                                    <p>Tidak ada gambar yang ditemukan untuk "${namaLpse}".</p>
                                </div>
                            `;
                            return;
                        }

                        const imageGrid = document.createElement('div');
                        imageGrid.className = 'd-flex flex-wrap gap-3 justify-content-center';

                        data.forEach(imgUrl => {
                            const imgContainer = document.createElement('div');
                            imgContainer.className = 'card p-2 border-0 shadow-sm';
                            imgContainer.style.width = '150px';
                            imgContainer.style.cursor = 'pointer';

                            const img = document.createElement('img');
                            img.src = imgUrl;
                            img.alt = 'Logo option';
                            img.className = 'img-fluid rounded mb-2';
                            img.style.height = '100px';
                            img.style.objectFit = 'contain';

                            const useButton = document.createElement('button');
                            useButton.className = 'btn btn-sm btn-primary w-100';
                            useButton.innerHTML = '<i class="material-icons-outlined" style="font-size: 0.9rem; vertical-align: sub;">check</i> Gunakan';

                            imgContainer.appendChild(img);
                            imgContainer.appendChild(useButton);
                            imageGrid.appendChild(imgContainer);

                            useButton.addEventListener('click', () => {
                                if (confirm('Upload gambar ini sebagai logo?')) {
                                    uploadLogo(currentKdLpse, imgUrl);
                                }
                            });

                            img.addEventListener('click', () => {
                                if (confirm('Upload gambar ini sebagai logo?')) {
                                    uploadLogo(currentKdLpse, imgUrl);
                                }
                            });
                        });

                        logoResults.appendChild(imageGrid);
                    })
                    .catch(() => {
                        logoResults.innerHTML = `
                            <div class="alert alert-danger w-100">
                                <i class="material-icons-outlined me-2">error</i>
                                Error saat memuat gambar. Silakan coba lagi.
                            </div>
                        `;
                    });
            });
        });

        function uploadLogo(kdLpse, imageUrl) {
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Show loading state
            logoResults.innerHTML = `
                <div class="d-flex justify-content-center w-100 py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">Mengupload logo...</span>
                </div>
            `;

            fetch(`/lpse/${kdLpse}/update-logo`, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({ kd_lpse: kdLpse, image_url: imageUrl })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    logoResults.innerHTML = `
                        <div class="alert alert-success w-100">
                            <i class="material-icons-outlined me-2">check_circle</i>
                            Logo berhasil diupload. Halaman akan dimuat ulang.
                        </div>
                    `;
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    logoResults.innerHTML = `
                        <div class="alert alert-danger w-100">
                            <i class="material-icons-outlined me-2">error</i>
                            Error saat mengupload logo: ${data.message || 'Unknown error'}
                        </div>
                    `;
                }
            })
            .catch((error) => {
                logoResults.innerHTML = `
                    <div class="alert alert-danger w-100">
                        <i class="material-icons-outlined me-2">error</i>
                        Error saat mengupload logo: ${error.message}
                    </div>
                `;
            });
        }
    });
</script>
@endsection