@extends('layouts.master')

@section('title', 'Tender Hari Ini')

@section('css')
<style>
    .table tr.bg-warning-important td {
        background-color: #ffcc00;
        /* Menggunakan warna kuning yang lebih lembut */
    }

    .product-box img {
        transition: transform 0.3s ease;
    }

    .product-box img:hover {
        transform: scale(1.1);
        /* Animasi zoom ketika hover gambar */
    }

    .product-title {
        font-weight: bold;
        color: #007bff;
    }

    .product-title:hover {
        text-decoration: underline;
    }

    .product-category a {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        color: #007bff;
    }

    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 8px;
    }

    .btn-filter {
        background-color: #007bff;
        color: white;
    }

    .btn-filter:hover {
        background-color: #0056b3;
    }

    /* Pagination Custom */
    .pagination .page-link {
        color: #007bff;
    }

    .pagination .page-link:hover {
        background-color: #f1f1f1;
    }

    .pagination .active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }
</style>
@endsection
@section('content')
<x-page-title title="Tender" pagetitle="Tender Hari Ini" />

<!-- Tender Count Section -->
<div id="tender-count"
    class="product-count d-flex align-items-center gap-3 gap-lg-4 mb-4 fw-medium flex-wrap font-text1">
</div>

<div class="row g-3">
    <div class="col-auto">
        <div class="position-relative">
            <form method="GET" action="{{ route('tender_lpse.hari_ini') }}" class="mb-4">
                <input class="form-control px-5" type="search" name="search" placeholder="Cari Nama Tender"
                    value="{{ request('search') }}">
                <span
                    class="material-icons-outlined position-absolute ms-3 translate-middle-y start-0 top-50 fs-5">search</span>
            </form>
        </div>
    </div>

</div><!--end row-->

<div class="card mt-4">
    <div class="card-body">
        <div class="product-table">
            <div class="table-responsive white-space-nowrap">
                @if($tenders->isEmpty())
                    <p>Tidak ada tender yang ditemukan.</p>
                @else
                                <table class="table align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Kode Tender</th>
                                            <th>Nama Paket</th>
                                            <th>Kategori</th>
                                            <th>Tahapan Tender</th>
                                            <th>Pagu</th>
                                            <th>HPS</th>
                                            <th>Tanggal Tayang</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($tenders as $tender)
                                                                <tr class="{{ $tender->status_tender !== 'Aktif' ? 'bg-warning-important' : '' }}">
                                                                    <td><a href="#">#{{ $tender->kode_tender }}</a></td>
                                                                    <td>
                                                                        <div class="d-flex align-items-center gap-3">
                                                                            <div class="product-box">
                                                                                <img src="{{ asset(str_replace('http://proyeklpse.test', '', $tender->lpse->logo_path)) }}"
                                                                                    alt="Logo" width="40" class="rounded-3">
                                                                            </div>
                                                                            <div class="product-info">
                                                                                <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}"
                                                                                    class="product-title">{{ $tender->nama_paket }}</a>
                                                                                <p class="mb-0 product-category"><a
                                                                                        href="{{ route('lpse.dashboard', ['kode_lpse' => $tender->lpse->kd_lpse]) }}">{{ $tender->lpse->nama_lpse }}</a>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                    <td>{{ $tender->kategori_pekerjaan }}</td>
                                                                    <td>{{ $tender->tahap_tender }}</td>
                                                                    <td>{{ format_number_short($tender->pagu) }}</td>
                                                                    <td>{{ format_number_short($tender->hps) }}</td>
                                                                    <td>{{ format_date_indonesian($tender->tanggal_paket_tayang) }}</td>
                                                                    <td>
                                                                        @auth
                                                                                                        @php
                                                                                                            $followed = json_decode(auth()->user()->tender_followed) ?? [];
                                                                                                          @endphp
                                                                                                        @if (in_array($tender->kode_tender, $followed))
                                                                                                            <button class="btn btn-warning btn-sm unfollow-btn"
                                                                                                                data-tender-id="{{ $tender->kode_tender }}">Unfollow</button>
                                                                                                        @else
                                                                                                            <button class="btn btn-primary btn-sm follow-btn"
                                                                                                                data-tender-id="{{ $tender->kode_tender }}">Follow</button>
                                                                                                        @endif
                                                                        @endauth
                                                                    </td>
                                                                </tr>
                                        @endforeach

                                    </tbody>
                                </table>
                                <!-- Link Pagination -->
                                <div class="d-flex justify-content-center">
                                    {{ $tenders->links('vendor.pagination.costum') }}
                                </div>
                @endif
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    document.addEventListener("DOMContentLoaded", function () {
        fetch("{{ route('tender.lpse.count') }}")
            .then(response => response.json())
            .then(data => {
                const tenderCountDiv = document.getElementById('tender-count');
                tenderCountDiv.innerHTML = `  
                    <a href="{{URL::route('tender_lpse.index')}}"><span class="me-1">Semua</span><span class="text-secondary">(${data.total})</span></a>  
                    <a href="{{URL::route('tender_lpse.tenderSelesai')}}"><span class="me-1">Selesai</span><span class="text-secondary">(${data.completed})</span></a>  
                    <a href="{{URL::route('tender_lpse.tenderGagal')}}"><span class="me-1">Gagal/Batal</span><span class="text-secondary">(${data.failed})</span></a>  
                    <a href="{{URL::route('tender_lpse.tenderOnProcess')}}"><span class="me-1"></span>On Process<span class="text-secondary">(${data.ongoing})</span></a>  
                `;
            })
            .catch(error => console.error('Error fetching tender counts:', error));
    });  
</script>
<script>
    $(document).ready(function () {
        $('.follow-btn, .unfollow-btn').click(function () {
            var btn = $(this);
            var id = btn.data('tender-id') || btn.data('lpse-id');
            var type = btn.data('tender-id') ? 'tender' : 'lpse';
            var action = btn.hasClass('follow-btn') ? 'follow' : 'unfollow';
            var url = `/${action}/${type}/${id}`;

            $.post(url, {
                _token: '{{ csrf_token() }}'
            }, function (response) {
                if (response.status === 'followed' || response.status === 'unfollowed') {
                    if (action === 'follow') {
                        btn.removeClass('btn-primary follow-btn').addClass('btn-warning unfollow-btn');
                        btn.text('Unfollow');
                    } else {
                        btn.removeClass('btn-warning unfollow-btn').addClass('btn-primary follow-btn');
                        btn.text('Follow');
                    }
                }
            });
        });
    });
</script>
@endsection