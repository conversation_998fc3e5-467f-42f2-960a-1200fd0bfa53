@extends('layouts.master')

@section('title', '<PERSON><PERSON><PERSON>')
@section('css')
  <!-- Select2 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

  <style>
    /* Card Styling */
    .card {
      border: none;
      border-radius: 12px;
      box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      margin-bottom: 24px;
    }

    .card:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      border-radius: 12px 12px 0 0 !important;
      padding: 18px 24px;
    }

    .card-title {
      font-weight: 600;
      color: #344767;
      margin-bottom: 0;
      font-size: 1.1rem;
    }

    .card-body {
      padding: 24px;
    }

    /* Form Controls */
    .form-control, .form-select {
      border-radius: 8px;
      padding: 10px 15px;
      border: 1px solid #e9ecef;
      font-size: 0.9rem;
      transition: all 0.2s ease;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    }

    .form-control:focus, .form-select:focus {
      border-color: #4481eb;
      box-shadow: 0 0 0 0.25rem rgba(68, 129, 235, 0.1);
    }

    .form-label {
      font-weight: 500;
      font-size: 0.9rem;
      color: #495057;
      margin-bottom: 8px;
    }

    .form-text {
      font-size: 0.75rem;
      color: #6c757d;
    }

    .input-group-text {
      background-color: #f8f9fa;
      border-color: #e9ecef;
      color: #6c757d;
    }

    /* Table Styling */
    .table {
      margin-bottom: 0;
      table-layout: auto;
      width: 100%;
    }

    .table tbody td {
      padding: 16px;
      vertical-align: middle;
      border-bottom: 1px solid #f8f9fa;
      font-size: 0.9rem;
      word-wrap: break-word;
      white-space: normal;
    }

    .table tbody tr:last-child td {
      border-bottom: none;
    }

    .table tbody tr {
      transition: all 0.2s ease;
    }

    .table tbody tr:hover {
      background-color: #f8f9fa;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
    }

    /* Product Box */
    .product-box {
      width: 50px;
      height: 50px;
      border-radius: 10px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .product-box img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      transition: transform 0.3s ease;
    }

    .product-box img:hover {
      transform: scale(1.15);
    }

    .product-info {
      flex: 1;
    }

    .product-title {
      font-weight: 600;
      color: #344767;
      margin-bottom: 5px;
      display: block;
      text-decoration: none;
    }

    .product-title:hover {
      color: #4481eb;
      text-decoration: underline;
    }

    .product-category {
      color: #6c757d;
      font-size: 0.85rem;
      margin-bottom: 0;
    }

    /* Badge Styling */
    .badge {
      padding: 6px 12px;
      font-weight: 500;
      font-size: 0.75rem;
      border-radius: 30px;
    }

    .badge.tenderid_header {
      background: linear-gradient(90deg, #4caf50, #2e7d32);
      color: white;
    }

    /* Follow Button Animation */
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.15); }
      100% { transform: scale(1); }
    }

    @keyframes rotate {
      0% { transform: rotate(0deg); }
      25% { transform: rotate(-15deg); }
      75% { transform: rotate(15deg); }
      100% { transform: rotate(0deg); }
    }

    .follow-btn:hover .follow-icon {
      animation: pulse 0.5s ease;
    }

    .unfollow-btn .follow-icon {
      color: #fff;
    }

    .unfollow-btn:hover .follow-icon {
      animation: rotate 0.5s ease;
    }

    /* Pagination Custom */
    .pagination {
      margin-bottom: 0;
      margin-top: 20px;
    }

    .pagination .page-link {
      color: #4481eb;
      border: none;
      padding: 10px 15px;
      margin: 0 5px;
      border-radius: 8px;
      font-size: 0.9rem;
      transition: all 0.2s ease;
    }

    .pagination .page-link:hover {
      background-color: #f8f9fa;
      color: #4481eb;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .pagination .active .page-link {
      background: linear-gradient(90deg, #4481eb, #04befe);
      color: white;
      box-shadow: 0 4px 10px rgba(68, 129, 235, 0.3);
    }

    /* Empty State */
    .empty-state {
      text-align: center;
      padding: 40px 20px;
    }

    .empty-state i {
      font-size: 48px;
      color: #e9ecef;
      margin-bottom: 15px;
    }

    .empty-state h5 {
      color: #6c757d;
      font-weight: 500;
      margin-bottom: 10px;
    }

    .empty-state p {
      color: #adb5bd;
      max-width: 400px;
      margin: 0 auto;
    }

    /* Filter toggle styling */
    .filter-toggle-icon {
      transition: transform 0.3s ease;
    }

    .collapsed .filter-toggle-icon {
      transform: rotate(180deg);
    }

    /* Compact view styling */
    .compact-view td {
      padding-top: 10px !important;
      padding-bottom: 10px !important;
    }

    /* Select2 customization */
    .select2-container .select2-selection--single {
      height: 42px !important;
      padding: 8px 12px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      background-color: #fff;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
      height: 42px;
    }

    .select2-dropdown {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
      background-color: #4481eb;
    }

    /* Status styling */
    .table tr.bg-warning-important td {
      background-color: #fff8e1;
      border-left: 3px solid #ffc107;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .spin-animation {
      animation: spin 1s linear infinite;
      display: inline-block;
    }
  </style>
@endsection

@section('content')
<!-- Tender Count Section -->
<div id="tender-count"
  class="product-count d-flex align-items-center gap-3 gap-lg-4 mb-4 fw-medium flex-wrap font-text1">
</div>

<!-- Search and Filter Section -->
<div class="card mb-4">
  <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
    <h5 class="card-title mb-0">
      <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
        <span>Filter Tender</span>
        <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
      </a>
    </h5>
    <span class="badge bg-white text-primary" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
      <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">gavel</i>
      @if(method_exists($tenders, 'total'))
        {{ number_format($tenders->total(), 0, ',', '.') }} Tender
      @else
        {{ number_format($tenders->count(), 0, ',', '.') }} Tender
      @endif
    </span>
  </div>
  <div class="collapse show" id="filterCollapse">
    <div class="card-body">
      <form method="GET" action="{{ route('tender_lpse.index') }}">
        <div class="row g-3">
          <!-- Pencarian -->
          <div class="col-md-4">
            <label for="search-input" class="form-label">Pencarian</label>
            <div class="input-group">
              <span class="input-group-text"><i class="material-icons-outlined">search</i></span>
              <input type="text" name="search" id="search-input" class="form-control"
                placeholder="Cari Nama Tender atau Kode Tender..." value="{{ request('search') }}">
            </div>
            <div class="form-text">Cari berdasarkan nama paket atau kode tender</div>
          </div>

          <!-- Filter Kategori (Dari database) -->
          <div class="col-md-3">
            <label for="kategori-select" class="form-label">Kategori Pekerjaan</label>
            <select class="form-select select2" id="kategori-select" name="kategori">
              <option value="">Semua Kategori</option>
              @foreach($kategori_pekerjaan as $kategori)
                <option value="{{ $kategori }}" {{ request('kategori') == $kategori ? 'selected' : '' }}>{{ $kategori }}</option>
              @endforeach
            </select>
            <div class="form-text">Pilih kategori pekerjaan</div>
          </div>

          <!-- Filter Status (Jika tersedia) -->
          <div class="col-md-3">
            <label for="status-select" class="form-label">Status Tender</label>
            <select class="form-select" id="status-select" name="status">
              <option value="">Semua Status</option>
              <option value="Aktif" {{ request('status') == 'Aktif' ? 'selected' : '' }}>Aktif</option>
              <option value="Tidak Aktif" {{ request('status') == 'Tidak Aktif' ? 'selected' : '' }}>Tidak Aktif</option>
            </select>
            <div class="form-text">Filter berdasarkan status tender</div>
          </div>

          <!-- Tombol Filter & Reset -->
          <div class="col-12 mt-4">
            <div class="d-flex justify-content-end gap-2">
              <button type="submit" class="btn btn-primary d-inline-flex align-items-center">
                <i class="material-icons-outlined me-2">filter_alt</i>
                <span>Terapkan Filter</span>
              </button>
              <a href="{{ route('tender_lpse.index') }}" class="btn btn-outline-secondary d-inline-flex align-items-center">
                <i class="material-icons-outlined me-2">refresh</i>
                <span>Reset Filter</span>
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Tender Table Section -->
<div class="card">
  <div class="card-header d-flex align-items-center justify-content-between">
    <h5 class="card-title mb-0">
      <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">gavel</i>
      Daftar Tender
    </h5>
    <div>
      <button class="btn btn-sm btn-outline-primary me-2 d-inline-flex align-items-center" id="btn-refresh-data">
        <i class="material-icons-outlined me-1">refresh</i>
        <span>Refresh Data</span>
      </button>
      <div class="dropdown d-inline-block">
        <button class="btn btn-sm btn-outline-secondary dropdown-toggle d-inline-flex align-items-center" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
          <i class="material-icons-outlined me-1">tune</i>
          <span>Tampilan</span>
        </button>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
          <li><a class="dropdown-item d-flex align-items-center" href="#" id="toggle-compact-view">
            <i class="material-icons-outlined me-2">view_compact</i>
            <span>Tampilan Kompak</span>
          </a></li>
          <li><a class="dropdown-item d-flex align-items-center" href="#" id="toggle-full-view">
            <i class="material-icons-outlined me-2">view_agenda</i>
            <span>Tampilan Lengkap</span>
          </a></li>
        </ul>
      </div>
    </div>
  </div>
  <div class="card-body p-0">
    <div class="product-table">
      <div class="table-responsive">
        <table class="table align-middle mb-0">
          <thead>
            <tr class="tenderid_header">
              <th>Kode Tender</th>
              <th>Nama Paket</th>
              <th>Kategori</th>
              <th>Tahapan Tender</th>
              <th>Pagu</th>
              <th>HPS</th>
              <th>Tanggal Tayang</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            @foreach($tenders as $tender)
            @php
              if ($tender->status_tender === 'Tender Sedang Berjalan') {
                $tender->status_tender = 'Aktif';
              }

              if ($tender->status_tender === 'Tender Ditutup') {
                $tender->status_tender = 'Tidak Aktif';
              }
            @endphp
            <tr class="{{ $tender->status_tender !== 'Aktif' ? 'bg-warning-important' : '' }}">
              <td><a
                href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}">#{{ $tender->kode_tender }}</a>
              </td>
              <td>
              <div class="d-flex align-items-center gap-3">
                <div class="product-box">
                <!-- LPSE Logo -->
                <img src="{{ asset(str_replace('http://proyeklpse.test', '', $tender->lpse->logo_path)) }}"
                  alt="Logo" width="40" class="rounded-3">
                </div>
                <div class="product-info">
                <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}"
                  class="product-title">{{ $tender->nama_paket }} <span class="badge bg-primary"> {{ $tender->metode_pemilihan }}</span> </a>
                <p class="mb-0 product-category"><a
                  href="{{ route('lpse.dashboard', ['kode_lpse' => $tender->lpse->kd_lpse]) }}">{{ $tender->lpse->nama_lpse }}</a>
                  @if(auth()->user()->account_type === 'Super Admin')
                   -  {{ $tender->updated_at }}
                  @endif
                </p>
                </div>
              </div>
              </td>
              <td>{{ $tender->kategori_pekerjaan }}</td>
              <td>{{ $tender->tahap_tender }}</td>
              <td>{{ format_number_short($tender->pagu) }}</td>
              <td>{{ format_number_short($tender->hps) }}</td>
              <td>{{ format_date_indonesian($tender->tanggal_paket_tayang) }}</td>
              <td>
              @auth
                @php
                  $followed = json_decode(auth()->user()->tender_followed) ?? [];
                @endphp
                @if (in_array($tender->kode_tender, $followed))
                  <button class="btn btn-warning btn-sm unfollow-btn d-flex align-items-center gap-1"
                    data-tender-id="{{ $tender->kode_tender }}">
                    <i class="material-icons-outlined follow-icon">star</i>
                    <span>Unfollow</span>
                  </button>
                @else
                  <button class="btn btn-outline-primary btn-sm follow-btn d-flex align-items-center gap-1"
                    data-tender-id="{{ $tender->kode_tender }}">
                    <i class="material-icons-outlined follow-icon">star_border</i>
                    <span>Follow</span>
                  </button>
                @endif
              @endauth
              </td>
            </tr>
      @endforeach
          </tbody>
        </table>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
          <div class="text-muted small">
            @if(method_exists($tenders, 'firstItem') && method_exists($tenders, 'lastItem') && method_exists($tenders, 'total'))
              Menampilkan {{ $tenders->firstItem() ?? 0 }} - {{ $tenders->lastItem() ?? 0 }} dari {{ $tenders->total() }} tender
            @else
              Menampilkan {{ $tenders->count() }} tender
            @endif
          </div>
          <div>
            @if(method_exists($tenders, 'links'))
              {{ $tenders->links('vendor.pagination.costum') }}
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Empty State (akan ditampilkan jika tidak ada data) -->
@if($tenders->count() == 0)
<div class="empty-state">
  <i class="material-icons-outlined">search_off</i>
  <h5>Tidak Ada Data Tender</h5>
  <p>Tidak ada data tender yang sesuai dengan filter yang Anda pilih. Silakan ubah filter atau coba kata kunci lain.</p>
</div>
@endif

@endsection

@section('scripts')
  <!-- Select2 JS -->
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

  <script>
    $(document).ready(function () {
      // Inisialisasi Select2
      $('#kategori-select').select2({
        width: '100%',
        placeholder: "Pilih Kategori Pekerjaan",
        allowClear: true
      });

      // Refresh button
      $('#btn-refresh-data').on('click', function(e) {
        e.preventDefault();

        // Tampilkan animasi loading
        $(this).html(`
          <i class="material-icons-outlined me-1 spin-animation">refresh</i>
          <span>Memuat...</span>
        `);
        $(this).prop('disabled', true);

        // Reload halaman
        setTimeout(function() {
          window.location.reload();
        }, 500);
      });

      // Toggle compact view
      $('#toggle-compact-view').on('click', function(e) {
        e.preventDefault();
        $('tr').addClass('compact-view');
        $('.product-category').hide();
        $('.table').addClass('table-sm');
        localStorage.setItem('tender_view_mode', 'compact');

        // Notifikasi
        showToast('Tampilan kompak diaktifkan');
      });

      // Toggle full view
      $('#toggle-full-view').on('click', function(e) {
        e.preventDefault();
        $('tr').removeClass('compact-view');
        $('.product-category').show();
        $('.table').removeClass('table-sm');
        localStorage.setItem('tender_view_mode', 'full');

        // Notifikasi
        showToast('Tampilan lengkap diaktifkan');
      });

      // Load saved view preferences
      loadViewPreferences();

      // Handle filter collapse toggle
      $('#filterCollapse').on('show.bs.collapse', function () {
        $('.filter-toggle-icon').text('expand_less');
        localStorage.setItem('tender_filter_collapsed', 'false');
      });

      $('#filterCollapse').on('hide.bs.collapse', function () {
        $('.filter-toggle-icon').text('expand_more');
        localStorage.setItem('tender_filter_collapsed', 'true');
      });

      // Load filter collapse state from localStorage
      if (localStorage.getItem('tender_filter_collapsed') === 'true') {
        $('#filterCollapse').collapse('hide');
      }

      // Follow/Unfollow functionality
      $('.follow-btn, .unfollow-btn').click(function () {
        var btn = $(this);
        var id = btn.data('tender-id') || btn.data('lpse-id');
        var type = btn.data('tender-id') ? 'tender' : 'lpse';
        var action = btn.hasClass('follow-btn') ? 'follow' : 'unfollow';
        var url = `/${action}/${type}/${id}`;

        // Simpan konten asli tombol
        var originalHtml = btn.html();

        // Tampilkan loading state dengan animasi pulse
        btn.html(`<div class="d-flex align-items-center gap-1"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> <span>Loading...</span></div>`);
        btn.prop('disabled', true);

        $.post(url, {
          _token: '{{ csrf_token() }}'
        }, function (response) {
          if (response.status === 'followed' || response.status === 'unfollowed') {
            if (action === 'follow') {
              // Animasi untuk follow
              btn.removeClass('btn-outline-primary follow-btn').addClass('btn-warning unfollow-btn');
              btn.html(`<i class="material-icons-outlined follow-icon">star</i><span>Unfollow</span>`);

              // Tambahkan animasi pulse pada icon
              $('.follow-icon').css('animation', 'pulse 0.5s ease');

              // Tampilkan toast
              showToast('Tender berhasil ditambahkan ke daftar yang diikuti', 'success');
            } else {
              // Animasi untuk unfollow
              btn.removeClass('btn-warning unfollow-btn').addClass('btn-outline-primary follow-btn');
              btn.html(`<i class="material-icons-outlined follow-icon">star_border</i><span>Follow</span>`);

              // Tampilkan toast
              showToast('Tender dihapus dari daftar yang diikuti', 'info');
            }
          } else {
            // Kembalikan ke tampilan semula jika gagal
            btn.html(originalHtml);
            showToast('Gagal memperbarui status follow', 'error');
          }
          btn.prop('disabled', false);
        }).fail(function() {
          // Kembalikan ke tampilan semula jika error
          btn.html(originalHtml);
          btn.prop('disabled', false);
          showToast('Terjadi kesalahan saat memproses permintaan', 'error');
        });
      });
    });

    // Function to load view preferences
    function loadViewPreferences() {
      // Load view mode
      const viewMode = localStorage.getItem('tender_view_mode');
      if (viewMode === 'compact') {
        $('tr').addClass('compact-view');
        $('.product-category').hide();
        $('.table').addClass('table-sm');
      }
    }

    // Function to show toast notification
    function showToast(message, type = 'success') {
      // Check if toast container exists
      let toastContainer = document.getElementById('toast-container');
      if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
      }

      // Set toast color based on type
      let bgColor = 'bg-success';
      let icon = 'check_circle';

      if (type === 'error') {
        bgColor = 'bg-danger';
        icon = 'error';
      } else if (type === 'warning') {
        bgColor = 'bg-warning text-dark';
        icon = 'warning';
      } else if (type === 'info') {
        bgColor = 'bg-info text-dark';
        icon = 'info';
      }

      // Create toast element
      const toastId = 'toast-' + Date.now();
      const toast = document.createElement('div');
      toast.className = `toast align-items-center ${bgColor} text-white border-0`;
      toast.id = toastId;
      toast.setAttribute('role', 'alert');
      toast.setAttribute('aria-live', 'assertive');
      toast.setAttribute('aria-atomic', 'true');

      toast.innerHTML = `
        <div class="d-flex">
          <div class="toast-body">
            <i class="material-icons-outlined me-2">
              ${icon}
            </i>
            ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      `;

      toastContainer.appendChild(toast);

      // Initialize and show toast
      const bsToast = new bootstrap.Toast(toast, {
        animation: true,
        autohide: true,
        delay: 3000
      });

      bsToast.show();

      // Remove toast after it's hidden
      toast.addEventListener('hidden.bs.toast', function () {
        toast.remove();
      });
    }

    // Load tender counts
    document.addEventListener("DOMContentLoaded", function () {
      fetch("{{ secure_url(route('tender.lpse.count')) }}")
        .then(response => response.json())
        .then(data => {
          const tenderCountDiv = document.getElementById('tender-count');
          tenderCountDiv.innerHTML = `
            <a href="{{URL::route('tender_lpse.index')}}" class="btn btn-sm btn-outline-primary me-2">
              <span class="me-1">Semua</span><span class="badge bg-secondary">${data.total}</span>
            </a>
            <a href="{{URL::route('tender_lpse.tenderSelesai')}}" class="btn btn-sm btn-outline-success me-2">
              <span class="me-1">Selesai</span><span class="badge bg-secondary">${data.completed}</span>
            </a>
            <a href="{{URL::route('tender_lpse.tenderGagal')}}" class="btn btn-sm btn-outline-danger me-2">
              <span class="me-1">Gagal/Batal</span><span class="badge bg-secondary">${data.failed}</span>
            </a>
            <a href="{{URL::route('tender_lpse.tenderOnProcess')}}" class="btn btn-sm btn-outline-info me-2">
              <span class="me-1">On Process</span><span class="badge bg-secondary">${data.ongoing}</span>
            </a>
          `;
        })
        .catch(error => console.error('Error fetching tender counts:', error));
    });

  </script>
@endsection