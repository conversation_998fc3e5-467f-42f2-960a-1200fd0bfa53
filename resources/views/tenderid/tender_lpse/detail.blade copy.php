@extends('layouts.master')

@section('title', $tender->nama_paket)
@section('css')

@section('content')
<x-page-title title="Tender" pagetitle="Detail Tender" />

<div class="row d-flex align-items-stretch">
    <!-- Card utama dengan ukuran 10 -->
    <div class="col-md-9">
        @php
            if ($tender->status_tender == 'Ditutup') {
                $background = 'bg-warning';
            } else {
                $background = '';
            }
        @endphp
        <div class="card {{$background}}">
            <div class="row g-0">
                <div class="col-md-2 border-end">
                    <div class="p-3">
                        <img src="{{ asset(str_replace('http://proyeklpse.test', '', $tender->lpse->logo_path)) }}"
                            class="w-100 rounded-start" alt="...">
                    </div>
                </div>
                <div class="col-md-10">
                    <div class="card-body">
                        <h5 class="card-title">{{ $tender->nama_paket }}</h5>
                        <p class="card-text">#{{ $tender->kode_tender }}</p>
                        <h5>{{ convert_to_rupiah($tender->hps) }} - ({{format_number_short($tender->hps)}})</h5>
                        <a href="{{ route('lpse.dashboard', ['kode_lpse' => $tender->lpse->kd_lpse]) }}"
                            class="btn btn-primary position-relative gap-2" target="_blank">
                            {{ $tender->lpse->nama_lpse }}
                        </a>
                        <div class="mt-4 d-flex align-items-center justify-content-between">
                            <a href="{{$tender->lpse->url}}/lelang/{{$tender->kode_tender}}/pengumumanlelang"
                                target="_blank" class="btn btn-light d-flex gap-2 px-3">
                                <i class="material-icons-outlined">link</i>Sumber Informasi
                            </a>
                            <div class="d-flex gap-1">
                                <a href="{{ url()->previous() }}" class="sharelink">
                                    <i class="material-icons-outlined">arrow_back</i>
                                </a>
                                <div class="dropdown position-relative">
                                    <a href="javascript:;" class="sharelink dropdown-toggle dropdown-toggle-nocaret"
                                        data-bs-auto-close="outside" data-bs-toggle="dropdown">
                                        <i class="material-icons-outlined">share</i>
                                    </a>
                                    <div
                                        class="dropdown-menu dropdown-menu-end dropdown-menu-share shadow-lg border-0 p-3">
                                        <div class="input-group">
                                            <input id="shareUrl" type="text" class="form-control ps-5"
                                                value="{{ url()->current() }}" placeholder="Enter Url">
                                            <span
                                                class="material-icons-outlined position-absolute ms-3 translate-middle-y start-0 top-50">link</span>
                                            <button class="input-group-text gap-1" id="copyButton">
                                                <i class="material-icons-outlined fs-6">content_copy</i>Copy link
                                            </button>
                                        </div>
                                        <div class="d-flex align-items-center gap-2 mt-3">
                                            <a href="https://pinterest.com/pin/create/button/?url={{ url()->current() }}"
                                                target="_blank"
                                                class="py-1 px-3 border-0 rounded bg-pinterest text-white flex-fill d-flex gap-1">
                                                <i class="bi bi-pinterest"></i>Pinterest
                                            </a>
                                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ url()->current() }}"
                                                target="_blank"
                                                class="py-1 px-3 border-0 rounded bg-facebook text-white flex-fill d-flex gap-1">
                                                <i class="bi bi-facebook"></i>Facebook
                                            </a>
                                            <a href="https://www.linkedin.com/shareArticle?url={{ url()->current() }}&title={{ $tender->nama_paket }}"
                                                target="_blank"
                                                class="py-1 px-3 border-0 rounded bg-linkedin text-white flex-fill d-flex gap-1">
                                                <i class="bi bi-linkedin"></i>LinkedIn
                                            </a>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Card baru dengan ukuran 2 -->
    <div class="col-md-3">
        <div class="card {{$background}}">
            <div class="card-body">
                <div class="d-flex align-items-center gap-3 mb-2">
                    <div class="">
                        <h2 class="mb-0">Pagu HPS</h2>
                    </div>
                    <div class="">
                        @php
                            $style = 'bg-danger text-danger bg-opacity-10';
                            $icon = 'arrow_downward';

                            if (number_format($hpsDecreasePercentage, 2) == 0.00) {
                                $style = 'bg-success text-success bg-opacity-10';
                                $icon = '=';
                            }
                        @endphp

                        <p class="dash-lable d-flex align-items-center gap-1 rounded mb-0 {{ $style }}">
                            <span
                                class="material-icons-outlined fs-6">{{ $icon }}</span>{{ number_format($hpsDecreasePercentage, 2) }}%
                        </p>

                    </div>
                </div>
                <p class="mb-0">Pagu : {{convert_to_rupiah($tender->pagu)}}</p>
                <div class="mt-4">
                    <p class="mb-2 d-flex align-items-center justify-content-between">HPS :
                        {{convert_to_rupiah($tender->hps)}}<span
                            class="">{{ 100 - number_format($hpsDecreasePercentage, 2)}}%</span>
                    </p>
                    <div class="progress w-100" style="height: 7px;">
                        <div class="progress-bar bg-primary"
                            style="width: {{ 100 - number_format($hpsDecreasePercentage, 2)}}%"></div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="row d-flex align-items-stretch">
    <div class="col-md-9">

        <div class="card radius-10 {{$background}}">
            <div class="card-header py-3">
                <div class="row align-items-center g-3">
                    <div class="col-12 col-lg-6">
                        <h5 class="mb-0">Detail Tender</h5>
                    </div>
                    <div class="col-12 col-lg-6 text-md-end">
                        <span class="btn btn-primary btn-sm">
                            <i class="bi bi-file-earmark-ruled-fill me-2"></i>{{$tender->metode_pemilihan}}
                        </span>
                        @php
                            if ($tender->tahap_tender == 'Tender Sudah Selesai' && $tender->status_tender == 'Aktif') {
                                $class = 'btn-success';
                                $icon = 'bi-check-circle-fill';
                            } elseif ($tender->tahap_tender == 'Tender Batal' || $tender->tahap_tender == 'Seleksi Batal' || $tender->status_tender == 'Ditutup') {
                                $class = 'btn-danger';
                                $icon = 'bi-x-circle-fill';
                            } else {
                                $class = 'btn-primary';
                                $icon = 'bi-clock-fill';
                            }
                        @endphp
                        <span class="btn {{$class}} btn-sm">
                            <i class="bi {{$icon}} me-2"></i>{{$tender->tahap_tender}}
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-header py-2">
                <div class="row row-cols-1 row-cols-lg-3">
                    <!-- Data Instansi -->
                    <div class="col">
                        <a href="#satker"><small>Instansi dan Satuan Kerja</small></a>
                        <address class="m-t-5 m-b-5">
                            @if($satker = $tender->instansiSatker->first())
                                <strong class="text-inverse">{{ $satker->nama_instansi }}</strong><br>
                                Satker: {{ $satker->nama_satker }}
                            @endif

                        </address>
                    </div>

                    <!-- Data Lokasi Pekerjaan -->
                    <div class="col">
                        <a href="#lokasiPekerjaan"><small>Lokasi Pekerjaan</small></a>
                        <address class="m-t-5 m-b-5">
                            @foreach($tender->lokasiPaket as $index => $lokasi)
                                <strong class="text-inverse">{{ $lokasi->pkt_lokasi }}</strong><br>
                                {{ $lokasi->prp_nama }} - {{ $lokasi->kbp_nama }}
                            @endforeach
                        </address>
                    </div>

                    @if($tender->pemenang && $tender->pemenang->count() > 0)
                        <div class="col">
                            <small>Pemenang</small>
                            <address class="m-t-5 m-b-5">
                                @if($pemenang = $tender->pemenang->first())
                                    <strong class="text-inverse">{{$pemenang->peserta->nama_peserta}}</strong><br>
                                    {{$pemenang->peserta->alamat}}
                                @endif
                            </address>
                        </div>
                    @else
                        <!-- Data Anggaran -->
                        <div class="col">
                            <small>Anggaran</small>
                            <div>
                                @foreach($tender->anggaran as $index => $anggaran)
                                    <strong>{{$anggaran->sbd_id}} {{ $anggaran->ang_tahun }}</strong>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <div class="card-body {{$background}}">
                <div class="table-responsive">
                    <table class="table table-invoice">
                        <thead>
                            <tr>
                                <th>Rincian Tender</th>
                                <th>Keterangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Nilai Pagu</td>
                                <td>Rp {{ number_format($tender->pagu, 0, ',', '.') }}</td>
                            </tr>
                            <tr>
                                <td>Nilai HPS</td>
                                <td>Rp {{ number_format($tender->hps, 0, ',', '.') }}</td>
                            </tr>
                            <tr>
                                <td>Metode Pemilihan</td>
                                <td>{{ $tender->metode_pemilihan }}</td>
                            </tr>
                            <tr>
                                <td>Metode Pengadaan</td>
                                <td>{{ $tender->metode_pengadaan }}</td>
                            </tr>
                            <tr>
                                <td>Jumlah Pendaftar</td>
                                <td>{{ $tender->jumlah_pendaftar }}</td>
                            </tr>
                            <tr>
                                <td>Jumlah Penawar</td>
                                <td>{{ $tender->jumlah_penawar }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card-footer py-3 bg-transparent">
                <p class="text-center mb-2">
                    Terima Kasih Atas Partisipasi Anda
                </p>
                <p class="text-center d-flex align-items-center gap-3 justify-content-center mb-0">
                    <span><i class="bi bi-globe"></i> www.lpse.gov.id</span>
                    <span><i class="bi bi-telephone-fill"></i> T: +62-21-1234567</span>
                    <span><i class="bi bi-envelope-fill"></i> <EMAIL></span>
                </p>
            </div>
        </div>

        <div class="card radius-10 {{$background}}" id="satker">
            <div class="card-header py-3">
                <div class="row align-items-center g-3">
                    <div class="col-12 col-lg-6">
                        <h5 class="mb-0">Instansi dan Satuan Kerja</h5>
                    </div>
                    <div class="col-12 col-lg-6 text-md-end">
                        <a href="javascript:;" class="btn btn-danger btn-sm me-2">
                            <i class="bi bi-file-earmark-pdf me-2"></i>Export as PDF
                        </a>
                        <a href="javascript:;" onclick="window.print()" class="btn btn-dark btn-sm">
                            <i class="bi bi-printer-fill me-2"></i>Print
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-header py-2">
                <div class="row row-cols-1 row-cols-lg-3">
                    <!-- Data Instansi -->
                    <div class="col">
                        <small>Instansi</small>
                        <address class="m-t-5 m-b-5">
                            @foreach($tender->instansiSatker as $index => $satker)
                                <strong class="text-inverse">{{ $satker->nama_instansi }}</strong><br>
                                Satker: {{ $satker->nama_satker }}<br><br>
                            @endforeach
                        </address>
                    </div>

                    <!-- Data Lokasi Pekerjaan -->
                    <div class="col">
                        <small>Lokasi Pekerjaan</small>
                        <address class="m-t-5 m-b-5">
                            @foreach($tender->lokasiPaket as $index => $lokasi)
                                <strong class="text-inverse">{{ $lokasi->kbp_nama }}</strong><br>
                                {{ $lokasi->prp_nama }} - {{ $lokasi->pkt_lokasi }}<br><br>
                            @endforeach
                        </address>
                    </div>

                    <!-- Data Anggaran -->
                    <div class="col">
                        <small>Anggaran</small>
                        <div>
                            @foreach($tender->anggaran as $index => $anggaran)
                                <strong>#{{ $index + 1 }} - {{$anggaran->rup_id}}:</strong> Rp
                                {{ number_format($anggaran->ang_nilai, 0, ',', '.') }}<br>
                                {{$anggaran->sbd_id}} {{ $anggaran->ang_tahun }}<br><br>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-invoice">
                        <thead>
                            <tr>
                                <th>Rincian Tender</th>
                                <th>Nilai</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Nilai Pagu</td>
                                <td>Rp {{ number_format($tender->pagu, 0, ',', '.') }}</td>
                            </tr>
                            <tr>
                                <td>Nilai HPS</td>
                                <td>Rp {{ number_format($tender->hps, 0, ',', '.') }}</td>
                            </tr>
                            <tr>
                                <td>Metode Pemilihan</td>
                                <td>{{ $tender->metode_pemilihan }}</td>
                            </tr>
                            <tr>
                                <td>Metode Pengadaan</td>
                                <td>{{ $tender->metode_pengadaan }}</td>
                            </tr>
                            <tr>
                                <td>Jumlah Pendaftar</td>
                                <td>{{ $tender->jumlah_pendaftar }}</td>
                            </tr>
                            <tr>
                                <td>Jumlah Penawar</td>
                                <td>{{ $tender->jumlah_penawar }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="row bg-light align-items-center m-0">
                    <div class="col col-auto p-4">
                        <p class="mb-0">SUBTOTAL</p>
                        <h4 class="mb-0">Rp {{ number_format($tender->hps, 0, ',', '.') }}</h4>
                    </div>
                    <div class="col col-auto p-4">
                        <i class="bi bi-plus-lg text-muted"></i>
                    </div>
                    <div class="col col-auto me-auto p-4">
                        <p class="mb-0">Pengurangan dari Pagu</p>
                        <h4 class="mb-0">{{ round($tender->getHpsDecreasePercentage(), 2) }}%</h4>
                    </div>
                </div>
            </div>

            <div class="card-footer py-3 bg-transparent">
                <p class="text-center mb-2">
                    Terima Kasih Atas Partisipasi Anda
                </p>
                <p class="text-center d-flex align-items-center gap-3 justify-content-center mb-0">
                    <span><i class="bi bi-globe"></i> www.lpse.gov.id</span>
                    <span><i class="bi bi-telephone-fill"></i> T: +62-21-1234567</span>
                    <span><i class="bi bi-envelope-fill"></i> <EMAIL></span>
                </p>
            </div>
        </div>

        <div class="card radius-10 {{$background}}" id="lokasiPekerjaan">
            <div class="card-header py-3">
                <div class="row align-items-center g-3">
                    <div class="col-12 col-lg-6">
                        <h5 class="mb-0">Lokasi Pekerjaan</h5>
                    </div>
                    <div class="col-12 col-lg-6 text-md-end">
                        <a href="javascript:;" class="btn btn-danger btn-sm me-2">
                            <i class="bi bi-file-earmark-pdf me-2"></i>Export as PDF
                        </a>
                        <a href="javascript:;" onclick="window.print()" class="btn btn-dark btn-sm">
                            <i class="bi bi-printer-fill me-2"></i>Print
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-header py-2">
                <div class="row row-cols-1 row-cols-lg-3">
                    <!-- Data Instansi -->
                    <div class="col">
                        <small>Instansi</small>
                        <address class="m-t-5 m-b-5">
                            @foreach($tender->instansiSatker as $index => $satker)
                                <strong class="text-inverse">{{ $satker->nama_instansi }}</strong><br>
                                Satker: {{ $satker->nama_satker }}<br><br>
                            @endforeach
                        </address>
                    </div>

                    <!-- Data Lokasi Pekerjaan -->
                    <div class="col">
                        <small>Lokasi Pekerjaan</small>
                        <address class="m-t-5 m-b-5">
                            @foreach($tender->lokasiPaket as $index => $lokasi)
                                <strong class="text-inverse">{{ $lokasi->kbp_nama }}</strong><br>
                                {{ $lokasi->prp_nama }} - {{ $lokasi->pkt_lokasi }}<br><br>
                            @endforeach
                        </address>
                    </div>

                    <!-- Data Anggaran -->
                    <div class="col">
                        <small>Anggaran</small>
                        <div>
                            @foreach($tender->anggaran as $index => $anggaran)
                                <strong>#{{ $index + 1 }} - {{$anggaran->rup_id}}:</strong> Rp
                                {{ number_format($anggaran->ang_nilai, 0, ',', '.') }}<br>
                                {{$anggaran->sbd_id}} {{ $anggaran->ang_tahun }}<br><br>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-invoice">
                        <thead>
                            <tr>
                                <th>Rincian Tender</th>
                                <th>Nilai</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Nilai Pagu</td>
                                <td>Rp {{ number_format($tender->pagu, 0, ',', '.') }}</td>
                            </tr>
                            <tr>
                                <td>Nilai HPS</td>
                                <td>Rp {{ number_format($tender->hps, 0, ',', '.') }}</td>
                            </tr>
                            <tr>
                                <td>Metode Pemilihan</td>
                                <td>{{ $tender->metode_pemilihan }}</td>
                            </tr>
                            <tr>
                                <td>Metode Pengadaan</td>
                                <td>{{ $tender->metode_pengadaan }}</td>
                            </tr>
                            <tr>
                                <td>Jumlah Pendaftar</td>
                                <td>{{ $tender->jumlah_pendaftar }}</td>
                            </tr>
                            <tr>
                                <td>Jumlah Penawar</td>
                                <td>{{ $tender->jumlah_penawar }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="row bg-light align-items-center m-0">
                    <div class="col col-auto p-4">
                        <p class="mb-0">SUBTOTAL</p>
                        <h4 class="mb-0">Rp {{ number_format($tender->hps, 0, ',', '.') }}</h4>
                    </div>
                    <div class="col col-auto p-4">
                        <i class="bi bi-plus-lg text-muted"></i>
                    </div>
                    <div class="col col-auto me-auto p-4">
                        <p class="mb-0">Pengurangan dari Pagu</p>
                        <h4 class="mb-0">{{ round($tender->getHpsDecreasePercentage(), 2) }}%</h4>
                    </div>
                </div>
            </div>

            <div class="card-footer py-3 bg-transparent">
                <p class="text-center mb-2">
                    Terima Kasih Atas Partisipasi Anda
                </p>
                <p class="text-center d-flex align-items-center gap-3 justify-content-center mb-0">
                    <span><i class="bi bi-globe"></i> www.lpse.gov.id</span>
                    <span><i class="bi bi-telephone-fill"></i> T: +62-21-1234567</span>
                    <span><i class="bi bi-envelope-fill"></i> <EMAIL></span>
                </p>
            </div>
        </div>


    </div>

    <div class="col-md-3">

        @if($tender->pemenang && $tender->pemenang->count() > 0)
                <div class="card radius-10 {{$background}}">
                    <div class="card-body">
                        <div class="d-flex align-items-center gap-3 mb-2">
                            <div class="">
                                <h2 class="mb-0">HPS Kontrak</h2>
                            </div>
                            <div class="">
                                @php
                                    $pemenang = $tender->pemenang->first();
                                    $nilaiPenawaran = $pemenang->harga_penawaran;
                                    $decreaseP = (($tender->hps - $nilaiPenawaran) / $tender->hps) * 100;
                                    $style = 'bg-danger text-danger bg-opacity-10';
                                    $icon = 'arrow_downward';

                                    if (number_format($decreaseP, 2) == 0.00) {
                                        $style = 'bg-success text-success bg-opacity-10';
                                        $icon = '=';
                                    }
                                @endphp

                                <p class="dash-lable d-flex align-items-center gap-1 rounded mb-0 {{ $style }}">
                                    <span
                                        class="material-icons-outlined fs-6">{{ $icon }}</span>{{ number_format($decreaseP, 2) }}%
                                </p>
                            </div>
                        </div>
                        <p class="mb-0"> {{ $pemenang->peserta->nama_peserta }}</p>
                        <div class="mt-4">
                            <p class="mb-2 d-flex align-items-center justify-content-between">
                                {{ convert_to_rupiah($pemenang->harga_penawaran) }}<span
                                    class="">{{ 100 - number_format($decreaseP, 2) }}%</span>
                            </p>
                            <div class="progress w-100" style="height: 7px;">
                                <div class="progress-bar bg-primary" style="width: {{ 100 - number_format($decreaseP, 2) }}%">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        @endif

        <div class="card radius-10 {{$background}}">
            <div class="card-header py-3">
                <div class="row align-items-center g-3">
                    <div class="col-12 col-lg-6">
                        <h5 class="mb-0">Peserta Tender</h5>
                    </div>
                </div>
            </div>

            <div class="card-body">
                @if($tender->peserta->isNotEmpty())
                            <div class="table-responsive">
                                <table class="table table-invoice">
                                    <tbody>
                                        @foreach($tender->peserta as $index => $peserta)
                                                                @php
                                                                    $bold = '';
                                                                    $icon = '';
                                                                    if ($tender->pemenang && $tender->pemenang->count() > 0) {
                                                                        if ($peserta->perusahaan->nama_peserta == $pemenang->peserta->nama_peserta) {
                                                                            $bold = 'style="font-weight: bold;"';
                                                                            $icon = '<i class="bi bi-trophy-fill"></i>';
                                                                        }
                                                                    }
                                                                @endphp
                                                                <tr {!! $bold !!}>
                                                                    <td>{{ $index + 1 }}</td>
                                                                    <td>{!! $peserta->perusahaan->nama_peserta !!} {!! $icon !!}</td>
                                                                </tr>
                                        @endforeach

                                    </tbody>
                                </table>
                @else
                    <p>Belum ada data peserta.</p>
                @endif
                </div>
            </div>
        </div>
    </div>
</div>
<div class="container">
    <h1>Detail Tender: {{ $tender->nama_paket }}</h1>

    <h3>Informasi Umum</h3>
    <p><strong>Kode Tender:</strong> {{ $tender->kode_tender }}</p>
    <p><strong>LPSE:</strong> {{ $tender->lpse->nama_lpse }}</p>
    <p><strong>Status Tender:</strong> {{ $tender->status_tender }}</p>
    <p><strong>Anggaran:</strong> {{ $tender->anggaran->sum('ang_nilai') }}</p>
    <p><strong>Pagu:</strong> {{ $tender->pagu }}</p>
    <p><strong>HPS:</strong> {{ $tender->hps }}</p>

    <h3>Peserta Tender</h3>
    <ul>
        @foreach($tender->peserta as $peserta)
            <li>{{ $peserta->perusahaan->nama_peserta }} - Status: {{ $peserta->status }}</li>
        @endforeach
    </ul>

    <h3>Pemenang Tender</h3>
    <ul>
        @foreach($tender->pemenang as $pemenang)
            <li>{{ $pemenang->peserta->nama_peserta }} - Harga Penawaran: {{ $pemenang->harga_penawaran }}</li>
        @endforeach
    </ul>

    <h3>Instansi</h3>
    <ul>
        @foreach($tender->instansiSatker as $satker)
            <li>{{ $satker->nama_satker }} - {{ $satker->nama_instansi }} ({{ $satker->jenis_instansi }})</li>
        @endforeach
    </ul>

    <h3>Jadwal</h3>
    <ul>
        @foreach($tender->jadwal as $jadwal)
            <li>{{ $jadwal->tahap }}: {{ $jadwal->tanggal_mulai }} hingga {{ $jadwal->tanggal_selesai }}</li>
        @endforeach
    </ul>

    <h3>Lokasi Paket</h3>
    <ul>
        @foreach($tender->lokasiPaket as $lokasi)
            <li>{{ $lokasi->kbp_nama }} - {{ $lokasi->prp_nama }} - {{ $lokasi->pkt_lokasi }}</li>
        @endforeach
    </ul>
</div>
@endsection

@section('scripts')
<script>
    document.getElementById('copyButton').addEventListener('click', function () {
        var shareUrl = document.getElementById('shareUrl');

        // Pilih teks secara manual
        shareUrl.select();
        shareUrl.setSelectionRange(0, 99999); // Untuk mendukung perangkat mobile

        // Salin teks dengan cara manual
        try {
            var successful = document.execCommand('copy');
            var msg = successful ? 'Link berhasil disalin ke clipboard' : 'Gagal menyalin link';
            alert(msg);
        } catch (err) {
            console.error('Gagal menyalin link: ', err);
        }
    });

</script>
@endsection