@extends('layouts.master')

@section('title', $tender->nama_paket)
@section('css')
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<!-- Leaflet Marker Cluster CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
<!-- Custom Leaflet Styling -->
<style>
  /* Map container styling */
  #project-location-map {
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
  }

  #project-location-map:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }

  /* Popup styling */
  .leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 3px 14px rgba(0, 0, 0, 0.2);
    padding: 0;
    overflow: hidden;
  }

  .leaflet-popup-content {
    margin: 0;
    padding: 12px 15px;
  }

  .popup-content {
    padding: 5px 0;
  }

  .popup-content strong {
    display: block;
    color: #344767;
    font-size: 14px;
    margin-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 5px;
  }

  .location-info {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 8px;
    color: #6c757d;
    font-size: 13px;
  }

  .popup-content small.text-success {
    display: inline-block;
    margin-top: 5px;
    font-size: 11px;
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    padding: 2px 6px;
    border-radius: 4px;
  }

  .popup-content small.text-info {
    display: inline-block;
    margin-top: 5px;
    font-size: 11px;
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    padding: 2px 6px;
    border-radius: 4px;
  }

  /* Marker cluster custom styling */
  .marker-cluster-small,
  .marker-cluster-medium,
  .marker-cluster-large {
    background-color: rgba(66, 133, 244, 0.2);
  }

  .marker-cluster-small div,
  .marker-cluster-medium div,
  .marker-cluster-large div {
    background-color: rgba(66, 133, 244, 0.7);
    color: white;
    font-weight: bold;
  }

  /* Zoom controls styling */
  .leaflet-control-zoom a {
    border-radius: 4px !important;
    color: #344767;
  }

  .leaflet-control-zoom-in {
    margin-bottom: 5px !important;
  }

  /* Attribution styling */
  .leaflet-control-attribution {
    background-color: rgba(255, 255, 255, 0.8) !important;
    padding: 2px 8px !important;
    border-radius: 4px 0 0 0 !important;
    font-size: 10px !important;
  }

  /* Card Styling */
  .card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    margin-bottom: 24px;
  }

  .card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 12px 12px 0 0 !important;
    padding: 18px 24px;
  }

  .card-title {
    font-weight: 600;
    color: #344767;
    margin-bottom: 0;
    font-size: 1.1rem;
  }

  .card-body {
    padding: 24px;
  }

  .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 0 0 12px 12px !important;
    padding: 16px 24px;
  }

  /* Table Styling */
  .table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
  }

  .table thead th {
    background-color: #f8f9fa;
    color: #344767;
    font-weight: 600;
    border-top: none;
    padding: 16px;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #e9ecef;
  }

  .table tbody td {
    padding: 16px;
    vertical-align: middle;
    border-bottom: 1px solid #f8f9fa;
    font-size: 0.9rem;
  }

  .table tbody tr:last-child td {
    border-bottom: none;
  }

  .table tbody tr {
    transition: all 0.2s ease;
  }

  .table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
  }

  /* Badge Styling */
  .badge {
    padding: 6px 12px;
    font-weight: 500;
    font-size: 0.75rem;
    border-radius: 30px;
  }

  /* Follow Button Animation */
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.15); }
    100% { transform: scale(1); }
  }

  @keyframes rotate {
    0% { transform: rotate(0deg); }
    25% { transform: rotate(-15deg); }
    75% { transform: rotate(15deg); }
    100% { transform: rotate(0deg); }
  }

  .follow-btn:hover .follow-icon {
    animation: pulse 0.5s ease;
  }

  .unfollow-btn .follow-icon {
    color: #fff;
  }

  .unfollow-btn:hover .follow-icon {
    animation: rotate 0.5s ease;
  }

  /* Progress Bar Styling */
  .progress {
    height: 8px;
    border-radius: 4px;
    background-color: #f1f1f1;
    margin-top: 8px;
    overflow: hidden;
  }

  .progress-bar {
    background: linear-gradient(90deg, #4481eb, #04befe);
    border-radius: 4px;
  }

  /* Tender Info Styling */
  .tender-info-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 12px;
    overflow: hidden;
  }

  .tender-info-card .card-body {
    padding: 20px;
  }

  .tender-info-card .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 15px;
    height: 100%;
  }

  .tender-info-card .logo-container img {
    max-width: 100%;
    max-height: 100px;
    object-fit: contain;
  }

  .tender-info-card h5.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #344767;
    margin-bottom: 10px;
  }

  .tender-info-card .tender-code {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 15px;
  }

  .tender-info-card .tender-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #344767;
    margin-bottom: 20px;
  }

  /* Status Badge Styling */
  .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 30px;
    font-weight: 500;
    font-size: 0.85rem;
    margin-right: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  }

  .status-badge i {
    margin-right: 6px;
    font-size: 1rem;
  }

  .status-badge.active {
    background-color: #4caf50;
    color: white;
  }

  .status-badge.completed {
    background-color: #2196f3;
    color: white;
  }

  .status-badge.cancelled {
    background-color: #f44336;
    color: white;
  }

  /* Section Title Styling */
  .section-title {
    position: relative;
    padding-left: 15px;
    margin-bottom: 20px;
    font-weight: 600;
    color: #344767;
  }

  .section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: linear-gradient(180deg, #4481eb, #04befe);
    border-radius: 2px;
  }

  /* Jadwal Styling */
  .jadwal-active {
    background-color: rgba(76, 175, 80, 0.1);
    border-left: 3px solid #4caf50;
    font-weight: 600;
  }

  /* Share Button Styling */
  .sharelink {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f8f9fa;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .sharelink:hover {
    background-color: #4481eb;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(68, 129, 235, 0.2);
  }

  .sharelink i {
    font-size: 1.2rem;
  }

  /* Dropdown Menu Styling */
  .dropdown-menu-share {
    min-width: 340px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    padding: 0;
    background-color: white;
    overflow: hidden;
    border: none;
  }

  .share-header {
    background: linear-gradient(135deg, #4481eb, #04befe);
    color: white;
    padding: 15px 20px;
    text-align: center;
    font-weight: 600;
  }

  .share-header h6 {
    font-weight: 600;
    font-size: 16px;
    margin: 0;
    color: white;
  }

  .share-body {
    padding: 20px;
  }

  .url-container {
    margin-bottom: 20px;
  }

  .url-container .input-group {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .url-container .input-group input {
    border: none;
    box-shadow: none;
    font-size: 14px;
    padding: 12px 12px 12px 45px;
    background-color: #f8f9fa;
  }

  .copy-btn {
    background-color: #4481eb;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s;
    padding: 0 15px;
    position: relative;
    overflow: hidden;
  }

  .copy-btn:hover {
    background-color: #04befe;
  }

  .copy-btn i {
    font-size: 18px;
    transition: all 0.3s;
  }

  .copy-btn.copy-success {
    background-color: #28a745;
  }

  .copy-btn.copy-error {
    background-color: #dc3545;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .spin {
    animation: spin 1s linear infinite;
  }

  .share-divider {
    position: relative;
    text-align: center;
    margin: 15px 0;
  }

  .share-divider:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: #e0e0e0;
    z-index: 1;
  }

  .share-divider span {
    display: inline-block;
    background-color: white;
    padding: 0 15px;
    position: relative;
    z-index: 2;
    color: #6c757d;
    font-size: 14px;
  }

  /* Social Media Buttons */
  .social-share-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
  }

  .social-share-btn {
    width: 100%;
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.3s ease;
    color: white;
    border: none;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .social-share-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 1;
  }

  .social-share-btn:hover:before {
    transform: translateY(0);
  }

  .social-share-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }

  .social-share-btn i {
    font-size: 1.2rem;
    position: relative;
    z-index: 2;
  }

  .social-share-btn.facebook {
    background-color: #1877f2;
  }

  .social-share-btn.twitter {
    background-color: #000000;
  }

  .social-share-btn.linkedin {
    background-color: #0077b5;
  }

  .social-share-btn.pinterest {
    background-color: #e60023;
  }

  .social-share-btn.whatsapp {
    background-color: #25D366;
  }

  .social-share-btn.telegram {
    background-color: #0088cc;
  }

  .social-share-btn.email {
    background-color: #6c757d;
  }

  .social-share-btn.reddit {
    background-color: #FF5700;
  }

  .social-share-btn.tumblr {
    background-color: #36465D;
  }

  .social-share-btn.more {
    background: linear-gradient(135deg, #4481eb, #04befe);
    cursor: pointer;
  }

  @media (max-width: 767.98px) {
    .dropdown-menu-share {
      min-width: 280px;
    }

    .social-share-grid {
      gap: 8px;
    }

    .share-header {
      padding: 12px 15px;
    }

    .share-body {
      padding: 15px;
    }
  }
</style>
@endsection

@section('content')

<div class="row d-flex align-items-stretch">
    <div class="col-md-9">
        @php
            $background = ($tender->status_tender == 'Ditutup' || $tender->status_tender == 'Tender Ditutup') ? 'bg-warning' : '';

            // Determine status badge class and icon
            if ($tender->tahap_tender == 'Tender Sudah Selesai' && $tender->status_tender == 'Aktif') {
                $statusClass = 'completed';
                $statusIcon = 'check_circle';
                $statusText = 'Selesai';
            } elseif ($tender->tahap_tender == 'Tender Batal' || $tender->tahap_tender == 'Seleksi Batal' || $tender->status_tender == 'Ditutup') {
                $statusClass = 'cancelled';
                $statusIcon = 'cancel';
                $statusText = 'Batal';
            } else {
                $statusClass = 'active';
                $statusIcon = 'schedule';
                $statusText = 'Aktif';
            }
        @endphp
        <div class="card tender-info-card">
            <div class="card-body p-0">
                <div class="row g-0">
                    <div class="col-md-2">
                        <div class="logo-container h-100 p-4">
                            <img src="{{ asset(str_replace('http://proyeklpse.test', '', $tender->lpse->logo_path)) }}"
                                class="img-fluid" alt="Logo LPSE">
                        </div>
                    </div>
                    <div class="col-md-10">
                        <div class="p-4">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="card-title mb-2">{{ $tender->nama_paket }}</h5>
                                    <p class="tender-code mb-2">#{{ $tender->kode_tender }}</p>
                                </div>
                                <div class="d-flex gap-2">
                                    <a href="{{ url()->previous() }}" class="sharelink" title="Kembali">
                                        <i class="material-icons-outlined">arrow_back</i>
                                    </a>
                                    <div class="dropdown position-relative">
                                        <a href="javascript:;" class="sharelink dropdown-toggle dropdown-toggle-nocaret"
                                            data-bs-auto-close="outside" data-bs-toggle="dropdown" title="Bagikan">
                                            <i class="material-icons-outlined">share</i>
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-share shadow-lg border-0 p-0">
                                            <div class="share-header">
                                                <h6 class="mb-0">Bagikan Tender</h6>
                                            </div>
                                            <div class="share-body p-3">
                                                <div class="url-container mb-3">
                                                    <div class="input-group">
                                                        <input id="shareUrl" type="text" class="form-control ps-5"
                                                            value="{{ route('landing.tender.detail', ['id' => $tender->kode_tender]) }}" readonly>
                                                        <span class="material-icons-outlined position-absolute ms-3 translate-middle-y start-0 top-50 text-primary">link</span>
                                                        <button class="input-group-text copy-btn" id="copyButton">
                                                            <i class="material-icons-outlined fs-6">content_copy</i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="social-share-grid">
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ route('landing.tender.detail', ['id' => $tender->kode_tender]) }}"
                                                        target="_blank" class="social-share-btn facebook" title="Bagikan ke Facebook">
                                                        <i class="bi bi-facebook"></i>
                                                    </a>
                                                    <a href="https://twitter.com/intent/tweet?url={{ route('landing.tender.detail', ['id' => $tender->kode_tender]) }}&text={{ urlencode('Informasi Tender: ' . $tender->nama_paket) }}"
                                                        target="_blank" class="social-share-btn twitter" title="Bagikan ke Twitter">
                                                        <i class="bi bi-twitter-x"></i>
                                                    </a>
                                                    <a href="https://www.linkedin.com/shareArticle?mini=true&url={{ route('landing.tender.detail', ['id' => $tender->kode_tender]) }}&title={{ urlencode($tender->nama_paket) }}"
                                                        target="_blank" class="social-share-btn linkedin" title="Bagikan ke LinkedIn">
                                                        <i class="bi bi-linkedin"></i>
                                                    </a>
                                                    <a href="https://wa.me/?text={{ urlencode('Informasi Tender: ' . $tender->nama_paket . ' - ' . route('landing.tender.detail', ['id' => $tender->kode_tender])) }}"
                                                        target="_blank" class="social-share-btn whatsapp" title="Bagikan ke WhatsApp">
                                                        <i class="bi bi-whatsapp"></i>
                                                    </a>
                                                    <a href="https://t.me/share/url?url={{ route('landing.tender.detail', ['id' => $tender->kode_tender]) }}&text={{ urlencode('Informasi Tender: ' . $tender->nama_paket) }}"
                                                        target="_blank" class="social-share-btn telegram" title="Bagikan ke Telegram">
                                                        <i class="bi bi-telegram"></i>
                                                    </a>
                                                </div>
                                                <div class="social-share-grid mt-2">
                                                    <a href="https://pinterest.com/pin/create/button/?url={{ route('landing.tender.detail', ['id' => $tender->kode_tender]) }}&description={{ urlencode($tender->nama_paket) }}"
                                                        target="_blank" class="social-share-btn pinterest" title="Bagikan ke Pinterest">
                                                        <i class="bi bi-pinterest"></i>
                                                    </a>
                                                    <a href="mailto:?subject={{ urlencode('Informasi Tender: ' . $tender->nama_paket) }}&body={{ urlencode('Lihat detail tender ini di: ' . route('landing.tender.detail', ['id' => $tender->kode_tender])) }}"
                                                        class="social-share-btn email" title="Bagikan via Email">
                                                        <i class="bi bi-envelope"></i>
                                                    </a>
                                                    <a href="https://www.reddit.com/submit?url={{ route('landing.tender.detail', ['id' => $tender->kode_tender]) }}&title={{ urlencode('Informasi Tender: ' . $tender->nama_paket) }}"
                                                        target="_blank" class="social-share-btn reddit" title="Bagikan ke Reddit">
                                                        <i class="bi bi-reddit"></i>
                                                    </a>
                                                    <a href="https://www.tumblr.com/widgets/share/tool?canonicalUrl={{ route('landing.tender.detail', ['id' => $tender->kode_tender]) }}&title={{ urlencode($tender->nama_paket) }}"
                                                        target="_blank" class="social-share-btn tumblr" title="Bagikan ke Tumblr">
                                                        <i class="bi bi-t"></i>
                                                    </a>
                                                    <div class="social-share-btn more" id="shareMore" title="Opsi Lainnya">
                                                        <i class="bi bi-three-dots"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tender-price">{{ convert_to_rupiah($tender->hps) }} <span class="fs-6 text-muted">({{format_number_short($tender->hps)}})</span></div>

                            <div class="d-flex flex-wrap gap-2 mb-3">
                                <!-- LPSE Badge (Urutan 1) -->
                                <a href="{{ route('lpse.dashboard', ['kode_lpse' => $tender->lpse->kd_lpse]) }}"
                                    class="status-badge d-inline-flex align-items-center" style="background-color: #009688; color: white;" target="_blank">
                                    <i class="material-icons-outlined" style="font-size: 16px; margin-right: 6px;">business</i>
                                    <span>{{ $tender->lpse->nama_lpse }}</span>
                                </a>

                                <!-- Metode Pemilihan Badge (Urutan 2) -->
                                <span class="status-badge d-inline-flex align-items-center" style="background-color: #3f51b5; color: white;">
                                    <i class="material-icons-outlined" style="font-size: 16px; margin-right: 6px;">gavel</i>
                                    <span>{{ $tender->metode_pemilihan }}</span>
                                </span>

                                <!-- Tahap Tender Badge (Urutan 3) -->
                                <span class="status-badge d-inline-flex align-items-center" style="background-color: #2196f3; color: white; font-weight: 600;">
                                    <i class="material-icons-outlined" style="font-size: 16px; margin-right: 6px;">flag</i>
                                    <span>{{ $tender->tahap_tender }}</span>
                                </span>

                                <!-- Status Badge (hanya ditampilkan jika tidak aktif) -->
                                @if($tender->status_tender == 'Ditutup' || $tender->status_tender == 'Tidak Aktif')
                                <span class="status-badge d-inline-flex align-items-center {{ $statusClass }}">
                                    <i class="material-icons-outlined" style="font-size: 16px; margin-right: 6px;">{{ $statusIcon }}</i>
                                    <span>{{ $statusText }}</span>
                                </span>
                                @endif
                            </div>

                            <div class="d-flex align-items-center gap-3">
                                <a href="{{$tender->lpse->url}}/lelang/{{$tender->kode_tender}}/pengumumanlelang"
                                    target="_blank" class="btn btn-outline-primary d-flex align-items-center gap-2">
                                    <i class="material-icons-outlined">link</i>
                                    <span>Sumber Informasi</span>
                                </a>

                                @auth
                                    @php
                                        $followed = json_decode(auth()->user()->tender_followed) ?? [];
                                    @endphp
                                    @if (in_array($tender->kode_tender, $followed))
                                        <button class="btn btn-warning unfollow-btn d-flex align-items-center gap-2"
                                            data-tender-id="{{ $tender->kode_tender }}">
                                            <i class="material-icons-outlined follow-icon">star</i>
                                            <span>Unfollow</span>
                                        </button>
                                    @else
                                        <button class="btn btn-outline-primary follow-btn d-flex align-items-center gap-2"
                                            data-tender-id="{{ $tender->kode_tender }}">
                                            <i class="material-icons-outlined follow-icon">star_border</i>
                                            <span>Follow</span>
                                        </button>
                                    @endif
                                @endauth
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card">
            {{-- <div class="card-header">
                <h5 class="section-title mb-0">Informasi Anggaran</h5>
            </div> --}}
            <div class="card-body">
                @php
                    $style = 'bg-danger text-danger bg-opacity-10';
                    $icon = 'trending_down';
                    $textColor = 'text-danger';

                    if (number_format($hpsDecreasePercentage, 2) == 0.00) {
                        $style = 'bg-success text-success bg-opacity-10';
                        $icon = 'horizontal_rule';
                        $textColor = 'text-success';
                    }

                    $hpsPercentage = 100 - number_format($hpsDecreasePercentage, 2);
                @endphp

                <!-- Pagu Section -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="fw-bold mb-0">Pagu Anggaran</h6>
                        <span class="badge {{ $style }} d-flex align-items-center">
                            <i class="material-icons-outlined me-1" style="font-size: 14px;">{{ $icon }}</i>
                            <span>{{ number_format($hpsDecreasePercentage, 2) }}%</span>
                        </span>
                    </div>
                    <h4 class="mb-0">{{convert_to_rupiah($tender->pagu)}}</h4>
                </div>

                <!-- HPS Section -->
                <div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="fw-bold mb-0">Harga Perkiraan Sendiri (HPS)</h6>
                        <span class="{{ $textColor }} fw-bold">{{ $hpsPercentage }}%</span>
                    </div>
                    <h4 class="mb-2">{{convert_to_rupiah($tender->hps)}}</h4>
                    <div class="progress">
                        <div class="progress-bar" style="width: {{ $hpsPercentage }}%"></div>
                    </div>
                </div>

                {{-- <!-- Additional Info -->
                <div class="mt-4 pt-3 border-top">
                    <div class="d-flex align-items-center mb-2">
                        <i class="material-icons-outlined me-2 text-primary">calendar_today</i>
                        <div>
                            <small class="text-muted d-block">Durasi Tender</small>
                            <span class="fw-semibold">{{ $tender->durasi_tender }} Hari</span>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined me-2 text-primary">people</i>
                        <div>
                            <small class="text-muted d-block">Jumlah Peserta</small>
                            <span class="fw-semibold">{{ $tender->jumlah_pendaftar }} Pendaftar, {{ $tender->jumlah_penawar }} Penawar</span>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
    </div>
</div>

<div class="row d-flex align-items-stretch">
    <div class="col-md-9">

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="section-title mb-0">Detail Tender</h5>
                <div id="dlUraian" class="d-flex align-items-center"></div>
            </div>

            <!-- Summary Cards -->
            <div class="card-body pb-0">
                <div class="row g-3 mb-4">
                    <!-- Instansi Card -->
                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="material-icons-outlined text-primary me-2">business</i>
                                    <h6 class="mb-0 fw-bold">Instansi dan Satuan Kerja</h6>
                                </div>
                                @if($satker = $tender->instansiSatker->first())
                                    <p class="mb-1 fw-semibold">{{ $satker->nama_instansi }}</p>
                                    <p class="mb-0 text-muted small">Satker: {{ $satker->nama_satker }}</p>
                                    <a href="#satker" class="mt-2 d-inline-block small text-primary">Lihat detail</a>
                                @else
                                    <p class="mb-0 text-muted">Tidak ada data instansi</p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Lokasi Card -->
                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="material-icons-outlined text-primary me-2">place</i>
                                    <h6 class="mb-0 fw-bold">Lokasi Pekerjaan</h6>
                                </div>
                                @if($tender->lokasiPaket->count() > 0)
                                    @foreach($tender->lokasiPaket->take(1) as $lokasi)
                                        <p class="mb-1 fw-semibold">{{ $lokasi->pkt_lokasi }}</p>
                                        <p class="mb-0 text-muted small">{{ $lokasi->prp_nama }} - {{ $lokasi->kbp_nama }}</p>
                                    @endforeach
                                    @if($tender->lokasiPaket->count() > 1)
                                        <a href="#lokasiPekerjaan" class="mt-2 d-inline-block small text-primary">Lihat {{ $tender->lokasiPaket->count() }} lokasi</a>
                                    @endif
                                @else
                                    <p class="mb-0 text-muted">Tidak ada data lokasi</p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Pemenang/Anggaran Card -->
                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                @if($tender->pemenang && $tender->pemenang->count() > 0)
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="material-icons-outlined text-primary me-2">emoji_events</i>
                                        <h6 class="mb-0 fw-bold">Pemenang Tender</h6>
                                    </div>
                                    @foreach($tender->pemenang->take(1) as $pemenang)
                                        <p class="mb-1 fw-semibold">
                                            <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($pemenang->peserta->pembeda)]) }}" class="text-primary">
                                                {{ $pemenang->peserta->nama_peserta }}
                                            </a>
                                        </p>
                                        <p class="mb-0 text-muted small">{{ $pemenang->peserta->alamat }}</p>
                                    @endforeach
                                    @if($tender->pemenang->count() > 1)
                                        <span class="badge bg-success mt-2">{{ $tender->pemenang->count() }} Pemenang</span>
                                    @endif
                                @else
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="material-icons-outlined text-primary me-2">account_balance</i>
                                        <h6 class="mb-0 fw-bold">Anggaran</h6>
                                    </div>
                                    @if($tender->anggaran->count() > 0)
                                        @foreach($tender->anggaran->take(1) as $anggaran)
                                            <p class="mb-1 fw-semibold">{{$anggaran->sbd_id}} {{ $anggaran->ang_tahun }}</p>
                                        @endforeach
                                        <a href="#anggaran" class="mt-2 d-inline-block small text-primary">Lihat detail anggaran</a>
                                    @else
                                        <p class="mb-0 text-muted">Tidak ada data anggaran</p>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detail Table -->
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr class="tenderid_header">
                                <th style="width: 30%;">Rincian Tender</th>
                                <th style="width: 70%;">Keterangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="fw-semibold">Kategori Pekerjaan</td>
                                <td>{{$tender->kategori_pekerjaan}}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Tanggal Pembuatan</td>
                                <td>{{format_date_indonesian($tender->tanggal_paket_dibuat)}}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Tanggal Penayangan</td>
                                <td>{{format_date_time($tender->tanggal_paket_tayang)}} WIB</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Nilai Pagu</td>
                                <td>Rp {{ number_format($tender->pagu, 0, ',', '.') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Nilai HPS</td>
                                <td>Rp {{ number_format($tender->hps, 0, ',', '.') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Waktu Tender</td>
                                <td>{{format_date_indonesian($tender->jadwal()->orderBy('tanggal_mulai')->value('tanggal_mulai'))}} s.d {{format_date_indonesian($tender->getTenderEndDate())}}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Durasi Tender</td>
                                <td>{{ $tender->durasi_tender }} Hari</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Kualifikasi Usaha</td>
                                <td>{{ $tender->kualifikasi_usaha }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Metode Pemilihan</td>
                                <td>
                                    <span class="badge d-inline-flex align-items-center" style="background-color: #3f51b5; color: white; padding: 6px 12px;">
                                        <i class="material-icons-outlined" style="font-size: 16px; margin-right: 6px; vertical-align: text-bottom;">gavel</i>
                                        <span>{{ $tender->metode_pemilihan }}</span>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Metode Pengadaan</td>
                                <td>{{ $tender->metode_pengadaan }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Metode Evaluasi</td>
                                <td>{{$tender->metode_evaluasi}}</td>
                            </tr>
                            @if($tender->status_tender == 'Ditutup' || $tender->status_tender == 'Tidak Aktif')
                            <tr>
                                <td class="fw-semibold">Status Tender</td>
                                <td>
                                    <span class="badge d-inline-flex align-items-center {{ $statusClass }}" style="padding: 6px 12px;">
                                        <i class="material-icons-outlined" style="font-size: 16px; margin-right: 6px; vertical-align: text-bottom;">{{ $statusIcon }}</i>
                                        <span>{{ $statusText }}</span>
                                    </span>
                                </td>
                            </tr>
                            @endif
                            <tr>
                                <td class="fw-semibold">Tahap Tender</td>
                                <td>
                                    <span class="badge d-inline-flex align-items-center" style="background-color: #2196f3; color: white; padding: 6px 12px;">
                                        <i class="material-icons-outlined" style="font-size: 16px; margin-right: 6px; vertical-align: text-bottom;">flag</i>
                                        <span>{{ $tender->tahap_tender }}</span>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Jumlah Peserta</td>
                                <td>
                                    <div class="d-flex gap-3">
                                        <div>
                                            <span class="badge d-inline-flex align-items-center bg-primary" style="padding: 6px 12px;">
                                                <i class="material-icons-outlined" style="font-size: 16px; margin-right: 6px; vertical-align: text-bottom;">how_to_reg</i>
                                                <span>{{ $tender->jumlah_pendaftar }} Pendaftar</span>
                                            </span>
                                        </div>
                                        <div>
                                            <span class="badge d-inline-flex align-items-center bg-success" style="padding: 6px 12px;">
                                                <i class="material-icons-outlined" style="font-size: 16px; margin-right: 6px; vertical-align: text-bottom;">gavel</i>
                                                <span>{{ $tender->jumlah_penawar }} Penawar</span>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        @if (!empty($tender->syarat_kualifikasi))
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="section-title mb-0">Syarat Kualifikasi</h5>
            </div>

            <div class="card-body">
                <div class="syarat-kualifikasi">
                    {!! $tender->syarat_kualifikasi !!}
                </div>
            </div>
        </div>
        @endif

        <div class="card" id="satker">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="section-title mb-0">Instansi dan Satuan Kerja</h5>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr class="tenderid_header">
                                <th>RUP</th>
                                <th>Nama Instansi</th>
                                <th>Nama Satuan Kerja</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($tender->instansiSatker as $index => $satker)
                                <tr>
                                    <td>
                                        <a target="_blank" href="{{ route('sirup.index') }}?search={{ $satker->rup_id }}" class="d-flex align-items-center text-primary">
                                            <i class="material-icons-outlined me-1" style="font-size: 16px;">description</i>
                                            <span>#{{$satker->rup_id}}</span>
                                        </a>
                                    </td>
                                    <td>{{$satker->nama_instansi}}</td>
                                    <td>{{$satker->nama_satker}}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card" id="lokasiPekerjaan">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="section-title mb-0">Lokasi Pekerjaan</h5>
                <span class="badge bg-info d-inline-flex align-items-center">
                    <i class="material-icons-outlined me-1" style="font-size: 14px;">place</i>
                    <span>{{ $tender->lokasiPaket->count() }} Lokasi</span>
                </span>
            </div>

            <div class="card-body">
                @if($tender->lokasiPaket->count() > 0)
                <!-- Peta Lokasi Proyek -->
                <div id="project-location-map" style="height: 400px; width: 100%; border-radius: 10px; margin-bottom: 10px;" class="shadow-sm"></div>

                <!-- Legenda Marker -->
                <div class="map-legend d-flex align-items-center justify-content-end mb-3">
                    <div class="d-flex align-items-center me-3">
                        <img src="https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-blue.png" alt="Marker Kabupaten/Kota" style="height: 20px; margin-right: 5px;">
                        <small class="text-muted">Lokasi Kabupaten/Kota</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <img src="https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png" alt="Marker Lokasi Spesifik" style="height: 20px; margin-right: 5px;">
                        <small class="text-muted">Lokasi Spesifik Proyek</small>
                    </div>
                </div>
                @endif

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr class="tenderid_header">
                                <th style="width: 60%;">Lokasi Paket</th>
                                <th style="width: 40%;">Wilayah</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($tender->lokasiPaket as $index => $lokasi)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="material-icons-outlined text-primary me-2" style="font-size: 18px;">place</i>
                                            <span>{{$lokasi->pkt_lokasi}}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            {{ $lokasi->prp_nama }} - {{ $lokasi->kbp_nama }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card" id="anggaran">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="section-title mb-0">Anggaran</h5>
                @if ($tender->anggaran->count() > 1)
                <span class="badge bg-success d-inline-flex align-items-center">
                    <i class="material-icons-outlined me-1" style="font-size: 14px;">account_balance</i>
                    <span>{{ $tender->anggaran->count() }} Sumber Anggaran</span>
                </span>
                @endif
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr class="tenderid_header">
                                <th>RUP</th>
                                <th>Sumber Anggaran</th>
                                <th>Nilai</th>
                                <th>Kode Rekening</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                            $total_anggaran = 0;
                            @endphp
                            @foreach($tender->anggaran as $index => $anggaran)
                                @php
                                $total_anggaran += $anggaran->ang_nilai;
                                @endphp
                                <tr>
                                    <td>
                                        <a target="_blank" href="{{ route('sirup.index') }}?search={{ $anggaran->rup_id }}" class="d-flex align-items-center text-primary">
                                            <i class="material-icons-outlined me-1" style="font-size: 16px;">description</i>
                                            <span>#{{$anggaran->rup_id}}</span>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            {{$anggaran->sbd_id}} {{$anggaran->ang_tahun}}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-semibold">{{convert_to_rupiah($anggaran->ang_nilai)}}</span>
                                    </td>
                                    <td>
                                        <code>{{$anggaran->ang_koderekening}}</code>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                @if ($tender->anggaran->count() > 1)
                <div class="d-flex align-items-center justify-content-end mt-3 p-3 bg-light rounded">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="text-muted">TOTAL ANGGARAN:</span>
                        </div>
                        <div>
                            <h4 class="mb-0 fw-bold text-primary">Rp {{ number_format($total_anggaran, 0, ',', '.') }}</h4>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <div class="card" id="jadwal">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="section-title mb-0">Jadwal Tender</h5>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-primary d-inline-flex align-items-center">
                        <i class="material-icons-outlined me-1" style="font-size: 14px;">date_range</i>
                        <span>Durasi: {{ $tender->durasi_tender }} Hari</span>
                    </span>
                    @if(auth()->user()->account_type === 'Super Admin')
                        <button id="syncJadwalButton" class="btn btn-sm btn-outline-primary d-flex align-items-center gap-1">
                            <i class="material-icons-outlined" style="font-size: 16px;">sync</i>
                            <span>Sync Jadwal</span>
                        </button>

                        <!-- Modal -->
                        <div class="modal fade" id="syncJadwalModal" tabindex="-1" aria-labelledby="syncJadwalModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="syncJadwalModalLabel">Sync Jadwal Output</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <pre id="syncJadwalOutput"></pre>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="setTimeout(() => location.reload(), 500)">Close</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr class="tenderid_header">
                                <th style="width: 5%;">No</th>
                                <th style="width: 35%;">Tahapan</th>
                                <th style="width: 20%;">Mulai</th>
                                <th style="width: 20%;">Selesai</th>
                                <th style="width: 20%;">
                                    <div class="d-flex align-items-center">
                                        <i class="material-icons-outlined me-1" style="font-size: 16px;">update</i>
                                        <span>Perubahan</span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($tender->jadwal->sortBy('step') as $index => $jadwal)
                                @php
                                    $now = Carbon\Carbon::now();
                                    // Konversi tanggal mulai dan selesai ke instance Carbon
                                    $tanggalMulai = Carbon\Carbon::parse($jadwal->tanggal_mulai);
                                    $tanggalSelesai = $jadwal->tanggal_selesai ? Carbon\Carbon::parse($jadwal->tanggal_selesai) : null;

                                    // Cek apakah tahap sedang berlangsung
                                    $sedangBerlangsung = $tanggalMulai <= $now && (!$tanggalSelesai || $tanggalSelesai >= $now);
                                    $isPast = $tanggalSelesai && $now->isAfter($tanggalSelesai);
                                @endphp

                                <tr class="{{ $sedangBerlangsung ? 'jadwal-active' : '' }}">
                                    <td>{{$jadwal->step}}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($sedangBerlangsung)
                                                <i class="material-icons-outlined text-success me-2">play_circle</i>
                                            @elseif($isPast)
                                                <i class="material-icons-outlined text-muted me-2">check_circle</i>
                                            @else
                                                <i class="material-icons-outlined text-primary me-2">schedule</i>
                                            @endif
                                            <span>{{$jadwal->tahap}}</span>

                                            @if($sedangBerlangsung)
                                                <span class="badge bg-success ms-2">Sedang Berlangsung</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span>{{ format_date_indonesian($jadwal->tanggal_mulai) }}</span>
                                            <small class="text-muted">{{ Carbon\Carbon::parse($jadwal->tanggal_mulai)->format('H:i') }} WIB</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span>{{ format_date_indonesian($jadwal->tanggal_selesai) }}</span>
                                            <small class="text-muted">{{ Carbon\Carbon::parse($jadwal->tanggal_selesai)->format('H:i') }} WIB</small>
                                        </div>
                                    </td>
                                    <td>
                                        @if($jadwal->keterangan)
                                            <span class="badge bg-info d-inline-flex align-items-center" style="padding: 8px 12px;">
                                                <i class="material-icons-outlined me-2" style="font-size: 16px;">update</i>
                                                <span>{{$jadwal->keterangan}}</span>
                                            </span>
                                        @else
                                            -
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


    </div>

    <div class="col-md-3">
    @if(!empty($tender->keterangan))
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="section-title mb-0">Alasan Batal / Gagal / Diulang</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger d-flex align-items-start" role="alert">
                            <i class="material-icons-outlined me-2 mt-1" style="font-size: 20px;">info</i>
                            <div>
                                {{ $tender->keterangan }}
                            </div>
                        </div>
                    </div>
                </div>
        @endif

@if($tender->pemenang && $tender->pemenang->count() > 0)
    @foreach($tender->pemenang as $pemenang)
        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="section-title mb-0">HPS Kontrak</h5>
                @php
                    $nilaiPenawaran = $pemenang->harga_penawaran;
                    if ($pemenang->harga_penawaran < 1){
                        $nilaiPenawaran = $pemenang->harga_terkoreksi;
                    }

                    $decreaseP = (($tender->hps - $nilaiPenawaran) / $tender->hps) * 100;
                    $badgeClass = 'bg-danger';
                    $icon = 'trending_down';

                    if (number_format($decreaseP, 2) == 0.00) {
                        $badgeClass = 'bg-success';
                        $icon = 'drag_handle';
                    } elseif ($decreaseP > 0) {
                        $badgeClass = 'bg-danger';
                        $icon = 'trending_down';
                    }
                @endphp
                <span class="badge {{ $badgeClass }} d-inline-flex align-items-center">
                    <i class="material-icons-outlined me-1" style="font-size: 14px;">{{ $icon }}</i>
                    <span>{{ number_format($decreaseP, 2) }}%</span>
                </span>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="material-icons-outlined text-primary me-2" style="font-size: 24px;">business</i>
                    <h6 class="mb-0 fw-semibold">{{ $pemenang->peserta->nama_peserta }}</h6>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-semibold fs-5 text-primary">{{ convert_to_rupiah($nilaiPenawaran) }}</span>
                    <span class="badge bg-light text-dark">{{ 100 - number_format($decreaseP, 2) }}% dari HPS</span>
                </div>

                <div class="progress" style="height: 8px;">
                    <div class="progress-bar bg-primary" style="width: {{ 100 - number_format($decreaseP, 2) }}%"></div>
                </div>
            </div>
        </div>
    @endforeach
@endif



            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="section-title mb-0">Peserta Tender</h5>
                    <div class="d-flex align-items-center gap-2">
                        <span class="badge bg-primary d-inline-flex align-items-center">
                            <i class="material-icons-outlined me-1" style="font-size: 14px;">groups</i>
                            <span>{{ $tender->peserta->count() }} Peserta</span>
                        </span>

                        @if(auth()->user()->account_type === 'Super Admin')
                        <button id="syncPesertaButton" class="btn btn-sm btn-outline-primary d-flex align-items-center gap-1">
                            <i class="material-icons-outlined" style="font-size: 16px;">sync</i>
                            <span>Sync</span>
                        </button>

                        <!-- Modal -->
                        <div class="modal fade" id="syncPesertaModal" tabindex="-1" aria-labelledby="syncPesertaModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="syncPesertaModalLabel">Sync Peserta Output</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <pre id="syncPesertaOutput"></pre>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="setTimeout(() => location.reload(), 500)">Close</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <div class="card-body">
                    @if($tender->peserta->isNotEmpty())
                    @php
                    $pesertas = $tender->peserta;
                    @endphp
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr class="tenderid_header">
                                    <th style="width: 10%;">No</th>
                                    <th style="width: 90%;">Nama Perusahaan</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($pesertas as $index => $peserta)
                                    @php
                                        $isWinner = false;
                                        $icon = '';

                                        if ($tender->pemenang && $tender->pemenang->count() == 1) {
                                            if ($peserta->perusahaan->nama_peserta == $pemenang->peserta->nama_peserta) {
                                                $isWinner = true;
                                            }
                                        }
                                        elseif ($tender->pemenang && $tender->pemenang->count() > 1) {
                                            foreach($tender->pemenang as $pemenang){
                                                if ($peserta->perusahaan->nama_peserta == $pemenang->peserta->nama_peserta) {
                                                    $isWinner = true;
                                                }
                                            }
                                        }
                                    @endphp
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($isWinner)
                                                <i class="material-icons-outlined text-warning me-2" style="font-size: 18px;">emoji_events</i>
                                                @else
                                                <i class="material-icons-outlined text-primary me-2" style="font-size: 18px;">business</i>
                                                @endif
                                                <a href="{!! route('perusahaan.show', ['pembeda' => encrypt($peserta->perusahaan->pembeda)]) !!}" class="{{ $isWinner ? 'fw-bold' : '' }}">
                                                    {!! $peserta->perusahaan->nama_peserta !!}
                                                </a>
                                                @if($isWinner)
                                                <span class="badge bg-warning text-dark ms-2">Pemenang</span>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="alert alert-info d-flex align-items-center" role="alert">
                        <i class="material-icons-outlined me-2">info</i>
                        <div>Belum ada data peserta tender.</div>
                    </div>
                    @endif
                </div>
            </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.getElementById('copyButton').addEventListener('click', function () {
        var shareUrl = document.getElementById('shareUrl');
        var copyBtn = this;
        var originalContent = copyBtn.innerHTML;

        // Pilih teks secara manual
        shareUrl.select();
        shareUrl.setSelectionRange(0, 99999); // Untuk mendukung perangkat mobile

        // Tampilkan animasi loading
        copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 spin">sync</i>';
        copyBtn.disabled = true;

        // Salin teks dengan cara modern jika tersedia
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(shareUrl.value)
                .then(() => {
                    // Tampilkan animasi sukses
                    copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 text-white">check</i>';
                    copyBtn.classList.add('copy-success');

                    setTimeout(function() {
                        copyBtn.innerHTML = originalContent;
                        copyBtn.classList.remove('copy-success');
                        copyBtn.disabled = false;
                    }, 2000);

                    showToast('Link berhasil disalin ke clipboard', 'success');
                })
                .catch(err => {
                    console.error('Gagal menyalin link: ', err);

                    // Tampilkan animasi error
                    copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 text-white">error</i>';
                    copyBtn.classList.add('copy-error');

                    setTimeout(function() {
                        copyBtn.innerHTML = originalContent;
                        copyBtn.classList.remove('copy-error');
                        copyBtn.disabled = false;
                    }, 2000);

                    showToast('Gagal menyalin link', 'error');
                });
        } else {
            // Fallback ke metode lama jika Clipboard API tidak tersedia
            try {
                var successful = document.execCommand('copy');

                if (successful) {
                    // Tampilkan animasi sukses
                    copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 text-white">check</i>';
                    copyBtn.classList.add('copy-success');
                    showToast('Link berhasil disalin ke clipboard', 'success');
                } else {
                    // Tampilkan animasi error
                    copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 text-white">error</i>';
                    copyBtn.classList.add('copy-error');
                    showToast('Gagal menyalin link', 'error');
                }

                setTimeout(function() {
                    copyBtn.innerHTML = originalContent;
                    copyBtn.classList.remove('copy-success', 'copy-error');
                    copyBtn.disabled = false;
                }, 2000);

            } catch (err) {
                console.error('Gagal menyalin link: ', err);

                // Tampilkan animasi error
                copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 text-white">error</i>';
                copyBtn.classList.add('copy-error');

                setTimeout(function() {
                    copyBtn.innerHTML = originalContent;
                    copyBtn.classList.remove('copy-error');
                    copyBtn.disabled = false;
                }, 2000);

                showToast('Gagal menyalin link', 'error');
            }
        }
    });

    // Tambahkan event listener untuk tombol "More"
    document.getElementById('shareMore').addEventListener('click', function() {
        // Implementasi untuk menampilkan opsi berbagi tambahan
        showToast('Fitur opsi berbagi tambahan akan segera hadir', 'info');
    });

</script>
<script>
  $(document).ready(function () {
    $('.follow-btn, .unfollow-btn').click(function () {
      var btn = $(this);
      var id = btn.data('tender-id') || btn.data('lpse-id');
      var type = btn.data('tender-id') ? 'tender' : 'lpse';
      var action = btn.hasClass('follow-btn') ? 'follow' : 'unfollow';
      var url = `/${action}/${type}/${id}`;

      // Simpan konten asli tombol
      var originalHtml = btn.html();

      // Tampilkan loading state dengan animasi pulse
      btn.html(`<div class="d-flex align-items-center gap-1"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> <span>Loading...</span></div>`);
      btn.prop('disabled', true);

      $.post(url, {
        _token: '{{ csrf_token() }}'
      }, function (response) {
        if (response.status === 'followed' || response.status === 'unfollowed') {
          if (action === 'follow') {
            // Animasi untuk follow
            btn.removeClass('btn-outline-primary follow-btn').addClass('btn-warning unfollow-btn');
            btn.html(`<i class="material-icons-outlined follow-icon">star</i><span>Unfollow</span>`);

            // Tambahkan animasi pulse pada icon
            $('.follow-icon').css('animation', 'pulse 0.5s ease');

            // Tampilkan toast
            showToast('Tender berhasil ditambahkan ke daftar pantauan', 'success');
          } else {
            // Animasi untuk unfollow
            btn.removeClass('btn-warning unfollow-btn').addClass('btn-outline-primary follow-btn');
            btn.html(`<i class="material-icons-outlined follow-icon">star_border</i><span>Follow</span>`);

            // Tampilkan toast
            showToast('Tender dihapus dari daftar pantauan', 'info');
          }
        } else {
          // Kembalikan ke tampilan semula jika gagal
          btn.html(originalHtml);
          showToast('Gagal memperbarui status follow', 'error');
        }
        btn.prop('disabled', false);
      }).fail(function() {
        // Kembalikan ke tampilan semula jika error
        btn.html(originalHtml);
        btn.prop('disabled', false);
        showToast('Terjadi kesalahan saat memproses permintaan', 'error');
      });
    });

    // Function to show toast notification
    function showToast(message, type = 'success') {
      // Check if toast container exists
      let toastContainer = document.getElementById('toast-container');
      if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
      }

      // Set toast color based on type
      let bgColor = 'bg-success';
      let icon = 'check_circle';

      if (type === 'error') {
        bgColor = 'bg-danger';
        icon = 'error';
      } else if (type === 'warning') {
        bgColor = 'bg-warning text-dark';
        icon = 'warning';
      } else if (type === 'info') {
        bgColor = 'bg-info text-dark';
        icon = 'info';
      }

      // Create toast element
      const toastId = 'toast-' + Date.now();
      const toast = document.createElement('div');
      toast.className = `toast align-items-center ${bgColor} text-white border-0`;
      toast.id = toastId;
      toast.setAttribute('role', 'alert');
      toast.setAttribute('aria-live', 'assertive');
      toast.setAttribute('aria-atomic', 'true');

      toast.innerHTML = `
        <div class="d-flex">
          <div class="toast-body">
            <i class="material-icons-outlined me-2">
              ${icon}
            </i>
            ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      `;

      toastContainer.appendChild(toast);

      // Initialize and show toast
      const bsToast = new bootstrap.Toast(toast, {
        animation: true,
        autohide: true,
        delay: 3000
      });

      bsToast.show();

      // Remove toast after it's hidden
      toast.addEventListener('hidden.bs.toast', function () {
        toast.remove();
      });
    }
  });

  $(window).on('load', function () {
    var tenderUrl = "{{ $tender->lpse->url }}/lelang/{{ $tender->kode_tender }}/pengumumanlelang";

    // Lakukan request AJAX ke controller untuk mengambil link setelah halaman selesai dimuat
    $.ajax({
      url: '/tender/getUraianFile',
      type: 'GET',
      data: {
        url: tenderUrl
      },
      success: function(response) {
        // Kosongkan konten div #dlUraian sebelum menambahkan konten baru
        $('#dlUraian').empty();

        if (response.success) {
            // Menampilkan link di dalam div#dlUraian
            var uraianLink = response.uraian_link;
            var fileName = response.filename.trim(); // Menghapus spasi ekstra di nama file
            var refresh = response.refresh;

            if (refresh == 'Yes') {
                document.location.reload();
            }

            // Membuat elemen <a> dengan class 'btn' dan menambahkan href
            var linkElement = $('<a>')
                .addClass('btn btn-primary btn-sm') // Menambahkan kelas 'btn btn-primary' untuk styling
                .attr('href', uraianLink)    // Menetapkan URL yang ditemukan
                .attr('download', '')        // Menambahkan atribut 'download' untuk memulai unduhan
                .html('<i class="bi bi-file-pdf-fill me-2"></i>' + fileName);             // Menetapkan teks nama file

            // Menambahkan elemen <a> ke dalam div#dlUraian
            $('#dlUraian').append(linkElement); // Menambahkan link baru

        } else {
            // Menampilkan pesan kesalahan di dalam div#dlUraian
            $('#dlUraian').append('<p>Link Uraian Singkat Pekerjaan tidak ditemukan.</p>');
        }
    },
    error: function() {
        // Menampilkan pesan kesalahan jika terjadi masalah saat mengambil data
        $('#dlUraian').empty(); // Kosongkan isi div jika terjadi error
        $('#dlUraian').append('<p>Terjadi kesalahan saat mengambil data.</p>');
    }
    });
  });
</script>

@if(auth()->user()->account_type === 'Super Admin')
<script>
    document.getElementById('syncPesertaButton').addEventListener('click', function () {
        var kodeTender = "{{ $tender->kode_tender }}";
        var button = this;

        // Disable button and show loading state
        button.disabled = true;
        var originalText = button.innerHTML;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Syncing...';

        $.ajax({
            url: '/tender/sync_peserta/' + kodeTender,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function (response) {
                // Re-enable button and restore text
                button.disabled = false;
                button.innerHTML = originalText;

                if (response.success) {
                    $('#syncPesertaOutput').text(response.output);

                    // If process is running in background, show a notification
                    if (response.isRunning) {
                        // Notification is already included in the response message
                    }

                    // Refresh the page after 5 seconds to show updated data
                    $('#syncPesertaModal').on('hidden.bs.modal', function () {
                        location.reload();
                    });

                    // Set a timer to auto-refresh if user doesn't close the modal
                    setTimeout(function() {
                        location.reload();
                    }, 15000);
                } else {
                    $('#syncPesertaOutput').text('Error: ' + response.error);
                }
                $('#syncPesertaModal').modal('show');
            },
            error: function (xhr, status, error) {
                // Re-enable button and restore text
                button.disabled = false;
                button.innerHTML = originalText;

                $('#syncPesertaOutput').text('Terjadi kesalahan saat menjalankan Sync Peserta. Error : ' + error);
                $('#syncPesertaModal').modal('show');
                console.error(error);
            }
        });
    });

    document.getElementById('syncJadwalButton').addEventListener('click', function () {
        var kodeTender = "{{ $tender->kode_tender }}";
        var button = this;

        // Disable button and show loading state
        button.disabled = true;
        var originalText = button.innerHTML;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Syncing...';

        $.ajax({
            url: '/tender/sync_jadwal/' + kodeTender,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function (response) {
                // Re-enable button and restore text
                button.disabled = false;
                button.innerHTML = originalText;

                if (response.success) {
                    $('#syncJadwalOutput').text(response.output);

                    // If process is running in background, show a notification
                    if (response.isRunning) {
                        // Notification is already included in the response message
                    }

                    // Refresh the page after 5 seconds to show updated data
                    $('#syncJadwalModal').on('hidden.bs.modal', function () {
                        location.reload();
                    });

                    // Set a timer to auto-refresh if user doesn't close the modal
                    setTimeout(function() {
                        location.reload();
                    }, 15000);
                } else {
                    $('#syncJadwalOutput').text('Error: ' + response.error);
                }
                $('#syncJadwalModal').modal('show');
            },
            error: function (xhr, status, error) {
                // Re-enable button and restore text
                button.disabled = false;
                button.innerHTML = originalText;

                $('#syncJadwalOutput').text('Terjadi kesalahan saat menjalankan Sync Jadwal. Error : ' + error);
                $('#syncJadwalModal').modal('show');
                console.error(error);
            }
        });
    });
</script>
@endif

<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<!-- Leaflet Marker Cluster JS -->
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

<script>
    // Inisialisasi peta Leaflet untuk lokasi proyek
    document.addEventListener('DOMContentLoaded', function() {
        // Cek apakah ada container peta
        const mapContainer = document.getElementById('project-location-map');
        if (!mapContainer) return;

        // Inisialisasi peta dengan pusat di Indonesia
        const map = L.map('project-location-map', {
            zoomControl: true,
            scrollWheelZoom: false // Disable zoom dengan scroll untuk mencegah zoom yang tidak diinginkan
        }).setView([-2.5489, 118.0149], 5);

        // Tambahkan tile layer (OpenStreetMap)
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Enable zoom dengan scroll saat mouse hover di atas peta
        map.on('mouseover', function() {
            map.scrollWheelZoom.enable();
        });

        // Disable zoom dengan scroll saat mouse meninggalkan peta
        map.on('mouseout', function() {
            map.scrollWheelZoom.disable();
        });

        // Custom marker icon untuk kabupaten/kota
        const defaultIcon = L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-blue.png',
            shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        });

        // Custom marker icon untuk lokasi spesifik proyek
        const accurateIcon = L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png',
            shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
            iconSize: [27, 44], // Sedikit lebih besar untuk lokasi spesifik
            iconAnchor: [13, 44],
            popupAnchor: [1, -40],
            shadowSize: [41, 41]
        });

        // Inisialisasi marker cluster group
        const markers = L.markerClusterGroup({
            showCoverageOnHover: false,
            maxClusterRadius: 50,
            spiderfyOnMaxZoom: true
        });

        // Tambahkan marker untuk setiap lokasi
        @if($tender->lokasiPaket->count() > 0)
            // Tambahkan marker cluster ke peta
            map.addLayer(markers);

            // Tambahkan marker default untuk setiap lokasi
            @foreach($tender->lokasiPaket as $index => $lokasi)
                // Gunakan koordinat default berdasarkan wilayah Indonesia
                @php
                    // Koordinat default untuk beberapa provinsi di Indonesia
                    $defaultCoords = [
                        'ACEH' => [4.6951, 96.7494],
                        'SUMATERA UTARA' => [2.1154, 99.5451],
                        'SUMATERA BARAT' => [-0.7399, 100.8000],
                        'RIAU' => [0.2933, 101.7068],
                        'JAMBI' => [-1.4852, 102.4381],
                        'SUMATERA SELATAN' => [-3.3194, 103.9144],
                        'BENGKULU' => [-3.5778, 102.3464],
                        'LAMPUNG' => [-4.5586, 105.4068],
                        'KEPULAUAN BANGKA BELITUNG' => [-2.7411, 106.4406],
                        'KEPULAUAN RIAU' => [3.9457, 108.1429],
                        'DKI JAKARTA' => [-6.2088, 106.8456],
                        'JAWA BARAT' => [-7.0909, 107.6689],
                        'JAWA TENGAH' => [-7.1510, 110.1403],
                        'DI YOGYAKARTA' => [-7.7956, 110.3695],
                        'JAWA TIMUR' => [-7.5361, 112.2384],
                        'BANTEN' => [-6.4058, 106.0640],
                        'BALI' => [-8.4095, 115.1889],
                        'NUSA TENGGARA BARAT' => [-8.6529, 117.3616],
                        'NUSA TENGGARA TIMUR' => [-8.6574, 121.0794],
                        'KALIMANTAN BARAT' => [-0.2788, 111.4753],
                        'KALIMANTAN TENGAH' => [-1.6815, 113.3824],
                        'KALIMANTAN SELATAN' => [-3.0926, 115.2838],
                        'KALIMANTAN TIMUR' => [0.5387, 116.4194],
                        'KALIMANTAN UTARA' => [3.0731, 116.0414],
                        'SULAWESI UTARA' => [0.6247, 123.9750],
                        'SULAWESI TENGAH' => [-1.4300, 121.4457],
                        'SULAWESI SELATAN' => [-3.6688, 119.9741],
                        'SULAWESI TENGGARA' => [-4.1449, 122.1746],
                        'GORONTALO' => [0.6999, 122.4467],
                        'SULAWESI BARAT' => [-2.8441, 119.2321],
                        'MALUKU' => [-3.2385, 130.1453],
                        'MALUKU UTARA' => [0.6301, 127.9725],
                        'PAPUA BARAT' => [-1.3361, 133.1747],
                        'PAPUA' => [-4.2699, 138.0804]
                    ];

                    // Database koordinat untuk kabupaten/kota di Indonesia
                    $kabupatenCoords = [
                        // Bali
                        'BADUNG' => [-8.5826, 115.1771],
                        'BANGLI' => [-8.4547, 115.3548],
                        'BULELENG' => [-8.1129, 115.0883],
                        'DENPASAR' => [-8.6705, 115.2126],
                        'GIANYAR' => [-8.4225, 115.2598],
                        'JEMBRANA' => [-8.3235, 114.6664],
                        'KARANGASEM' => [-8.3884, 115.5378],
                        'KLUNGKUNG' => [-8.5386, 115.4089],
                        'TABANAN' => [-8.5446, 115.1312],

                        // Jawa Barat
                        'BANDUNG' => [-7.1051, 107.6692],
                        'BEKASI' => [-6.2349, 107.0013],
                        'BOGOR' => [-6.5944, 106.7892],
                        'CIANJUR' => [-7.3572, 107.1951],
                        'CIREBON' => [-6.7161, 108.5607],
                        'GARUT' => [-7.5012, 107.7636],
                        'KARAWANG' => [-6.3227, 107.3375],
                        'KUNINGAN' => [-6.9757, 108.4837],
                        'MAJALENGKA' => [-6.8360, 108.2285],
                        'PURWAKARTA' => [-6.5649, 107.4322],
                        'SUBANG' => [-6.5695, 107.7539],
                        'SUKABUMI' => [-6.9277, 106.9300],
                        'SUMEDANG' => [-6.8562, 107.9135],
                        'TASIKMALAYA' => [-7.3274, 108.2207],
                        'CIAMIS' => [-7.3274, 108.3489],
                        'CIMAHI' => [-6.8840, 107.5413],
                        'DEPOK' => [-6.4025, 106.7942],

                        // DKI Jakarta
                        'JAKARTA PUSAT' => [-6.1753, 106.8271],
                        'JAKARTA UTARA' => [-6.1339, 106.8823],
                        'JAKARTA BARAT' => [-6.1683, 106.7588],
                        'JAKARTA SELATAN' => [-6.2615, 106.8106],
                        'JAKARTA TIMUR' => [-6.2250, 106.9004],
                        'KEPULAUAN SERIBU' => [-5.6122, 106.6171],

                        // Jawa Tengah
                        'BANJARNEGARA' => [-7.3971, 109.6913],
                        'BANYUMAS' => [-7.5246, 109.2995],
                        'BATANG' => [-7.0392, 109.9021],
                        'BLORA' => [-7.0123, 111.3798],
                        'BOYOLALI' => [-7.5319, 110.5947],
                        'BREBES' => [-6.9724, 109.0407],
                        'CILACAP' => [-7.7267, 109.0152],
                        'DEMAK' => [-6.8946, 110.6384],
                        'GROBOGAN' => [-7.0134, 110.9177],
                        'JEPARA' => [-6.5827, 110.6676],
                        'KARANGANYAR' => [-7.5986, 110.9353],
                        'KEBUMEN' => [-7.6681, 109.6528],
                        'KENDAL' => [-7.0265, 110.1879],
                        'KLATEN' => [-7.7022, 110.6031],
                        'KUDUS' => [-6.8051, 110.8418],
                        'MAGELANG' => [-7.4797, 110.2177],
                        'PATI' => [-6.7559, 111.0461],
                        'PEKALONGAN' => [-7.0051, 109.5918],
                        'PEMALANG' => [-6.8898, 109.3799],
                        'PURBALINGGA' => [-7.3059, 109.4268],
                        'PURWOREJO' => [-7.7130, 110.0085],
                        'REMBANG' => [-6.7084, 111.3422],
                        'SEMARANG' => [-7.0051, 110.4381],
                        'SRAGEN' => [-7.4278, 111.0296],
                        'SUKOHARJO' => [-7.6484, 110.8551],
                        'TEGAL' => [-6.8694, 109.1402],
                        'TEMANGGUNG' => [-7.3155, 110.1742],
                        'WONOGIRI' => [-7.8138, 110.9231],
                        'WONOSOBO' => [-7.3632, 109.9003],
                        'SALATIGA' => [-7.3305, 110.5084],
                        'SURAKARTA' => [-7.5655, 110.8280],

                        // Jawa Timur
                        'BANGKALAN' => [-7.0297, 112.7490],
                        'BANYUWANGI' => [-8.2191, 114.3691],
                        'BLITAR' => [-8.0955, 112.1609],
                        'BOJONEGORO' => [-7.1502, 111.8817],
                        'BONDOWOSO' => [-7.9674, 113.9060],
                        'GRESIK' => [-7.1542, 112.6528],
                        'JEMBER' => [-8.1845, 113.6681],
                        'JOMBANG' => [-7.5539, 112.2261],
                        'KEDIRI' => [-7.8480, 112.0178],
                        'LAMONGAN' => [-7.1195, 112.4165],
                        'LUMAJANG' => [-8.1339, 113.2226],
                        'MADIUN' => [-7.6311, 111.5300],
                        'MAGETAN' => [-7.6433, 111.3522],
                        'MALANG' => [-8.0051, 112.6200],
                        'MOJOKERTO' => [-7.4722, 112.4401],
                        'NGANJUK' => [-7.6050, 111.9046],
                        'NGAWI' => [-7.4312, 111.3290],
                        'PACITAN' => [-8.1911, 111.1005],
                        'PAMEKASAN' => [-7.1620, 113.4746],
                        'PASURUAN' => [-7.6469, 112.9030],
                        'PONOROGO' => [-7.8651, 111.4696],
                        'PROBOLINGGO' => [-7.7764, 113.2037],
                        'SAMPANG' => [-7.0402, 113.2394],
                        'SIDOARJO' => [-7.4726, 112.6675],
                        'SITUBONDO' => [-7.7029, 114.0095],
                        'SUMENEP' => [-7.0080, 113.8647],
                        'TRENGGALEK' => [-8.0501, 111.7068],
                        'TUBAN' => [-6.8955, 112.0298],
                        'TULUNGAGUNG' => [-8.0912, 111.9642],
                        'BATU' => [-7.8672, 112.5239],
                        'SURABAYA' => [-7.2575, 112.7521]
                    ];

                    // Set default koordinat Indonesia
                    $lat = -2.5489;
                    $lon = 118.0149;

                    // Coba dapatkan koordinat berdasarkan kabupaten/kota
                    $kabupatenKota = strtoupper(trim($lokasi->kbp_nama));
                    if (array_key_exists($kabupatenKota, $kabupatenCoords)) {
                        $lat = $kabupatenCoords[$kabupatenKota][0];
                        $lon = $kabupatenCoords[$kabupatenKota][1];
                    } else {
                        // Jika kabupaten/kota tidak ditemukan, gunakan koordinat provinsi
                        $provinsi = strtoupper(trim($lokasi->prp_nama));
                        if (array_key_exists($provinsi, $defaultCoords)) {
                            $lat = $defaultCoords[$provinsi][0];
                            $lon = $defaultCoords[$provinsi][1];
                        }
                    }

                    // Log untuk debugging
                    error_log("Lokasi: {$lokasi->pkt_lokasi}, Kabupaten: {$kabupatenKota}, Provinsi: {$provinsi}, Koordinat: [{$lat}, {$lon}]");
                @endphp

                // Tambahkan marker dengan koordinat kabupaten/kota
                var marker{{ $index }} = L.marker([{{ $lat }}, {{ $lon }}], {icon: defaultIcon})
                    .bindPopup(`
                        <div class="popup-content">
                            <strong>{{ $lokasi->pkt_lokasi }}</strong><br>
                            <div class="location-info">
                                <i class="material-icons-outlined" style="font-size: 14px;">place</i>
                                {{ $lokasi->kbp_nama }}, {{ $lokasi->prp_nama }}
                            </div>
                            <small class="text-info">Lokasi berdasarkan kabupaten/kota</small>
                        </div>
                    `);

                markers.addLayer(marker{{ $index }});

                // Coba geocode lokasi spesifik menggunakan Nominatim dengan delay untuk menghindari rate limiting
                setTimeout(() => {
                    // Gunakan lokasi spesifik jika tersedia, jika tidak gunakan kabupaten/kota
                    const lokasiSpesifik = "{{ trim($lokasi->pkt_lokasi) }}";
                    const kabupatenKota = "{{ strtolower(trim($lokasi->kbp_nama)) }}";

                    // Hanya lakukan geocoding jika lokasi spesifik berbeda dari kabupaten/kota dan bukan tanda "-"
                    if (lokasiSpesifik &&
                        lokasiSpesifik.toLowerCase() !== kabupatenKota &&
                        lokasiSpesifik !== "-" &&
                        lokasiSpesifik.length > 3) {

                        // Tambahkan provinsi dan kabupaten ke query untuk meningkatkan akurasi
                        const searchQuery = `{{ urlencode(trim($lokasi->pkt_lokasi)) }}+{{ urlencode(trim($lokasi->kbp_nama)) }}+{{ urlencode(trim($lokasi->prp_nama)) }}+Indonesia`;

                        // Gunakan parameter countrycodes=id untuk membatasi pencarian hanya di Indonesia
                        fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${searchQuery}&countrycodes=id&limit=1`)
                            .then(response => response.json())
                            .then(data => {
                                if (data && data.length > 0) {
                                    const lat = parseFloat(data[0].lat);
                                    const lon = parseFloat(data[0].lon);

                                    // Validasi koordinat - pastikan masih di Indonesia
                                    if (lat >= -11 && lat <= 6 && lon >= 95 && lon <= 141) {
                                        // Hapus marker default
                                        markers.removeLayer(marker{{ $index }});

                                        // Tambahkan marker baru dengan koordinat yang akurat
                                        const accurateMarker = L.marker([lat, lon], {icon: accurateIcon})
                                            .bindPopup(`
                                                <div class="popup-content">
                                                    <strong>{{ $lokasi->pkt_lokasi }}</strong><br>
                                                    <div class="location-info">
                                                        <i class="material-icons-outlined" style="font-size: 14px;">place</i>
                                                        {{ $lokasi->kbp_nama }}, {{ $lokasi->prp_nama }}
                                                    </div>
                                                    <small class="text-success">Lokasi spesifik proyek</small>
                                                </div>
                                            `);

                                        markers.addLayer(accurateMarker);

                                        // Jika hanya ada satu lokasi, zoom ke lokasi tersebut
                                        @if($tender->lokasiPaket->count() == 1)
                                            map.setView([lat, lon], 12); // Zoom lebih dekat untuk lokasi spesifik
                                        @endif

                                        // Fit bounds jika ada lebih dari satu lokasi
                                        @if($tender->lokasiPaket->count() > 1)
                                            map.fitBounds(markers.getBounds(), { padding: [50, 50] });
                                        @endif

                                        console.log(`Lokasi spesifik ditemukan untuk: {{ $lokasi->pkt_lokasi }} di [${lat}, ${lon}]`);
                                    } else {
                                        console.warn(`Koordinat di luar Indonesia untuk: {{ $lokasi->pkt_lokasi }} - [${lat}, ${lon}]`);
                                    }
                                } else {
                                    console.warn(`Tidak dapat menemukan lokasi spesifik untuk: {{ $lokasi->pkt_lokasi }}`);
                                }
                            })
                            .catch(error => {
                                console.error('Error geocoding:', error);
                            });
                    } else {
                        console.log(`Menggunakan koordinat kabupaten/kota untuk: {{ $lokasi->pkt_lokasi }}`);
                    }
                }, {{ $index * 1000 }}); // Delay setiap request dengan 1 detik untuk menghindari rate limiting
            @endforeach

            // Fit bounds untuk marker default
            map.fitBounds(markers.getBounds(), { padding: [50, 50] });
        @endif
    });

    // Copy Link Button
    document.addEventListener('DOMContentLoaded', function() {
        const copyButton = document.getElementById('copyButton');
        if (copyButton) {
            copyButton.addEventListener('click', function () {
                var shareUrl = document.getElementById('shareUrl');
                var copyBtn = this;
                var originalContent = copyBtn.innerHTML;

                // Pilih teks secara manual
                shareUrl.select();
                shareUrl.setSelectionRange(0, 99999); // Untuk mendukung perangkat mobile

                // Salin teks dengan cara modern jika tersedia
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(shareUrl.value)
                        .then(() => {
                            // Tampilkan animasi sukses
                            copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 text-white">check</i>';
                            copyBtn.classList.add('copy-success');

                            setTimeout(function() {
                                copyBtn.innerHTML = originalContent;
                                copyBtn.classList.remove('copy-success');
                                copyBtn.disabled = false;
                            }, 2000);

                            showToast('Link berhasil disalin ke clipboard', 'success');
                        })
                        .catch(err => {
                            console.error('Gagal menyalin link: ', err);

                            // Tampilkan animasi error
                            copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 text-white">error</i>';
                            copyBtn.classList.add('copy-error');

                            setTimeout(function() {
                                copyBtn.innerHTML = originalContent;
                                copyBtn.classList.remove('copy-error');
                                copyBtn.disabled = false;
                            }, 2000);

                            showToast('Gagal menyalin link', 'error');
                        });
                } else {
                    // Fallback ke metode lama jika Clipboard API tidak tersedia
                    try {
                        var successful = document.execCommand('copy');

                        if (successful) {
                            // Tampilkan animasi sukses
                            copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 text-white">check</i>';
                            copyBtn.classList.add('copy-success');
                            showToast('Link berhasil disalin ke clipboard', 'success');
                        } else {
                            // Tampilkan animasi error
                            copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 text-white">error</i>';
                            copyBtn.classList.add('copy-error');
                            showToast('Gagal menyalin link', 'error');
                        }

                        setTimeout(function() {
                            copyBtn.innerHTML = originalContent;
                            copyBtn.classList.remove('copy-success', 'copy-error');
                            copyBtn.disabled = false;
                        }, 2000);

                    } catch (err) {
                        console.error('Gagal menyalin link: ', err);

                        // Tampilkan animasi error
                        copyBtn.innerHTML = '<i class="material-icons-outlined fs-6 text-white">error</i>';
                        copyBtn.classList.add('copy-error');

                        setTimeout(function() {
                            copyBtn.innerHTML = originalContent;
                            copyBtn.classList.remove('copy-error');
                            copyBtn.disabled = false;
                        }, 2000);

                        showToast('Gagal menyalin link', 'error');
                    }
                }
            });
        }

        // Tambahkan event listener untuk tombol "More"
        const shareMore = document.getElementById('shareMore');
        if (shareMore) {
            shareMore.addEventListener('click', function() {
                // Implementasi untuk menampilkan opsi berbagi tambahan
                showToast('Fitur opsi berbagi tambahan akan segera hadir', 'info');
            });
        }
    });

    function showToast(message, type = 'info') {
        // Cek apakah container toast sudah ada
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        // Buat elemen toast
        const toastEl = document.createElement('div');
        toastEl.className = `toast align-items-center text-white bg-${type} border-0`;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');

        // Buat body toast
        const toastBody = document.createElement('div');
        toastBody.className = 'd-flex';

        const toastBodyContent = document.createElement('div');
        toastBodyContent.className = 'toast-body';
        toastBodyContent.textContent = message;

        const closeButton = document.createElement('button');
        closeButton.type = 'button';
        closeButton.className = 'btn-close btn-close-white me-2 m-auto';
        closeButton.setAttribute('data-bs-dismiss', 'toast');
        closeButton.setAttribute('aria-label', 'Close');

        toastBody.appendChild(toastBodyContent);
        toastBody.appendChild(closeButton);
        toastEl.appendChild(toastBody);

        // Tambahkan toast ke container
        toastContainer.appendChild(toastEl);

        // Inisialisasi toast
        const toast = new bootstrap.Toast(toastEl, {
            autohide: true,
            delay: 3000
        });

        // Tampilkan toast
        toast.show();

        // Hapus toast dari DOM setelah disembunyikan
        toastEl.addEventListener('hidden.bs.toast', function () {
            toastEl.remove();
        });
    }
</script>
@endsection