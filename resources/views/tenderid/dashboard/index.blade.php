@extends('layouts.master')

@section('title', 'Dashboard')

@section('css')
    <style>
        /* Loading state styling */
        .loading, .chart-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 300px;
            font-size: 18px;
            color: #9ba7b2;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            z-index: 10;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            border: 3px solid rgba(0, 123, 255, 0.1);
            border-radius: 50%;
            border-top: 3px solid #0d6efd;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Chart containers */
        #chartTender,
        #chartEkatalog,
        #chartKategoriPekerjaan,
        #chartStatusUMKM {
            position: relative;
            min-height: 300px;
        }

        #chartTenderContainer,
        #chartEkatalogContainer {
            height: 300px;
            width: 100%;
            border-radius: 8px;
            overflow: hidden;
        }

        /* Card styling */
        .dashboard-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 12px !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .dashboard-card .card-body {
            padding: 1.5rem;
        }

        .dashboard-card .card-title {
            color: #344767;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        /* Stat cards */
        .stat-card {
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            background-color: white;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        .stat-card .card-body {
            padding: 1.25rem;
        }

        .stat-card .stat-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .stat-card .blue-icon {
            background-color: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
        }

        .stat-card .purple-icon {
            background-color: rgba(111, 66, 193, 0.1);
            color: #6f42c1;
        }

        .stat-card .green-icon {
            background-color: rgba(25, 135, 84, 0.1);
            color: #198754;
        }

        .stat-card .orange-icon {
            background-color: rgba(253, 126, 20, 0.1);
            color: #fd7e14;
        }

        .stat-card .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            color: #344767;
        }

        .stat-card .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0;
        }

        .stat-card .stat-change {
            position: absolute;
            top: 1rem;
            right: 1rem;
            border-radius: 20px;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .stat-card .stat-change.up {
            background-color: rgba(25, 135, 84, 0.1);
            color: #198754;
        }

        .stat-card .stat-change.down {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .stat-card .stat-chart {
            margin-top: 10px;
            height: 60px;
            width: 100%;
        }

        /* List group styling */
        .list-group-item {
            border-color: #f1f1f1;
            padding: 1rem;
            transition: all 0.2s ease;
        }

        .list-group-item:hover {
            background-color: #f8f9fa;
        }

        .list-group-item .item-title {
            font-weight: 600;
            color: #344767;
            margin-bottom: 0.25rem;
            display: block;
        }

        .list-group-item .item-subtitle {
            color: #6c757d;
            font-size: 0.85rem;
        }

        .list-group-item .item-value {
            font-weight: 700;
            color: #0d6efd;
        }

        /* Badge styling */
        .badge-custom {
            padding: 0.35rem 0.65rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 6px;
        }

        .bg-purple {
            background-color: #6f42c1 !important;
            color: white !important;
        }

        .text-purple {
            color: #6f42c1 !important;
        }

        .btn-outline-purple {
            color: #6f42c1;
            border-color: #6f42c1;
        }

        .btn-outline-purple:hover {
            color: #fff;
            background-color: #6f42c1;
            border-color: #6f42c1;
        }

        /* Subscription card */
        .subscription-card {
            background: linear-gradient(45deg, #4481eb, #04befe);
            color: white;
            border-radius: 12px;
            overflow: hidden;
        }

        .subscription-card.warning {
            background: linear-gradient(45deg, #fb6340, #fbb140);
        }

        .subscription-card .card-body {
            position: relative;
            z-index: 1;
        }

        .subscription-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(50%, -50%);
            z-index: 0;
        }

        .subscription-card::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(-30%, 30%);
            z-index: 0;
        }

        /* Notification card */
        .notification-card {
            border-radius: 12px;
            overflow: hidden;
            background-color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .notification-item {
            padding: 1rem;
            border-bottom: 1px solid #f1f1f1;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .notification-content {
            flex-grow: 1;
        }

        .notification-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: #344767;
        }

        .notification-subtitle {
            color: #6c757d;
            font-size: 0.85rem;
            margin-bottom: 0;
        }

        /* Login activity styling */
        .login-activity {
            position: relative;
        }

        .login-activity-item {
            position: relative;
            padding-left: 2rem;
            margin-bottom: 1.5rem;
        }

        .login-activity-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #0d6efd;
            z-index: 1;
        }

        .login-activity-item::after {
            content: '';
            position: absolute;
            top: 12px;
            left: 5px;
            width: 2px;
            height: calc(100% + 1.5rem);
            background-color: #e9ecef;
            z-index: 0;
        }

        .login-activity-item:last-child::after {
            display: none;
        }

        .login-activity-content {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
        }

        .login-activity-title {
            font-weight: 600;
            color: #344767;
            margin-bottom: 0.25rem;
        }

        .login-activity-subtitle {
            color: #6c757d;
            font-size: 0.85rem;
            margin-bottom: 0;
        }
    </style>
@endsection

@section('content')

    @php
        $activeSubscription = Auth::user()->userSubscriptions()
            ->where('status', 'active')
            ->where('is_active', 1)
            ->where('end_date', '>=', now())
            ->first();

        if ($activeSubscription) {
            $user = Auth::user();
            $commissionBalance = $user->commission_balance ?? 0;
            $payment = $activeSubscription->payments()->latest()->first();
            $referralCount = $user->referrals()->count();
        }
    @endphp

    <div class="row mb-4">
        <!-- Subscription Information Card -->
        @if($activeSubscription)
        <div class="col-12 col-lg-8 mb-3 mb-lg-0">
            <div class="card subscription-card dashboard-card position-relative">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="wh-48 d-flex align-items-center justify-content-center bg-white rounded-circle shadow-sm">
                                <span class="material-icons-outlined text-primary">verified</span>
                            </div>
                        </div>
                        <div class="col">
                            <h5 class="mb-0 text-white fw-bold">{{ $activeSubscription->subscription->name }}</h5>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-white text-primary me-2">Aktif</span>
                                <small class="text-white opacity-75">Berakhir pada {{ \Carbon\Carbon::parse($activeSubscription->end_date)->format('d M Y') }}</small>
                            </div>
                        </div>
                        <div class="col-auto mt-3 mt-md-0">
                            <div class="d-flex flex-wrap gap-4">
                                <div class="text-center">
                                    <div class="fs-4 fw-bold text-white">
                                        {{ floor(now()->floatDiffInDays($activeSubscription->end_date)) }}
                                    </div>
                                    <small class="text-white opacity-75">Hari Tersisa</small>
                                </div>
                                <div class="text-center">
                                    <div class="fs-4 fw-bold text-white">Rp
                                        {{ number_format($activeSubscription->total_amount, 0, ',', '.') }}
                                    </div>
                                    <small class="text-white opacity-75">Total Bayar</small>
                                </div>
                                <div class="text-center">
                                    <div class="fs-4 fw-bold text-white">{{ $referralCount }}</div>
                                    <small class="text-white opacity-75">Referral</small>
                                </div>
                                <div class="text-center">
                                    <div class="fs-4 fw-bold text-white">Rp {{ number_format($commissionBalance, 0, ',', '.') }}</div>
                                    <small class="text-white opacity-75">Saldo Komisi</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mt-4 text-end">
                            <div class="d-flex justify-content-end gap-2">
                                @if($commissionBalance > 0)
                                    <a href="{{ route('withdrawals.request') }}"
                                        class="btn btn-light text-primary d-flex align-items-center gap-1">
                                        <span class="material-icons-outlined" style="font-size: 16px;">monetization_on</span>
                                        <span>Tarik Saldo</span>
                                    </a>
                                @endif
                                <a href="{{ route('subscriptions.select') }}" class="btn btn-light text-primary d-flex align-items-center gap-1">
                                    <span class="material-icons-outlined" style="font-size: 16px;">card_membership</span>
                                    <span>Lihat Paket</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @else
        <div class="col-12 col-lg-8 mb-3 mb-lg-0">
            <div class="card subscription-card warning dashboard-card position-relative">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="wh-48 d-flex align-items-center justify-content-center bg-white rounded-circle shadow-sm">
                                <span class="material-icons-outlined text-warning">warning</span>
                            </div>
                        </div>
                        <div class="col">
                            <h5 class="mb-0 text-white fw-bold">Anda belum memiliki langganan aktif</h5>
                            <small class="text-white opacity-75">Silakan pilih paket langganan untuk mengakses semua fitur</small>
                        </div>
                        <div class="col-12 col-md-auto mt-3 mt-md-0">
                            <a href="{{ route('subscriptions.select') }}" class="btn btn-light text-warning d-flex align-items-center gap-1">
                                <span class="material-icons-outlined" style="font-size: 16px;">shopping_cart</span>
                                <span>Pilih Paket</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Notification Settings Card -->
        <div class="col-12 col-lg-4">
            <div class="card notification-card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center">
                    <i class="material-icons-outlined text-primary me-2">notifications</i>
                    <h5 class="card-title mb-0">Pengaturan Notifikasi</h5>
                </div>
                <div class="card-body p-0">
                    <!-- Telegram Notification -->
                    <div class="notification-item">
                        <div class="notification-icon {{ Auth::user()->telegram_bot ? 'bg-success' : 'bg-warning' }}">
                            <i class="material-icons-outlined text-white">{{ Auth::user()->telegram_bot ? 'notifications_active' : 'notifications_off' }}</i>
                        </div>
                        <div class="notification-content">
                            <h6 class="notification-title">Telegram</h6>
                            <p class="notification-subtitle">{{ Auth::user()->telegram_bot ? 'Terhubung' : 'Belum terhubung' }}</p>
                        </div>
                        <div>
                            @if(Auth::user()->telegram_bot)
                                <span class="badge bg-success">Aktif</span>
                            @else
                                <a href="{{ route('telegram.link') }}" class="btn btn-sm btn-primary d-flex align-items-center gap-1">
                                    <i class="material-icons-outlined" style="font-size: 16px;">link</i>
                                    <span>Hubungkan</span>
                                </a>
                            @endif
                        </div>
                    </div>

                    <!-- WhatsApp Notification -->
                    <div class="notification-item">
                        <div class="notification-icon {{ Auth::user()->phone ? 'bg-success' : 'bg-warning' }}">
                            <i class="material-icons-outlined text-white">{{ Auth::user()->phone ? 'notifications_active' : 'notifications_off' }}</i>
                        </div>
                        <div class="notification-content">
                            <h6 class="notification-title">WhatsApp</h6>
                            <p class="notification-subtitle">{{ Auth::user()->phone ? 'Terhubung' : 'Belum terhubung' }}</p>
                        </div>
                        <div>
                            @if(Auth::user()->phone)
                                <span class="badge bg-success">Aktif</span>
                            @else
                                <a href="{{ route('tenderid.profile.edit') }}#phone" class="btn btn-sm btn-primary d-flex align-items-center gap-1">
                                    <i class="material-icons-outlined" style="font-size: 16px;">phone</i>
                                    <span>Tambah</span>
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Total Tender -->
        <div class="col-12 col-xl-3 col-md-6 mb-4">
            <div class="card stat-card dashboard-card">
                <div class="card-body" id="chart_tender01-container">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon blue-icon">
                                <i class="material-icons-outlined">assignment</i>
                            </div>
                            <div>
                                <h2 class="stat-value" id="totalTender">
                                    <div class="loading-spinner"></div> Loading...
                                </h2>
                                <p class="stat-label">Total Tender</p>
                            </div>
                        </div>
                        <div class="stat-change up" id="selisihTender">
                            <!-- Icon will be dynamically injected -->
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="mb-1 small text-muted">Trend 7 hari terakhir</p>
                        <div id="chart_tender01"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total SIRUP -->
        <div class="col-12 col-xl-3 col-md-6 mb-4">
            <div class="card stat-card dashboard-card">
                <div class="card-body" id="chart_sirup01-container">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon purple-icon">
                                <i class="material-icons-outlined">description</i>
                            </div>
                            <div>
                                <h2 class="stat-value" id="totalSirup">
                                    <div class="loading-spinner"></div> Loading...
                                </h2>
                                <p class="stat-label">Total SIRUP</p>
                            </div>
                        </div>
                        <div class="stat-change down" id="selisihSirup">
                            <!-- Icon will be dynamically injected -->
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="mb-1 small text-muted">Trend 7 hari terakhir</p>
                        <div id="chart_sirup01"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Perusahaan -->
        <div class="col-12 col-xl-3 col-md-6 mb-4">
            <div class="card stat-card dashboard-card">
                <div class="card-body" id="chart_perusahaan01-container">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon green-icon">
                                <i class="material-icons-outlined">business</i>
                            </div>
                            <div>
                                <h2 class="stat-value" id="totalPerusahaan">
                                    <div class="loading-spinner"></div> Loading...
                                </h2>
                                <p class="stat-label">Total Perusahaan</p>
                            </div>
                        </div>
                        <div class="stat-change up" id="selisihPerusahaan">
                            <!-- Icon will be dynamically injected -->
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="mb-1 small text-muted">Trend 7 hari terakhir</p>
                        <div id="chart_perusahaan01"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total E-Katalog -->
        <div class="col-12 col-xl-3 col-md-6 mb-4">
            <div class="card stat-card dashboard-card">
                <div class="card-body" id="chart_ekatalog01-container">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon orange-icon">
                                <i class="material-icons-outlined">shopping_cart</i>
                            </div>
                            <div>
                                <h2 class="stat-value" id="totalEKatalog">
                                    <div class="loading-spinner"></div> Loading...
                                </h2>
                                <p class="stat-label">Total E-Katalog</p>
                            </div>
                        </div>
                        <div class="stat-change up" id="selisihEKatalog">
                            <!-- Icon will be dynamically injected -->
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="mb-1 small text-muted">Trend 7 hari terakhir</p>
                        <div id="chart_ekatalog01"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end row-->

    <div class="row mb-4">
        <!-- Kategori Pekerjaan Chart -->
        <div class="col-12 col-xl-5 col-xxl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center">
                    <i class="material-icons-outlined text-primary me-2">pie_chart</i>
                    <h5 class="card-title mb-0">Tender - Kategori Pekerjaan</h5>
                </div>
                <div class="card-body">
                    <div id="chartKategoriPekerjaan">
                        <div class="chart-loading">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading chart...</span>
                        </div>
                        <div id="chartKategoriPekerjaanContainer"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tender Chart -->
        <div class="col-12 col-xl-7 col-xxl-8 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center">
                    <i class="material-icons-outlined text-primary me-2">bar_chart</i>
                    <h5 class="card-title mb-0">Tender Chart</h5>
                </div>
                <div class="card-body">
                    <div id="chartTender">
                        <div class="chart-loading">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading chart...</span>
                        </div>
                        <div id="chartTenderContainer"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end row-->

    <div class="row mb-4">
        <!-- Sebaran Paket UMKM Chart -->
        <div class="col-12 col-xl-5 col-xxl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center">
                    <i class="material-icons-outlined text-primary me-2">donut_large</i>
                    <h5 class="card-title mb-0">Ekatalog - Sebaran Paket UMKM</h5>
                </div>
                <div class="card-body">
                    <div id="chartStatusUMKM">
                        <div class="chart-loading">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading chart...</span>
                        </div>
                        <div id="chartStatusUMKMContainer"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- E-Katalog Chart -->
        <div class="col-12 col-xl-7 col-xxl-8 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center">
                    <i class="material-icons-outlined text-primary me-2">trending_up</i>
                    <h5 class="card-title mb-0">E-Katalog Chart</h5>
                </div>
                <div class="card-body">
                    <div id="chartEkatalog">
                        <div class="chart-loading">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading chart...</span>
                        </div>
                        <div id="chartEkatalogContainer"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end row-->

    <!-- Tender Lists -->
    <div class="row mb-4">
        <h4 class="mb-3 text-primary fw-bold">
            <i class="material-icons-outlined me-2" style="vertical-align: sub;">assignment</i>
            Tender Tertinggi
        </h4>

        <!-- Tender HPS Tertinggi 1 Tahun -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-primary me-2">calendar_today</i>
                        <h5 class="card-title mb-0">HPS Tertinggi 1 Tahun</h5>
                    </div>
                    <span class="badge bg-primary">Top 5</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="topTenderList">
                        <div class="d-flex justify-content-center align-items-center py-4">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading data...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tender HPS Tertinggi 1 Bulan -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-primary me-2">date_range</i>
                        <h5 class="card-title mb-0">HPS Tertinggi 1 Bulan</h5>
                    </div>
                    <span class="badge bg-primary">Top 5</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="topTenderListMonth">
                        <div class="d-flex justify-content-center align-items-center py-4">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading data...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tender HPS Tertinggi 1 Minggu -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-primary me-2">today</i>
                        <h5 class="card-title mb-0">HPS Tertinggi 1 Minggu</h5>
                    </div>
                    <span class="badge bg-primary">Top 5</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="topTenderListWeek">
                        <div class="d-flex justify-content-center align-items-center py-4">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading data...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- E-Katalog Lists -->
    <div class="row mb-4">
        <h4 class="mb-3 text-success fw-bold">
            <i class="material-icons-outlined me-2" style="vertical-align: sub;">shopping_cart</i>
            E-Katalog Tertinggi
        </h4>

        <!-- E-Katalog Nilai Kontrak Tertinggi 1 Tahun -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-success me-2">calendar_today</i>
                        <h5 class="card-title mb-0">Nilai Kontrak 1 Tahun</h5>
                    </div>
                    <span class="badge bg-success">Top 5</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="topPengadaanList">
                        <div class="d-flex justify-content-center align-items-center py-4">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading data...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- E-Katalog Nilai Kontrak Tertinggi 1 Bulan -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-success me-2">date_range</i>
                        <h5 class="card-title mb-0">Nilai Kontrak 1 Bulan</h5>
                    </div>
                    <span class="badge bg-success">Top 5</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="topPengadaanListMonth">
                        <div class="d-flex justify-content-center align-items-center py-4">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading data...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- E-Katalog Nilai Kontrak Tertinggi 1 Minggu -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-success me-2">today</i>
                        <h5 class="card-title mb-0">Nilai Kontrak 1 Minggu</h5>
                    </div>
                    <span class="badge bg-success">Top 5</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="topPengadaanListWeek">
                        <div class="d-flex justify-content-center align-items-center py-4">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading data...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- E-Katalog6 Lists -->
    <div class="row mb-4">
        <h4 class="mb-3 text-purple fw-bold">
            <i class="material-icons-outlined me-2" style="vertical-align: sub;">shopping_bag</i>
            E-Katalog6 Tertinggi
        </h4>

        <!-- E-Katalog6 Nilai Kontrak Tertinggi 1 Tahun -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-purple me-2">calendar_today</i>
                        <h5 class="card-title mb-0">Nilai Kontrak 1 Tahun</h5>
                    </div>
                    <span class="badge bg-purple">Top 5</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="topPengadaan6List">
                        <div class="d-flex justify-content-center align-items-center py-4">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading data...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- E-Katalog6 Nilai Kontrak Tertinggi 1 Bulan -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-purple me-2">date_range</i>
                        <h5 class="card-title mb-0">Nilai Kontrak 1 Bulan</h5>
                    </div>
                    <span class="badge bg-purple">Top 5</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="topPengadaan6ListMonth">
                        <div class="d-flex justify-content-center align-items-center py-4">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading data...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- E-Katalog6 Nilai Kontrak Tertinggi 1 Minggu -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-purple me-2">today</i>
                        <h5 class="card-title mb-0">Nilai Kontrak 1 Minggu</h5>
                    </div>
                    <span class="badge bg-purple">Top 5</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="topPengadaan6ListWeek">
                        <div class="d-flex justify-content-center align-items-center py-4">
                            <div class="loading-spinner"></div>
                            <span class="ms-2">Loading data...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Info Row -->
    <div class="row mb-4">
        <h4 class="mb-3 text-info fw-bold">
            <i class="material-icons-outlined me-2" style="vertical-align: sub;">dashboard</i>
            Informasi Dashboard
        </h4>

        <!-- Login Activity -->
        <div class="col-12 col-lg-4 mb-4">
            <div class="card dashboard-card h-100">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-info me-2">login</i>
                        <h5 class="card-title mb-0">Riwayat Login</h5>
                    </div>
                    <span class="badge bg-info">{{ $loginLogs->count() }}</span>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning d-flex align-items-center mb-3 p-2 small">
                        <i class="material-icons-outlined me-2" style="font-size: 1rem;">warning</i>
                        <div>
                            Gunakan akun Anda hanya pada satu perangkat pada satu waktu.
                        </div>
                    </div>

                    <div class="login-activity">
                        @forelse($loginLogs->take(3) as $log)
                            <div class="login-activity-item">
                                <div class="login-activity-content">
                                    <div class="d-flex align-items-center justify-content-between mb-1">
                                        <h6 class="login-activity-title small">{{ $log->browser }} {{ $log->os }}</h6>
                                        <span class="badge bg-success">{{ $log->status ?? 'Success' }}</span>
                                    </div>
                                    <div class="d-flex flex-wrap gap-2">
                                        <div class="d-flex align-items-center">
                                            <i class="material-icons-outlined me-1 text-muted" style="font-size: 14px;">schedule</i>
                                            <span class="text-muted small" style="font-size: 12px;">{{ isset($log->login_at) ? $log->login_at->format('d M Y H:i') : $log->created_at->format('d M Y H:i') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-4">
                                <i class="material-icons-outlined text-muted mb-2" style="font-size: 32px;">history</i>
                                <p class="text-muted small">Tidak ada aktivitas login</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="col-12 col-lg-4 mb-4">
            <div class="card dashboard-card h-100">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-success me-2">health_and_safety</i>
                        <h5 class="card-title mb-0">Status Sistem</h5>
                    </div>
                    <span class="badge bg-success">Aktif</span>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div class="d-flex align-items-center">
                                <i class="material-icons-outlined text-success me-2" style="font-size: 1rem;">check_circle</i>
                                <span>Database</span>
                            </div>
                            <span class="badge bg-success rounded-pill">Online</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div class="d-flex align-items-center">
                                <i class="material-icons-outlined text-success me-2" style="font-size: 1rem;">check_circle</i>
                                <span>API Services</span>
                            </div>
                            <span class="badge bg-success rounded-pill">Online</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div class="d-flex align-items-center">
                                <i class="material-icons-outlined text-success me-2" style="font-size: 1rem;">check_circle</i>
                                <span>Notifikasi</span>
                            </div>
                            <span class="badge bg-success rounded-pill">Aktif</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div class="d-flex align-items-center">
                                <i class="material-icons-outlined text-success me-2" style="font-size: 1rem;">check_circle</i>
                                <span>Sinkronisasi Data</span>
                            </div>
                            <span class="badge bg-success rounded-pill">Berjalan</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="col-12 col-lg-4 mb-4">
            <div class="card dashboard-card h-100">
                <div class="card-header bg-light d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="material-icons-outlined text-primary me-2">link</i>
                        <h5 class="card-title mb-0">Tautan Cepat</h5>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <a href="{{ route('tender_lpse.index') }}" class="btn btn-outline-primary w-100 d-flex flex-column align-items-center py-3">
                                <i class="material-icons-outlined mb-2">assignment</i>
                                <span>Tender</span>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{{ route('sirup.index') }}" class="btn btn-outline-purple w-100 d-flex flex-column align-items-center py-3">
                                <i class="material-icons-outlined mb-2">description</i>
                                <span>SIRUP</span>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{{ route('ekatalog.index') }}" class="btn btn-outline-success w-100 d-flex flex-column align-items-center py-3">
                                <i class="material-icons-outlined mb-2">shopping_cart</i>
                                <span>E-Katalog</span>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{{ route('perusahaan.index') }}" class="btn btn-outline-info w-100 d-flex flex-column align-items-center py-3">
                                <i class="material-icons-outlined mb-2">business</i>
                                <span>Perusahaan</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
@endsection

@section('scripts')

    <script src="{{ URL::asset('build/plugins/apexchart/apexcharts.min.js') }}"></script>
    <script src="{{ URL::asset('build/js/dashboard.js') }}"></script>
    <script src="{{ URL::asset('build/plugins/peity/jquery.peity.min.js') }}"></script>
    <script>
        $(".data-attributes span").peity("donut")
    </script>

    <script>
        const routeDetailTemplate = "{{ route('tender_lpse.detail', ['kode_tender' => ':kode_tender']) }}";
        const routeDetailEkatalog = "{{ route('ekatalog.show', ['id' => ':id']) }}";
    </script>


    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Render chart tender
            $.ajax({
                url: "{{ secure_url('/dashboard/chartTender') }}",
                method: 'GET',
                dataType: 'json',
                success: function (data) {
                    const container = document.querySelector("#chartTender");
                    const loadingElement = container.querySelector(".chart-loading");
                    const chartContainer = document.querySelector("#chartTenderContainer");

                    // Sembunyikan loading
                    loadingElement.style.display = "none";

                    var options = {
                        series: [
                            {
                                name: "Tender 2024",
                                data: Object.values(data["2024"])
                            },
                            {
                                name: "Tender 2025",
                                data: Object.values(data["2025"])
                            }
                        ],
                        chart: {
                            foreColor: "#6c757d",
                            height: 300,
                            type: 'bar',
                            toolbar: { show: false },
                            zoom: { enabled: false },
                            animations: {
                                enabled: true,
                                easing: 'easeinout',
                                speed: 800,
                                animateGradually: {
                                    enabled: true,
                                    delay: 150
                                },
                                dynamicAnimation: {
                                    enabled: true,
                                    speed: 350
                                }
                            }
                        },
                        dataLabels: { enabled: false },
                        stroke: { width: 2, colors: ['transparent'] },
                        fill: {
                            type: 'gradient',
                            gradient: {
                                shade: 'light',
                                gradientToColors: ['#0d6efd', '#6f42c1'],
                                shadeIntensity: 1,
                                type: 'vertical',
                                stops: [0, 100, 100, 100]
                            }
                        },
                        colors: ['#0d6efd', "#6f42c1"],
                        plotOptions: {
                            bar: {
                                horizontal: false,
                                borderRadius: 6,
                                columnWidth: '55%',
                                distributed: false,
                                endingShape: 'rounded'
                            }
                        },
                        grid: {
                            show: true,
                            borderColor: '#f1f1f1',
                            strokeDashArray: 4
                        },
                        tooltip: {
                            theme: "light",
                            fixed: { enabled: true },
                            y: {
                                formatter: function (val) {
                                    return val + " tender";
                                }
                            }
                        },
                        xaxis: {
                            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                            labels: {
                                style: {
                                    colors: '#6c757d',
                                    fontSize: '12px'
                                }
                            }
                        },
                        yaxis: {
                            labels: {
                                style: {
                                    colors: '#6c757d'
                                }
                            }
                        },
                        legend: {
                            position: 'top',
                            horizontalAlign: 'right',
                            markers: {
                                width: 12,
                                height: 12,
                                radius: 12
                            }
                        }
                    };

                    var chart = new ApexCharts(chartContainer, options);
                    chart.render();
                },
                error: function (error) {
                    console.error('Error fetching chart data:', error);
                    const container = document.querySelector("#chartTender");
                    const loadingElement = container.querySelector(".chart-loading");
                    loadingElement.innerHTML = '<div class="text-danger"><i class="material-icons-outlined me-2">error</i>Gagal memuat data</div>';
                }
            });

            // Render chart e-katalog
            $.ajax({
                url: "{{ secure_url('/dashboard/chartEkatalog') }}",
                method: 'GET',
                dataType: 'json',
                success: function (data) {
                    const container = document.querySelector("#chartEkatalog");
                    const loadingElement = container.querySelector(".chart-loading");
                    const chartContainer = document.querySelector("#chartEkatalogContainer");

                    // Sembunyikan loading
                    loadingElement.style.display = "none";

                    var options = {
                        series: [
                            {
                                name: "Paket Ekatalog 2024",
                                data: Object.values(data["2024"])
                            },
                            {
                                name: "Paket Ekatalog 2025",
                                data: Object.values(data["2025"])
                            }
                        ],
                        chart: {
                            foreColor: "#6c757d",
                            height: 300,
                            type: 'bar',
                            toolbar: { show: false },
                            zoom: { enabled: false },
                            animations: {
                                enabled: true,
                                easing: 'easeinout',
                                speed: 800,
                                animateGradually: {
                                    enabled: true,
                                    delay: 150
                                },
                                dynamicAnimation: {
                                    enabled: true,
                                    speed: 350
                                }
                            }
                        },
                        dataLabels: { enabled: false },
                        stroke: { width: 2, colors: ['transparent'] },
                        fill: {
                            type: 'gradient',
                            gradient: {
                                shade: 'light',
                                gradientToColors: ['#28a745', '#20c997'],
                                shadeIntensity: 1,
                                type: 'vertical',
                                stops: [0, 100, 100, 100]
                            }
                        },
                        colors: ['#28a745', "#20c997"],
                        plotOptions: {
                            bar: {
                                horizontal: false,
                                borderRadius: 6,
                                columnWidth: '55%',
                                distributed: false,
                                endingShape: 'rounded'
                            }
                        },
                        grid: {
                            show: true,
                            borderColor: '#f1f1f1',
                            strokeDashArray: 4
                        },
                        tooltip: {
                            theme: "light",
                            fixed: { enabled: true },
                            y: {
                                formatter: function (val) {
                                    return val + " paket";
                                }
                            }
                        },
                        xaxis: {
                            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                            labels: {
                                style: {
                                    colors: '#6c757d',
                                    fontSize: '12px'
                                }
                            }
                        },
                        yaxis: {
                            labels: {
                                style: {
                                    colors: '#6c757d'
                                }
                            }
                        },
                        legend: {
                            position: 'top',
                            horizontalAlign: 'right',
                            markers: {
                                width: 12,
                                height: 12,
                                radius: 12
                            }
                        }
                    };

                    var chart = new ApexCharts(chartContainer, options);
                    chart.render();
                },
                error: function (error) {
                    console.error('Error fetching chart data:', error);
                    const container = document.querySelector("#chartEkatalog");
                    const loadingElement = container.querySelector(".chart-loading");
                    loadingElement.innerHTML = '<div class="text-danger"><i class="material-icons-outlined me-2">error</i>Gagal memuat data</div>';
                }
            });

            // Fetch and render top tenders for 1 Year, 1 Month, 1 Week
            function fetchTopTenders(period, containerId) {
                $.ajax({
                    url: `/dashboard/topTendersByPeriod/${period}`,
                    method: 'GET',
                    dataType: 'json',
                    success: function (response) {
                        if (response.status === 'success') {
                            let tenders = response.data;
                            let container = $(`#${containerId}`);
                            container.empty();

                            $.each(tenders, function (index, tender) {
                                let detailUrl = routeDetailTemplate.replace(':kode_tender', tender.kode_tender);
                                let iconUrl = tender.logo_path ? tender.logo_path : "{{ URL::asset('build/images/logo-default.png') }}";
                                let hpsFormatted = new Intl.NumberFormat('id-ID', {
                                    style: 'currency',
                                    currency: 'IDR',
                                    minimumFractionDigits: 0
                                }).format(tender.hps);

                                let item = `
                                <a href="${detailUrl}" target="_blank">
                                    <div class="list-group-item list-group-item-action d-flex align-items-start py-3">
                                        <div class="flex-shrink-0 me-3">
                                            <img src="${iconUrl}" alt="Logo LPSE" width="40" height="40" class="rounded border" style="object-fit:contain;">
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <h6 class="mb-0 fw-bold text-truncate" style="max-width: 100%;white-space: normal;">${tender.nama_paket}</h6>
                                            </div>
                                            <small class="text-muted" style="display: block;white-space: normal;">${tender.nama_lpse ? tender.nama_lpse : 'LPSE tidak tersedia'}</small>
                                            <span class="badge bg-primary" style="white-space: normal; font-size:medium;">${hpsFormatted}</span>
                                        </div>
                                    </div>
                                </a>
                                `;
                                container.append(item);
                            });
                        }
                    },
                    error: function (error) {
                        console.error('Error fetching top tenders:', error);
                    }
                });
            }

            // Fetch and render top ekatalog packages for 1 Year, 1 Month, 1 Week
            function fetchTopEkatalog(period, containerId) {
                $.ajax({
                    url: `/dashboard/topEkatalogByPeriod/${period}`,
                    method: 'GET',
                    dataType: 'json',
                    success: function (response) {
                        if (response.status === 'success') {
                            let pakets = response.data;
                            let container = $(`#${containerId}`);
                            container.empty();

                            $.each(pakets, function (index, paket) {
                                let detailUrl = routeDetailEkatalog.replace(':id', paket.id);
                                let iconUrl = paket.logo_path;
                                let nilaiKontrakFormatted = new Intl.NumberFormat('id-ID', {
                                    style: 'currency',
                                    currency: 'IDR',
                                    minimumFractionDigits: 0
                                }).format(paket.kontrak);

                                let item = `
                                <a href="${detailUrl}" target="_blank">
                                    <div class="list-group-item list-group-item-action d-flex align-items-start py-3">
                                        <div class="flex-shrink-0 me-3">
                                            <img src="${iconUrl}" alt="Logo LPSE" width="40" height="40" class="rounded border" style="object-fit:contain;">
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <h6 class="mb-0 fw-bold text-truncate" style="max-width: 100%; white-space: normal;">
                                                    ${paket.judul}
                                                </h6>
                                            </div>
                                            <small class="text-muted" style="display:block; white-space: normal;">
                                                ${paket.instansi}
                                            </small>
                                            <span class="badge bg-primary" style="white-space: normal; font-size: medium;">
                                                ${nilaiKontrakFormatted}
                                            </span>
                                        </div>
                                    </div>
                                </a>
                                `;
                                container.append(item);
                            });
                        }
                    },
                    error: function (error) {
                        console.error('Error fetching top ekatalog:', error);
                    }
                });
            }

            // Initial fetch calls
            fetchTopTenders('1 year', 'topTenderList');
            fetchTopTenders('1 month', 'topTenderListMonth');
            fetchTopTenders('1 week', 'topTenderListWeek');

            fetchTopEkatalog('1 year', 'topPengadaanList');
            fetchTopEkatalog('1 month', 'topPengadaanListMonth');
            fetchTopEkatalog('1 week', 'topPengadaanListWeek');

            // Fetch and render top ekatalog6 packages for 1 Year, 1 Month, 1 Week
            function fetchTopEkatalog6(period, containerId) {
                $.ajax({
                    url: `/dashboard/topEkatalog6ByPeriod/${period}`,
                    method: 'GET',
                    dataType: 'json',
                    success: function (response) {
                        if (response.status === 'success') {
                            let pakets = response.data;
                            let container = $(`#${containerId}`);
                            container.empty();

                            $.each(pakets, function (index, paket) {
                                let iconUrl = paket.logo_path;
                                let nilaiKontrakFormatted = new Intl.NumberFormat('id-ID', {
                                    style: 'currency',
                                    currency: 'IDR',
                                    minimumFractionDigits: 0
                                }).format(paket.nilai_kontrak);

                                let item = `
                                <a href="#" target="_blank">
                                    <div class="list-group-item list-group-item-action d-flex align-items-start py-3">
                                        <div class="flex-shrink-0 me-3">
                                            <img src="${iconUrl}" alt="Logo LPSE" width="40" height="40" class="rounded border" style="object-fit:contain;">
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <h6 class="mb-0 fw-bold text-truncate" style="max-width: 100%; white-space: normal;">
                                                    ${paket.nama_paket}
                                                </h6>
                                            </div>
                                            <small class="text-muted" style="display:block; white-space: normal;">
                                                ${paket.instansi_pembeli}
                                            </small>
                                            <span class="badge bg-primary" style="white-space: normal; font-size: medium;">
                                                ${nilaiKontrakFormatted}
                                            </span>
                                        </div>
                                    </div>
                                </a>
                                `;
                                container.append(item);
                            });
                        }
                    },
                    error: function (error) {
                        console.error('Error fetching top ekatalog6:', error);
                    }
                });
            }

            // Initial fetch calls for ekatalog6
            fetchTopEkatalog6('1 year', 'topPengadaan6List');
            fetchTopEkatalog6('1 month', 'topPengadaan6ListMonth');
            fetchTopEkatalog6('1 week', 'topPengadaan6ListWeek');
        });
    </script>


    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Mendapatkan elemen loading dan container
            const chartKategoriContainer = document.querySelector("#chartKategoriPekerjaan");
            const chartKategoriLoading = chartKategoriContainer.querySelector(".chart-loading");
            const chartKategoriChartContainer = document.querySelector("#chartKategoriPekerjaanContainer");

            fetch("{{ secure_url('/dashboard/chartKategoriPekerjaan') }}", { method: 'GET', credentials: 'include' })
                .then(response => response.json())
                .then(data => {
                    // Sembunyikan loading
                    chartKategoriLoading.style.display = "none";

                    var options = {
                        series: [{
                            data: data.values
                        }],
                        chart: {
                            type: 'bar',
                            height: 400,
                            toolbar: {
                                show: false
                            }
                        },
                        plotOptions: {
                            bar: {
                                horizontal: true,
                                borderRadius: 4,
                                barHeight: '70%',
                                distributed: false
                            }
                        },
                        dataLabels: {
                            enabled: true,
                            formatter: function (val, opt) {
                                return val;
                            },
                            style: {
                                colors: ['#fff']
                            }
                        },
                        xaxis: {
                            categories: data.categories,
                            labels: {
                                style: {
                                    colors: '#6c757d',
                                    fontSize: '12px'
                                }
                            }
                        },
                        colors: ['#0d6efd', '#6f42c1', '#28a745', '#ffc107', '#dc3545'],
                        grid: {
                            borderColor: '#f1f1f1'
                        }
                    };

                    var chart = new ApexCharts(document.querySelector("#chartKategoriPekerjaanContainer"), options);
                    chart.render();
                })
                .catch(error => {
                    console.error('Error fetching chart data:', error);
                    // Tampilkan pesan error
                    chartKategoriLoading.innerHTML = '<div class="text-danger"><i class="material-icons-outlined me-2">error</i>Gagal memuat data</div>';
                });
        });
    </script>


    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Mendapatkan elemen loading dan container
            const chartUMKMContainer = document.querySelector("#chartStatusUMKM");
            const chartUMKMLoading = chartUMKMContainer.querySelector(".chart-loading");
            const chartUMKMChartContainer = document.querySelector("#chartStatusUMKMContainer");

            fetch("{{ secure_url('/dashboard/chartEkatStatusUMKM') }}", { method: 'GET', credentials: 'include' })
                .then(response => response.json())
                .then(data => {
                    // Sembunyikan loading
                    chartUMKMLoading.style.display = "none";

                    var options = {
                        series: data.values,
                        chart: {
                            width: 500,
                            type: 'donut',
                            toolbar: {
                                show: false
                            }
                        },
                        labels: data.categories,
                        plotOptions: {
                            pie: {
                                startAngle: -90,
                                endAngle: 270,
                                donut: {
                                    size: '70%',
                                    labels: {
                                        show: true,
                                        name: {
                                            show: true
                                        },
                                        value: {
                                            show: true,
                                            formatter: function(val) {
                                                return val;
                                            }
                                        },
                                        total: {
                                            show: true,
                                            label: 'Total',
                                            formatter: function(w) {
                                                return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        dataLabels: {
                            enabled: false,
                            formatter: function (val, opts) {
                                return opts.w.config.labels[opts.seriesIndex] + ": " + val.toFixed(1) + "%";
                            }
                        },
                        fill: {
                            type: 'gradient',
                            gradient: {
                                shade: 'light',
                                type: 'horizontal',
                                shadeIntensity: 0.25,
                                gradientToColors: undefined,
                                inverseColors: true,
                                opacityFrom: 1,
                                opacityTo: 1,
                                stops: [50, 0, 100]
                            }
                        },
                        legend: {
                            formatter: function (val, opts) {
                                return val + " - " + opts.w.globals.series[opts.seriesIndex];
                            },
                            position: 'bottom'
                        },
                        colors: ['#0d6efd', '#6f42c1', '#28a745', '#ffc107', '#dc3545'],
                        responsive: [{
                            breakpoint: 480,
                            options: {
                                chart: {
                                    width: 300
                                },
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }]
                    };

                    var chart = new ApexCharts(document.querySelector("#chartStatusUMKMContainer"), options);
                    chart.render();
                })
                .catch(error => {
                    console.error('Error fetching chart data:', error);
                    // Tampilkan pesan error
                    chartUMKMLoading.innerHTML = '<div class="text-danger"><i class="material-icons-outlined me-2">error</i>Gagal memuat data</div>';
                });
        });
    </script>

    <script>
        $(document).ready(function () {
            $.ajax({
                url: '/dashboard/topTender',
                method: 'GET',
                dataType: 'json',
                success: function (response) {
                    if (response.status === 'success') {
                        let tenders = response.data;
                        let container = $('#topTenderList');
                        container.empty();

                        $.each(tenders, function (index, tender) {
                            let detailUrl = routeDetailTemplate.replace(':kode_tender', tender.kode_tender);
                            // Gunakan logo dari LPSE jika ada, jika tidak gunakan default
                            let iconUrl = tender.logo_path
                                ? tender.logo_path
                                : "{{ URL::asset('build/images/logo-default.png') }}";

                            // Format HPS (opsional, jika ingin menambahkan pemisah ribuan)
                            let hpsFormatted = new Intl.NumberFormat('id-ID', {
                                style: 'currency',
                                currency: 'IDR',
                                minimumFractionDigits: 0
                            }).format(tender.hps);

                            // List group item
                            let item = `
                            <a href="${detailUrl}"  target="_blank">
                                <div class="list-group-item list-group-item-action d-flex align-items-start py-3">
                                    <div class="flex-shrink-0 me-3">
                                        <img src="${iconUrl}" alt="Logo LPSE" width="40" height="40" class="rounded border" style="object-fit:contain;">
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <h6 class="mb-0 fw-bold text-truncate" style="max-width: 100%;white-space: normal;">${tender.nama_paket}</h6>
                                        </div>
                                        <small class="text-muted" style="display: block;white-space: normal;">${tender.nama_lpse ? tender.nama_lpse : 'LPSE tidak tersedia'}</small>
                                         <span class="badge bg-primary" style="white-space: normal; font-size:medium;">${hpsFormatted}</span>
                                    </div>
                                </div>
                            </a>
                            `;
                            container.append(item);
                        });
                    }
                },
                error: function (error) {
                    console.error('Terjadi kesalahan:', error);
                }
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            $.ajax({
                url: '/dashboard/topPaketPengadaan',
                method: 'GET',
                dataType: 'json',
                success: function (response) {
                    if (response.status === 'success') {
                        let pakets = response.data;
                        let container = $('#topPengadaanList');
                        container.empty();

                        $.each(pakets, function (index, paket) {
                            // Jika ada detail page, Anda bisa mengganti "#" dengan route template serupa
                            let detailUrl = routeDetailEkatalog.replace(':id', paket.id);

                            // Gunakan logo yang sudah didapat (nilai default sudah di-COALESCE jika tidak ditemukan)
                            let iconUrl = paket.logo_path;

                            // Format nilai kontrak
                            let nilaiKontrakFormatted = new Intl.NumberFormat('id-ID', {
                                style: 'currency',
                                currency: 'IDR',
                                minimumFractionDigits: 0
                            }).format(paket.kontrak);

                            // Buat list-group item untuk setiap paket
                            let item = `
                            <a href="${detailUrl}" target="_blank">
                                <div class="list-group-item list-group-item-action d-flex align-items-start py-3">
                                    <div class="flex-shrink-0 me-3">
                                        <img src="${iconUrl}" alt="Logo LPSE" width="40" height="40" class="rounded border" style="object-fit:contain;">
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <h6 class="mb-0 fw-bold text-truncate" style="max-width: 100%; white-space: normal;">
                                                ${paket.judul}
                                            </h6>
                                        </div>
                                        <small class="text-muted" style="display:block; white-space: normal;">
                                            ${paket.instansi}
                                        </small>
                                        <span class="badge bg-primary" style="white-space: normal; font-size: medium;">
                                            ${nilaiKontrakFormatted}
                                        </span>
                                    </div>
                                </div>
                            </a>
                            `;
                            container.append(item);
                        });
                    }
                },
                error: function (error) {
                    console.error('Terjadi kesalahan:', error);
                }
            });
        });
    </script>
@endsection