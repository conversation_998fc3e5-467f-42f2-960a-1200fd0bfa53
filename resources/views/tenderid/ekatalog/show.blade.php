@php
use App\Models\Lpse;
@endphp
@extends('layouts.master')

@section('title', 'Detail Data Sirup')

@section('css')
<style>
  /* Card Styling */
  .card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    margin-bottom: 24px;
  }

  .card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 12px 12px 0 0 !important;
    padding: 18px 24px;
  }

  .card-title {
    font-weight: 600;
    color: #344767;
    margin-bottom: 0;
    font-size: 1.1rem;
  }

  .card-body {
    padding: 24px;
  }

  .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 0 0 12px 12px !important;
    padding: 16px 24px;
  }

  /* Table Styling */
  .table-responsive {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 1.5rem;
  }

  .table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
  }

  .table thead th {
    background: linear-gradient(90deg, #4481eb, #04befe);
    color: white;
    font-weight: 600;
    border-top: none;
    padding: 16px;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: none;
  }

  .table tbody td {
    padding: 16px;
    vertical-align: middle;
    border-bottom: 1px solid #f8f9fa;
    font-size: 0.9rem;
    word-wrap: break-word;
    white-space: normal;
  }

  .table tbody tr:last-child td {
    border-bottom: none;
  }

  .table tbody tr {
    transition: all 0.2s ease;
  }

  .table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
  }

  /* Section Headings */
  .section-heading {
    font-size: 1.1rem;
    font-weight: 600;
    color: #344767;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f8f9fa;
    display: flex;
    align-items: center;
  }

  .section-heading i {
    margin-right: 0.5rem;
    color: #4481eb;
  }

  /* Product Box */
  .product-box {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  }

  .product-box img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
  }

  .product-box img:hover {
    transform: scale(1.15);
  }

  /* Product Info */
  .product-info {
    flex: 1;
  }

  .product-title {
    font-weight: 600;
    color: #0d6efd;
    display: block;
    margin-bottom: 5px;
    font-size: 0.95rem;
    text-decoration: none;
    transition: all 0.2s ease;
  }

  .product-title:hover {
    color: #0b5ed7;
    text-decoration: underline;
  }

  .product-category {
    color: #6c757d;
    font-size: 0.85rem;
    margin-bottom: 0;
  }

  .product-category a {
    color: #6c757d;
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .product-category a:hover {
    color: #0d6efd;
    text-decoration: underline;
  }

  /* Badges */
  .badge {
    padding: 6px 10px;
    font-weight: 500;
    font-size: 0.75rem;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    line-height: 1;
  }

  .badge i {
    font-size: 12px;
    margin-right: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: baseline;
  }

  /* Buttons */
  .btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
    box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
  }

  .btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0b5ed7;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
  }

  .btn-outline-primary {
    border-color: #0d6efd;
    color: #0d6efd;
  }

  .btn-outline-primary:hover {
    background-color: #0d6efd;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
  }

  /* Status Indicators */
  .status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
  }

  .status-indicator i {
    margin-right: 6px;
    font-size: 0.9rem;
  }

  .status-indicator.active {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
  }

  .status-indicator.inactive {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
  }

  .status-indicator.pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
  }

  /* Warning Row */
  .table tr.bg-warning-important td {
    background-color: rgba(255, 193, 7, 0.1);
  }

  /* Print Styles */
  @media print {
    .btn-print-hide {
      display: none !important;
    }

    .card {
      box-shadow: none !important;
      border: 1px solid #dee2e6 !important;
    }

    .table thead th {
      background: #f8f9fa !important;
      color: #212529 !important;
      border: 1px solid #dee2e6 !important;
    }
  }
</style>
@endsection

@section('content')
<x-page-title title="Data Sirup" pagetitle="Detail Paket Pengadaan" />

<div class="card radius-10">
  <div class="card-header py-3">
    <div class="row align-items-center g-3">
      <div class="col-12 col-lg-6">
        <h5 class="mb-0">Detail Data Sirup</h5>
      </div>
      <div class="col-12 col-lg-6 text-md-end">
        <a href="javascript:;" class="btn btn-danger btn-sm me-2"><i class="bi bi-file-earmark-pdf me-2"></i>Export as
          PDF</a>
        <a href="javascript:;" onclick="window.print()" class="btn btn-dark btn-sm"><i
            class="bi bi-printer-fill me-2"></i>Print</a>
      </div>
    </div>
  </div>

  <div class="card-body">
    <!-- Tabel Detail Paket -->
    <table class="table table-bordered">
      <thead>
        <tr>
          <th colspan="2" class="text-center">Kode RUP {{ $dataSirup->kode_rup }}</th>
        </tr>
      </thead>
      <tbody>
        <!-- Paket -->
        <tr>
          <td><strong>Nama Paket</strong></td>
          <td>{{ $dataSirup->nama_paket }}</td>
        </tr>
        <!-- Nama KLPD dan Lokasi -->
        <tr>
          <td><strong>Nama KLPD</strong></td>
          <td>{{ $dataSirup->nama_klpd }}</td>
        </tr>

        <tr>
          <td><strong>Satuan Kerja</strong></td>
          <td>{{ $dataSirup->satuan_kerja }}</td>
        </tr>
        <tr>
          <td><strong>Lokasi Pekerjaan</strong></td>
          <td>
            @if($dataSirup->lokasi_pekerjaan && is_array($dataSirup->lokasi_pekerjaan))
        @foreach($dataSirup->lokasi_pekerjaan as $lokasi)
      {{ $lokasi['detail_lokasi'] ?? 'Not specified' }} - {{ $lokasi['kabupaten_kota'] ?? 'Not specified' }} -
      {{ $lokasi['provinsi'] ?? 'Not specified' }}<br>
    @endforeach
      @else
    Not specified
  @endif
          </td>
        </tr>
        <tr>
          <td><strong>Tahun Anggaran</strong></td>
          <td>{{ $dataSirup->tahun_anggaran }}</td>
        </tr>

        <tr>
          <td><strong>Pagu Anggaran</strong></td>
          <td>{{ convert_to_rupiah($dataSirup->total_pagu) }}</td>
        </tr>

        <tr>
          <td><strong>Uraian Pekerjaan</strong></td>
          <td>{{ $dataSirup->uraian_pekerjaan }}</td>
        </tr>
        <tr>
          <td><strong>Spesifikasi Pekerjaan</strong></td>
          <td>{{ $dataSirup->spesifikasi_pekerjaan }}</td>
        </tr>
        <tr>
          <td><strong>Volume Pekerjaan</strong></td>
          <td>{{ $dataSirup->volume_pekerjaan }}</td>
        </tr>
        <tr>
          <td><strong>Produk Dalam Negeri</strong></td>
          <td>{{ $dataSirup->produk_dalam_negeri ? 'Yes' : 'No' }}</td>
        </tr>
        <tr>
          <td><strong>UMKM</strong></td>
          <td>{{ $dataSirup->umkm ? 'Yes' : 'No' }}</td>
        </tr>

        <!-- Metode & Pengadaan -->
        <tr>
          <td><strong>Metode Pemilihan</strong></td>
          <td>{{ $dataSirup->metode_pemilihan }}</td>
        </tr>
        <tr>
          <td><strong>Jenis Pengadaan</strong></td>
          <td>
            @if($dataSirup->jenis_pengadaan && is_array($dataSirup->jenis_pengadaan))
        @foreach($dataSirup->jenis_pengadaan as $pengadaan)
      {{ $pengadaan['jenis_pengadaan'] ?? 'Not specified' }}<br>
    @endforeach
      @else
    Not specified
  @endif
          </td>
        </tr>

      </tbody>
    </table>

    <!-- Tabel Sumber Dana -->
    <h5 class="mt-4">Sumber Dana</h5>
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>No</th>
          <th>Tahun Anggaran</th>
          <th>Pagu</th>
          <th>Kode Rekening</th>
        </tr>
      </thead>
      <tbody>
        @if($dataSirup->sumber_dana && is_array($dataSirup->sumber_dana))
      @foreach($dataSirup->sumber_dana as $dana)
      <tr>
      <td>{{ $dana['no'] ?? '-' }}</td>
      <td>{{ $dana['sumber_dana'] ?? 'Not specified' }} - {{ $dana['ta'] ?? 'Not specified' }}</td>
      <td>{{ $dana['pagu'] ? convert_to_rupiah($dana['pagu']) : 'Not specified' }}</td>
      <td style="white-space: normal; word-break: break-all;">{{ $dana['mak'] ?? 'Not specified' }}</td>
      </tr>
    @endforeach
    @else
    <tr>
      <td colspan="6" class="text-center">No Sumber Dana specified</td>
    </tr>
  @endif
      </tbody>
    </table>

    <!-- Tabel Jadwal Pemilihan Penyedia -->
    <h5 class="mt-4">Jadwal Pemilihan Penyedia</h5>
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Mulai</th>
          <th>Akhir</th>
        </tr>
      </thead>
      <tbody>
        @if($dataSirup->jadwal_pemilihan_penyedia && is_array($dataSirup->jadwal_pemilihan_penyedia))
      @foreach($dataSirup->jadwal_pemilihan_penyedia as $penyedia)
      <tr>
      <td>{{ $penyedia['mulai'] ?? 'Not specified' }}</td>
      <td>{{ $penyedia['akhir'] ?? 'Not specified' }}</td>
      </tr>
    @endforeach
    @else
    <tr>
      <td colspan="3" class="text-center">No Jadwal Pemilihan Penyedia specified</td>
    </tr>
  @endif
      </tbody>
    </table>

    <!-- Tabel Jadwal Pelaksanaan Kontrak -->
    <h5 class="mt-4">Jadwal Pelaksanaan Kontrak</h5>
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Mulai</th>
          <th>Akhir</th>
        </tr>
      </thead>
      <tbody>
        @if($dataSirup->jadwal_pelaksanaan_kontrak && is_array($dataSirup->jadwal_pelaksanaan_kontrak))
      @foreach($dataSirup->jadwal_pelaksanaan_kontrak as $jadwal)
      <tr>
      <td>{{ $jadwal['mulai'] ?? 'Not specified' }}</td>
      <td>{{ $jadwal['akhir'] ?? 'Not specified' }}</td>
      </tr>
    @endforeach
    @else
    <tr>
      <td colspan="3" class="text-center">No Jadwal Pelaksanaan Kontrak specified</td>
    </tr>
  @endif
      </tbody>
    </table>

    <!-- Tabel Pemanfaatan Barang Jasa -->
    <h5 class="mt-4">Pemanfaatan Barang Jasa</h5>
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Mulai</th>
          <th>Akhir</th>
        </tr>
      </thead>
      <tbody>
        @if($dataSirup->pemanfaatan_barang_jasa && is_array($dataSirup->pemanfaatan_barang_jasa))
      @foreach($dataSirup->pemanfaatan_barang_jasa as $pemanfaatan)
      <tr>
      <td>{{ $pemanfaatan['mulai'] ?? 'Not specified' }}</td>
      <td>{{ $pemanfaatan['akhir'] ?? 'Not specified' }}</td>
      </tr>
    @endforeach
    @else
    <tr>
      <td colspan="2" class="text-center">No Pemanfaatan Barang Jasa specified</td>
    </tr>
  @endif
      </tbody>
    </table>

  </div>

  <div class="card-footer py-3 bg-transparent">
    <p class="text-center mb-2">
      PAKET INI DIUMUMKAN PADA
    </p>
    <p class="text-center d-flex align-items-center gap-3 justify-content-center mb-0">
      <span><i class="bi bi-clock"></i> {{ format_date_time($dataSirup->tanggal_paket_diumumkan) ?? 'Not specified' }}
        WIB</span>
    </p>
  </div>
</div>


@if($dataSirup->status_paket === 'Sudah Tayang' && $dataSirup->tender_data->isNotEmpty())
<hr>
  <div class="card">
    <div class="card-body">
    <h5 class="mb-3">Realisasi</h5>
    <div class="product-table">
      <div class="table-responsive white-space-nowrap">
      <table class="table align-middle">
        <thead class="table-light">
        <tr>
          <th>Kode Tender</th>
          <th>Nama Paket</th>
          <th>Kategori</th>
          <th>Tahapan Tender</th>
          <th>Pagu</th>
          <th>HPS</th>
          <th>Tanggal Tayang</th>
        </tr>
        </thead>
        <tbody>

        @foreach($dataSirup->tender_data as $tender)
        @php
        $kodeLpse = $tender->kd_lpse;
        $lpse = Lpse::where('kd_lpse',$kodeLpse)->first();
        @endphp
      <tr class="{{ $tender->status_tender !== 'Aktif' ? 'bg-warning-important' : '' }}">
        <td><a href="#">#{{ $tender->kode_tender }}</a></td>
        <td>
        <div class="d-flex align-items-center gap-3">
        <div class="product-box">
        <img src="{{ asset(str_replace('http://proyeklpse.test', '', $lpse->logo_path)) }}" alt="Logo"
          width="40" class="rounded-3">
        </div>
        <div class="product-info">
        <a href="{{ route('tender_lpse.detail', ['kode_tender' => $tender->kode_tender]) }}"
          class="product-title">{{ $tender->nama_paket }}</a>
        <p class="mb-0 product-category">
          <a
          href="{{ route('lpse.dashboard', ['kode_lpse' => $tender->lpse->kd_lpse]) }}">{{ $tender->lpse->nama_lpse }}</a>
        </p>
        </div>
        </div>
        </td>
        <td>{{ $tender->kategori_pekerjaan }}</td>
        <td>{{ $tender->tahap_tender }}</td>
        <td>{{ format_number_short($tender->pagu) }}</td>
        <td>{{ format_number_short($tender->hps) }}</td>
        <td>{{ format_date_indonesian($tender->tanggal_paket_tayang) }}</td>
      </tr>
    @endforeach

        </tbody>
      </table>
      </div>
    </div>
    </div>
  </div>
@endif
@endsection