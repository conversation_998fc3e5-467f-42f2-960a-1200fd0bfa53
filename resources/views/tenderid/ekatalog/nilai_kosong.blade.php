{{-- resources/views/ekatalog/index.blade.php --}}
@php
  use App\Models\Lpse;
@endphp
@extends('layouts.master')

@section('title', 'Semua Paket Ekatalog')

@section('css')
<style>
  .table tr.bg-warning-important td {
    background-color: #ffcc00;
    /* Menggunakan warna kuning yang lebih lembut */
  }

  .product-box img {
    transition: transform 0.3s ease;
  }

  .product-box img:hover {
    transform: scale(1.1);
    /* Animasi zoom ketika hover gambar */
  }

  .product-title {
    font-weight: bold;
    color: #007bff;
  }

  .product-title:hover {
    text-decoration: underline;
  }

  .product-category a {
    color: #6c757d;
    font-size: 0.9rem;
  }

  .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #007bff;
  }

  .table-responsive {
    border: 1px solid #dee2e6;
    border-radius: 8px;
  }

  .btn-filter {
    background-color: #007bff;
    color: white;
  }

  .btn-filter:hover {
    background-color: #0056b3;
  }

  /* Pagination Custom */
  .pagination .page-link {
    color: #007bff;
  }

  .pagination .page-link:hover {
    background-color: #f1f1f1;
  }

  .pagination .active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
  }

  .table td, .table th {
  word-wrap: break-word; /* Membungkus kata panjang */
  white-space: normal; /* Membiarkan teks membungkus ke baris baru */
}

.table {
  table-layout: auto; /* Memberikan tabel fleksibilitas untuk menyesuaikan kolom */
}

</style>
@endsection

@section('content')
<x-page-title title="Ekatalog" pagetitle="Semua Paket Ekatalog" />

<!-- Search and Filter Section -->
<div class="row g-3 mb-4">
  <div class="col-md-6">
    <form method="GET" action="{{ route('ekatalog.index') }}" class="d-flex">
      <input class="form-control me-2" type="search" name="search"
        placeholder="Cari Nama Paket, Nomor Paket, Pengelola, Satuan Kerja, Instansi Pembeli..."
        value="{{ request('search') }}">
      <button class="btn btn-outline-primary" type="submit">Cari</button>
    </form>
  </div>
  <div class="col-md-4">
    {{-- Tambahkan filter tambahan jika diperlukan --}}
    <form method="GET" action="{{ route('ekatalog.index') }}">
      <select class="form-select" name="status_paket" onchange="this.form.submit()">
        <option value="">Pilih Status Paket</option>
        <option value="Sudah Tayang" {{ request('status_paket') == 'Sudah Tayang' ? 'selected' : '' }}>Sudah Tayang
        </option>
        <option value="Belum Tayang" {{ request('status_paket') == 'Belum Tayang' ? 'selected' : '' }}>Belum Tayang
        </option>
        <!-- Tambahkan opsi lainnya sesuai kebutuhan -->
      </select>
    </form>
  </div>
  <div class="col-md-2">
    <a href="{{ route('ekatalog.index') }}" class="btn btn-secondary w-100">Reset</a>
  </div>
</div>

<!-- Paket Pengadaan Table Section -->
<div class="card mt-4">
  <div class="card-body">
    <div class="product-table">
      <div class="table-responsive white-space-nowrap">
        <table class="table align-middle">
          <thead class="table-light">
            <tr>
              <th>Nomor Paket</th>
              <th>Nama Paket</th>
              <th>Instansi Pembeli & SatKer</th>
              <th>Pelaksana</th>
              <th>Tanggal Klik</th>
              <th>Status Paket</th>
            </tr>
          </thead>
          <tbody>
            @forelse($paketPengadaan as $paket)
              @php
          $nama_klpd = $paket->instansi_pembeli;
          if (strpos($nama_klpd, "Kab.") !== false) {
          $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
          }
          $lpse = Lpse::where('nama_lpse', 'LIKE', "%{$nama_klpd}%")->first();
      @endphp
              <tr>
                <td>
                <a href="{{ route('ekatalog.show', ['id' => $paket->id_paket]) }}">#{{ $paket->nomor_paket }}</a>
                </td>
                <td >
                <div class="d-flex align-items-center gap-3">
                  {{-- Jika ada logo atau gambar terkait, tambahkan di sini --}}

                  @if($lpse)
            <img src="{{ asset($lpse->logo_path) }}" alt="Logo" width="40" class="rounded-3">
          @else
        <img src="{{ asset('build/images/logo-default.png') }}" alt="Logo" width="40" class="rounded-3">
      @endif

                  <div class="product-info">
                  <a href="{{ route('ekatalog.show', ['id' => $paket->id_paket]) }}" class="product-title">
                    {{ $paket->nama_paket }} <span class="badge bg-primary"> {{ $paket->jenis_katalog }}</span> <span class="badge bg-secondary"> {{ convert_to_rupiah($paket->nilai_kontrak) }}</span>
                  </a>
                  <p class="mb-0 product-category">
                    {{-- Jika ada kategori atau informasi tambahan, tambahkan di sini --}}
                    {{ $paket->pengelola }}
                  </p>
                  </div>
                </div>
                </td>
                <td><strong>{{ $paket->instansi_pembeli }}</strong><br>
                Satker : {{ $paket->satuan_kerja}}</td>
                <td><a href="{{ route('perusahaan.show', ['npwp' => encrypt($paket->pelaksana->npwp)]) }}">{{ $paket->pelaksana->nama_peserta  }}</a></td>

                <td>
                {{ format_date_time($paket->tanggal_paket) }}
                </td>
                <td>
                @php
            if ($paket->status_paket == 'paket_selesai') {
            $class = 'bg-success';
            $icon = 'bi-check-circle-fill';
            $status = 'Paket Selesai';
            } elseif ($paket->status_paket == 'melakukan_pengiriman_dan_penerimaan') {
            $class = 'bg-facebook';
            $icon = 'bi-minecart-loaded';
            $status = 'On Progress';
            } elseif ($paket->status_paket == 'proses_kontrak_ppk') {
            $class = 'bg-warning text-black';
            $icon = 'bi-clock-fill';
            $status = 'Proses Kontrak PPK';
            }
        @endphp
                <span class="badge {{$class}} ">
                  <i class="bi {{$icon}} me-2"></i>{{$status}}
                </span>
                </td>
              </tr>
      @empty
    <tr>
      <td colspan="8" class="text-center">Tidak ada data ditemukan.</td>
    </tr>
  @endforelse
          </tbody>
        </table>

        <!-- Pagination -->
        <div class="d-flex justify-content-center">
          {{ $paketPengadaan->links('vendor.pagination.simple') }}
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@section('scripts')
<script>
  // Anda bisa menambahkan script khusus untuk halaman Ekatalog di sini
</script>
@endsection
