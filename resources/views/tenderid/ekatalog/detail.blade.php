@php
    use App\Models\Lpse;
    $title = "{$paketPengadaan->nomor_paket} - {$paketPengadaan->nama_paket}";
@endphp
@extends('layouts.master')

@section('title', $title)
@section('css')
<style>
    /* Print styling */
    @media print {
        @page {
            size: A3 landscape;
            margin: 10mm;
        }
    }

    /* Card styling */
    .card {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    }

    .card-title {
        font-weight: 600;
        color: #344767;
    }

    /* Logo container */
    .col-md-2.border-end {
        background-color: #f8f9fa;
    }

    .col-md-2.border-end img {
        transition: all 0.3s ease;
        max-height: 120px;
        object-fit: contain;
    }

    .col-md-2.border-end img:hover {
        transform: scale(1.05);
    }

    /* Modern Badge styling */
    .badge-modern {
        display: inline-flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 50px;
        color: white;
        font-weight: 500;
        font-size: 0.85rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        margin-right: 5px;
    }

    .badge-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }

    .badge-modern i {
        margin-right: 8px;
        font-size: 16px;
    }

    .badge-modern.bg-blue {
        background: linear-gradient(90deg, #1a73e8, #0d47a1);
    }

    .badge-modern.bg-cyan {
        background: linear-gradient(90deg, #00bcd4, #0097a7);
    }

    .badge-modern.bg-green {
        background: linear-gradient(90deg, #4caf50, #2e7d32);
    }

    .badge-modern.bg-orange {
        background: linear-gradient(90deg, #ff9800, #e65100);
    }

    .badge-modern.bg-dark-blue {
        background: linear-gradient(90deg, #0d47a1, #01579b);
    }

    .badge-modern.bg-gray {
        background: linear-gradient(90deg, #607d8b, #455a64);
    }

    /* Modern Table styling */
    .table-modern {
        border-radius: 10px;
        overflow: hidden;
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .table-modern thead {
        background: linear-gradient(90deg, #1a73e8, #0d47a1);
    }

    .table-modern thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
        padding: 15px;
        border: none;
        border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    }

    .table-modern tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #f1f1f1;
    }

    .table-modern tbody tr:last-child {
        border-bottom: none;
    }

    .table-modern tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-modern tbody tr:hover {
        background-color: rgba(26, 115, 232, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .table-modern tbody td {
        padding: 15px;
        vertical-align: middle;
        border: none;
        white-space: nowrap;
    }

    .table-modern td.text-end {
        font-family: 'Roboto Mono', monospace, sans-serif;
    }

    .table-modern .product-name {
        font-weight: 600;
        color: #344767;
        margin-bottom: 4px;
        white-space: normal;
    }

    .table-modern .product-category {
        font-size: 0.8rem;
        color: #6c757d;
        white-space: normal;
    }

    .table-modern tfoot tr {
        background: linear-gradient(90deg, #f8f9fa, #e9ecef);
    }

    .table-modern tfoot td {
        padding: 15px;
        font-size: 1rem;
        border-top: 2px solid #e9ecef;
    }

    /* Card header styling */
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Share link styling */
    .sharelink {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        color: #495057;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .sharelink:hover {
        background-color: #0d6efd;
        color: white;
        transform: translateY(-2px);
    }

    /* Button styling */
    .btn-light {
        background-color: #f8f9fa;
        border-color: #e9ecef;
        color: #495057;
        transition: all 0.3s ease;
    }

    .btn-light:hover {
        background-color: #e9ecef;
        border-color: #dde2e6;
        transform: translateY(-1px);
    }

    /* Total summary styling */
    .total-summary {
        margin: 20px 0;
    }

    /* Info Card styling */
    .info-card {
        display: flex;
        align-items: center;
        background: white;
        border-radius: 10px;
        padding: 0;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .info-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .info-card-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        color: white;
    }

    .info-card-icon i {
        font-size: 24px;
    }

    .info-card-icon.bg-blue {
        background: linear-gradient(135deg, #1a73e8, #0d47a1);
    }

    .info-card-icon.bg-green {
        background: linear-gradient(135deg, #4caf50, #2e7d32);
    }

    .info-card-content {
        padding: 15px 20px;
        flex-grow: 1;
    }

    .info-card-label {
        font-size: 0.75rem;
        text-transform: uppercase;
        color: #6c757d;
        font-weight: 600;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
    }

    .info-card-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #344767;
    }

    .info-card-value.text-success {
        color: #28a745;
    }

    /* Text styling */
    .text-inverse {
        color: #344767;
        font-weight: 600;
    }

    /* Address styling */
    address {
        margin-bottom: 0;
        font-style: normal;
    }

    address small {
        color: #6c757d;
        font-size: 0.85rem;
    }

    address strong {
        display: block;
        margin-bottom: 5px;
    }

    address a {
        color: #0d6efd;
        text-decoration: none;
    }

    address a:hover {
        text-decoration: underline;
    }
</style>
@section('content')

<div class="row d-flex align-items-stretch">
    <!-- Card utama dengan ukuran 10 -->
    <div class="col-md-12">
        @php
            $nama_klpd = $paketPengadaan->instansi_pembeli;
            if (strpos($nama_klpd, "Kab.") !== false) {
                $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
            }
            $lpse = Lpse::where('nama_lpse', 'LIKE', "%{$nama_klpd}%")->first();
            if ($lpse) {
        $lpse->logo_path = str_replace('http://proyeklpse.test', '', $lpse->logo_path);
        }

        @endphp
        <div class="card">
            <div class="row g-0">
                <div class="col-md-2 border-end">
                    <div class="p-3 d-flex align-items-center justify-content-center h-100">
                        @if($lpse)
                            <img src="{{ asset(str_replace('http://proyeklpse.test', '', $lpse->logo_path)) }}"
                                class="rounded-start" alt="Logo Instansi">
                        @else
                            <img src="{{ asset('build/images/logo-default.png') }}" class="rounded-start" alt="Logo Default">
                        @endif
                    </div>
                </div>
                <div class="col-md-10">
                    <div class="card-body">
                        <h5 class="card-title mb-3">{{ $paketPengadaan->nama_paket }}</h5>

                        <div class="d-flex flex-wrap gap-2 mb-4">
                            <div class="badge-modern bg-blue">
                                <i class="material-icons-outlined">tag</i>
                                <span>{{ $paketPengadaan->nomor_paket }}</span>
                            </div>
                            <div class="badge-modern bg-cyan">
                                <i class="material-icons-outlined">receipt_long</i>
                                <a href="{{ route('sirup.index') }}?search={{ $paketPengadaan->rup_id }}" class="text-white text-decoration-none" target="_blank">
                                    RUP #{{ $paketPengadaan->rup_id }}
                                </a>
                            </div>
                            <div class="badge-modern bg-green">
                                <i class="material-icons-outlined">payments</i>
                                <span>{{ convert_to_rupiah($totalNilaiKontrak) }}</span>
                            </div>
                        </div>

                        <div class="badge-modern bg-dark-blue mb-3" style="max-width: 100%;">
                            <i class="material-icons-outlined">account_balance</i>
                            <span>{{ $paketPengadaan->instansi_pembeli }}</span>
                        </div>

                        <div class="mt-4 d-flex align-items-center justify-content-between">
                            <a href="#" class="btn btn-light d-flex align-items-center gap-2 px-3">
                                <i class="material-icons-outlined">event</i>
                                {{ \Carbon\Carbon::parse($paketPengadaan->tanggal_paket, 'UTC')->setTimezone('Asia/Jakarta')->format('j F Y H:i') }} WIB
                            </a>

                            <div class="d-flex gap-2">
                                <a href="{{ url()->previous() }}" class="sharelink">
                                    <i class="material-icons-outlined">arrow_back</i>
                                </a>
                                <button type="button" class="sharelink" id="printButton">
                                    <i class="material-icons-outlined">print</i>
                                </button>
                                <div class="dropdown position-relative">
                                    <a href="javascript:;" class="sharelink dropdown-toggle dropdown-toggle-nocaret"
                                        data-bs-auto-close="outside" data-bs-toggle="dropdown">
                                        <i class="material-icons-outlined">share</i>
                                    </a>
                                    <div
                                        class="dropdown-menu dropdown-menu-end dropdown-menu-share shadow-lg border-0 p-3">
                                        <div class="input-group">
                                            <input id="shareUrl" type="text" class="form-control ps-5"
                                                value="{{ url()->current() }}" placeholder="Enter Url">
                                            <span
                                                class="material-icons-outlined position-absolute ms-3 translate-middle-y start-0 top-50">link</span>
                                            <button class="input-group-text gap-1" id="copyButton">
                                                <i class="material-icons-outlined fs-6">content_copy</i>Copy link
                                            </button>
                                        </div>
                                        <div class="d-flex align-items-center gap-2 mt-3">
                                            <a href="https://pinterest.com/pin/create/button/?url={{ url()->current() }}"
                                                target="_blank"
                                                class="py-1 px-3 border-0 rounded bg-pinterest text-white flex-fill d-flex gap-1">
                                                <i class="bi bi-pinterest"></i>Pinterest
                                            </a>
                                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ url()->current() }}"
                                                target="_blank"
                                                class="py-1 px-3 border-0 rounded bg-facebook text-white flex-fill d-flex gap-1">
                                                <i class="bi bi-facebook"></i>Facebook
                                            </a>
                                            <a href="https://www.linkedin.com/shareArticle?url={{ url()->current() }}&title={{ $paketPengadaan->nama_paket }}"
                                                target="_blank"
                                                class="py-1 px-3 border-0 rounded bg-linkedin text-white flex-fill d-flex gap-1">
                                                <i class="bi bi-linkedin"></i>LinkedIn
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>

<div class="row d-flex align-items-stretch">
    <div class="col-md-12">

        <div class="card radius-10">
            <div class="card-header py-3" style="background: linear-gradient(to right, #f8f9fa, #e9ecef);">
                <div class="row align-items-center g-3">
                    <div class="col-12 col-lg-6">
                        <h5 class="mb-0 fw-bold">
                            <i class="material-icons-outlined me-2 align-middle">inventory_2</i>
                            Detail Paket Ekatalog - {{ $paketPengadaan->nomor_paket }}
                        </h5>
                    </div>
                    <div class="col-12 col-lg-6 text-md-end">
                        <div class="badge-modern bg-blue">
                            <i class="material-icons-outlined">category</i>
                            <span>{{$paketPengadaan->jenis_katalog}}</span>
                        </div>
                        @php
                            switch($paketPengadaan->status_paket) {
                                case 'paket_selesai':
                                    $statusClass = 'bg-green';
                                    $statusIcon = 'check_circle';
                                    $status = 'Paket Selesai';
                                    break;
                                case 'melakukan_pengiriman_dan_penerimaan':
                                    $statusClass = 'bg-blue';
                                    $statusIcon = 'local_shipping';
                                    $status = 'On Progress';
                                    break;
                                case 'proses_kontrak_ppk':
                                    $statusClass = 'bg-orange';
                                    $statusIcon = 'schedule';
                                    $status = 'Proses Kontrak PPK';
                                    break;
                                default:
                                    $statusClass = 'bg-gray';
                                    $statusIcon = 'help';
                                    $status = 'Status Tidak Diketahui';
                            }
                        @endphp
                        <div class="badge-modern {{ $statusClass }} ms-2">
                            <i class="material-icons-outlined">{{ $statusIcon }}</i>
                            <span>{{ $status }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-header py-3" style="background-color: #ffffff; border-bottom: 1px solid rgba(0,0,0,0.05);">
                <div class="row row-cols-1 row-cols-lg-4 g-3">
                    <div class="col">
                        <div class="info-box p-3 rounded" style="background-color: #f8f9fa;">
                            <small class="text-muted d-flex align-items-center">
                                <i class="material-icons-outlined me-1" style="font-size: 14px;">storefront</i>
                                Etalase
                            </small>
                            <div class="mt-2">
                                <strong class="text-inverse">{{ $paketPengadaan->etalase }}</strong>
                            </div>
                        </div>
                    </div>

                    <div class="col">
                        <div class="info-box p-3 rounded" style="background-color: #f8f9fa;">
                            <small class="text-muted d-flex align-items-center">
                                <i class="material-icons-outlined me-1" style="font-size: 14px;">account_balance</i>
                                Instansi Pembeli
                            </small>
                            <div class="mt-2">
                                <strong class="text-inverse">{{ $paketPengadaan->instansi_pembeli }}</strong>
                                <div class="text-muted small">Satker: {{ $paketPengadaan->satuan_kerja }}</div>
                            </div>
                        </div>
                    </div>

                    <div class="col">
                        <div class="info-box p-3 rounded" style="background-color: #f8f9fa;">
                            <small class="text-muted d-flex align-items-center">
                                <i class="material-icons-outlined me-1" style="font-size: 14px;">business</i>
                                Perusahaan Penyedia
                            </small>
                            <div class="mt-2">
                                <strong class="text-inverse">
                                    <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($paketPengadaan->penyedia->pembeda)]) }}" class="text-primary">
                                        {{ $paketPengadaan->penyedia->nama_peserta }}
                                    </a>
                                </strong>
                                @if ($paketPengadaan->penyedia->alamat)
                                    <div class="text-muted small">{{ \Illuminate\Support\Str::limit($paketPengadaan->penyedia->alamat, 50) }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col">
                        <div class="info-box p-3 rounded" style="background-color: #f8f9fa;">
                            <small class="text-muted d-flex align-items-center">
                                <i class="material-icons-outlined me-1" style="font-size: 14px;">engineering</i>
                                Perusahaan Pelaksana
                            </small>
                            <div class="mt-2">
                                <strong class="text-inverse">
                                    <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($paketPengadaan->pelaksana->pembeda)]) }}" class="text-primary">
                                        {{ $paketPengadaan->pelaksana->nama_peserta }}
                                    </a>
                                </strong>
                                @if ($paketPengadaan->pelaksana->alamat)
                                    <div class="text-muted small">{{ \Illuminate\Support\Str::limit($paketPengadaan->pelaksana->alamat, 50) }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <h6 class="mb-3 fw-bold d-flex align-items-center">
                    <i class="material-icons-outlined me-2">shopping_basket</i>
                    Daftar Produk dalam Paket
                </h6>

                <div class="table-responsive">
                    <table class="table-modern">
                        <thead>
                            <tr>
                                <th class="text-center" width="5%">No</th>
                                <th width="22%">Nama Produk</th>
                                <th width="13%">Jenis Produk</th>
                                <th width="13%">Pabrikan</th>
                                <th class="text-center" width="10%">Kuantitas</th>
                                <th class="text-end" width="13%">Harga Satuan</th>
                                <th class="text-end" width="13%">Harga Ongkir</th>
                                <th class="text-end" width="16%">Total Harga</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($paketPengadaan->transaksiPaket as $transaksi)
                                <tr>
                                    <td class="text-center">{{ $loop->iteration }}</td>
                                    <td>
                                        <div class="product-name">{{ $transaksi->produk->nama_produk }}</div>
                                        <div class="product-category">{{ $transaksi->produk->kategori_lv1 }} - {{ $transaksi->produk->kategori_lv2 }}</div>
                                    </td>
                                    <td>{{ $transaksi->produk->jenis_produk }}</td>
                                    <td>
                                        @if ($transaksi->produk->nama_manufaktur)
                                            {{ $transaksi->produk->nama_manufaktur }}
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td class="text-center fw-semibold">{{ number_format($transaksi->kuantitas_produk, 0, ',', '.') }}</td>
                                    <td class="text-end">{{ convert_to_rupiah($transaksi->harga_satuan) }}</td>
                                    <td class="text-end">{{ convert_to_rupiah($transaksi->harga_ongkos_kirim) }}</td>
                                    <td class="text-end fw-bold">{{ convert_to_rupiah($transaksi->total_harga) }}</td>
                                </tr>
                            @endforeach
                        </tbody>

                    </table>
                </div>

                <div class="total-summary">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="info-card" style="background-color: #f8f9fa;">
                                <div class="info-card-icon bg-green">
                                    <i class="material-icons-outlined">payments</i>
                                </div>
                                <div class="info-card-content">
                                    <div class="info-card-label">TOTAL NILAI KONTRAK</div>
                                    <div class="info-card-value text-success">{{ convert_to_rupiah($totalNilaiKontrak) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer py-3" style="background-color: #f8f9fa;">
                <div id="dlUraian"></div>
            </div>
        </div>

    </div>



</div>

@endsection



@section('scripts')
<script>
    // Tunggu sampai dokumen sepenuhnya dimuat
    document.addEventListener('DOMContentLoaded', function() {
        // Setup copy button
        document.getElementById('copyButton').addEventListener('click', function () {
            var shareUrl = document.getElementById('shareUrl');

            // Pilih teks secara manual
            shareUrl.select();
            shareUrl.setSelectionRange(0, 99999); // Untuk mendukung perangkat mobile

            // Salin teks dengan cara manual
            try {
                var successful = document.execCommand('copy');
                var msg = successful ? 'Link berhasil disalin ke clipboard' : 'Gagal menyalin link';
                alert(msg);
            } catch (err) {
                console.error('Gagal menyalin link: ', err);
            }
        });

        // Setup print button - pendekatan sederhana
        document.getElementById('printButton').addEventListener('click', function() {
            // Tampilkan feedback visual
            var button = this;
            button.style.backgroundColor = '#0d6efd';
            button.style.color = 'white';

            // Tambahkan CSS print ke head
            var printCSS = document.createElement('style');
            printCSS.type = 'text/css';
            printCSS.id = 'print-css';
            printCSS.innerHTML = `
                @media print {
                    @page {
                        size: A3 landscape;
                        margin: 0;
                    }

                    /* Hide elements that shouldn't be printed */
                    .top-header, header,
                    .sidebar-wrapper,
                    .page-footer, footer, .footer, .page-footer-wrapper,
                    button, .btn, .no-print, .sharelink, .dropdown, .btn-light,
                    nav, aside, .page-breadcrumb,
                    .page-wrapper > .topbar, .page-wrapper > .sidebar-wrapper {
                        display: none !important;
                    }

                    /* Main content styling */
                    #content, .page-content {
                        display: block !important;
                        position: static !important;
                        width: 100% !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    body {
                        background-color: white !important;
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    /* Reset wrapper and containers */
                    .wrapper, .page-wrapper, #wrapper {
                        margin-left: 0 !important;
                        padding-left: 0 !important;
                        width: 100% !important;
                    }

                    .container-fluid, .container, .row {
                        width: 100% !important;
                        max-width: 100% !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    /* Card styling for print */
                    .card {
                        box-shadow: none !important;
                        border: 1px solid #dee2e6 !important;
                        break-inside: avoid !important;
                        page-break-inside: avoid !important;
                        margin-bottom: 15px !important;
                    }

                    .card-header, .card-footer {
                        background-color: white !important;
                    }

                    /* Badge styling for print */
                    .badge, .badge-modern {
                        border: none !important;
                        color: white !important;
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    .badge-modern.bg-blue {
                        background: linear-gradient(90deg, #1a73e8, #0d47a1) !important;
                    }

                    .badge-modern.bg-cyan {
                        background: linear-gradient(90deg, #00bcd4, #0097a7) !important;
                    }

                    .badge-modern.bg-green {
                        background: linear-gradient(90deg, #4caf50, #2e7d32) !important;
                    }

                    .badge-modern.bg-orange {
                        background: linear-gradient(90deg, #ff9800, #e65100) !important;
                    }

                    .badge-modern.bg-dark-blue {
                        background: linear-gradient(90deg, #0d47a1, #01579b) !important;
                    }

                    .badge-modern.bg-gray {
                        background: linear-gradient(90deg, #607d8b, #455a64) !important;
                    }

                    /* Ensure tables don't break across pages */
                    table {
                        page-break-inside: avoid !important;
                    }

                    .table-responsive {
                        overflow: visible !important;
                    }

                    /* Ensure images are properly sized */
                    img {
                        max-height: 100px !important;
                    }

                    /* Fix for logo positioning */
                    .border-end img {
                        display: block !important;
                        margin: 0 auto !important;
                    }

                    /* Column adjustments */
                    .col-md-12 {
                        width: 100% !important;
                        max-width: 100% !important;
                        flex: 0 0 100% !important;
                    }

                    /* Preserve original layout for logo and info */
                    .col-md-2.border-end {
                        width: 15% !important;
                        max-width: 15% !important;
                        flex: 0 0 15% !important;
                        display: inline-block !important;
                        vertical-align: top !important;
                    }

                    .col-md-10 {
                        width: 85% !important;
                        max-width: 85% !important;
                        flex: 0 0 85% !important;
                        display: inline-block !important;
                        vertical-align: top !important;
                    }

                    /* Ensure the first card has proper layout */
                    .row.d-flex.align-items-stretch {
                        display: flex !important;
                        flex-wrap: wrap !important;
                    }

                    /* Box sizing for all elements */
                    * {
                        box-sizing: border-box !important;
                    }
                }
            `;
            document.head.appendChild(printCSS);

            // Panggil print
            setTimeout(function() {
                window.print();

                // Kembalikan tampilan tombol dan hapus CSS
                setTimeout(function() {
                    button.style.backgroundColor = '';
                    button.style.color = '';

                    // Hapus CSS print
                    var printStyle = document.getElementById('print-css');
                    if (printStyle) {
                        printStyle.parentNode.removeChild(printStyle);
                    }
                }, 1000);
            }, 300);
        });
    });
</script>
@endsection