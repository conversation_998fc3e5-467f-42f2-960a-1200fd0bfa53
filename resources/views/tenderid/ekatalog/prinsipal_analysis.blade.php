@extends('layouts.master')

@section('title', '<PERSON><PERSON><PERSON> (Penyedia E-Katalog)')

@section('css')
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<style>
    /* Card Styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 18px 24px;
    }

    .card-header.gradient-header {
        background: linear-gradient(90deg, #4481eb, #04befe);
        color: white;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
    }

    .card-body {
        padding: 24px;
    }

    /* Form Controls */
    .form-control, .form-select {
        border-radius: 8px;
        padding: 10px 15px;
        border: 1px solid #e9ecef;
        font-size: 0.9rem;
        transition: all 0.2s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    }

    .form-control:focus, .form-select:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    }

    .input-group-text {
        border-radius: 8px 0 0 8px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .form-label {
        font-weight: 500;
        font-size: 0.85rem;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    /* Buttons */
    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
        color: white;
        box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
    }

    .btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0b5ed7;
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
    }

    .btn-outline-secondary {
        background-color: #f8f9fa;
        border-color: #e9ecef;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background-color: #e9ecef;
        border-color: #dfe2e5;
        color: #495057;
    }

    /* Loading Container */
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }

    .loading-spinner {
        width: 3rem;
        height: 3rem;
        margin-bottom: 1rem;
    }

    /* Card Stats */
    .card-stats {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
    }

    .card-stats:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    /* Table Styling */
    .table-responsive {
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }

    .table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table thead th {
        background-color: #f8f9fa;
        color: #344767;
        font-weight: 600;
        border-top: none;
        padding: 16px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e9ecef;
    }

    .table tbody td {
        padding: 16px;
        vertical-align: middle;
        border-bottom: 1px solid #f8f9fa;
        font-size: 0.9rem;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    .table tbody tr {
        transition: all 0.2s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
    }

    /* Product and Company Items */
    .product-item, .company-item {
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
    }

    .product-item:hover, .company-item:hover {
        background-color: #f8f9fa;
        border-left: 3px solid #5e72e4;
    }

    /* Badges */
    .badge {
        padding: 6px 10px;
        font-weight: 500;
        font-size: 0.75rem;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        line-height: 1;
    }

    .badge-value {
        font-size: 0.85rem;
        font-weight: 500;
    }

    /* Chart Container */
    .chart-container {
        min-height: 300px;
    }

    /* Filter toggle styling */
    .filter-toggle-icon {
        transition: transform 0.3s ease;
    }

    .collapsed .filter-toggle-icon {
        transform: rotate(180deg);
    }

    /* Select2 Customization */
    .select2-container {
        width: 100% !important;
    }

    .select2-container--default .select2-selection--single {
        height: 42px !important;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        padding: 5px 12px;
        background-color: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 30px;
        padding-left: 0;
        color: #495057;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field:focus {
        border-color: #0d6efd;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #0d6efd;
    }
</style>
@endsection

@section('content')
<x-page-title title="E-Katalog" pagetitle="Analisis Prinsipal (Penyedia)" />

<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header d-flex align-items-center justify-content-between gradient-header">
                <h5 class="card-title mb-0">
                    <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
                        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
                        <span>Filter Analisis Prinsipal</span>
                        <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
                    </a>
                </h5>
                <span class="badge bg-white text-primary">
                    {{ count($prinsipalList) }} Prinsipal
                </span>
            </div>
            <div class="collapse show" id="filterCollapse">
                <div class="card-body">
                    <form id="filter-form" action="{{ route('ekatalog.prinsipal') }}" method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="tahun" class="form-label">Tahun</label>
                            <select class="form-select" id="tahun" name="tahun">
                                @foreach($tahunList as $t)
                                    <option value="{{ $t }}" {{ $tahun == $t ? 'selected' : '' }}>{{ $t }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="kode_prinsipal" class="form-label">Prinsipal (Penyedia)</label>
                            <select class="form-select select2" id="kode_prinsipal" name="kode_prinsipal">
                                <option value="">-- Semua Prinsipal --</option>
                                @foreach($prinsipalList as $prinsipal)
                                    <option value="{{ $prinsipal->kode_prinsipal }}" {{ isset($kode_prinsipal) && $kode_prinsipal == $prinsipal->kode_prinsipal ? 'selected' : '' }}>{{ $prinsipal->nama_peserta }}</option>
                                @endforeach
                            </select>
                            <div class="form-text">Pilih prinsipal untuk melihat analisis spesifik</div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="material-icons-outlined" style="font-size: 18px; vertical-align: text-bottom;">filter_alt</i>
                                <span>Terapkan Filter</span>
                            </button>
                            @if(isset($kode_prinsipal) && $kode_prinsipal)
                                <a href="{{ route('ekatalog.prinsipal', ['tahun' => $tahun]) }}" class="btn btn-outline-secondary">
                                    <i class="material-icons-outlined" style="font-size: 18px; vertical-align: text-bottom;">clear</i>
                                    <span>Reset</span>
                                </a>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistik Utama -->
<div class="row" id="stats-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat statistik...</p>
        </div>
    </div>
</div>

<!-- Produk Paling Laku -->
<div class="row" id="top-products-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat data produk terlaris...</p>
        </div>
    </div>
</div>

<!-- Instansi Paling Banyak Belanja -->
<div class="row" id="top-buyers-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat data instansi pembeli...</p>
        </div>
    </div>
</div>

<!-- Distributor Paling Aktif -->
<div class="row" id="top-distributors-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat data distributor...</p>
        </div>
    </div>
</div>

<!-- Merek Paling Populer -->
<div class="row" id="top-brands-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat data merek populer...</p>
        </div>
    </div>
</div>
@endsection

@section('css')
@parent
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="{{ URL::asset('build/js/ekatalog-prinsipal-analysis.js') }}?v=2942"></script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // Debug information
    console.log('DOM loaded, initializing prinsipal analysis...');

    // Initialize Select2 with proper theme and styling
    try {
        $('.select2').select2({
            placeholder: "-- Pilih Prinsipal --",
            allowClear: true,
            width: '100%',
            dropdownCssClass: "select2-dropdown-custom",
            selectionCssClass: "select2-selection-custom"
        });
        console.log('Select2 initialized successfully');
    } catch (error) {
        console.error('Error initializing Select2:', error);
    }

    // Get current year and prinsipal from select
    const tahun = document.getElementById('tahun').value;
    const kode_prinsipal = document.getElementById('kode_prinsipal').value;
    console.log('Current year:', tahun);
    console.log('Current kode prinsipal:', kode_prinsipal);

    // Get base URL and ensure it uses HTTPS
    let baseUrl = '{{ url("/") }}';
    baseUrl = baseUrl.replace(/^http:\/\//i, 'https://');

    // Add global error handler for debugging
    window.addEventListener('error', function(e) {
        console.error('Global error caught:', e.message, 'at', e.filename, 'line', e.lineno);
    });

    // Add unhandled promise rejection handler
    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled Promise Rejection:', e.reason);
    });

    try {
        // Load all data
        loadStats(tahun, kode_prinsipal, baseUrl);
        loadTopProducts(tahun, kode_prinsipal, baseUrl);
        loadTopBuyers(tahun, kode_prinsipal, baseUrl);
        loadTopDistributors(tahun, kode_prinsipal, baseUrl);
        loadTopBrands(tahun, kode_prinsipal, baseUrl);
    } catch (error) {
        console.error('Error loading data:', error);
    }

    // Handle filter form submission
    document.getElementById('filter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const tahun = document.getElementById('tahun').value;
        const kode_prinsipal = document.getElementById('kode_prinsipal').value;

        // Reset all containers to loading state
        document.getElementById('stats-container').innerHTML = getLoadingHTML('Memuat statistik...');
        document.getElementById('top-products-container').innerHTML = getLoadingHTML('Memuat data produk terlaris...');
        document.getElementById('top-buyers-container').innerHTML = getLoadingHTML('Memuat data instansi pembeli...');
        document.getElementById('top-distributors-container').innerHTML = getLoadingHTML('Memuat data distributor...');
        document.getElementById('top-brands-container').innerHTML = getLoadingHTML('Memuat data merek populer...');

        // Load data with new filter
        loadStats(tahun, kode_prinsipal, baseUrl);
        loadTopProducts(tahun, kode_prinsipal, baseUrl);
        loadTopBuyers(tahun, kode_prinsipal, baseUrl);
        loadTopDistributors(tahun, kode_prinsipal, baseUrl);
        loadTopBrands(tahun, kode_prinsipal, baseUrl);

        // Update URL without reloading page
        const url = new URL(window.location);
        url.searchParams.set('tahun', tahun);
        if (kode_prinsipal) {
            url.searchParams.set('kode_prinsipal', kode_prinsipal);
        } else {
            url.searchParams.delete('kode_prinsipal');
        }
        window.history.pushState({}, '', url);
    });

    // Handle tahun change to update prinsipal list
    document.getElementById('tahun').addEventListener('change', function() {
        const tahun = this.value;
        const kode_prinsipal_select = document.getElementById('kode_prinsipal');

        // Show loading in select
        kode_prinsipal_select.innerHTML = '<option value="">Loading...</option>';
        $(kode_prinsipal_select).prop('disabled', true);

        // Destroy and reinitialize Select2 to show loading state
        if ($.fn.select2 && $.fn.select2.instances && $.fn.select2.instances[kode_prinsipal_select.id]) {
            $(kode_prinsipal_select).select2('destroy');
        }
        $(kode_prinsipal_select).select2({
            placeholder: "Loading...",
            disabled: true,
            width: '100%'
        });

        // Fetch prinsipal list for selected year
        fetch(`${baseUrl}/api/ekatalog-prinsipal/prinsipal-list?tahun=${tahun}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Reset select
                kode_prinsipal_select.innerHTML = '<option value="">-- Semua Prinsipal --</option>';

                // Add options
                if (data.prinsipalList && data.prinsipalList.length > 0) {
                    data.prinsipalList.forEach(prinsipal => {
                        const option = document.createElement('option');
                        option.value = prinsipal.kode_prinsipal; // Gunakan kode_prinsipal yang terenkripsi
                        option.textContent = prinsipal.nama_peserta;
                        kode_prinsipal_select.appendChild(option);
                    });
                    console.log(`Loaded ${data.prinsipalList.length} prinsipal options`);
                } else {
                    console.log('No prinsipal data returned from API');
                }

                // Re-enable select
                $(kode_prinsipal_select).prop('disabled', false);

                // Destroy and reinitialize Select2
                if ($.fn.select2 && $.fn.select2.instances && $.fn.select2.instances[kode_prinsipal_select.id]) {
                    $(kode_prinsipal_select).select2('destroy');
                }

                $(kode_prinsipal_select).select2({
                    placeholder: "-- Pilih Prinsipal --",
                    allowClear: true,
                    width: '100%',
                    dropdownCssClass: "select2-dropdown-custom",
                    selectionCssClass: "select2-selection-custom"
                });
            })
            .catch(error => {
                console.error('Error fetching prinsipal list:', error);
                kode_prinsipal_select.innerHTML = '<option value="">-- Error loading data --</option>';
                $(kode_prinsipal_select).prop('disabled', false);

                // Reinitialize Select2 in error state
                if ($.fn.select2 && $.fn.select2.instances && $.fn.select2.instances[kode_prinsipal_select.id]) {
                    $(kode_prinsipal_select).select2('destroy');
                }

                $(kode_prinsipal_select).select2({
                    placeholder: "-- Error loading data --",
                    allowClear: true,
                    width: '100%'
                });
            });
    });

    // Initialize filter collapse functionality
    const filterToggle = document.querySelector('[data-bs-toggle="collapse"]');
    if (filterToggle) {
        filterToggle.addEventListener('click', function() {
            const icon = this.querySelector('.filter-toggle-icon');
            if (icon) {
                icon.classList.toggle('collapsed');
            }
        });
    }
});

// Helper function to generate loading HTML
function getLoadingHTML(message) {
    return `
        <div class="col-12">
            <div class="loading-container">
                <div class="spinner-border text-primary loading-spinner" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p>${message}</p>
            </div>
        </div>
    `;
}
</script>
@endsection
