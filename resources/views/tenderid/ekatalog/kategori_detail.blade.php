@extends('layouts.master')

@section('title', 'Detail Kategori ' . $kategori)

@section('css')
<link href="{{ URL::asset('build/libs/apexcharts/apexcharts.min.css') }}" rel="stylesheet">
@endsection

@section('content')
<x-page-title title="e-Katalog" pagetitle="Detail Kategori" />

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex align-items-center">
                <h5 class="card-title mb-0 flex-grow-1">Ana<PERSON><PERSON>: {{ $kategori }}</h5>
                <div class="flex-shrink-0">
                    <a href="{{ route('ekatalog.analytics', ['tahun' => $tahun]) }}" class="btn btn-primary">
                        <i class="material-icons-outlined align-middle me-1">arrow_back</i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form action="{{ route('ekatalog.analytics.kategori', ['kategori' => $kategori]) }}" method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="tahun" class="form-label">Tahun</label>
                        <select class="form-select" id="tahun" name="tahun" onchange="this.form.submit()">
                            @php
                                $currentYear = date('Y');
                                $startYear = $currentYear - 5;
                            @endphp
                            @for($year = $currentYear; $year >= $startYear; $year--)
                                <option value="{{ $year }}" {{ $tahun == $year ? 'selected' : '' }}>{{ $year }}</option>
                            @endfor
                        </select>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Tren Bulanan Kategori -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Tren Nilai Kontrak Bulanan Kategori {{ $kategori }} ({{ $tahun }})</h4>
            </div>
            <div class="card-body">
                <div id="chart-tren-bulanan" class="apex-charts" dir="ltr"></div>
            </div>
        </div>
    </div>
</div>

<!-- Top Produk dalam Kategori -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Top Produk dalam Kategori {{ $kategori }}</h4>
            </div>
            <div class="card-body">
                <div id="chart-top-produk" class="apex-charts" dir="ltr"></div>
                <div class="mt-4">
                    <div class="table-responsive">
                        <table class="table table-centered mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>No</th>
                                    <th>Nama Produk</th>
                                    <th>Total Kuantitas</th>
                                    <th>Total Nilai</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($topProdukKategori as $index => $produk)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>{{ $produk->nama_produk }}</td>
                                    <td>{{ number_format($produk->total_kuantitas, 0, ',', '.') }}</td>
                                    <td>Rp {{ number_format($produk->total_nilai, 0, ',', '.') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Instansi Pembeli untuk Kategori -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Top Instansi Pembeli untuk Kategori {{ $kategori }}</h4>
            </div>
            <div class="card-body">
                <div id="chart-top-instansi" class="apex-charts" dir="ltr"></div>
                <div class="mt-4">
                    <div class="table-responsive">
                        <table class="table table-centered mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>No</th>
                                    <th>Instansi Pembeli</th>
                                    <th>Jumlah Paket</th>
                                    <th>Total Nilai</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($topInstansiKategori as $index => $instansi)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>{{ $instansi->instansi_pembeli }}</td>
                                    <td>{{ number_format($instansi->jumlah_paket, 0, ',', '.') }}</td>
                                    <td>Rp {{ number_format($instansi->total_nilai, 0, ',', '.') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="{{ URL::asset('build/libs/apexcharts/apexcharts.min.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // Tren Bulanan
    var trenBulananOptions = {
        series: [{
            name: 'Nilai Kontrak',
            data: [
                @foreach(range(1, 12) as $bulan)
                    {{ $trenBulanan->where('bulan', $bulan)->first()->total_nilai ?? 0 }},
                @endforeach
            ]
        }],
        chart: {
            type: 'area',
            height: 350,
            toolbar: {
                show: false
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2,
        },
        xaxis: {
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
        },
        yaxis: {
            title: {
                text: 'Nilai Kontrak (Rp)'
            },
            labels: {
                formatter: function (value) {
                    return new Intl.NumberFormat('id-ID', { 
                        style: 'currency', 
                        currency: 'IDR',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(value);
                }
            }
        },
        colors: ['#4b38b3'],
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.6,
                opacityTo: 0.1,
                stops: [0, 90, 100]
            }
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return new Intl.NumberFormat('id-ID', { 
                        style: 'currency', 
                        currency: 'IDR',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(val);
                }
            }
        }
    };
    var trenBulananChart = new ApexCharts(document.querySelector("#chart-tren-bulanan"), trenBulananOptions);
    trenBulananChart.render();

    // Top Produk
    var topProdukOptions = {
        series: [{
            name: 'Total Kuantitas',
            data: [
                @foreach($topProdukKategori as $produk)
                    {{ $produk->total_kuantitas }},
                @endforeach
            ]
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                borderRadius: 4,
                horizontal: true,
            }
        },
        dataLabels: {
            enabled: false
        },
        xaxis: {
            categories: [
                @foreach($topProdukKategori as $produk)
                    '{{ Str::limit($produk->nama_produk, 30) }}',
                @endforeach
            ],
        },
        colors: ['#4b38b3'],
    };
    var topProdukChart = new ApexCharts(document.querySelector("#chart-top-produk"), topProdukOptions);
    topProdukChart.render();

    // Top Instansi
    var topInstansiOptions = {
        series: [{
            name: 'Total Nilai',
            data: [
                @foreach($topInstansiKategori as $instansi)
                    {{ $instansi->total_nilai }},
                @endforeach
            ]
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                borderRadius: 4,
                horizontal: true,
            }
        },
        dataLabels: {
            enabled: false
        },
        xaxis: {
            categories: [
                @foreach($topInstansiKategori as $instansi)
                    '{{ Str::limit($instansi->instansi_pembeli, 30) }}',
                @endforeach
            ],
        },
        colors: ['#0ab39c'],
        tooltip: {
            y: {
                formatter: function (val) {
                    return new Intl.NumberFormat('id-ID', { 
                        style: 'currency', 
                        currency: 'IDR',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(val);
                }
            }
        }
    };
    var topInstansiChart = new ApexCharts(document.querySelector("#chart-top-instansi"), topInstansiOptions);
    topInstansiChart.render();
});
</script>
@endsection
