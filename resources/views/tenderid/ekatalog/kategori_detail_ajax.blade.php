@extends('layouts.master')

@section('title', 'Detail Kategori ' . $kategori)

@section('css')
<style>
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }
    .loading-spinner {
        width: 3rem;
        height: 3rem;
        margin-bottom: 1rem;
    }
</style>
@endsection

@section('content')
<x-page-title title="e-Katalog" pagetitle="Detail Kategori" />

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex align-items-center">
                <h5 class="card-title mb-0 flex-grow-1">Analisis <PERSON>gor<PERSON>: {{ $kategori }}</h5>
                <div class="flex-shrink-0">
                    <a href="{{ url('/ekatalog-analytics-ajax') }}" class="btn btn-primary">
                        <i class="material-icons-outlined align-middle me-1">arrow_back</i> <PERSON><PERSON><PERSON>
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-4">
                        <label for="tahun" class="form-label">Tahun</label>
                        <select class="form-select" id="tahun" name="tahun">
                            @php
                                $currentYear = date('Y');
                                $startYear = $currentYear - 5;
                            @endphp
                            @for($year = $currentYear; $year >= $startYear; $year--)
                                <option value="{{ $year }}" {{ $tahun == $year ? 'selected' : '' }}>{{ $year }}</option>
                            @endfor
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">Terapkan Filter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Tren Bulanan Kategori -->
<div class="row" id="tren-bulanan-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat tren bulanan...</p>
        </div>
    </div>
</div>

<!-- Top Produk dalam Kategori -->
<div class="row" id="top-produk-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat data produk...</p>
        </div>
    </div>
</div>

<!-- Top Instansi Pembeli untuk Kategori -->
<div class="row" id="top-instansi-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat data instansi...</p>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script src="{{ URL::asset('build/js/ekatalog-kategori-detail-ajax.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // Debug information
    console.log('DOM loaded, initializing kategori detail...');

    // Get current year and kategori
    const tahun = document.getElementById('tahun').value;
    const kategori = '{{ $kategori }}';
    console.log('Current year:', tahun, 'Kategori:', kategori);

    // Get base URL and ensure it uses HTTPS
    let baseUrl = '{{ url("/") }}';
    baseUrl = baseUrl.replace(/^http:\/\//i, 'https://');

    // Add global error handler for debugging
    window.addEventListener('error', function(e) {
        console.error('Global error caught:', e.message, 'at', e.filename, 'line', e.lineno);
    });

    // Add unhandled promise rejection handler
    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled Promise Rejection:', e.reason);
    });

    try {
        // Load data
        loadKategoriDetail(kategori, tahun, baseUrl);
    } catch (error) {
        console.error('Error loading kategori detail:', error);
    }

    // Handle filter form submission
    document.getElementById('filter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const tahun = document.getElementById('tahun').value;

        // Reset all containers to loading state
        document.getElementById('tren-bulanan-container').innerHTML = getLoadingHTML('Memuat tren bulanan...');
        document.getElementById('top-produk-container').innerHTML = getLoadingHTML('Memuat data produk...');
        document.getElementById('top-instansi-container').innerHTML = getLoadingHTML('Memuat data instansi...');

        // Load data with new filter
        loadKategoriDetail(kategori, tahun, baseUrl);
    });
});
</script>
@endsection
