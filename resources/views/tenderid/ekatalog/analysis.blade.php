@extends('layouts.master')

@section('title', 'Analisis E-Katalog')

@section('css')
<style>
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 0 0 !important;
        padding: 16px 24px;
        display: flex;
        align-items: center;
    }

    .card-title {
        font-weight: 600;
        color: #344767;
        margin-bottom: 0;
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .card-body {
        padding: 24px;
    }

    .stats-card {
        background: linear-gradient(to right, #0d6efd, #0a58ca);
        color: white;
        border-radius: 12px;
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .stats-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stats-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .stats-icon {
        font-size: 2.5rem;
        opacity: 0.2;
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
    }

    .product-item, .vendor-item, .brand-item, .category-item {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 15px;
        background-color: #f8f9fa;
        border-left: 4px solid #0d6efd;
        transition: all 0.2s ease;
    }

    .product-item:hover, .vendor-item:hover, .brand-item:hover, .category-item:hover {
        background-color: #f0f4f8;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .institution-item {
        background-color: #ffffff;
        border-left: 4px solid #e9ecef;
        transition: all 0.2s ease;
    }

    .institution-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08) !important;
    }

    .institution-item.border-primary {
        border: 1px solid rgba(13, 110, 253, 0.2);
        border-left: 4px solid #0d6efd;
    }

    .institution-item.border-success {
        border: 1px solid rgba(25, 135, 84, 0.2);
        border-left: 4px solid #198754;
    }

    .expensive-product-item {
        background-color: #ffffff;
        border: 1px solid rgba(220, 53, 69, 0.1);
        transition: all 0.2s ease;
    }

    .expensive-product-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(220, 53, 69, 0.1) !important;
    }

    .price-tag {
        background: linear-gradient(to right, #f8f9fa, #fff3f5);
    }

    .popular-product-item {
        background-color: #ffffff;
        border: 1px solid rgba(255, 193, 7, 0.1);
        transition: all 0.2s ease;
    }

    .popular-product-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(255, 193, 7, 0.1) !important;
    }

    .quantity-tag {
        background: linear-gradient(to right, #f8f9fa, #fffbf0);
    }

    .vendor-card {
        background-color: #ffffff;
        border: 1px solid rgba(13, 110, 253, 0.1);
        transition: all 0.2s ease;
    }

    .vendor-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(13, 110, 253, 0.1) !important;
    }

    .executor-card {
        background-color: #ffffff;
        border: 1px solid rgba(220, 53, 69, 0.1);
        transition: all 0.2s ease;
    }

    .executor-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(220, 53, 69, 0.1) !important;
    }

    .product-title, .vendor-name, .brand-name, .category-name {
        font-weight: 600;
        color: #344767;
        margin-bottom: 8px;
        font-size: 0.95rem;
    }

    .product-info, .vendor-info, .brand-info, .category-info {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-top: 10px;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .info-label {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .info-value {
        font-weight: 600;
        color: #344767;
        font-size: 0.85rem;
    }

    .badge {
        padding: 6px 10px;
        font-weight: 500;
        font-size: 0.75rem;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        line-height: 1;
    }

    .badge i {
        font-size: 12px;
        margin-right: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: baseline;
    }

    .chart-container {
        height: 300px;
    }

    .filter-section {
        margin-bottom: 20px;
    }

    .filter-section .btn-group .btn {
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .filter-section .btn-group .btn.active {
        background-color: #0d6efd;
        color: white;
    }

    /* Skeleton loader */
    .skeleton-loader {
        position: relative;
        overflow: hidden;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        min-height: 1.5em;
    }

    .skeleton-loader::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        transform: translateX(-100%);
        background-image: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0,
            rgba(255, 255, 255, 0.2) 20%,
            rgba(255, 255, 255, 0.5) 60%,
            rgba(255, 255, 255, 0)
        );
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        100% {
            transform: translateX(100%);
        }
    }

    .product-item-skeleton, .vendor-item-skeleton, .brand-item-skeleton, .category-item-skeleton {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 15px;
        background-color: #f8f9fa;
        border-left: 4px solid #0d6efd;
        height: 100px;
    }
</style>
@apexchartsScripts
@endsection

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">analytics</i>
                <h5 class="card-title mb-0">Analisis E-Katalog</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Analisis komprehensif tentang data E-Katalog, termasuk produk termahal, produk paling banyak dibeli, penyedia paling aktif, dan tren pembelian.</p>

                <div class="filter-section">
                    <form action="{{ route('ekatalog.analysis') }}" method="GET">
                        <div class="btn-group" role="group">
                            <button type="submit" name="period" value="all" class="btn {{ $period == 'all' ? 'btn-primary' : 'btn-light' }}">Semua Waktu</button>
                            <button type="submit" name="period" value="year" class="btn {{ $period == 'year' ? 'btn-primary' : 'btn-light' }}">1 Tahun</button>
                            <button type="submit" name="period" value="month" class="btn {{ $period == 'month' ? 'btn-primary' : 'btn-light' }}">1 Bulan</button>
                            <button type="submit" name="period" value="week" class="btn {{ $period == 'week' ? 'btn-primary' : 'btn-light' }}">1 Minggu</button>
                            <button type="submit" name="period" value="today" class="btn {{ $period == 'today' ? 'btn-primary' : 'btn-light' }}">Hari Ini</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4" id="statistics-container">
    <div class="col-md-3">
        <div class="stats-card position-relative">
            <div>
                <div class="stats-value skeleton-loader">-</div>
                <div class="stats-label">Total Paket</div>
            </div>
            <i class="material-icons-outlined stats-icon">inventory_2</i>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card position-relative" style="background: linear-gradient(to right, #198754, #157347);">
            <div>
                <div class="stats-value skeleton-loader">-</div>
                <div class="stats-label">Total Nilai</div>
            </div>
            <i class="material-icons-outlined stats-icon">payments</i>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card position-relative" style="background: linear-gradient(to right, #dc3545, #b02a37);">
            <div>
                <div class="stats-value skeleton-loader">-</div>
                <div class="stats-label">Total Penyedia</div>
            </div>
            <i class="material-icons-outlined stats-icon">business</i>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card position-relative" style="background: linear-gradient(to right, #6f42c1, #5a34a4);">
            <div>
                <div class="stats-value skeleton-loader">-</div>
                <div class="stats-label">Total Produk</div>
            </div>
            <i class="material-icons-outlined stats-icon">category</i>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Grafik Tren Bulanan -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">trending_up</i>
                <h5 class="card-title mb-0">Tren Bulanan E-Katalog (12 Bulan Terakhir)</h5>
            </div>
            <div class="card-body">
                <div id="monthly-trend-chart" class="chart-container">
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instansi Pembeli Paling Aktif -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">account_balance</i>
                <h5 class="card-title mb-0">Instansi Pembeli Paling Aktif</h5>
            </div>
            <div class="card-body" id="top-buyers-container">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Produk Termahal -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">diamond</i>
                <h5 class="card-title mb-0">Produk Termahal</h5>
            </div>
            <div class="card-body" id="most-expensive-products-container">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Produk Paling Banyak Dibeli -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">shopping_cart</i>
                <h5 class="card-title mb-0">Produk Paling Banyak Dibeli</h5>
            </div>
            <div class="card-body" id="most-purchased-products-container">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Penyedia Paling Banyak -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">business</i>
                <h5 class="card-title mb-0">Penyedia Paling Aktif</h5>
            </div>
            <div class="card-body" id="top-vendors-container">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pelaksana Paling Banyak -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">engineering</i>
                <h5 class="card-title mb-0">Pelaksana Paling Aktif</h5>
            </div>
            <div class="card-body" id="top-executors-container">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Merek Paling Laris -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">verified</i>
                <h5 class="card-title mb-0">Merek Paling Laris</h5>
            </div>
            <div class="card-body" id="top-brands-container">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kategori Paling Populer -->
        <div class="card">
            <div class="card-header">
                <i class="material-icons-outlined text-primary me-2" style="font-size: 20px; vertical-align: text-bottom;">category</i>
                <h5 class="card-title mb-0">Kategori Paling Populer</h5>
            </div>
            <div class="card-body" id="top-categories-container">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="{{ URL::asset('build/plugins/apexchart/apexcharts.min.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mendapatkan periode yang dipilih
        const selectedPeriod = '{{ $period }}';

        // Fungsi untuk memformat angka ke format Rupiah
        function formatRupiah(angka, prefix = 'Rp') {
            // Jika angka sangat besar (triliun atau lebih), format dengan T
            if (angka >= 1000000000000) {
                return prefix + (angka / 1000000000000).toFixed(2) + ' T';
            }
            // Jika angka besar (milyar atau lebih), format dengan M
            else if (angka >= 1000000000) {
                return prefix + (angka / 1000000000).toFixed(2) + ' M';
            }
            // Jika angka sedang (juta atau lebih), format dengan Jt
            else if (angka >= 1000000) {
                return prefix + (angka / 1000000).toFixed(2) + ' Jt';
            }
            // Jika angka kecil (ribu atau lebih), format dengan Rb
            else if (angka >= 1000) {
                return prefix + (angka / 1000).toFixed(2) + ' Rb';
            }

            // Format angka biasa
            const numberString = angka.toString().replace(/[^,\d]/g, '');
            const split = numberString.split(',');
            const sisa = split[0].length % 3;
            let rupiah = split[0].substr(0, sisa);
            const ribuan = split[0].substr(sisa).match(/\d{3}/gi);

            if (ribuan) {
                const separator = sisa ? '.' : '';
                rupiah += separator + ribuan.join('.');
            }

            rupiah = split[1] !== undefined ? rupiah + ',' + split[1] : rupiah;
            return prefix === undefined ? rupiah : (rupiah ? prefix + rupiah : '');
        }

        // Fungsi untuk memuat statistik
        function loadStatistics() {
            fetch(`/api/ekatalog-analisis/statistics?period=${selectedPeriod}`)
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('statistics-container');

                    // Update nilai statistik
                    container.innerHTML = `
                        <div class="col-md-3">
                            <div class="stats-card position-relative">
                                <div>
                                    <div class="stats-value">${formatNumber(data.total_packages)}</div>
                                    <div class="stats-label">Total Paket</div>
                                </div>
                                <i class="material-icons-outlined stats-icon">inventory_2</i>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card position-relative" style="background: linear-gradient(to right, #198754, #157347);">
                                <div>
                                    <div class="stats-value">${formatRupiah(data.total_value)}</div>
                                    <div class="stats-label">Total Nilai</div>
                                </div>
                                <i class="material-icons-outlined stats-icon">payments</i>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card position-relative" style="background: linear-gradient(to right, #dc3545, #b02a37);">
                                <div>
                                    <div class="stats-value">${formatNumber(data.total_vendors)}</div>
                                    <div class="stats-label">Total Penyedia</div>
                                </div>
                                <i class="material-icons-outlined stats-icon">business</i>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card position-relative" style="background: linear-gradient(to right, #6f42c1, #5a34a4);">
                                <div>
                                    <div class="stats-value">${formatNumber(data.total_products)}</div>
                                    <div class="stats-label">Total Produk</div>
                                </div>
                                <i class="material-icons-outlined stats-icon">category</i>
                            </div>
                        </div>
                    `;
                })
                .catch(error => {
                    console.error('Error loading statistics:', error);
                });
        }

        // Fungsi untuk memuat tren bulanan
        function loadMonthlyTrend() {
            fetch(`/api/ekatalog-analisis/monthly-trend`)
                .then(response => response.json())
                .then(monthlyData => {
                    // Memformat data untuk chart
                    const months = monthlyData.map(item => {
                        const [year, month] = item.month.split('-');
                        return new Date(year, month - 1).toLocaleString('id-ID', { month: 'short', year: 'numeric' });
                    });

                    const totalPackages = monthlyData.map(item => item.total_packages);
                    const totalValues = monthlyData.map(item => item.total_value / 1000000000); // Convert to billions

                    // Membuat chart
                    const options = {
                        series: [{
                            name: 'Jumlah Paket',
                            type: 'column',
                            data: totalPackages
                        }, {
                            name: 'Nilai Total (Miliar)',
                            type: 'line',
                            data: totalValues
                        }],
                        chart: {
                            height: 300,
                            type: 'line',
                            stacked: false,
                            toolbar: {
                                show: false
                            }
                        },
                        stroke: {
                            width: [0, 3],
                            curve: 'smooth'
                        },
                        plotOptions: {
                            bar: {
                                columnWidth: '50%',
                                borderRadius: 5
                            }
                        },
                        fill: {
                            opacity: [0.85, 1],
                            gradient: {
                                inverseColors: false,
                                shade: 'light',
                                type: "vertical",
                                opacityFrom: 0.85,
                                opacityTo: 0.55,
                                stops: [0, 100, 100, 100]
                            }
                        },
                        markers: {
                            size: 0
                        },
                        xaxis: {
                            categories: months
                        },
                        yaxis: [
                            {
                                title: {
                                    text: 'Jumlah Paket',
                                },
                            },
                            {
                                opposite: true,
                                title: {
                                    text: 'Nilai Total (Miliar)'
                                }
                            }
                        ],
                        tooltip: {
                            shared: true,
                            intersect: false,
                            y: {
                                formatter: function (y, { seriesIndex }) {
                                    if (seriesIndex === 0) {
                                        return y.toFixed(0) + " paket";
                                    } else {
                                        return "Rp" + y.toFixed(2) + " M";
                                    }
                                }
                            }
                        },
                        colors: ['#0d6efd', '#198754']
                    };

                    // Hapus spinner
                    document.getElementById('monthly-trend-chart').innerHTML = '';

                    // Render chart
                    const chart = new ApexCharts(document.querySelector("#monthly-trend-chart"), options);
                    chart.render();
                })
                .catch(error => {
                    console.error('Error loading monthly trend:', error);
                });
        }

        // Fungsi untuk memuat produk termahal
        function loadTopProducts() {
            fetch(`/api/ekatalog-analisis/top-products?period=${selectedPeriod}`)
                .then(response => response.json())
                .then(data => {
                    // Produk termahal
                    let mostExpensiveHtml = '';
                    data.most_expensive.forEach((product, index) => {
                        const priceTag = formatRupiah(product.max_price);
                        const rankBadge = index < 3 ?
                            `<div class="position-absolute top-0 end-0 mt-2 me-2">
                                <span class="badge bg-danger px-2 py-1">
                                    <i class="material-icons-outlined" style="font-size: 12px; vertical-align: text-bottom;">diamond</i>
                                    #${index + 1}
                                </span>
                             </div>` : '';

                        mostExpensiveHtml += `
                            <div class="expensive-product-item position-relative p-3 mb-3 rounded shadow-sm">
                                ${rankBadge}
                                <div class="d-flex align-items-center mb-3">
                                    <div class="product-icon me-3 d-flex align-items-center justify-content-center rounded-circle bg-light" style="width: 48px; height: 48px;">
                                        <i class="material-icons-outlined text-danger" style="font-size: 24px;">inventory_2</i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold">
                                            ${product.nama_produk}
                                        </h6>
                                        <div class="small text-muted">${product.kategori_lv1}</div>
                                    </div>
                                </div>

                                <div class="price-tag mb-2 p-2 bg-light rounded text-center">
                                    <span class="fw-bold text-danger" style="font-size: 1.2rem;">${priceTag}</span>
                                </div>

                                <div class="d-flex justify-content-between mt-2">
                                    <div class="d-flex flex-column">
                                        <div class="d-flex align-items-center mb-1">
                                            <i class="material-icons-outlined text-info me-1" style="font-size: 14px;">business</i>
                                            <span class="small">
                                                ${product.nama_manufaktur}
                                            </span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="material-icons-outlined text-warning me-1" style="font-size: 14px;">shopping_cart</i>
                                            <span class="small">${formatNumber(product.total_quantity)} unit</span>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <i class="material-icons-outlined text-primary me-1" style="font-size: 14px;">inventory_2</i>
                                            <span class="small">${formatNumber(product.total_packages)} paket</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    document.getElementById('most-expensive-products-container').innerHTML = mostExpensiveHtml;

                    // Produk paling banyak dibeli
                    let mostPurchasedHtml = '';
                    data.most_purchased.forEach((product, index) => {
                        const rankBadge = index < 3 ?
                            `<div class="position-absolute top-0 end-0 mt-2 me-2">
                                <span class="badge bg-warning px-2 py-1">
                                    <i class="material-icons-outlined" style="font-size: 12px; vertical-align: text-bottom;">shopping_cart</i>
                                    #${index + 1}
                                </span>
                             </div>` : '';

                        mostPurchasedHtml += `
                            <div class="popular-product-item position-relative p-3 mb-3 rounded shadow-sm">
                                ${rankBadge}
                                <div class="d-flex align-items-center mb-3">
                                    <div class="product-icon me-3 d-flex align-items-center justify-content-center rounded-circle bg-light" style="width: 48px; height: 48px;">
                                        <i class="material-icons-outlined text-warning" style="font-size: 24px;">shopping_cart</i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold">
                                            ${product.nama_produk}
                                        </h6>
                                        <div class="small text-muted">${product.kategori_lv1}</div>
                                    </div>
                                </div>

                                <div class="quantity-tag mb-2 p-2 bg-light rounded d-flex justify-content-between align-items-center">
                                    <span class="fw-bold text-warning" style="font-size: 1.2rem;">${formatNumber(product.total_quantity)}</span>
                                    <span class="text-muted small">unit terjual</span>
                                </div>

                                <div class="d-flex justify-content-between mt-2">
                                    <div class="d-flex flex-column">
                                        <div class="d-flex align-items-center mb-1">
                                            <i class="material-icons-outlined text-info me-1" style="font-size: 14px;">business</i>
                                            <span class="small">
                                                ${product.nama_manufaktur}
                                            </span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="material-icons-outlined text-primary me-1" style="font-size: 14px;">payments</i>
                                            <span class="small">${formatRupiah(product.avg_price)}</span>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <i class="material-icons-outlined text-primary me-1" style="font-size: 14px;">inventory_2</i>
                                            <span class="small">${formatNumber(product.total_packages)} paket</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    document.getElementById('most-purchased-products-container').innerHTML = mostPurchasedHtml;
                })
                .catch(error => {
                    console.error('Error loading top products:', error);
                });
        }

        // Fungsi untuk memuat penyedia paling aktif
        function loadTopVendors() {
            fetch(`/api/ekatalog-analisis/top-vendors?period=${selectedPeriod}`)
                .then(response => response.json())
                .then(vendors => {
                    let html = '';
                    vendors.forEach((vendor, index) => {
                        const rankBadge = index < 3 ?
                            `<div class="position-absolute top-0 end-0 mt-2 me-2">
                                <span class="badge bg-primary px-2 py-1">
                                    <i class="material-icons-outlined" style="font-size: 12px; vertical-align: text-bottom;">star</i>
                                    #${index + 1}
                                </span>
                             </div>` : '';

                        html += `
                            <div class="vendor-card position-relative p-3 mb-3 rounded shadow-sm">
                                ${rankBadge}
                                <div class="d-flex align-items-center mb-2">
                                    <div class="vendor-icon me-3 d-flex align-items-center justify-content-center rounded-circle bg-primary bg-opacity-10" style="width: 48px; height: 48px;">
                                        <i class="material-icons-outlined text-primary" style="font-size: 24px;">business</i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0 fw-bold">
                                            <a href="#" class="text-decoration-none text-dark company-link" data-pembeda="${vendor.pembeda}">
                                                ${vendor.nama_peserta}
                                            </a>
                                        </h6>
                                        <div class="small text-muted">
                                            NPWP: ${vendor.npwp || vendor.npwp_blur}
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mt-3 pt-2 border-top">
                                    <div>
                                        <div class="d-flex align-items-center">
                                            <i class="material-icons-outlined text-primary me-1" style="font-size: 16px;">inventory_2</i>
                                            <span class="fw-bold">${formatNumber(vendor.total_packages)}</span>
                                            <span class="ms-1 text-muted small">paket</span>
                                        </div>
                                        <div class="mt-1">
                                            <span class="fw-bold text-success">${formatRupiah(vendor.total_value)}</span>
                                            <span class="text-muted small">nilai</span>
                                        </div>
                                    </div>
                                    <a href="#" class="btn btn-sm btn-light border company-analysis-link" data-pembeda="${vendor.pembeda}" title="Analisis Perusahaan">
                                        <i class="material-icons-outlined text-primary" style="font-size: 16px; vertical-align: text-bottom;">analytics</i>
                                        <span class="ms-1 d-none d-md-inline">Analisis</span>
                                    </a>
                                </div>
                            </div>
                        `;
                    });
                    document.getElementById('top-vendors-container').innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading top vendors:', error);
                });
        }

        // Fungsi untuk memuat pelaksana paling aktif
        function loadTopExecutors() {
            fetch(`/api/ekatalog-analisis/top-executors?period=${selectedPeriod}`)
                .then(response => response.json())
                .then(executors => {
                    let html = '';
                    executors.forEach((executor, index) => {
                        const rankBadge = index < 3 ?
                            `<div class="position-absolute top-0 end-0 mt-2 me-2">
                                <span class="badge bg-danger px-2 py-1">
                                    <i class="material-icons-outlined" style="font-size: 12px; vertical-align: text-bottom;">engineering</i>
                                    #${index + 1}
                                </span>
                             </div>` : '';

                        html += `
                            <div class="executor-card position-relative p-3 mb-3 rounded shadow-sm">
                                ${rankBadge}
                                <div class="d-flex align-items-center mb-2">
                                    <div class="executor-icon me-3 d-flex align-items-center justify-content-center rounded-circle bg-danger bg-opacity-10" style="width: 48px; height: 48px;">
                                        <i class="material-icons-outlined text-danger" style="font-size: 24px;">engineering</i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0 fw-bold">
                                            <a href="#" class="text-decoration-none text-dark company-link" data-pembeda="${executor.pembeda}">
                                                ${executor.nama_peserta}
                                            </a>
                                        </h6>
                                        <div class="small text-muted">
                                            NPWP: ${executor.npwp || executor.npwp_blur}
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mt-3 pt-2 border-top">
                                    <div>
                                        <div class="d-flex align-items-center">
                                            <i class="material-icons-outlined text-primary me-1" style="font-size: 16px;">inventory_2</i>
                                            <span class="fw-bold">${formatNumber(executor.total_packages)}</span>
                                            <span class="ms-1 text-muted small">paket</span>
                                        </div>
                                        <div class="mt-1">
                                            <span class="fw-bold text-success">${formatRupiah(executor.total_value)}</span>
                                            <span class="text-muted small">nilai</span>
                                        </div>
                                    </div>
                                    <a href="#" class="btn btn-sm btn-light border company-analysis-link" data-pembeda="${executor.pembeda}" title="Analisis Perusahaan">
                                        <i class="material-icons-outlined text-danger" style="font-size: 16px; vertical-align: text-bottom;">analytics</i>
                                        <span class="ms-1 d-none d-md-inline">Analisis</span>
                                    </a>
                                </div>
                            </div>
                        `;
                    });
                    document.getElementById('top-executors-container').innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading top executors:', error);
                });
        }

        // Fungsi untuk memuat merek paling laris
        function loadTopBrands() {
            fetch(`/api/ekatalog-analisis/top-brands?period=${selectedPeriod}`)
                .then(response => response.json())
                .then(brands => {
                    let html = '';
                    brands.forEach(brand => {
                        html += `
                            <div class="brand-item p-2">
                                <div class="brand-name">
                                    <i class="material-icons-outlined me-1" style="font-size: 16px; vertical-align: text-bottom;">verified</i>
                                    ${brand.nama_manufaktur}
                                </div>
                                <div class="brand-info">
                                    <div class="info-item">
                                        <i class="material-icons-outlined text-primary" style="font-size: 14px; vertical-align: text-bottom;">inventory_2</i>
                                        <span class="info-label">Paket:</span>
                                        <span class="info-value">${formatNumber(brand.total_packages)}</span>
                                    </div>
                                    <div class="info-item">
                                        <i class="material-icons-outlined text-success" style="font-size: 14px; vertical-align: text-bottom;">category</i>
                                        <span class="info-label">Produk:</span>
                                        <span class="info-value">${formatNumber(brand.total_products)}</span>
                                    </div>
                                    <div class="info-item">
                                        <i class="material-icons-outlined text-info" style="font-size: 14px; vertical-align: text-bottom;">payments</i>
                                        <span class="info-label">Nilai:</span>
                                        <span class="info-value">${formatRupiah(brand.total_value)}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    document.getElementById('top-brands-container').innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading top brands:', error);
                });
        }

        // Fungsi untuk memuat kategori paling populer
        function loadTopCategories() {
            fetch(`/api/ekatalog-analisis/top-categories?period=${selectedPeriod}`)
                .then(response => response.json())
                .then(categories => {
                    let html = '';
                    categories.forEach(category => {
                        html += `
                            <div class="category-item p-2">
                                <div class="category-name">
                                    <i class="material-icons-outlined me-1" style="font-size: 16px; vertical-align: text-bottom;">category</i>
                                    ${category.kategori_lv1}
                                </div>
                                <div class="category-info">
                                    <div class="info-item">
                                        <i class="material-icons-outlined text-primary" style="font-size: 14px; vertical-align: text-bottom;">inventory_2</i>
                                        <span class="info-label">Paket:</span>
                                        <span class="info-value">${formatNumber(category.total_packages)}</span>
                                    </div>
                                    <div class="info-item">
                                        <i class="material-icons-outlined text-success" style="font-size: 14px; vertical-align: text-bottom;">category</i>
                                        <span class="info-label">Produk:</span>
                                        <span class="info-value">${formatNumber(category.total_products)}</span>
                                    </div>
                                    <div class="info-item">
                                        <i class="material-icons-outlined text-info" style="font-size: 14px; vertical-align: text-bottom;">payments</i>
                                        <span class="info-label">Nilai:</span>
                                        <span class="info-value">${formatRupiah(category.total_value)}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    document.getElementById('top-categories-container').innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading top categories:', error);
                });
        }

        // Fungsi untuk memformat angka
        function formatNumber(number) {
            return new Intl.NumberFormat('id-ID').format(number);
        }

        // Fungsi untuk memuat instansi pembeli paling aktif
        function loadTopBuyers() {
            fetch(`/api/ekatalog-analisis/top-buyers?period=${selectedPeriod}`)
                .then(response => response.json())
                .then(data => {
                    let html = '<div class="row">';

                    // Instansi dengan jumlah paket terbanyak
                    html += '<div class="col-md-6 mb-4"><h6 class="fw-bold mb-3">Berdasarkan Jumlah Paket</h6>';
                    data.top_by_packages.forEach((buyer, index) => {
                        const colorClass = index < 3 ? 'border-primary' : '';
                        const rankBadge = index < 3 ? `<span class="position-absolute top-0 start-0 translate-middle badge rounded-pill bg-primary">#${index + 1}</span>` : '';

                        html += `
                            <div class="institution-item position-relative mb-3 p-3 rounded shadow-sm ${colorClass}">
                                ${rankBadge}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="material-icons-outlined text-primary me-2" style="font-size: 24px;">account_balance</i>
                                    <h6 class="mb-0 fw-bold">
                                        ${buyer.instansi_pembeli}
                                    </h6>
                                </div>
                                <div class="d-flex justify-content-between mt-2">
                                    <div>
                                        <div class="d-flex align-items-center">
                                            <i class="material-icons-outlined text-primary me-1" style="font-size: 16px;">inventory_2</i>
                                            <span class="fw-bold">${formatNumber(buyer.total_packages)}</span>
                                            <span class="ms-1 text-muted small">paket</span>
                                        </div>
                                        <div class="small text-muted mt-1">
                                            ${buyer.satuan_kerja || 'Satuan Kerja Tidak Diketahui'}
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-success">${formatRupiah(buyer.total_value)}</div>
                                        <div class="small text-muted">total nilai</div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';

                    // Instansi dengan nilai transaksi tertinggi
                    html += '<div class="col-md-6 mb-4"><h6 class="fw-bold mb-3">Berdasarkan Nilai Transaksi</h6>';
                    data.top_by_value.forEach((buyer, index) => {
                        const colorClass = index < 3 ? 'border-success' : '';
                        const rankBadge = index < 3 ? `<span class="position-absolute top-0 start-0 translate-middle badge rounded-pill bg-success">#${index + 1}</span>` : '';

                        html += `
                            <div class="institution-item position-relative mb-3 p-3 rounded shadow-sm ${colorClass}">
                                ${rankBadge}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="material-icons-outlined text-success me-2" style="font-size: 24px;">account_balance</i>
                                    <h6 class="mb-0 fw-bold">
                                        ${buyer.instansi_pembeli}
                                    </h6>
                                </div>
                                <div class="d-flex justify-content-between mt-2">
                                    <div>
                                        <div class="d-flex align-items-center">
                                            <i class="material-icons-outlined text-primary me-1" style="font-size: 16px;">inventory_2</i>
                                            <span class="fw-bold">${formatNumber(buyer.total_packages)}</span>
                                            <span class="ms-1 text-muted small">paket</span>
                                        </div>
                                        <div class="small text-muted mt-1">
                                            ${buyer.satuan_kerja || 'Satuan Kerja Tidak Diketahui'}
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-success" style="font-size: 1.1rem;">${formatRupiah(buyer.total_value)}</div>
                                        <div class="small text-muted">total nilai</div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';

                    html += '</div>';
                    document.getElementById('top-buyers-container').innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading top buyers:', error);
                });
        }

        // Memuat semua data
        loadStatistics();
        loadMonthlyTrend();
        loadTopProducts();
        loadTopVendors();
        loadTopExecutors();
        loadTopBrands();
        loadTopCategories();
        loadTopBuyers();

        // Menangani klik pada link perusahaan dan analisis
        document.addEventListener('click', function(e) {
            // Link detail perusahaan
            if (e.target.closest('.company-link')) {
                e.preventDefault();
                const link = e.target.closest('.company-link');
                const pembeda = link.dataset.pembeda;

                fetch(`/api/helper/company-urls?pembeda=${pembeda}`)
                    .then(response => response.json())
                    .then(data => {
                        window.location.href = data.detail_url;
                    })
                    .catch(error => {
                        console.error('Error getting company URL:', error);
                    });
            }

            // Link analisis perusahaan
            if (e.target.closest('.company-analysis-link')) {
                e.preventDefault();
                const link = e.target.closest('.company-analysis-link');
                const pembeda = link.dataset.pembeda;

                fetch(`/api/helper/company-urls?pembeda=${pembeda}`)
                    .then(response => response.json())
                    .then(data => {
                        window.location.href = data.analysis_url;
                    })
                    .catch(error => {
                        console.error('Error getting company analysis URL:', error);
                    });
            }
        });
    });
</script>
@endsection
