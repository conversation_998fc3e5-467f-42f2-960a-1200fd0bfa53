{{-- resources/views/ekatalog/index.blade.php --}}
@php
  use App\Models\Lpse;
@endphp
@extends('layouts.master')

@section('title', 'Semua Paket Ekatalog')

@section('css')
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <style>
        /* Card Styling */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            margin-bottom: 24px;
        }

        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 12px 12px 0 0 !important;
            padding: 18px 24px;
        }

        .card-title {
            font-weight: 600;
            color: #344767;
            margin-bottom: 0;
            font-size: 1.1rem;
        }

        .card-body {
            padding: 24px;
        }

        /* Form Controls */
        .form-control, .form-select {
            border-radius: 8px;
            padding: 10px 15px;
            border: 1px solid #e9ecef;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
        }

        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
        }

        .input-group-text {
            border-radius: 8px 0 0 8px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .form-label {
            font-weight: 500;
            font-size: 0.85rem;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        /* Buttons */
        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
            box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
        }

        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0b5ed7;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
        }

        .btn-secondary {
            background-color: #f8f9fa;
            border-color: #e9ecef;
            color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #e9ecef;
            border-color: #dfe2e5;
            color: #495057;
        }

        /* Table Styling */
        .table-responsive {
            border: none;
            border-radius: 12px;
            overflow: hidden;
        }

        .table {
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background-color: #f8f9fa;
            color: #344767;
            font-weight: 600;
            border-top: none;
            padding: 16px;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e9ecef;
        }

        .table tbody td {
            padding: 16px;
            vertical-align: middle;
            border-bottom: 1px solid #f8f9fa;
            font-size: 0.9rem;
            word-wrap: break-word;
            white-space: normal;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
        }

        /* Product Box */
        .product-box {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .product-box img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .product-box img:hover {
            transform: scale(1.15);
        }

        /* Product Info */
        .product-info {
            flex: 1;
        }

        .product-title {
            font-weight: 600;
            color: #0d6efd;
            display: block;
            margin-bottom: 5px;
            font-size: 0.95rem;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .product-title:hover {
            color: #0b5ed7;
            text-decoration: underline;
        }

        .product-category {
            color: #6c757d;
            font-size: 0.85rem;
            margin-bottom: 0;
        }

        .product-category a {
            color: #6c757d;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .product-category a:hover {
            color: #0d6efd;
            text-decoration: underline;
        }

        /* Badges */
        .badge {
            padding: 6px 10px;
            font-weight: 500;
            font-size: 0.75rem;
            border-radius: 6px;
            display: inline-flex;
            align-items: center;
            line-height: 1;
        }

        .badge i {
            font-size: 12px;
            margin-right: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            vertical-align: baseline;
        }

        .badge.bg-success {
            background-color: #198754 !important;
            box-shadow: 0 2px 5px rgba(25, 135, 84, 0.2);
        }

        .badge.bg-danger {
            background-color: #dc3545 !important;
            box-shadow: 0 2px 5px rgba(220, 53, 69, 0.2);
        }

        .badge.bg-warning {
            background-color: #ffc107 !important;
            color: #212529;
            box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
        }

        .badge.bg-info {
            background-color: #0dcaf0 !important;
            box-shadow: 0 2px 5px rgba(13, 202, 240, 0.2);
        }

        .badge.bg-secondary {
            background-color: #6c757d !important;
            box-shadow: 0 2px 5px rgba(108, 117, 125, 0.2);
        }

        .badge.bg-light {
            background-color: #f8f9fa !important;
            color: #212529;
            box-shadow: 0 2px 5px rgba(248, 249, 250, 0.2);
        }

        .badge.bg-facebook {
            background-color: #3b5998 !important;
            color: white;
            box-shadow: 0 2px 5px rgba(59, 89, 152, 0.2);
        }

        /* Pagination */
        .pagination {
            margin-top: 24px;
            justify-content: center;
            gap: 5px;
        }

        .pagination .page-link {
            border-radius: 8px;
            margin: 0 3px;
            color: #0d6efd;
            border: 1px solid #e9ecef;
            padding: 8px 15px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .pagination .page-link:hover {
            background-color: #f8f9fa;
            color: #0b5ed7;
            border-color: #e9ecef;
            z-index: 1;
        }

        .pagination .active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
            box-shadow: 0 4px 8px rgba(13, 110, 253, 0.2);
        }

        /* Select2 Customization */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--single {
            height: 38px !important;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            padding: 5px 12px;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 28px;
            padding-left: 0;
            color: #495057;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 8px 12px;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field:focus {
            border-color: #0d6efd;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #0d6efd;
        }

        /* Filter toggle styling */
        .filter-toggle-icon {
            transition: transform 0.3s ease;
        }

        .collapsed .filter-toggle-icon {
            transform: rotate(180deg);
        }

        /* Filter buttons styling - konsisten dengan halaman sirup */
        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
            box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
        }

        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0b5ed7;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
        }

        .btn-outline-secondary {
            background-color: #f8f9fa;
            border-color: #e9ecef;
            color: #6c757d;
        }

        .btn-outline-secondary:hover {
            background-color: #e9ecef;
            border-color: #dfe2e5;
            color: #495057;
        }

        /* Empty state styling */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
        }

        .empty-state i {
            font-size: 48px;
            color: #e9ecef;
            margin-bottom: 15px;
        }

        .empty-state h5 {
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: #adb5bd;
            max-width: 400px;
            margin: 0 auto;
        }
    </style>
@endsection

@section('content')
  <!-- Search and Filter Section -->
  <div class="card mb-4">
    <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
      <h5 class="card-title mb-0">
        <a href="#filterCollapse" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="filterCollapse" class="d-flex align-items-center text-decoration-none text-white">
          <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">filter_list</i>
          <span>Filter Paket e-Katalog</span>
          <i class="material-icons-outlined ms-2 filter-toggle-icon">expand_less</i>
        </a>
      </h5>
      <span class="badge bg-white text-primary" style="font-size: 0.85rem; padding: 8px 12px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
        <i class="material-icons-outlined me-1" style="font-size: 0.9rem; vertical-align: middle;">shopping_bag</i>
        @if(method_exists($paketPengadaan, 'total'))
          {{ number_format($paketPengadaan->total(), 0, ',', '.') }} Paket
        @else
          {{ number_format($paketPengadaan->count(), 0, ',', '.') }} Paket
        @endif
      </span>
    </div>
    <div class="collapse show" id="filterCollapse">
      <div class="card-body">
        <form method="GET" action="{{ route('ekatalog.index') }}">
          <div class="row g-3">
            <!-- Input Pencarian -->
            <div class="col-md-3">
              <label for="search-input" class="form-label">Pencarian</label>
              <div class="input-group">
                <span class="input-group-text"><i class="material-icons-outlined">search</i></span>
                <input type="text" name="search" id="search-input" class="form-control" placeholder="Cari nama paket..."
                  value="{{ request('search') }}">
              </div>
              <div class="form-text">Cari berdasarkan nama paket atau nomor paket</div>
            </div>

            <!-- Dropdown Instansi Pembeli -->
            <div class="col-md-3">
              <label for="instansi-select" class="form-label">Instansi</label>
              <select name="instansi" id="instansi-select" class="form-control select2">
                <option value="">Pilih Instansi</option>
                @foreach($instansi_pembeli_list as $instansi)
                  <option value="{{ $instansi }}" {{ request('instansi') == $instansi ? 'selected' : '' }}>
                    {{ $instansi }}
                  </option>
                @endforeach
              </select>
            </div>

            <!-- Input Tanggal Mulai -->
            <div class="col-md-2">
              <label for="tanggal-mulai" class="form-label">Tanggal Mulai</label>
              <div class="input-group">
                <span class="input-group-text"><i class="material-icons-outlined">event</i></span>
                <input type="date" name="tanggal_mulai" id="tanggal-mulai" class="form-control"
                  value="{{ request('tanggal_mulai') }}">
              </div>
            </div>

            <!-- Input Tanggal Akhir -->
            <div class="col-md-2">
              <label for="tanggal-akhir" class="form-label">Tanggal Akhir</label>
              <div class="input-group">
                <span class="input-group-text"><i class="material-icons-outlined">event</i></span>
                <input type="date" name="tanggal_akhir" id="tanggal-akhir" class="form-control"
                  value="{{ request('tanggal_akhir') }}">
              </div>
            </div>

            <!-- Tombol Filter & Reset -->
            <div class="col-12 mt-4">
              <div class="d-flex justify-content-between gap-2">
                <a href="{{ route('ekatalog.analysis') }}" class="btn btn-light d-inline-flex align-items-center">
                  <i class="material-icons-outlined me-2 text-dark">analytics</i>
                  <span class="text-dark">Analisis E-Katalog</span>
                </a>
                <div class="d-flex gap-2">
                  <button type="submit" class="btn btn-primary d-inline-flex align-items-center">
                    <i class="material-icons-outlined me-2">filter_alt</i>
                    <span>Terapkan Filter</span>
                  </button>
                  <a href="{{ route('ekatalog.index') }}" class="btn btn-outline-secondary d-inline-flex align-items-center">
                    <i class="material-icons-outlined me-2">refresh</i>
                    <span>Reset Filter</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Paket Pengadaan Table Section -->
  <div class="card">
    <div class="card-header d-flex align-items-center justify-content-between">
      <h5 class="card-title mb-0">
        <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: sub;">shopping_cart</i>
        Daftar Paket e-Katalog
      </h5>
      <div>
        <button class="btn btn-sm btn-outline-primary me-2 d-inline-flex align-items-center" id="btn-refresh-data">
          <i class="material-icons-outlined me-1">refresh</i>
          <span>Refresh Data</span>
        </button>
        <div class="dropdown d-inline-block">
          <button class="btn btn-sm btn-outline-secondary dropdown-toggle d-inline-flex align-items-center" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="material-icons-outlined me-1">tune</i>
            <span>Tampilan</span>
          </button>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
            <li><a class="dropdown-item d-flex align-items-center" href="#" id="toggle-compact-view">
              <i class="material-icons-outlined me-2">view_compact</i>
              <span>Tampilan Kompak</span>
            </a></li>
            <li><a class="dropdown-item d-flex align-items-center" href="#" id="toggle-full-view">
              <i class="material-icons-outlined me-2">view_agenda</i>
              <span>Tampilan Lengkap</span>
            </a></li>
          </ul>
        </div>
      </div>
    </div>
    <div class="card-body p-0">
      @if($paketPengadaan->count() > 0)
        <div class="table-responsive">
          <table class="table align-middle mb-0">
            <thead>
              <tr>
                <th style="width: 10%;">Nomor Paket</th>
                <th style="width: 30%;">Nama Paket</th>
                <th style="width: 20%;">Instansi Pembeli & SatKer</th>
                <th style="width: 15%;">Pelaksana</th>
                <th style="width: 15%;">Tanggal Klik</th>
                <th style="width: 10%;">Status Paket</th>
              </tr>
            </thead>
            <tbody>
              @forelse($paketPengadaan as $paket)
                @php
                  $nama_klpd = $paket->instansi_pembeli;
                  if (strpos($nama_klpd, "Kab.") !== false) {
                    $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
                  }
                  $lpse = Lpse::where('nama_lpse', 'LIKE', "%{$nama_klpd}%")->first();
                  if ($lpse) {
                    $lpse->logo_path = str_replace('http://proyeklpse.test', '', $lpse->logo_path);
                  }

                  // Status paket dengan switch
                  switch($paket->status_paket) {
                    case 'paket_selesai':
                      $class = 'bg-success';
                      $icon = 'check_circle';
                      $status = 'Paket Selesai';
                      break;
                    case 'melakukan_pengiriman_dan_penerimaan':
                      $class = 'bg-facebook';
                      $icon = 'local_shipping';
                      $status = 'On Progress';
                      break;
                    case 'proses_kontrak_ppk':
                      $class = 'bg-warning text-dark';
                      $icon = 'schedule';
                      $status = 'Proses Kontrak PPK';
                      break;
                    default:
                      $class = 'bg-secondary';
                      $icon = 'help_outline';
                      $status = 'Status Tidak Diketahui';
                  }
                @endphp
                <tr class="ekatalog-row">
                  <td>
                    <a href="{{ route('ekatalog.show', ['id' => $paket->id_paket]) }}" class="fw-semibold text-primary">
                      #{{ $paket->nomor_paket }}
                    </a>
                  </td>
                  <td>
                    <div class="d-flex align-items-center gap-3">
                      <div class="product-box">
                        @if($lpse)
                          <img src="{{ asset($lpse->logo_path) }}" alt="Logo" class="rounded-3">
                        @else
                          <img src="{{ asset('build/images/logo-default.png') }}" alt="Logo" class="rounded-3">
                        @endif
                      </div>
                      <div class="product-info">
                        <a href="{{ route('ekatalog.show', ['id' => $paket->id_paket]) }}" class="product-title">
                          {{ \Illuminate\Support\Str::limit($paket->nama_paket, 70) }}
                        </a>
                        <div class="mt-1">
                          <span class="badge bg-primary">{{ $paket->jenis_katalog }}</span>
                          <span class="badge bg-secondary">{{ convert_to_rupiah($paket->nilai_kontrak) }}</span>
                        </div>
                        <p class="mb-0 product-category">
                          {{ $paket->pengelola }}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td>
                    <strong>
                      <a href="{{ route('ekatalog.index', ['instansi' => $paket->instansi_pembeli]) }}" class="text-decoration-none">
                        {{ \Illuminate\Support\Str::limit($paket->instansi_pembeli, 40) }}
                      </a>
                    </strong>
                    <br>
                    <span class="text-muted small">Satker: {{ \Illuminate\Support\Str::limit($paket->satuan_kerja, 40) }}</span>
                  </td>
                  <td>
                    <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($paket->pelaksana->pembeda)]) }}" class="text-decoration-none">
                      {{ \Illuminate\Support\Str::limit($paket->pelaksana->nama_peserta, 40) }}
                    </a>
                  </td>
                  <td>
                    <span class="text-muted small d-flex align-items-center">
                      <i class="material-icons-outlined me-1" style="font-size: 14px;">event</i>
                      <span>{{ \Carbon\Carbon::parse($paket->tanggal_paket, 'UTC')->setTimezone('Asia/Jakarta')->format('j F Y H:i') }} WIB</span>
                    </span>
                  </td>
                  <td>
                    <span class="badge {{ $class }}">
                      <i class="material-icons-outlined me-1" style="font-size: 12px;">{{ $icon }}</i>
                      {{ $status }}
                    </span>
                  </td>
                </tr>
              @empty
                <tr>
                  <td colspan="6" class="text-center py-4">
                    <div class="empty-state">
                      <i class="material-icons-outlined">search_off</i>
                      <h5>Tidak Ada Data e-Katalog</h5>
                      <p>Tidak ada data e-Katalog yang sesuai dengan filter yang Anda pilih. Silakan ubah filter atau coba kata kunci lain.</p>
                    </div>
                  </td>
                </tr>
              @endforelse
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
          <div class="text-muted small">
            @if(method_exists($paketPengadaan, 'firstItem') && method_exists($paketPengadaan, 'lastItem') && method_exists($paketPengadaan, 'total'))
              Menampilkan {{ $paketPengadaan->firstItem() ?? 0 }} - {{ $paketPengadaan->lastItem() ?? 0 }} dari {{ $paketPengadaan->total() }} data
            @else
              Menampilkan {{ $paketPengadaan->count() }} data
            @endif
          </div>
          <div>
            @if(method_exists($paketPengadaan, 'links'))
              {{ $paketPengadaan->links('vendor.pagination.simple') }}
            @endif
          </div>
        </div>
      @else
        <div class="empty-state">
          <i class="material-icons-outlined">search_off</i>
          <h5>Tidak Ada Data e-Katalog</h5>
          <p>Tidak ada data e-Katalog yang sesuai dengan filter yang Anda pilih. Silakan ubah filter atau coba kata kunci lain.</p>
        </div>
      @endif
    </div>
  </div>
@endsection

@section('scripts')
  <!-- Select2 JS -->
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

  <script>
    $(document).ready(function () {
      // Inisialisasi Select2 dengan konfigurasi sederhana
      $('#instansi-select').select2({
        width: '100%',
        placeholder: "Pilih Nama Instansi",
        allowClear: true,
        minimumInputLength: 2,
        language: {
          inputTooShort: function() {
            return "Ketik minimal 2 karakter untuk mencari";
          },
          noResults: function() {
            return "Tidak ada instansi yang ditemukan";
          },
          searching: function() {
            return "Mencari...";
          }
        }
      });

      // Refresh data button
      $('#btn-refresh-data').on('click', function() {
        $(this).html(`
          <i class="material-icons-outlined spin-animation me-1">refresh</i>
          <span>Memuat...</span>
        `);
        $(this).prop('disabled', true);

        // Reload halaman setelah 1 detik
        setTimeout(function() {
          location.reload();
        }, 1000);
      });

      // Toggle compact view
      $('#toggle-compact-view').on('click', function(e) {
        e.preventDefault();
        $('.ekatalog-row').addClass('compact-view');
        $('.product-category').hide();
        $('.table').addClass('table-sm');
        localStorage.setItem('ekatalog_view_mode', 'compact');

        // Notifikasi
        showToast('Tampilan kompak diaktifkan');
      });

      // Toggle full view
      $('#toggle-full-view').on('click', function(e) {
        e.preventDefault();
        $('.ekatalog-row').removeClass('compact-view');
        $('.product-category').show();
        $('.table').removeClass('table-sm');
        localStorage.setItem('ekatalog_view_mode', 'full');

        // Notifikasi
        showToast('Tampilan lengkap diaktifkan');
      });

      // Load saved view preferences
      loadViewPreferences();

      // Handle filter collapse toggle
      $('#filterCollapse').on('show.bs.collapse', function () {
        $('.filter-toggle-icon').text('expand_less');
        localStorage.setItem('ekatalog_filter_collapsed', 'false');
      });

      $('#filterCollapse').on('hide.bs.collapse', function () {
        $('.filter-toggle-icon').text('expand_more');
        localStorage.setItem('ekatalog_filter_collapsed', 'true');
      });

      // Load filter collapse state from localStorage
      if (localStorage.getItem('ekatalog_filter_collapsed') === 'true') {
        $('#filterCollapse').collapse('hide');
      }
    });

    // Function to load view preferences
    function loadViewPreferences() {
      // Load view mode
      const viewMode = localStorage.getItem('ekatalog_view_mode');
      if (viewMode === 'compact') {
        $('.ekatalog-row').addClass('compact-view');
        $('.product-category').hide();
        $('.table').addClass('table-sm');
      }
    }

    // Function to show toast notification
    function showToast(message, type = 'success') {
      // Create toast container if it doesn't exist
      if ($('#toast-container').length === 0) {
        $('body').append('<div id="toast-container" class="position-fixed bottom-0 end-0 p-3" style="z-index: 9999;"></div>');
      }

      // Set toast color based on type
      let bgColor = 'bg-success';
      let icon = 'check_circle';

      if (type === 'error') {
        bgColor = 'bg-danger';
        icon = 'error';
      } else if (type === 'warning') {
        bgColor = 'bg-warning text-dark';
        icon = 'warning';
      } else if (type === 'info') {
        bgColor = 'bg-info text-dark';
        icon = 'info';
      }

      // Create toast
      const toastId = 'toast-' + Date.now();
      const toast = `
        <div id="${toastId}" class="toast align-items-center ${bgColor} text-white border-0" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="d-flex">
            <div class="toast-body">
              <i class="material-icons-outlined me-2" style="vertical-align: middle;">${icon}</i>
              ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
        </div>
      `;

      // Add toast to container
      $('#toast-container').append(toast);

      // Initialize and show toast
      const toastElement = new bootstrap.Toast(document.getElementById(toastId), {
        delay: 3000
      });
      toastElement.show();

      // Remove toast after it's hidden
      $(`#${toastId}`).on('hidden.bs.toast', function() {
        $(this).remove();
      });
    }
  </script>

  <style>
    /* Spinning animation for refresh icon */
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .spin-animation {
      animation: spin 1s linear infinite;
      display: inline-block;
    }
  </style>
@endsection