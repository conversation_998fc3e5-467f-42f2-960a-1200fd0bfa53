@php
    use App\Models\Lpse;
    $title = "{$paketPengadaan->nomor_paket} - {$paketPengadaan->nama_paket}";
@endphp
@extends('layouts.master')

@section('title', $title)
@section('css')
<link rel="stylesheet" href="{{ asset('build/css/ekatalog-detail.css') }}">
<style>
    /* Card Styling */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        margin-bottom: 24px;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Detail Section Styling */
    .detail-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        height: 100%;
        border: 1px solid #e9ecef;
    }

    .detail-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #495057;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 8px;
    }

    .detail-content {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .detail-label {
        font-size: 0.85rem;
        color: #6c757d;
        font-weight: 500;
    }

    .detail-value {
        font-size: 0.95rem;
    }

    /* Product Section */
    .product-section {
        margin-top: 20px;
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
    }

    /* Analytics Card */
    .analytics-card {
        background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
        border: none;
        transition: all 0.3s ease;
    }

    .analytics-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .analytics-card .btn-primary {
        background: linear-gradient(90deg, #4481eb, #04befe);
        border: none;
        box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
    }

    .analytics-card .btn-primary:hover {
        background: linear-gradient(90deg, #3b72d9, #03a9e0);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(13, 110, 253, 0.25);
    }
</style>

@section('content')
<x-page-title title="Ekatalog" pagetitle="Detail Paket Ekatalog" />

<!-- Analytics Button -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card analytics-card">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="mb-1 fw-bold">Analisis Tren Pembelian Produk dan Layanan</h5>
                        <p class="text-muted mb-0">Lihat tren pembelian, produk terlaris, dan kategori paling diminati di e-Katalog</p>
                    </div>
                    <div>
                        <a href="{{ route('ekatalog.analytics') }}" class="btn btn-primary">
                            <i class="material-icons-outlined align-middle me-1">insights</i> Lihat Analisis
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row d-flex align-items-stretch">
    <!-- Card utama dengan ukuran 10 -->
    <div class="col-md-12">
        @php
            $nama_klpd = $paketPengadaan->instansi_pembeli;
            if (strpos($nama_klpd, "Kab.") !== false) {
                $nama_klpd = str_replace("Kab.", "Kabupaten", $nama_klpd);
            }
            $lpse = Lpse::where('nama_lpse', 'LIKE', "%{$nama_klpd}%")->first();
            if ($lpse) {
        $lpse->logo_path = str_replace('http://proyeklpse.test', '', $lpse->logo_path);
        }

        @endphp
        <div class="card">
            <div class="card-header gradient-header">
                <div class="d-flex align-items-center justify-content-between">
                    <h5 class="card-title mb-0">
                        <i class="material-icons-outlined me-2">shopping_cart</i>
                        Detail Paket e-Katalog
                    </h5>
                    <div class="d-flex gap-2">
                        <a href="{{ url()->previous() }}" class="btn btn-sm btn-light d-inline-flex align-items-center">
                            <i class="material-icons-outlined me-1">arrow_back</i>
                            <span>Kembali</span>
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-light dropdown-toggle d-inline-flex align-items-center" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="material-icons-outlined me-1">share</i>
                                <span>Bagikan</span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end shadow-lg border-0 p-3" style="width: 300px;">
                                <div class="input-group">
                                    <input id="shareUrl" type="text" class="form-control ps-5" value="{{ url()->current() }}" placeholder="Enter Url">
                                    <span class="material-icons-outlined position-absolute ms-3 translate-middle-y start-0 top-50">link</span>
                                    <button class="input-group-text gap-1" id="copyButton">
                                        <i class="material-icons-outlined fs-6">content_copy</i>Copy
                                    </button>
                                </div>
                                <div class="d-flex align-items-center gap-2 mt-3">
                                    <a href="https://pinterest.com/pin/create/button/?url={{ url()->current() }}" target="_blank" class="py-1 px-3 border-0 rounded bg-pinterest text-white flex-fill d-flex gap-1">
                                        <i class="bi bi-pinterest"></i>Pinterest
                                    </a>
                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ url()->current() }}" target="_blank" class="py-1 px-3 border-0 rounded bg-facebook text-white flex-fill d-flex gap-1">
                                        <i class="bi bi-facebook"></i>Facebook
                                    </a>
                                    <a href="https://www.linkedin.com/shareArticle?url={{ url()->current() }}&title={{ $paketPengadaan->nama_paket }}" target="_blank" class="py-1 px-3 border-0 rounded bg-linkedin text-white flex-fill d-flex gap-1">
                                        <i class="bi bi-linkedin"></i>LinkedIn
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 text-center">
                        <div class="product-image-container">
                            @if($lpse)
                                <img src="{{ asset(str_replace('http://proyeklpse.test', '', $lpse->logo_path)) }}" class="rounded" alt="Logo Instansi">
                            @else
                                <img src="{{ asset('build/images/logo-default.png') }}" class="rounded" alt="Logo Default">
                            @endif
                        </div>
                    </div>
                    <div class="col-md-10">
                        <div class="d-flex flex-column">
                            <h4 class="fw-bold mb-3">{{ $paketPengadaan->nama_paket }}</h4>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="d-flex flex-column gap-2">
                                        <div class="d-flex align-items-center">
                                            <div style="width: 140px;" class="text-muted">Nomor Paket</div>
                                            <div class="fw-semibold">: {{ $paketPengadaan->nomor_paket }}</div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div style="width: 140px;" class="text-muted">Nomor RUP</div>
                                            <div class="fw-semibold">:
                                                <a href="{{ route('sirup.index') }}?search={{ $paketPengadaan->rup_id }}" class="text-decoration-none" target="_blank">
                                                    {{ $paketPengadaan->rup_id }}
                                                </a>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div style="width: 140px;" class="text-muted">Nilai Kontrak</div>
                                            <div class="fw-semibold text-success">: {{ convert_to_rupiah($totalNilaiKontrak) }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex flex-column gap-2">
                                        <div class="d-flex align-items-center">
                                            <div style="width: 140px;" class="text-muted">Instansi</div>
                                            <div class="fw-semibold">: {{ $paketPengadaan->instansi_pembeli }}</div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div style="width: 140px;" class="text-muted">Tanggal</div>
                                            <div class="fw-semibold">: {{ \Carbon\Carbon::parse($paketPengadaan->tanggal_paket, 'UTC')->setTimezone('Asia/Jakarta')->format('j F Y H:i') }} WIB</div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div style="width: 140px;" class="text-muted">Status</div>
                                            <div>:
                                                @php
                                                    if ($paketPengadaan->status_paket == 'paket_selesai') {
                                                        $statusClass = 'bg-success';
                                                        $statusIcon = 'check_circle';
                                                        $status = 'Paket Selesai';
                                                    } elseif ($paketPengadaan->status_paket == 'melakukan_pengiriman_dan_penerimaan') {
                                                        $statusClass = 'bg-primary';
                                                        $statusIcon = 'local_shipping';
                                                        $status = 'On Progress';
                                                    } elseif ($paketPengadaan->status_paket == 'proses_kontrak_ppk') {
                                                        $statusClass = 'bg-warning';
                                                        $statusIcon = 'schedule';
                                                        $status = 'Proses Kontrak PPK';
                                                    }
                                                @endphp
                                                <span class="badge {{ $statusClass }}">
                                                    <i class="material-icons-outlined" style="font-size: 12px; vertical-align: middle;">{{ $statusIcon }}</i>
                                                    {{ $status }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>

<div class="row d-flex align-items-stretch">
    <div class="col-md-12">

        <div class="card">
            <div class="card-header gradient-header">
                <div class="d-flex align-items-center justify-content-between">
                    <h5 class="card-title mb-0">
                        <i class="material-icons-outlined me-2">inventory_2</i>
                        Informasi Detail Paket
                    </h5>
                    <div>
                        <span class="badge bg-primary">
                            <i class="material-icons-outlined me-1">category</i>
                            {{$paketPengadaan->jenis_katalog}}
                        </span>
                        <span class="badge bg-info">
                            <i class="material-icons-outlined me-1">storefront</i>
                            {{ $paketPengadaan->etalase }}
                        </span>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                        <div class="detail-section">
                            <h6 class="detail-title">
                                <i class="material-icons-outlined me-1">business</i>
                                Informasi Perusahaan
                            </h6>
                            <div class="detail-content">
                                <div class="detail-item">
                                    <div class="detail-label">Perusahaan Penyedia</div>
                                    <div class="detail-value">
                                        <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($paketPengadaan->penyedia->pembeda)]) }}" class="text-primary fw-semibold">
                                            {{ $paketPengadaan->penyedia->nama_peserta }}
                                        </a>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-label">Perusahaan Pelaksana</div>
                                    <div class="detail-value">
                                        <a href="{{ route('perusahaan.show', ['pembeda' => encrypt($paketPengadaan->pelaksana->pembeda)]) }}" class="text-primary fw-semibold">
                                            {{ $paketPengadaan->pelaksana->nama_peserta }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="detail-section">
                            <h6 class="detail-title">
                                <i class="material-icons-outlined me-1">account_balance</i>
                                Informasi Instansi
                            </h6>
                            <div class="detail-content">
                                <div class="detail-item">
                                    <div class="detail-label">Instansi Pembeli</div>
                                    <div class="detail-value fw-semibold">{{ $paketPengadaan->instansi_pembeli }}</div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-label">Satuan Kerja</div>
                                    <div class="detail-value">{{ $paketPengadaan->satuan_kerja }}</div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-label">Nilai Kontrak</div>
                                    <div class="detail-value text-success fw-bold">{{ convert_to_rupiah($totalNilaiKontrak) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="product-section">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="section-title mb-0">
                            <i class="material-icons-outlined me-1">shopping_basket</i>
                            Daftar Produk dalam Paket
                        </h6>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-center" style="width: 5%;">No</th>
                                    <th style="width: 30%;">Nama Produk</th>
                                    <th style="width: 15%;">Jenis Produk</th>
                                    <th style="width: 15%;">Pabrikan</th>
                                    <th class="text-center" style="width: 8%;">Kuantitas</th>
                                    <th class="text-end" style="width: 12%;">Harga Satuan</th>
                                    <th class="text-end" style="width: 15%;">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($paketPengadaan->transaksiPaket as $transaksi)
                                    <tr>
                                        <td class="text-center">{{ $loop->iteration }}</td>
                                        <td>
                                            <div class="fw-semibold">{{ $transaksi->produk->nama_produk }}</div>
                                            <div class="text-muted small">{{ $transaksi->produk->kategori_lv1 }} - {{ $transaksi->produk->kategori_lv2 }}</div>
                                        </td>
                                        <td>{{ $transaksi->produk->jenis_produk }}</td>
                                        <td>
                                            @if ($transaksi->produk->nama_manufaktur)
                                                {{ $transaksi->produk->nama_manufaktur }}
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td class="text-center fw-semibold">{{ $transaksi->kuantitas_produk }}</td>
                                        <td class="text-end">{{ convert_to_rupiah($transaksi->harga_satuan) }}</td>
                                        <td class="text-end fw-bold">{{ convert_to_rupiah($transaksi->total_harga) }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="py-3">
                                                <i class="material-icons-outlined" style="font-size: 48px; color: #e9ecef;">inventory_2</i>
                                                <h6 class="mt-2">Tidak Ada Data Produk</h6>
                                                <p class="text-muted">Tidak ada data produk yang tersedia untuk paket ini</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <td colspan="5"></td>
                                    <td class="text-end fw-bold">Total:</td>
                                    <td class="text-end fw-bold text-success">{{ convert_to_rupiah($totalNilaiKontrak) }}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>

    </div>



</div>

@endsection

@section('scripts')
<script>
    document.getElementById('copyButton').addEventListener('click', function () {
        var shareUrl = document.getElementById('shareUrl');

        // Pilih teks secara manual
        shareUrl.select();
        shareUrl.setSelectionRange(0, 99999); // Untuk mendukung perangkat mobile

        // Salin teks dengan cara manual
        try {
            var successful = document.execCommand('copy');
            var msg = successful ? 'Link berhasil disalin ke clipboard' : 'Gagal menyalin link';
            alert(msg);
        } catch (err) {
            console.error('Gagal menyalin link: ', err);
        }
    });

</script>
@endsection