@extends('layouts.master')

@section('title', 'Semua Paket Ekatalog')

@section('css')
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css' rel='stylesheet' />
<style>
        /* Custom styling for the calendar events */
        .fc-event-content {
            display: flex;
            align-items: center;
            font-size: 1.2rem; /* Membesarkan teks */
            color: #000; /* Warna teks menjadi hitam */
        }
        .fc-event-content i {
            margin-right: 8px; /* Memberikan sedikit jarak antara ikon dan teks */
        }
        .fc-event {
            border: 1px solid #ddd; /* Memberikan border pada event */
            padding: 5px; /* Memberikan padding agar tampak lebih rapi */
            border-radius: 8px; /* Membuat event terlihat lebih modern */
            background-color: #f0f0f0 !important; /* Warna latar belakang abu-abu yang sangat halus */
        }
        .fc-daygrid-event {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        #calendar {
            padding: 20px;
        }
    </style>

@endsection

@section('content')
<x-page-title title="Ekatalog Pengadaan" pagetitle="Semua Paket Ekatalog" />


<!-- Paket Pengadaan Table Section -->
<div class="card mt-4">
  <div class="card-body">
        <div id="calendar"></div>
  </div>
</div>
@endsection

@section('scripts')
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js'></script>
<script>
    $(document).ready(function () {
        var calendarEl = document.getElementById('calendar');
        var calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            events: function (fetchInfo, successCallback, failureCallback) {
                $.ajax({
                    url: '/api/paket-pengadaan',
                    type: 'GET',
                    success: function (response) {
                        let events = response.map(data => {
                            let icon = '';
                            if (data.status === 'bertambah') {
                                icon = '<i class="bi bi-arrow-up-circle-fill text-success"></i>';
                            } else if (data.status === 'berkurang') {
                                icon = '<i class="bi bi-arrow-down-circle-fill text-danger"></i>';
                            } else if (data.status === 'sama') {
                                icon = '<i class="bi bi-dash-circle-fill text-primary"></i>';
                            } else {
                                icon = '<i class="bi bi-file-earmark text-secondary"></i>';
                            }

                            return {
                                title: `${icon} <strong>${data.jumlah} Paket</strong>`,
                                start: data.tanggal,
                                extendedProps: {
                                    perubahan: data.perubahan
                                },
                                allDay: true,
                                display: 'block'
                            };
                        });
                        successCallback(events);
                    },
                    error: function () {
                        failureCallback();
                    }
                });
            },
            eventContent: function (arg) {
                let event = arg.event;
                let containerEl = document.createElement('div');
                containerEl.classList.add('fc-event-content');

                // Menggunakan HTML untuk ikon dan teks
                containerEl.innerHTML = event.title;

                return { domNodes: [containerEl] };
            }
        });

        calendar.render();
    });
</script>
@endsection