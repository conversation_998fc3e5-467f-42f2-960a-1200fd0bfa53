@extends('layouts.master')

@section('title', 'Analisis Tren Pembelian e-Katalog')

@section('css')
<style>
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }
    .loading-spinner {
        width: 3rem;
        height: 3rem;
        margin-bottom: 1rem;
    }
</style>
@endsection

@section('content')
<x-page-title title="e-Katalog" pagetitle="Analisis Tren Pembelian" />

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex align-items-center">
                <h5 class="card-title mb-0 flex-grow-1">Filter Analisis</h5>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-4">
                        <label for="tahun" class="form-label">Tahun</label>
                        <select class="form-select" id="tahun" name="tahun">
                            @foreach($tahunList as $t)
                                <option value="{{ $t }}" {{ $tahun == $t ? 'selected' : '' }}>{{ $t }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">Terapkan Filter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistik Utama -->
<div class="row" id="stats-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat statistik...</p>
        </div>
    </div>
</div>

<!-- Grafik Tren Bulanan -->
<div class="row" id="tren-bulanan-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat tren bulanan...</p>
        </div>
    </div>
</div>

<!-- Top Produk dan Kategori -->
<div class="row" id="top-produk-kategori-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat data produk dan kategori...</p>
        </div>
    </div>
</div>

<!-- Top Instansi dan Penyedia -->
<div class="row" id="top-instansi-penyedia-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat data instansi dan penyedia...</p>
        </div>
    </div>
</div>

<!-- Distribusi Jenis Katalog dan Status Paket -->
<div class="row" id="distribusi-container">
    <div class="col-12">
        <div class="loading-container">
            <div class="spinner-border text-primary loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Memuat data distribusi...</p>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script src="{{ URL::asset('build/js/ekatalog-analytics-ajax.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // Debug information
    console.log('DOM loaded, initializing AJAX analytics...');

    // Get current year from select
    const tahun = document.getElementById('tahun').value;
    console.log('Current year:', tahun);

    // Get base URL and ensure it uses HTTPS
    let baseUrl = '{{ url("/") }}';
    baseUrl = baseUrl.replace(/^http:\/\//i, 'https://');

    // Add global error handler for debugging
    window.addEventListener('error', function(e) {
        console.error('Global error caught:', e.message, 'at', e.filename, 'line', e.lineno);
    });

    // Add unhandled promise rejection handler
    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled Promise Rejection:', e.reason);
    });

    try {
        // Load all data
        loadStats(tahun, baseUrl);
        loadTrenBulanan(tahun, baseUrl);
        loadTopProdukKategori(tahun, baseUrl);
        loadTopInstansiPenyedia(tahun, baseUrl);
        loadDistribusi(tahun, baseUrl);
    } catch (error) {
        console.error('Error loading data:', error);
    }

    // Handle filter form submission
    document.getElementById('filter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const tahun = document.getElementById('tahun').value;

        // Reset all containers to loading state
        document.getElementById('stats-container').innerHTML = getLoadingHTML('Memuat statistik...');
        document.getElementById('tren-bulanan-container').innerHTML = getLoadingHTML('Memuat tren bulanan...');
        document.getElementById('top-produk-kategori-container').innerHTML = getLoadingHTML('Memuat data produk dan kategori...');
        document.getElementById('top-instansi-penyedia-container').innerHTML = getLoadingHTML('Memuat data instansi dan penyedia...');
        document.getElementById('distribusi-container').innerHTML = getLoadingHTML('Memuat data distribusi...');

        // Load all data with new filter
        loadStats(tahun, baseUrl);
        loadTrenBulanan(tahun, baseUrl);
        loadTopProdukKategori(tahun, baseUrl);
        loadTopInstansiPenyedia(tahun, baseUrl);
        loadDistribusi(tahun, baseUrl);
    });
});
</script>
@endsection
