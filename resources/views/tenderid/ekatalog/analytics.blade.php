@extends('layouts.master')

@section('title', 'Analisis Tren Pembelian e-Katalog')

@section('css')
<link href="{{ URL::asset('build/libs/apexcharts/apexcharts.min.css') }}" rel="stylesheet">
@endsection

@section('content')
<x-page-title title="e-Katalog" pagetitle="Analisis Tren Pembelian" />

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex align-items-center">
                <h5 class="card-title mb-0 flex-grow-1">Filter Analisis</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('ekatalog.analytics') }}" method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="tahun" class="form-label">Tahun</label>
                        <select class="form-select" id="tahun" name="tahun" onchange="this.form.submit()">
                            @foreach($tahunList as $t)
                                <option value="{{ $t }}" {{ $tahun == $t ? 'selected' : '' }}>{{ $t }}</option>
                            @endforeach
                        </select>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistik Utama -->
<div class="row">
    <div class="col-xl-6 col-md-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <p class="text-uppercase fw-medium text-muted mb-0">Total Paket e-Katalog</p>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">{{ number_format($totalPaket, 0, ',', '.') }}</h4>
                        <p class="text-muted mb-0">Paket Pengadaan Tahun {{ $tahun }}</p>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-primary-subtle rounded fs-3">
                            <i class="material-icons-outlined text-primary">shopping_cart</i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-6 col-md-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <p class="text-uppercase fw-medium text-muted mb-0">Total Nilai Kontrak</p>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">Rp {{ number_format($totalNilai, 0, ',', '.') }}</h4>
                        <p class="text-muted mb-0">Nilai Kontrak Tahun {{ $tahun }}</p>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-success-subtle rounded fs-3">
                            <i class="material-icons-outlined text-success">payments</i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Grafik Tren Bulanan -->
<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Tren Jumlah Paket Bulanan ({{ $tahun }})</h4>
            </div>
            <div class="card-body">
                <div id="chart-paket-bulanan" class="apex-charts" dir="ltr"></div>
            </div>
        </div>
    </div>
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Tren Nilai Kontrak Bulanan ({{ $tahun }})</h4>
            </div>
            <div class="card-body">
                <div id="chart-nilai-bulanan" class="apex-charts" dir="ltr"></div>
            </div>
        </div>
    </div>
</div>

<!-- Top Produk dan Kategori -->
<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Top 10 Produk Terlaris</h4>
            </div>
            <div class="card-body">
                <div id="chart-top-produk" class="apex-charts" dir="ltr"></div>
            </div>
        </div>
    </div>
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Top 10 Kategori Produk</h4>
            </div>
            <div class="card-body">
                <div id="chart-top-kategori" class="apex-charts" dir="ltr"></div>
                <div class="mt-3">
                    <div class="table-responsive">
                        <table class="table table-sm table-centered mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Kategori</th>
                                    <th>Jumlah Transaksi</th>
                                    <th>Total Nilai</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($topKategori as $kategori)
                                <tr>
                                    <td>{{ $kategori->kategori_lv1 }}</td>
                                    <td>{{ number_format($kategori->jumlah_transaksi, 0, ',', '.') }}</td>
                                    <td>Rp {{ number_format($kategori->total_nilai, 0, ',', '.') }}</td>
                                    <td>
                                        <a href="{{ route('ekatalog.analytics.kategori', ['kategori' => $kategori->kategori_lv1, 'tahun' => $tahun]) }}" class="btn btn-sm btn-primary">Detail</a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Instansi dan Penyedia -->
<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Top 10 Instansi Pembeli</h4>
            </div>
            <div class="card-body">
                <div id="chart-top-instansi" class="apex-charts" dir="ltr"></div>
            </div>
        </div>
    </div>
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Top 10 Penyedia</h4>
            </div>
            <div class="card-body">
                <div id="chart-top-penyedia" class="apex-charts" dir="ltr"></div>
            </div>
        </div>
    </div>
</div>

<!-- Distribusi Jenis Katalog dan Status Paket -->
<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Distribusi Jenis Katalog</h4>
            </div>
            <div class="card-body">
                <div id="chart-jenis-katalog" class="apex-charts" dir="ltr"></div>
            </div>
        </div>
    </div>
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Distribusi Status Paket</h4>
            </div>
            <div class="card-body">
                <div id="chart-status-paket" class="apex-charts" dir="ltr"></div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="{{ URL::asset('build/libs/apexcharts/apexcharts.min.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // Tren Paket Bulanan
    var trenPaketOptions = {
        series: [{
            name: 'Jumlah Paket',
            data: [
                @foreach(range(1, 12) as $bulan)
                    {{ $trenPembelianBulanan->where('bulan', $bulan)->first()->jumlah_paket ?? 0 }},
                @endforeach
            ]
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded'
            },
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        xaxis: {
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
        },
        yaxis: {
            title: {
                text: 'Jumlah Paket'
            }
        },
        fill: {
            opacity: 1,
            colors: ['#4b38b3']
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " paket"
                }
            }
        }
    };
    var trenPaketChart = new ApexCharts(document.querySelector("#chart-paket-bulanan"), trenPaketOptions);
    trenPaketChart.render();

    // Tren Nilai Bulanan
    var trenNilaiOptions = {
        series: [{
            name: 'Nilai Kontrak',
            data: [
                @foreach(range(1, 12) as $bulan)
                    {{ $trenNilaiBulanan->where('bulan', $bulan)->first()->total_nilai ?? 0 }},
                @endforeach
            ]
        }],
        chart: {
            type: 'area',
            height: 350,
            toolbar: {
                show: false
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2,
        },
        xaxis: {
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'],
        },
        yaxis: {
            title: {
                text: 'Nilai Kontrak (Rp)'
            },
            labels: {
                formatter: function (value) {
                    return new Intl.NumberFormat('id-ID', { 
                        style: 'currency', 
                        currency: 'IDR',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(value);
                }
            }
        },
        colors: ['#0ab39c'],
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.6,
                opacityTo: 0.1,
                stops: [0, 90, 100]
            }
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return new Intl.NumberFormat('id-ID', { 
                        style: 'currency', 
                        currency: 'IDR',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(val);
                }
            }
        }
    };
    var trenNilaiChart = new ApexCharts(document.querySelector("#chart-nilai-bulanan"), trenNilaiOptions);
    trenNilaiChart.render();

    // Top Produk
    var topProdukOptions = {
        series: [{
            data: [
                @foreach($topProduk as $produk)
                    {{ $produk->total_kuantitas }},
                @endforeach
            ]
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                borderRadius: 4,
                horizontal: true,
            }
        },
        dataLabels: {
            enabled: false
        },
        xaxis: {
            categories: [
                @foreach($topProduk as $produk)
                    '{{ Str::limit($produk->nama_produk, 30) }}',
                @endforeach
            ],
        },
        colors: ['#4b38b3'],
    };
    var topProdukChart = new ApexCharts(document.querySelector("#chart-top-produk"), topProdukOptions);
    topProdukChart.render();

    // Top Kategori
    var topKategoriOptions = {
        series: [
            @foreach($topKategori as $kategori)
                {{ $kategori->total_nilai }},
            @endforeach
        ],
        chart: {
            type: 'donut',
            height: 350,
        },
        labels: [
            @foreach($topKategori as $kategori)
                '{{ Str::limit($kategori->kategori_lv1, 20) }}',
            @endforeach
        ],
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }],
        colors: ['#4b38b3', '#0ab39c', '#f7b84b', '#f06548', '#299cdb', '#6559cc', '#3577f1', '#f7b84b', '#f06548', '#299cdb'],
    };
    var topKategoriChart = new ApexCharts(document.querySelector("#chart-top-kategori"), topKategoriOptions);
    topKategoriChart.render();

    // Top Instansi
    var topInstansiOptions = {
        series: [{
            data: [
                @foreach($topInstansi as $instansi)
                    {{ $instansi->jumlah_paket }},
                @endforeach
            ]
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                borderRadius: 4,
                horizontal: true,
            }
        },
        dataLabels: {
            enabled: false
        },
        xaxis: {
            categories: [
                @foreach($topInstansi as $instansi)
                    '{{ Str::limit($instansi->instansi_pembeli, 30) }}',
                @endforeach
            ],
        },
        colors: ['#0ab39c'],
    };
    var topInstansiChart = new ApexCharts(document.querySelector("#chart-top-instansi"), topInstansiOptions);
    topInstansiChart.render();

    // Top Penyedia
    var topPenyediaOptions = {
        series: [{
            data: [
                @foreach($topPenyedia as $penyedia)
                    {{ $penyedia->jumlah_paket }},
                @endforeach
            ]
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                borderRadius: 4,
                horizontal: true,
            }
        },
        dataLabels: {
            enabled: false
        },
        xaxis: {
            categories: [
                @foreach($topPenyedia as $penyedia)
                    '{{ Str::limit($penyedia->nama_perusahaan, 30) }}',
                @endforeach
            ],
        },
        colors: ['#f7b84b'],
    };
    var topPenyediaChart = new ApexCharts(document.querySelector("#chart-top-penyedia"), topPenyediaOptions);
    topPenyediaChart.render();

    // Jenis Katalog
    var jenisKatalogOptions = {
        series: [
            @foreach($jenisKatalog as $jenis)
                {{ $jenis->jumlah_paket }},
            @endforeach
        ],
        chart: {
            type: 'pie',
            height: 350,
        },
        labels: [
            @foreach($jenisKatalog as $jenis)
                '{{ $jenis->jenis_katalog }}',
            @endforeach
        ],
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }],
        colors: ['#4b38b3', '#0ab39c', '#f7b84b', '#f06548', '#299cdb'],
    };
    var jenisKatalogChart = new ApexCharts(document.querySelector("#chart-jenis-katalog"), jenisKatalogOptions);
    jenisKatalogChart.render();

    // Status Paket
    var statusPaketOptions = {
        series: [
            @foreach($statusPaket as $status)
                {{ $status->jumlah_paket }},
            @endforeach
        ],
        chart: {
            type: 'pie',
            height: 350,
        },
        labels: [
            @foreach($statusPaket as $status)
                '{{ $status->status_paket }}',
            @endforeach
        ],
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }],
        colors: ['#0ab39c', '#f7b84b', '#f06548', '#299cdb', '#4b38b3'],
    };
    var statusPaketChart = new ApexCharts(document.querySelector("#chart-status-paket"), statusPaketOptions);
    statusPaketChart.render();
});
</script>
@endsection
