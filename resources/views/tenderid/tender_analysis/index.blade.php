@extends('layouts.master')

@section('title', '<PERSON><PERSON><PERSON>')

@section('css')
<style>
/* Card Styling */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    margin-bottom: 24px;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 12px 12px 0 0 !important;
    padding: 18px 24px;
}

.card-title {
    font-weight: 600;
    color: #344767;
    margin-bottom: 0;
    font-size: 1.1rem;
}

.card-body {
    padding: 24px;
}

/* Statistics Cards */
.stats-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    margin-bottom: 24px;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats-card .card-body {
    padding: 20px;
    position: relative;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stats-icon.primary {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.stats-icon.success {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.stats-icon.info {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
}

.stats-icon.warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.stats-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    color: #344767;
    line-height: 1;
}

/* Period Buttons */
.period-btn {
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    background-color: white;
    color: #6c757d;
}

.period-btn:hover {
    background-color: #f8f9fa;
    border-color: #0d6efd;
    color: #0d6efd;
}

.period-btn.active {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: white !important;
    box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
}

/* Table Styling */
.table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: #f8f9fa;
    color: #344767;
    font-weight: 600;
    border-top: none;
    padding: 16px;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #e9ecef;
}

.table tbody td {
    padding: 16px;
    vertical-align: middle;
    border-bottom: 1px solid #f8f9fa;
    font-size: 0.9rem;
    color: #495057;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table-responsive {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

/* Company Rank */
.company-rank {
    background: linear-gradient(45deg, #0d6efd, #0dcaf0);
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
    box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
}

/* Win Rate Badges */
.win-rate-badge {
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    line-height: 1;
}

.win-rate-high {
    background-color: #198754 !important;
    color: white;
    box-shadow: 0 2px 5px rgba(25, 135, 84, 0.2);
}

.win-rate-medium {
    background-color: #ffc107 !important;
    color: #212529;
    box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
}

.win-rate-low {
    background-color: #dc3545 !important;
    color: white;
    box-shadow: 0 2px 5px rgba(220, 53, 69, 0.2);
}

/* Chart containers */
.chart-container {
    position: relative;
    height: 350px;
    width: 100%;
}

/* LPSE List styling */
.lpse-item {
    border-radius: 10px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    margin-bottom: 12px;
    padding: 15px;
}

.lpse-item:hover {
    background-color: #f0f4f8;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

/* Badge styling */
.badge {
    padding: 6px 10px;
    font-weight: 500;
    font-size: 0.75rem;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    line-height: 1;
}

.badge i {
    font-size: 12px;
    margin-right: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: baseline;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
    box-shadow: 0 2px 5px rgba(13, 110, 253, 0.2);
}

.badge.bg-success {
    background-color: #198754 !important;
    box-shadow: 0 2px 5px rgba(25, 135, 84, 0.2);
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    box-shadow: 0 2px 5px rgba(13, 202, 240, 0.2);
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529;
    box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
}

.badge.bg-secondary {
    background-color: #6c757d !important;
    color: white;
    box-shadow: 0 2px 5px rgba(108, 117, 125, 0.2);
}

/* Table column widths */
.table th:first-child,
.table td:first-child {
    width: 60px;
    text-align: center;
}

.table th:nth-child(3),
.table td:nth-child(3),
.table th:nth-child(4),
.table td:nth-child(4) {
    width: 120px;
    text-align: center;
}

/* LPSE Card Styling */
.vendor-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef !important;
}

.vendor-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    border-color: #0d6efd !important;
}

.lpse-dashboard-btn {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    background-color: white;
}

.lpse-dashboard-btn:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(13, 110, 253, 0.2);
}

.lpse-dashboard-btn:hover i {
    color: white !important;
}

/* Fraud Analysis Table Styling */
#fraud-analysis-table {
    font-size: 0.9rem;
}

#fraud-analysis-table thead th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    text-align: center;
    padding: 1rem 0.75rem;
    border-bottom: 2px solid #dee2e6;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#fraud-analysis-table tbody tr {
    border-bottom: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

#fraud-analysis-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

#fraud-analysis-table td {
    vertical-align: middle;
    padding: 1.2rem 0.75rem;
    border: none;
}

/* Kolom Tender */
#fraud-analysis-table td:nth-child(1) {
    width: 28%;
    max-width: 300px;
}

#fraud-analysis-table td:nth-child(1) strong {
    font-size: 0.95rem;
    color: #0d6efd;
    line-height: 1.4;
    display: block;
    margin-bottom: 0.3rem;
    transition: color 0.2s ease;
}

#fraud-analysis-table td:nth-child(1) a:hover strong {
    color: #0a58ca;
    text-decoration: underline;
}

#fraud-analysis-table td:nth-child(1) small {
    color: #6c757d;
    font-size: 0.8rem;
}

#fraud-analysis-table td:nth-child(1) .text-info {
    transition: color 0.2s ease;
}

#fraud-analysis-table td:nth-child(1) .text-info:hover {
    color: #0dcaf0 !important;
}

#fraud-analysis-table td:nth-child(1) .text-info i {
    vertical-align: middle;
}

#fraud-analysis-table .lpse-logo {
    border-radius: 3px;
    border: 1px solid #e9ecef;
    background-color: white;
    transition: transform 0.2s ease;
}

#fraud-analysis-table .lpse-logo:hover {
    transform: scale(1.1);
}

/* Kolom Instansi */
#fraud-analysis-table td:nth-child(2) {
    width: 25%;
    color: #495057;
    font-weight: 500;
}

#fraud-analysis-table .instansi-lpse-info {
    padding: 0.5rem;
}

#fraud-analysis-table .lpse-logo {
    border-radius: 6px;
    border: 1px solid #e9ecef;
    background-color: white;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

#fraud-analysis-table .lpse-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

#fraud-analysis-table .lpse-logo-placeholder {
    border-radius: 6px;
    border: 1px dashed #dee2e6;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

#fraud-analysis-table .instansi-info {
    padding: 0.4rem 0.6rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #0d6efd;
}

#fraud-analysis-table .instansi-lpse-info a:hover .fw-bold {
    color: #0a58ca !important;
    text-decoration: underline;
}

/* Kolom Pemenang */
#fraud-analysis-table td:nth-child(3) {
    width: 20%;
}

#fraud-analysis-table td:nth-child(3) .fw-bold {
    color: #0d6efd;
    font-size: 0.9rem;
    display: block;
    margin-bottom: 0.3rem;
}

#fraud-analysis-table td:nth-child(3) small {
    color: #6c757d;
    font-size: 0.75rem;
}

#fraud-analysis-table td:nth-child(3) code {
    background-color: #f8f9fa;
    color: #495057;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

#fraud-analysis-table .company-link {
    transition: color 0.2s ease;
    position: relative;
}

#fraud-analysis-table .company-link:hover {
    color: #0a58ca !important;
    text-decoration: underline;
}

#fraud-analysis-table .company-link::after {
    content: "🔗";
    font-size: 0.7rem;
    margin-left: 0.3rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

#fraud-analysis-table .company-link:hover::after {
    opacity: 1;
}

/* Kolom Indikator Fraud */
#fraud-analysis-table td:nth-child(4) {
    width: 17%;
    color: #dc3545;
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Kolom Risk Score */
#fraud-analysis-table td:nth-child(5) {
    width: 10%;
    text-align: center;
}

#fraud-analysis-table .badge {
    font-size: 0.85rem;
    padding: 0.6rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    min-width: 80px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    #fraud-analysis-table {
        font-size: 0.85rem;
    }

    #fraud-analysis-table td {
        padding: 1rem 0.5rem;
    }

    #fraud-analysis-table td:nth-child(1) {
        width: 35%;
    }

    #fraud-analysis-table td:nth-child(2) {
        width: 15%;
    }

    #fraud-analysis-table td:nth-child(3) {
        width: 25%;
    }

    #fraud-analysis-table td:nth-child(4) {
        width: 15%;
    }

    #fraud-analysis-table td:nth-child(5) {
        width: 10%;
    }
}

@media (max-width: 768px) {
    #fraud-analysis-table {
        font-size: 0.8rem;
    }

    #fraud-analysis-table td {
        padding: 0.8rem 0.4rem;
    }

    #fraud-analysis-table .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
        min-width: 60px;
    }
}
</style>
@endsection

@section('content')
<!-- Header Card -->
<div class="card mb-4">
    <div class="card-header d-flex align-items-center justify-content-between" style="background: linear-gradient(90deg, #4481eb, #04befe); color: white;">
        <h5 class="card-title mb-0">
            <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">analytics</i>
            <span>Analisis Tender</span>
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-light btn-sm period-btn active" data-period="7">7 Hari</button>
            <button type="button" class="btn btn-light btn-sm period-btn" data-period="30">30 Hari</button>
            <button type="button" class="btn btn-light btn-sm period-btn" data-period="90">90 Hari</button>
            <button type="button" class="btn btn-light btn-sm period-btn" data-period="365">1 Tahun</button>
        </div>
    </div>
    <div class="card-body">
        <p class="text-muted mb-0">
            <i class="material-icons-outlined me-1" style="font-size: 16px; vertical-align: middle;">info</i>
            Analisis komprehensif data tender, kompetisi, dan tren pasar pengadaan
        </p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4" id="stats-cards">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="card-body">
                <div class="d-flex align-items-start">
                    <div class="stats-icon primary me-3">
                        <i class="material-icons-outlined">gavel</i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-label">TOTAL TENDER</div>
                        <div class="stats-value" id="total-tenders">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="card-body">
                <div class="d-flex align-items-start">
                    <div class="stats-icon success me-3">
                        <i class="material-icons-outlined">play_circle</i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-label">TENDER AKTIF</div>
                        <div class="stats-value" id="active-tenders">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="card-body">
                <div class="d-flex align-items-start">
                    <div class="stats-icon info me-3">
                        <i class="material-icons-outlined">payments</i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-label">TOTAL NILAI</div>
                        <div class="stats-value" id="total-value">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="card-body">
                <div class="d-flex align-items-start">
                    <div class="stats-icon warning me-3">
                        <i class="material-icons-outlined">trending_up</i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-label">RASIO KOMPETISI</div>
                        <div class="stats-value" id="competition-ratio">-</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="card">
            <div class="card-header d-flex align-items-center justify-content-between">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">trending_up</i>
                    Tren Tender Bulanan
                </h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="material-icons-outlined me-1" style="font-size: 16px;">more_vert</i>
                        Opsi
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="exportChart('tender-trends')">
                            <i class="material-icons-outlined me-2" style="font-size: 16px;">download</i>
                            Export PNG
                        </a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <div id="tender-trends-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-5">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">emoji_events</i>
                    Top LPSE
                </h5>
            </div>
            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                <div id="top-lpse-list">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="text-muted mt-2 mb-0">Memuat data LPSE...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <div class="col-xl-6 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">category</i>
                    Analisis Kategori
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <div id="category-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">balance</i>
                    Metode Pemilihan
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <div id="method-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 3 -->
<div class="row mb-4">
    <div class="col-xl-6 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">payments</i>
                    Distribusi Nilai Tender
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <div id="value-distribution-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">schedule</i>
                    Durasi Tender
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <div id="duration-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Company Analysis Row -->
<div class="row mb-4">
    <!-- Active Companies Table -->
    <div class="col-xl-6 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">business</i>
                    Perusahaan Paling Aktif
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table align-middle mb-0" id="active-companies-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Perusahaan</th>
                                <th>Partisipasi</th>
                                <th>Win Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="4" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="text-muted mt-2 mb-0">Memuat data perusahaan...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Winners Table -->
    <div class="col-xl-6 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">emoji_events</i>
                    Perusahaan Paling Rajin Menang
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table align-middle mb-0" id="top-winners-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Perusahaan</th>
                                <th>Total Menang</th>
                                <th>Nilai Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="4" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="text-muted mt-2 mb-0">Memuat data pemenang...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fraud Analysis -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">security</i>
                    Analisis Potensi Fraud
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table align-middle mb-0" id="fraud-analysis-table">
                        <thead>
                            <tr>
                                <th>Tender</th>
                                <th>Instansi</th>
                                <th>Pemenang</th>
                                <th>Indikator Fraud</th>
                                <th>Risk Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="text-muted mt-2 mb-0">Menganalisis potensi fraud...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Regional Analysis -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="material-icons-outlined me-2" style="font-size: 22px; vertical-align: middle;">map</i>
                    Analisis Regional
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 400px;">
                    <div id="regional-chart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script src="{{ asset('js/tender-analysis-charts.js?=').time() }}"></script>
<script>
let currentPeriod = 7;
let charts = {};

$(document).ready(function() {
    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    console.log('Tender Analysis Dashboard initialized');
    loadAllData();

    // Period button handlers
    $('.period-btn').click(function() {
        $('.period-btn').removeClass('active');
        $(this).addClass('active');
        currentPeriod = $(this).data('period');
        console.log('Period changed to:', currentPeriod);
        loadAllData();
    });

    // Company link handlers for fraud analysis and other sections
    $(document).on('click', '.company-link', function(e) {
        e.preventDefault();
        const pembeda = $(this).data('pembeda');

        if (!pembeda) {
            console.error('No pembeda found for company link');
            return;
        }

        // Get encrypted URL from helper API
        fetch(`/api/helper/company-urls?pembeda=${pembeda}`)
            .then(response => response.json())
            .then(data => {
                if (data.detail_url) {
                    window.open(data.detail_url, '_blank');
                } else {
                    console.error('No detail URL returned');
                }
            })
            .catch(error => {
                console.error('Error getting company URL:', error);
            });
    });
});

function loadAllData() {
    loadStats();
    loadTenderTrends();
    loadTopLpse();
    loadCategoryAnalysis();
    loadMethodAnalysis();
    loadValueDistribution();
    loadDurationAnalysis();
    loadActiveCompanies();
    loadTopWinners();
    loadFraudAnalysis();
    loadRegionalAnalysis();
}

function loadStats() {
    console.log('Loading stats for period:', currentPeriod);
    $.get('/api/tender-analysis/stats', { period: currentPeriod })
        .done(function(data) {
            console.log('Stats data received:', data);
            $('#total-tenders').text(formatNumber(data.total_tenders));
            $('#active-tenders').text(formatNumber(data.active_tenders));
            $('#total-value').text(formatCurrency(data.total_value));
            $('#competition-ratio').text(data.competition_ratio + '%');
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading stats:', error, xhr.responseText);
            $('#total-tenders').text('Error');
            $('#active-tenders').text('Error');
            $('#total-value').text('Error');
            $('#competition-ratio').text('Error');
        });
}

function loadTenderTrends() {
    console.log('Loading tender trends...');
    $.get('/api/tender-analysis/trends', { months: Math.ceil(currentPeriod / 30) || 12 })
        .done(function(data) {
            console.log('Trends data received:', data);
            renderTenderTrendsChart(data);
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading trends:', error, xhr.responseText);
        });
}

function loadTopLpse() {
    console.log('Loading top LPSE...');
    $.get('/api/tender-analysis/top-lpse', { period: currentPeriod })
        .done(function(data) {
            console.log('Top LPSE data received:', data);
            renderTopLpseList(data);
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading top LPSE:', error, xhr.responseText);
        });
}

function loadCategoryAnalysis() {
    console.log('Loading category analysis...');
    $.get('/api/tender-analysis/category', { period: currentPeriod })
        .done(function(data) {
            console.log('Category data received:', data);
            renderCategoryChart(data);
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading category analysis:', error, xhr.responseText);
        });
}

function loadMethodAnalysis() {
    console.log('Loading method analysis...');
    $.get('/api/tender-analysis/method', { period: currentPeriod })
        .done(function(data) {
            console.log('Method data received:', data);
            renderMethodChart(data);
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading method analysis:', error, xhr.responseText);
        });
}

function loadValueDistribution() {
    console.log('Loading value distribution...');
    $.get('/api/tender-analysis/value-distribution', { period: currentPeriod })
        .done(function(data) {
            console.log('Value distribution data received:', data);
            renderValueDistributionChart(data);
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading value distribution:', error, xhr.responseText);
        });
}

function loadDurationAnalysis() {
    console.log('Loading duration analysis...');
    $.get('/api/tender-analysis/duration', { period: currentPeriod })
        .done(function(data) {
            console.log('Duration data received:', data);
            renderDurationChart(data);
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading duration analysis:', error, xhr.responseText);
        });
}

function loadActiveCompanies() {
    console.log('Loading active companies for period:', currentPeriod);
    $.get('/api/tender-analysis/active-companies', { period: currentPeriod })
        .done(function(data) {
            console.log('Active companies API response:', data);
            console.log('Data type:', typeof data, 'Is array:', Array.isArray(data), 'Length:', data ? data.length : 'N/A');

            if (data && data.length > 0) {
                console.log('First company sample:', data[0]);
            }

            // Check if response contains error
            if (data && data.error) {
                console.error('API Error:', data.error, data.debug);
                renderActiveCompaniesTable([]);
            } else {
                renderActiveCompaniesTable(data);
            }
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading active companies:', error, xhr.responseText);
            renderActiveCompaniesTable([]);
        });
}

function loadTopWinners() {
    console.log('Loading top winners...');
    $.get('/api/tender-analysis/top-winners', { period: currentPeriod })
        .done(function(data) {
            console.log('Top winners data received:', data);
            renderTopWinnersTable(data);
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading top winners:', error, xhr.responseText);
            renderTopWinnersTable([]);
        });
}

function loadFraudAnalysis() {
    console.log('Loading fraud analysis...');
    $.get('/api/tender-analysis/fraud-analysis', { period: currentPeriod })
        .done(function(data) {
            console.log('Fraud analysis data received:', data);
            renderFraudAnalysisTable(data);
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading fraud analysis:', error, xhr.responseText);
            renderFraudAnalysisTable([]);
        });
}

function loadRegionalAnalysis() {
    console.log('Loading regional analysis...');
    $.get('/api/tender-analysis/regional', { period: currentPeriod })
        .done(function(data) {
            console.log('Regional data received:', data);
            renderRegionalChart(data);
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading regional analysis:', error, xhr.responseText);
        });
}

// Utility functions (fallback if not loaded from external file)
function formatNumber(num) {
    console.log('formatNumber called with:', num, 'type:', typeof num);
    if (num === null || num === undefined || num === '') return '-';

    // Convert to number if it's a string
    const numValue = typeof num === 'string' ? parseInt(num) : num;

    if (isNaN(numValue)) return '-';

    try {
        return new Intl.NumberFormat('id-ID').format(numValue);
    } catch (e) {
        console.error('Error formatting number:', e);
        return numValue.toString();
    }
}

function formatCurrency(amount) {
    if (amount === null || amount === undefined || amount === 0) return '-';

    if (amount >= 1000000000000) {
        return 'Rp ' + (amount / 1000000000000).toFixed(1) + ' T';
    } else if (amount >= 1000000000) {
        return 'Rp ' + (amount / 1000000000).toFixed(1) + ' M';
    } else if (amount >= 1000000) {
        return 'Rp ' + (amount / 1000000).toFixed(1) + ' Jt';
    } else {
        return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
    }
}

// Fallback chart rendering functions if external file fails to load
if (typeof renderTenderTrendsChart === 'undefined') {
    function renderTenderTrendsChart(data) {
        console.log('Fallback: renderTenderTrendsChart', data);
        $('#tender-trends-chart').html('<div class="text-center p-4"><i class="material-icons-outlined">error</i><br>Chart loading failed</div>');
    }
}

if (typeof renderTopLpseList === 'undefined') {
    function renderTopLpseList(data) {
        console.log('Fallback: renderTopLpseList', data);
        let html = '<div class="text-center p-4"><i class="material-icons-outlined">error</i><br>Failed to load LPSE data</div>';
        if (data && data.length > 0) {
            html = '<div class="list-group">';
            data.slice(0, 5).forEach((lpse, index) => {
                html += `<div class="list-group-item d-flex justify-content-between align-items-center">
                    <div><strong>${lpse.nama_lpse}</strong><br><small class="text-muted">${lpse.provinsi}</small></div>
                    <span class="badge bg-primary">${lpse.total_tenders} tender</span>
                </div>`;
            });
            html += '</div>';
        }
        $('#top-lpse-list').html(html);
    }
}

if (typeof renderCategoryChart === 'undefined') {
    function renderCategoryChart(data) {
        console.log('Fallback: renderCategoryChart', data);
        $('#category-chart').html('<div class="text-center p-4"><i class="material-icons-outlined">error</i><br>Chart loading failed</div>');
    }
}

if (typeof renderMethodChart === 'undefined') {
    function renderMethodChart(data) {
        console.log('Fallback: renderMethodChart', data);
        $('#method-chart').html('<div class="text-center p-4"><i class="material-icons-outlined">error</i><br>Chart loading failed</div>');
    }
}

if (typeof renderValueDistributionChart === 'undefined') {
    function renderValueDistributionChart(data) {
        console.log('Fallback: renderValueDistributionChart', data);
        $('#value-distribution-chart').html('<div class="text-center p-4"><i class="material-icons-outlined">error</i><br>Chart loading failed</div>');
    }
}

if (typeof renderDurationChart === 'undefined') {
    function renderDurationChart(data) {
        console.log('Fallback: renderDurationChart', data);
        $('#duration-chart').html('<div class="text-center p-4"><i class="material-icons-outlined">error</i><br>Chart loading failed</div>');
    }
}

// renderActiveCompaniesTable is defined in external file

// renderTopWinnersTable is defined in external file

// renderFraudAnalysisTable is defined in external file

if (typeof renderRegionalChart === 'undefined') {
    function renderRegionalChart(data) {
        console.log('Fallback: renderRegionalChart', data);
        $('#regional-chart').html('<div class="text-center p-4"><i class="material-icons-outlined">error</i><br>Chart loading failed</div>');
    }
}
</script>
@endsection
