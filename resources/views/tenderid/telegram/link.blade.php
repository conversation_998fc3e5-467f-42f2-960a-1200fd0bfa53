@extends('layouts.master')

@section('title', 'Tautkan Telegram Anda')

@section('css')
<style>
    .telegram-bg {
        background-color: #0088cc !important;
        color: white !important;
        border: none !important;
        box-shadow: 0 4px 6px rgba(0, 136, 204, 0.2) !important;
        transition: all 0.3s ease !important;
    }
    .telegram-bg:hover {
        background-color: #0077b3 !important;
        box-shadow: 0 6px 8px rgba(0, 136, 204, 0.3) !important;
        transform: translateY(-2px);
    }
    .telegram-bg:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 136, 204, 0.2) !important;
    }
    .telegram-text {
        color: #0088cc;
    }
    .telegram-border {
        border-color: #0088cc;
    }
    .telegram-card {
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    .telegram-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 136, 204, 0.2);
    }
    .telegram-icon {
        font-size: 64px;
    }
    .telegram-feature {
        transition: all 0.3s ease;
    }
    .telegram-feature:hover {
        transform: translateY(-5px);
    }
    .qr-container {
        position: relative;
        width: 200px;
        height: 200px;
        margin: 0 auto;
        border: 8px solid white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    .telegram-logo {
        position: absolute;
        bottom: -5px;
        right: -5px;
        width: 50px;
        height: 50px;
        background-color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 10;
    }
    .telegram-logo i {
        color: #0088cc;
        font-size: 30px;
    }
</style>
@endsection

@section('content')
<div class="page-content-wrapper">
    <!-- Breadcrumb -->
    <div class="page-breadcrumb d-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">Pengaturan</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="material-icons-outlined fs-5">home</i></a></li>
                    <li class="breadcrumb-item"><a href="{{ route('tenderid.profile.index') }}">Profil</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Telegram</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- End Breadcrumb -->

    @if(session('success'))
        <div class="alert alert-success border-0 bg-success alert-dismissible fade show py-2 mb-3">
            <div class="d-flex align-items-center">
                <div class="fs-3 text-white"><i class="material-icons-outlined">check_circle</i></div>
                <div class="ms-3">
                    <div class="text-white">{{ session('success') }}</div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger border-0 bg-danger alert-dismissible fade show py-2 mb-3">
            <div class="d-flex align-items-center">
                <div class="fs-3 text-white"><i class="material-icons-outlined">error</i></div>
                <div class="ms-3">
                    <div class="text-white">{{ session('error') }}</div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('info'))
        <div class="alert alert-info border-0 bg-info alert-dismissible fade show py-2 mb-3">
            <div class="d-flex align-items-center">
                <div class="fs-3 text-white"><i class="material-icons-outlined">info</i></div>
                <div class="ms-3">
                    <div class="text-white">{{ session('info') }}</div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-12">
            <div class="card rounded-4">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="wh-48 d-flex align-items-center justify-content-center telegram-bg rounded-circle me-3">
                            <span class="material-icons-outlined text-white">send</span>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Telegram Notification</h5>
                            <p class="mb-0 text-secondary">Dapatkan notifikasi tender melalui Telegram</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-7">
                            <div class="card border shadow-none rounded-4 mb-4">
                                <div class="card-body">
                                    <h6 class="mb-3 fw-bold">Status Koneksi</h6>

                                    @if(auth()->user()->telegram_bot)
                                        <div class="d-flex align-items-center p-3 bg-light-success rounded-4 mb-3">
                                            <div class="wh-48 d-flex align-items-center justify-content-center bg-success rounded-circle me-3">
                                                <span class="material-icons-outlined text-white">check_circle</span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-bold">Akun Telegram Tertaut!</h6>
                                                <p class="mb-0 text-secondary">Anda akan menerima notifikasi tender melalui Telegram secara real-time.</p>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-end">
                                            <a href="{{ route('telegram.disconnect') }}" class="btn btn-outline-danger d-flex align-items-center gap-1" onclick="return confirm('Apakah Anda yakin ingin memutuskan koneksi Telegram?')">
                                                <span class="material-icons-outlined" style="font-size: 18px;">link_off</span>
                                                <span>Putuskan Koneksi</span>
                                            </a>
                                        </div>
                                    @else
                                        <div class="d-flex align-items-center p-3 bg-light-warning rounded-4 mb-3">
                                            <div class="wh-48 d-flex align-items-center justify-content-center bg-warning rounded-circle me-3">
                                                <span class="material-icons-outlined text-white">info</span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-bold">Akun Telegram Belum Tertaut</h6>
                                                <p class="mb-0 text-secondary">Tautkan akun Telegram Anda untuk menerima notifikasi tender secara real-time.</p>
                                            </div>
                                        </div>

                                        <div class="text-center my-4">
                                            <h6 class="mb-3 fw-bold">Cara Menautkan Akun Telegram</h6>

                                            <div class="row justify-content-center">
                                                <div class="col-md-8">
                                                    <div class="alert alert-info mb-4">
                                                        <div class="d-flex">
                                                            <div class="me-3">
                                                                <i class="material-icons-outlined">info</i>
                                                            </div>
                                                            <div class="text-start">
                                                                <p class="mb-1"><strong>Petunjuk Scan QR Code:</strong></p>
                                                                <ol class="mb-0 ps-3">
                                                                    <li>Buka aplikasi Telegram di ponsel Anda</li>
                                                                    <li>Ketuk ikon kamera di pojok kanan atas</li>
                                                                    <li>Arahkan kamera ke QR code di bawah ini</li>
                                                                </ol>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="qr-container mb-4">
                                                <img src="{{ route('qrcode.generate', ['data' => $linkUrl, 'size' => 200]) }}" alt="QR Code" class="img-fluid">
                                                <div class="telegram-logo">
                                                    <i class="material-icons-outlined">send</i>
                                                </div>
                                            </div>

                                            <p class="mb-3">Atau klik tombol di bawah ini untuk menautkan akun Telegram Anda:</p>
                                            <a href="{{ $linkUrl }}" target="_blank" class="btn btn-lg telegram-bg d-inline-flex align-items-center gap-2 px-4 py-2">
                                                <span class="material-icons-outlined">send</span>
                                                <span><strong>Tautkan Akun Telegram</strong></span>
                                            </a>

                                            <div class="mt-4 small text-muted">
                                                <p class="mb-1">Setelah terhubung, ketik <code>/start {{ auth()->user()->email }}</code> di bot Telegram.</p>
                                                <p class="mb-0">Jika QR code tidak bisa di-scan, gunakan tombol di atas untuk membuka Telegram secara langsung.</p>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-5">
                            <div class="card border shadow-none rounded-4">
                                <div class="card-body">
                                    <h6 class="mb-3 fw-bold">Keuntungan Menggunakan Telegram</h6>

                                    <div class="d-flex align-items-start gap-3 mb-3 telegram-feature">
                                        <div class="wh-40 d-flex align-items-center justify-content-center telegram-bg rounded-circle">
                                            <span class="material-icons-outlined text-white" style="font-size: 20px;">notifications_active</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-1 fw-medium">Notifikasi Real-time</h6>
                                            <p class="mb-0 text-secondary small">Dapatkan notifikasi instan saat ada perubahan status tender.</p>
                                        </div>
                                    </div>

                                    <div class="d-flex align-items-start gap-3 mb-3 telegram-feature">
                                        <div class="wh-40 d-flex align-items-center justify-content-center telegram-bg rounded-circle">
                                            <span class="material-icons-outlined text-white" style="font-size: 20px;">search</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-1 fw-medium">Cari Tender via Bot</h6>
                                            <p class="mb-0 text-secondary small">Gunakan perintah /tender untuk mencari dan melihat detail tender.</p>
                                        </div>
                                    </div>

                                    <div class="d-flex align-items-start gap-3 mb-3 telegram-feature">
                                        <div class="wh-40 d-flex align-items-center justify-content-center telegram-bg rounded-circle">
                                            <span class="material-icons-outlined text-white" style="font-size: 20px;">security</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-1 fw-medium">Aman & Terenkripsi</h6>
                                            <p class="mb-0 text-secondary small">Komunikasi melalui Telegram terenkripsi end-to-end.</p>
                                        </div>
                                    </div>

                                    <div class="d-flex align-items-start gap-3 telegram-feature">
                                        <div class="wh-40 d-flex align-items-center justify-content-center telegram-bg rounded-circle">
                                            <span class="material-icons-outlined text-white" style="font-size: 20px;">devices</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-1 fw-medium">Multi-platform</h6>
                                            <p class="mb-0 text-secondary small">Akses notifikasi dari berbagai perangkat (mobile, desktop, web).</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card border shadow-none rounded-4 mt-2">
                        <div class="card-body">
                            <h6 class="mb-3 fw-bold">Cara Menggunakan Bot Telegram</h6>

                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Perintah</th>
                                            <th>Deskripsi</th>
                                            <th>Contoh</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>/start</code></td>
                                            <td>Memulai bot dan menautkan akun</td>
                                            <td><code>/start</code></td>
                                        </tr>
                                        <tr>
                                            <td><code>/akun</code></td>
                                            <td>Melihat informasi akun Anda</td>
                                            <td><code>/akun</code></td>
                                        </tr>
                                        <tr>
                                            <td><code>/tender [id]</code></td>
                                            <td>Melihat detail tender berdasarkan ID</td>
                                            <td><code>/tender 123456</code></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
