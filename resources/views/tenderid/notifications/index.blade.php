@extends('layouts.master')

@section('title', 'Notifikasi')

@section('content')
<div class="page-content-wrapper">
    <!-- Breadcrumb -->
    <div class="page-breadcrumb d-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">Notifikasi</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="material-icons-outlined fs-5">home</i></a></li>
                    <li class="breadcrumb-item active" aria-current="page">Notifikasi</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- End Breadcrumb -->

    @if(session('success'))
        <div class="alert alert-success border-0 bg-success alert-dismissible fade show py-2 mb-3">
            <div class="d-flex align-items-center">
                <div class="fs-3 text-white"><i class="material-icons-outlined">check_circle</i></div>
                <div class="ms-3">
                    <div class="text-white">{{ session('success') }}</div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="card rounded-4">
        <div class="card-body">
            <div class="d-flex align-items-center justify-content-between mb-4">
                <div class="d-flex align-items-center">
                    <div class="wh-48 d-flex align-items-center justify-content-center bg-primary rounded-circle me-3">
                        <span class="material-icons-outlined text-white">notifications</span>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">Notifikasi</h5>
                        <p class="mb-0 text-secondary">Semua notifikasi Anda</p>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    @if(auth()->user()->unreadNotifications->count() > 0)
                        <a href="{{ route('notifications.markAllAsRead') }}" class="btn btn-outline-primary d-flex align-items-center gap-1">
                            <i class="material-icons-outlined" style="font-size: 18px;">done_all</i>
                            <span>Tandai Semua Dibaca</span>
                        </a>
                    @endif
                    @if(auth()->user()->notifications->count() > 0)
                        <a href="{{ route('notifications.destroyAll') }}" class="btn btn-outline-danger d-flex align-items-center gap-1" 
                           onclick="return confirm('Apakah Anda yakin ingin menghapus semua notifikasi?')">
                            <i class="material-icons-outlined" style="font-size: 18px;">delete_sweep</i>
                            <span>Hapus Semua</span>
                        </a>
                    @endif
                </div>
            </div>

            @if($notifications->count() > 0)
                <div class="table-responsive">
                    <table class="table align-middle">
                        <thead class="table-light">
                            <tr>
                                <th>Notifikasi</th>
                                <th class="text-center" style="width: 120px;">Status</th>
                                <th class="text-center" style="width: 150px;">Waktu</th>
                                <th class="text-center" style="width: 100px;">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($notifications as $notification)
                                @php
                                    $notifData = $notification->data;
                                    $notifType = $notifData['type'] ?? '';
                                    $notifIcon = 'notifications';
                                    $notifColor = 'primary';
                                    $notifUrl = '#';
                                    
                                    if (isset($notifData['tender_id'])) {
                                        $notifUrl = route('tender_lpse.detail', $notifData['tender_id']);
                                        $notifIcon = 'assignment';
                                        $notifColor = 'success';
                                    } elseif (isset($notifData['sirup_id'])) {
                                        $notifUrl = route('sirup.detail', $notifData['sirup_id']);
                                        $notifIcon = 'description';
                                        $notifColor = 'info';
                                    } elseif (isset($notifData['user_subscription_id'])) {
                                        $notifUrl = route('subscriptions.index');
                                        $notifIcon = 'card_membership';
                                        $notifColor = 'warning';
                                    } elseif (isset($notifData['withdrawal_id'])) {
                                        $notifUrl = route('withdrawals.history');
                                        $notifIcon = 'monetization_on';
                                        $notifColor = 'success';
                                    } elseif ($notifType == 'welcome' || $notifType == 'admin_notification') {
                                        $notifUrl = route('tenderid.profile.index');
                                        $notifIcon = 'person';
                                        $notifColor = 'primary';
                                    }
                                @endphp
                                <tr class="{{ $notification->read_at ? '' : 'bg-light-primary' }}">
                                    <td>
                                        <div class="d-flex align-items-center gap-3">
                                            <div class="wh-45 d-flex align-items-center justify-content-center bg-{{ $notifColor }} rounded-circle">
                                                <i class="material-icons-outlined text-white">{{ $notifIcon }}</i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $notifData['nama_paket'] ?? ($notifData['subscription_name'] ?? 'Notifikasi') }}</h6>
                                                <p class="mb-0 text-muted">
                                                    @if(isset($notifData['tahap_tender']))
                                                        Tahap Tender menjadi {{ $notifData['tahap_tender'] }}
                                                    @elseif(isset($notifData['status_tender']))
                                                        Status: {{ $notifData['status_tender'] }}
                                                    @elseif($notifType == 'welcome')
                                                        Selamat datang di TenderID!
                                                    @elseif($notifType == 'subscription_selected')
                                                        Paket langganan {{ $notifData['subscription_name'] }} dipilih
                                                    @elseif($notifType == 'payment_confirmation')
                                                        Konfirmasi pembayaran berhasil
                                                    @elseif($notifType == 'withdrawal_request')
                                                        Permintaan penarikan dana
                                                    @elseif($notifType == 'withdrawal_processed')
                                                        Penarikan dana {{ $notifData['status'] == 'approved' ? 'disetujui' : 'ditolak' }}
                                                    @else
                                                        {{ $notifType ?? 'Notifikasi baru' }}
                                                    @endif
                                                </p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        @if($notification->read_at)
                                            <span class="badge bg-light-success text-success">Dibaca</span>
                                        @else
                                            <span class="badge bg-light-primary text-primary">Belum Dibaca</span>
                                        @endif
                                    </td>
                                    <td class="text-center">{{ $notification->created_at->format('d M Y H:i') }}</td>
                                    <td>
                                        <div class="d-flex justify-content-center gap-2">
                                            <a href="{{ $notifUrl }}" class="btn btn-sm btn-primary" title="Lihat Detail">
                                                <i class="material-icons-outlined" style="font-size: 16px;">visibility</i>
                                            </a>
                                            @if(!$notification->read_at)
                                                <a href="{{ route('notifications.markAsRead', $notification->id) }}" class="btn btn-sm btn-success" title="Tandai Dibaca">
                                                    <i class="material-icons-outlined" style="font-size: 16px;">done</i>
                                                </a>
                                            @endif
                                            <a href="{{ route('notifications.destroy', $notification->id) }}" class="btn btn-sm btn-danger" 
                                               onclick="return confirm('Apakah Anda yakin ingin menghapus notifikasi ini?')" title="Hapus">
                                                <i class="material-icons-outlined" style="font-size: 16px;">delete</i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <div class="d-flex justify-content-center mt-4">
                    {{ $notifications->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="material-icons-outlined text-muted" style="font-size: 64px;">notifications_off</i>
                    </div>
                    <h5 class="text-muted">Belum ada notifikasi</h5>
                    <p class="text-muted">Notifikasi akan muncul di sini ketika ada pembaruan pada tender yang Anda ikuti atau aktivitas akun Anda.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
