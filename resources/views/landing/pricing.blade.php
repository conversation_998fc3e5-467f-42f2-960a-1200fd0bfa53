@extends('layouts.landing')

@section('title', 'Harga Paket TenderID')

@section('css')
<style>
    .pricing-card {
        border-radius: 15px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .pricing-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }

    .pricing-card.popular {
        border: 2px solid var(--primary-color);
        position: relative;
    }

    .pricing-card.popular::before {
        content: 'Populer';
        position: absolute;
        top: 0;
        right: 0;
        background-color: var(--primary-color);
        color: white;
        padding: 5px 15px;
        font-size: 12px;
        font-weight: 600;
        border-bottom-left-radius: 10px;
    }

    .pricing-header {
        padding: 30px;
        text-align: center;
        background-color: rgba(13, 110, 253, 0.05);
    }

    .pricing-body {
        padding: 30px;
    }

    .pricing-price {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 10px;
    }

    .pricing-duration {
        font-size: 1rem;
        color: var(--secondary-color);
    }

    .pricing-features {
        list-style: none;
        padding-left: 0;
        margin-bottom: 30px;
    }

    .pricing-features li {
        padding: 10px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        display: flex;
        align-items: center;
    }

    .pricing-features li:last-child {
        border-bottom: none;
    }

    .pricing-features i {
        margin-right: 10px;
        color: var(--success-color);
    }

    .pricing-features i.text-muted {
        color: var(--secondary-color);
    }

    .faq-item {
        margin-bottom: 20px;
        border-radius: 10px;
        overflow: hidden;
        border: 1px solid rgba(0,0,0,0.1);
    }

    .faq-question {
        padding: 20px;
        background-color: white;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .faq-answer {
        padding: 0 20px;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease, padding 0.3s ease;
    }

    .faq-item.active .faq-answer {
        padding: 20px;
        max-height: 500px;
        border-top: 1px solid rgba(0,0,0,0.1);
    }

    .faq-item.active .faq-question i {
        transform: rotate(180deg);
    }
</style>
@endsection

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container hero-content">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">Pilih Paket yang Tepat untuk Anda</h1>
                <p class="lead mb-4">Kami menawarkan berbagai paket yang dirancang untuk memenuhi kebutuhan bisnis Anda.</p>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Paket Berlangganan</h2>
            <p class="lead text-muted">Pilih paket yang sesuai dengan kebutuhan Anda</p>
        </div>

        <!-- Subscription Packages Table -->

        <div class="row g-4">
            @foreach($subscriptionPackages as $index => $package)
            <div class="col-lg-3 col-md-6">
                <div class="pricing-card card h-100 {{ $index == 3 ? 'popular' : '' }}">
                    <div class="pricing-header">
                        <h3 class="fw-bold">{{ $package['name'] }}</h3>
                        <div class="pricing-price">Rp {{ number_format($package['price'], 0, ',', '.') }}</div>
                        <div class="pricing-duration">
                            <span class="badge bg-success">Hemat {{ $package['discount_percentage'] }}%</span>
                        </div>
                    </div>
                    <div class="pricing-body">
                        <ul class="pricing-features">
                            <li><i class="material-icons-outlined">check_circle</i> Akses Data Tender</li>
                            <li><i class="material-icons-outlined">check_circle</i> Akses Data SIRUP</li>
                            <li><i class="material-icons-outlined">check_circle</i> Akses Data E-Katalog 5</li>
                            <li><i class="material-icons-outlined">check_circle</i> Akses Data E-Katalog 6</li>
                            <li><i class="material-icons-outlined">check_circle</i> Akses Data Perusahaan terkait Pekerjaan Yang didapatkan</li>
                            <li><i class="material-icons-outlined">check_circle</i> Notifikasi email</li>
                            <li><i class="material-icons-outlined">check_circle</i> Notifikasi Telegram</li>
                            <li><i class="material-icons-outlined">check_circle</i> Notifikasi WhatsApp</li>
                            <li><i class="material-icons-outlined">check_circle</i> Durasi {{ $package['duration_days'] }} hari</li>
                        </ul>
                        <a href="{{ route('register') }}" class="btn btn-{{ $index == 3 ? 'primary' : 'outline-primary' }} w-100">Pilih Paket</a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Comparison Table -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Perbandingan Paket</h2>
            <p class="lead text-muted">Bandingkan durasi dan harga dari setiap paket</p>
        </div>

        <!-- Subscription Packages Table -->
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th>Nama Paket</th>
                                <th>Harga</th>
                                <th>Durasi (hari)</th>
                                <th>Diskon</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($subscriptionPackages as $package)
                            <tr>
                                <td class="fw-bold">{{ $package['name'] }}</td>
                                <td>
                                    <div class="fw-bold text-primary">Rp {{ number_format($package['price'], 0, ',', '.') }}</div>
                                    <small class="text-muted text-decoration-line-through">Rp {{ number_format($package['normal_price'], 0, ',', '.') }}</small>
                                </td>
                                <td>{{ $package['duration_days'] }}</td>
                                <td>
                                    <span class="badge bg-success">{{ $package['discount_percentage'] }}%</span>
                                    <div class="small text-muted">Hemat Rp {{ number_format($package['discount_amount'], 0, ',', '.') }}</div>
                                </td>
                                <td>
                                    <a href="{{ route('register') }}" class="btn btn-sm btn-primary">Pilih Paket</a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


    </div>
</section>

<!-- FAQ Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Pertanyaan Umum</h2>
            <p class="lead text-muted">Jawaban untuk pertanyaan yang sering diajukan</p>
        </div>
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="faq-item" id="faq-1">
                    <div class="faq-question">
                        <span>Bagaimana cara berlangganan TenderID?</span>
                        <i class="material-icons-outlined">expand_more</i>
                    </div>
                    <div class="faq-answer">
                        <p>Untuk berlangganan TenderID, Anda perlu mendaftar terlebih dahulu. Setelah mendaftar, Anda dapat memilih paket berlangganan yang sesuai dengan kebutuhan Anda dan melakukan pembayaran.</p>
                    </div>
                </div>
                <div class="faq-item" id="faq-2">
                    <div class="faq-question">
                        <span>Apakah saya bisa mengubah paket berlangganan?</span>
                        <i class="material-icons-outlined">expand_more</i>
                    </div>
                    <div class="faq-answer">
                        <p>Ya, Anda dapat mengubah paket berlangganan kapan saja. Jika Anda meningkatkan paket, Anda akan dikenakan biaya tambahan sesuai dengan selisih harga paket. Jika Anda menurunkan paket, perubahan akan berlaku pada periode berlangganan berikutnya.</p>
                    </div>
                </div>
                <div class="faq-item" id="faq-3">
                    <div class="faq-question">
                        <span>Metode pembayaran apa yang tersedia?</span>
                        <i class="material-icons-outlined">expand_more</i>
                    </div>
                    <div class="faq-answer">
                        <p>Kami hanya menerima pembayaran melalui transfer bank ke rekening yang tercantum pada invoice pembayaran. Setelah Anda memilih paket berlangganan, sistem akan menampilkan invoice dengan detail rekening bank tujuan. Silakan lakukan pembayaran sesuai dengan instruksi yang tertera pada invoice tersebut.</p>
                    </div>
                </div>
                <div class="faq-item" id="faq-4">
                    <div class="faq-question">
                        <span>Apakah ada diskon untuk berlangganan jangka panjang?</span>
                        <i class="material-icons-outlined">expand_more</i>
                    </div>
                    <div class="faq-answer">
                        <p>Ya, kami menawarkan diskon untuk berlangganan jangka panjang. Semakin lama durasi berlangganan, semakin besar diskon yang Anda dapatkan. Paket Kuartalan memberikan diskon sekitar 28%, Semesteran sekitar 33%, dan Tahunan sekitar 44% dibandingkan dengan harga normal bulanan.</p>
                    </div>
                </div>
                <div class="faq-item" id="faq-5">
                    <div class="faq-question">
                        <span>Bagaimana proses konfirmasi pembayaran?</span>
                        <i class="material-icons-outlined">expand_more</i>
                    </div>
                    <div class="faq-answer">
                        <p>Setelah melakukan transfer bank, sistem kami akan memverifikasi pembayaran Anda dalam waktu maksimal 1 jam setelah konfirmasi pembayaran. Anda dapat mengunggah bukti transfer pada halaman invoice untuk mempercepat proses verifikasi. Setelah pembayaran terverifikasi, akun Anda akan langsung aktif dan Anda dapat mengakses semua fitur sesuai dengan paket berlangganan yang dipilih.</p>
                    </div>
                </div>
                <div class="faq-item" id="faq-6">
                    <div class="faq-question">
                        <span>Bagaimana cara melihat status pembayaran saya?</span>
                        <i class="material-icons-outlined">expand_more</i>
                    </div>
                    <div class="faq-answer">
                        <p>Anda dapat melihat status pembayaran Anda di halaman profil setelah login ke akun TenderID. Di sana Anda akan menemukan informasi tentang paket berlangganan Anda, tanggal mulai dan berakhir berlangganan, serta status pembayaran. Jika pembayaran Anda belum terverifikasi setelah 1 jam dari konfirmasi pembayaran, silakan hubungi kami melalui <NAME_EMAIL>.</p>
                    </div>
                </div>

            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="display-6 fw-bold mb-4">Siap Memulai?</h2>
                <p class="lead mb-4">Daftar sekarang dan pilih paket berlangganan yang sesuai dengan kebutuhan Anda. Semua paket menawarkan fitur yang sama dengan durasi berbeda.</p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="{{ route('register') }}" class="btn btn-light btn-lg px-4">Daftar Sekarang</a>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-light btn-lg px-4">Hubungi Kami</a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const faqItems = document.querySelectorAll('.faq-item');

        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');

            question.addEventListener('click', () => {
                item.classList.toggle('active');
            });
        });
    });
</script>
@endsection
