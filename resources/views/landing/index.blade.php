@extends('layouts.landing')

@section('title', 'Platform Monitoring Tender Indonesia')

@section('meta_description', 'TenderID - Platform monitoring tender, SIRUP, dan e-Katalog terlengkap di Indonesia. Pantau seluruh siklus pengadaan pemerintah dari SIRUP hingga Tender dan e-Katalog dalam satu platform.')

@section('meta_keywords', 'tender indonesia, sirup lkpp, e-katalog, monitoring tender, pengadaan, lpse, tender pemerintah, monitoring sirup, pantau tender, tender terbaru')

@section('css')
<style>
    /* Hero Title Styling */
    .hero-title {
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        position: relative;
    }

    .hero-title::after {
        content: '';
        position: absolute;
        width: 100px;
        height: 4px;
        background: linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
        bottom: -10px;
        left: 0;
        border-radius: 2px;
    }

    /* Hero <PERSON>tons Styling */
    .hero-btn-primary, .hero-btn-secondary {
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 30px;
    }

    .hero-btn-primary {
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
    }

    .hero-btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
    }

    .hero-btn-secondary {
        border-width: 2px;
    }

    .hero-btn-secondary:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: translateY(-3px);
    }

    .hero-btn-primary i, .hero-btn-secondary i {
        transition: transform 0.3s ease;
    }

    .hero-btn-primary:hover i, .hero-btn-secondary:hover i {
        transform: translateX(5px);
    }

    /* Hero Section Styling */
    .hero-section {
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        width: 300px;
        height: 300px;
        border-radius: 50%;
        background: linear-gradient(45deg, rgba(13, 110, 253, 0.05), rgba(13, 110, 253, 0.1));
        top: -150px;
        right: -150px;
        z-index: 0;
    }

    /* Feature Section Styling */
    .feature-section {
        position: relative;
        overflow: hidden;
        padding: 80px 0;
    }

    .feature-section:nth-child(even) {
        background-color: #f8f9fa;
    }

    .feature-section::before {
        content: '';
        position: absolute;
        width: 300px;
        height: 300px;
        border-radius: 50%;
        background: linear-gradient(45deg, rgba(13, 110, 253, 0.05), rgba(13, 110, 253, 0.1));
        top: -150px;
        right: -150px;
        z-index: 0;
    }

    .feature-section:nth-child(even)::before {
        left: -150px;
        right: auto;
        background: linear-gradient(45deg, rgba(25, 135, 84, 0.05), rgba(25, 135, 84, 0.1));
    }

    /* Image Styling */
    .feature-img {
        border-radius: 12px;
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        overflow: hidden;
        max-width: 105%;
        transition: transform 0.3s ease;
    }

    .feature-img-left {
        margin-left: -5%;
    }

    .feature-img-right {
        margin-right: -5%;
    }

    .feature-img:hover {
        transform: scale(1.02);
    }
</style>
@endsection

@section('content')
@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <div class="d-flex align-items-center">
        <i class="material-icons-outlined me-2">error</i>
        <div>{{ session('error') }}</div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif
<!-- Hero Section -->
<section class="hero-section">
    <div class="floating-shape shape-1"></div>
    <div class="floating-shape shape-2"></div>
    <div class="floating-shape shape-3"></div>
    <div class="floating-shape shape-4"></div>
    <div class="container-fluid px-lg-4 px-xl-5 hero-content">
        <div class="row align-items-center">
            <div class="col-lg-5">
                <h1 class="display-4 fw-bold mb-4 hero-title">Monitoring SIRUP & Tender Terlengkap di Indonesia</h1>
                <p class="lead mb-4">Pantau seluruh siklus pengadaan pemerintah dari SIRUP hingga Tender dan e-Katalog dalam satu platform. Ketahui status setiap paket pengadaan secara real-time.</p>
                <div class="d-flex gap-3">
                    <a href="{{ route('register') }}" class="btn btn-light btn-lg px-4 hero-btn-primary">
                        <span>Daftar Sekarang</span>
                        <i class="bi bi-arrow-right-circle ms-2"></i>
                    </a>
                    <a href="{{ route('landing.features') }}" class="btn btn-outline-light btn-lg px-4 hero-btn-secondary">
                        <span>Pelajari Fitur</span>
                        <i class="bi bi-info-circle ms-2"></i>
                    </a>
                </div>
            </div>
            <div class="col-lg-7 d-none d-lg-block">
                <img src="{{ URL::asset('build/images/hero-image.png') }}" alt="TenderID Dashboard" class="img-fluid feature-img feature-img-right">
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Data Terpercaya</h2>
            <p class="lead text-muted">Kami telah mengumpulkan data tender dari seluruh Indonesia</p>
        </div>

        <div id="stats-container">
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Memuat data statistik...</p>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Fitur Unggulan</h2>
            <p class="lead text-muted">Berbagai fitur yang memudahkan Anda memantau tender</p>
        </div>
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="material-icons-outlined">search</i>
                        </div>
                        <h4>Pencarian Tender</h4>
                        <p class="text-muted">Cari tender dengan mudah berdasarkan kata kunci, lokasi, atau kategori.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="material-icons-outlined">notifications_active</i>
                        </div>
                        <h4>Notifikasi Real-time</h4>
                        <p class="text-muted">Dapatkan notifikasi instan melalui Telegram dan WhatsApp untuk setiap perubahan status tender.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="material-icons-outlined">favorite</i>
                        </div>
                        <h4>Tender Favorit</h4>
                        <p class="text-muted">Simpan dan pantau tender favorit Anda dengan mudah.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="material-icons-outlined">analytics</i>
                        </div>
                        <h4>Analisis Data</h4>
                        <p class="text-muted">Lihat tren dan statistik tender untuk membantu pengambilan keputusan.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="material-icons-outlined">business</i>
                        </div>
                        <h4>Profil Perusahaan</h4>
                        <p class="text-muted">Lihat riwayat dan performa perusahaan dalam tender.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="material-icons-outlined">dashboard</i>
                        </div>
                        <h4>Dashboard Kustom</h4>
                        <p class="text-muted">Sesuaikan dashboard Anda untuk menampilkan informasi yang paling relevan.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- SIRUP Monitoring Feature Highlight -->
<section class="feature-section">
    <div class="container-fluid px-lg-4 px-xl-5">
        <div class="row align-items-center">
            <div class="col-lg-7 mb-4 mb-lg-0">
                <img src="{{ URL::asset('build/images/hero-sirup.png') }}" alt="SIRUP Monitoring" class="img-fluid feature-img feature-img-left" onerror="this.src='{{ URL::asset('build/images/hero-image.png') }}'">
            </div>
            <div class="col-lg-5">
                <span class="badge bg-primary mb-2">Fitur Unggulan</span>
                <h2 class="fw-bold mb-4">Monitoring SIRUP yang Revolusioner</h2>
                <p class="lead mb-4">Temukan peluang bisnis lebih awal dengan monitoring SIRUP yang komprehensif. Ketahui status setiap paket pengadaan, dari perencanaan hingga pelaksanaan.</p>

                <div class="d-flex mb-3">
                    <div class="me-3 text-primary">
                        <i class="material-icons-outlined">check_circle</i>
                    </div>
                    <div>
                        <h5 class="fw-bold">Lacak Status Paket SIRUP</h5>
                        <p>Pantau status paket SIRUP secara real-time. Ketahui apakah paket sudah berjalan, dalam proses tender, atau sudah menjadi e-Katalog.</p>
                    </div>
                </div>

                <div class="d-flex mb-3">
                    <div class="me-3 text-primary">
                        <i class="material-icons-outlined">check_circle</i>
                    </div>
                    <div>
                        <h5 class="fw-bold">Analisis Tren Pengadaan</h5>
                        <p>Analisis pola dan tren pengadaan berdasarkan data SIRUP. Dapatkan insight berharga untuk strategi bisnis Anda.</p>
                    </div>
                </div>

                <div class="d-flex mb-3">
                    <div class="me-3 text-primary">
                        <i class="material-icons-outlined">check_circle</i>
                    </div>
                    <div>
                        <h5 class="fw-bold">Notifikasi Perubahan Status</h5>
                        <p>Dapatkan notifikasi instan saat status paket SIRUP berubah. Jangan lewatkan peluang tender yang Anda incar.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Tender Monitoring Feature -->
<section class="feature-section">
    <div class="container-fluid px-lg-4 px-xl-5">
        <div class="row align-items-center">
            <div class="col-lg-5">
                <span class="badge bg-success mb-2">Fitur Utama</span>
                <h2 class="fw-bold mb-4">Monitoring Tender yang Komprehensif</h2>
                <p class="lead mb-4">Pantau semua tender dari berbagai LPSE di seluruh Indonesia dalam satu platform. Dapatkan informasi lengkap dan terkini tentang setiap tender.</p>

                <div class="d-flex mb-3">
                    <div class="me-3 text-success">
                        <i class="material-icons-outlined">check_circle</i>
                    </div>
                    <div>
                        <h5 class="fw-bold">Data Tender Terlengkap</h5>
                        <p>Akses data tender dari seluruh LPSE di Indonesia. Cari tender berdasarkan kategori, lokasi, atau nilai anggaran.</p>
                    </div>
                </div>

                <div class="d-flex mb-3">
                    <div class="me-3 text-success">
                        <i class="material-icons-outlined">check_circle</i>
                    </div>
                    <div>
                        <h5 class="fw-bold">Pantau Tender yang Diikuti</h5>
                        <p>Lacak status tender yang Anda ikuti. Dapatkan notifikasi saat ada perubahan atau pengumuman baru.</p>
                    </div>
                </div>

                <div class="d-flex mb-3">
                    <div class="me-3 text-success">
                        <i class="material-icons-outlined">check_circle</i>
                    </div>
                    <div>
                        <h5 class="fw-bold">Analisis Kompetitor</h5>
                        <p>Pelajari pola penawaran kompetitor Anda. Tingkatkan strategi penawaran untuk meningkatkan peluang menang.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-7">
                <img src="{{ URL::asset('build/images/monitoring-lpse.png') }}" alt="Tender Monitoring" class="img-fluid feature-img feature-img-right">
            </div>
        </div>
    </div>
</section>

<!-- Latest Tenders Section -->
<section class="py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="fw-bold mb-0">Tender Terbaru</h2>
            <a href="{{ route('login') }}" class="btn btn-primary">Lihat Semua</a>
        </div>

        <div id="latest-tenders-container">
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Memuat data tender terbaru...</p>
            </div>
        </div>
    </div>
</section>

<!-- Latest SIRUP Section -->
<section class="py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="fw-bold mb-0">SIRUP Terbaru</h2>
            <a href="{{ route('login') }}" class="btn btn-primary">Lihat Semua</a>
        </div>

        <div id="latest-sirup-container">
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Memuat data SIRUP terbaru...</p>
            </div>
        </div>
    </div>
</section>

<!-- E-Katalog Monitoring Feature -->
<section class="feature-section">
    <div class="container-fluid px-lg-4 px-xl-5">
        <div class="row align-items-center">
            <div class="col-lg-7 mb-4 mb-lg-0">
                <img src="{{ URL::asset('build/images/analitik.png') }}" alt="E-Katalog Monitoring" class="img-fluid feature-img feature-img-left">
            </div>
            <div class="col-lg-5">
                <span class="badge bg-info mb-2">Fitur Terbaru</span>
                <h2 class="fw-bold mb-4">Monitoring E-Katalog yang Lengkap</h2>
                <p class="lead mb-4">Pantau transaksi e-Katalog dari seluruh instansi pemerintah. Analisis tren pembelian produk dan layanan untuk strategi bisnis yang lebih baik.</p>

                <div class="d-flex mb-3">
                    <div class="me-3 text-info">
                        <i class="material-icons-outlined">check_circle</i>
                    </div>
                    <div>
                        <h5 class="fw-bold">Data E-Katalog Terlengkap</h5>
                        <p>Akses data e-Katalog 5 dan e-Katalog 6 dari seluruh instansi pemerintah. Lihat detail setiap transaksi.</p>
                    </div>
                </div>

                <div class="d-flex mb-3">
                    <div class="me-3 text-info">
                        <i class="material-icons-outlined">check_circle</i>
                    </div>
                    <div>
                        <h5 class="fw-bold">Analisis Tren Pembelian</h5>
                        <p>Analisis tren pembelian produk dan layanan berdasarkan data e-Katalog. Identifikasi peluang bisnis baru.</p>
                    </div>
                </div>

                <div class="d-flex mb-3">
                    <div class="me-3 text-info">
                        <i class="material-icons-outlined">check_circle</i>
                    </div>
                    <div>
                        <h5 class="fw-bold">Pantau Kompetitor</h5>
                        <p>Lihat produk dan layanan yang ditawarkan oleh kompetitor Anda di e-Katalog. Bandingkan harga dan spesifikasi.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Integration Feature Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Integrasi Lengkap SIRUP, Tender, dan e-Katalog</h2>
            <p class="lead text-muted">Satu platform untuk memantau seluruh siklus pengadaan pemerintah</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm rounded-4">
                    <div class="card-body p-4">
                        <div class="text-center mb-3">
                            <div class="d-inline-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle p-3 mb-3">
                                <i class="material-icons-outlined text-primary" style="font-size: 36px;">description</i>
                            </div>
                            <h4 class="fw-bold">SIRUP</h4>
                            <p class="text-muted">Rencana Pengadaan</p>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3 text-success">
                                <i class="material-icons-outlined">check_circle</i>
                            </div>
                            <div>
                                <p class="mb-0">Akses data SIRUP dari seluruh instansi pemerintah</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3 text-success">
                                <i class="material-icons-outlined">check_circle</i>
                            </div>
                            <div>
                                <p class="mb-0">Filter berdasarkan metode pemilihan dan nilai pagu</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="me-3 text-success">
                                <i class="material-icons-outlined">check_circle</i>
                            </div>
                            <div>
                                <p class="mb-0">Pantau status paket dari perencanaan hingga pelaksanaan</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm rounded-4">
                    <div class="card-body p-4">
                        <div class="text-center mb-3">
                            <div class="d-inline-flex align-items-center justify-content-center bg-success bg-opacity-10 rounded-circle p-3 mb-3">
                                <i class="material-icons-outlined text-success" style="font-size: 36px;">gavel</i>
                            </div>
                            <h4 class="fw-bold">Tender</h4>
                            <p class="text-muted">Proses Pengadaan</p>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3 text-success">
                                <i class="material-icons-outlined">check_circle</i>
                            </div>
                            <div>
                                <p class="mb-0">Pantau tender dari seluruh LPSE di Indonesia</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3 text-success">
                                <i class="material-icons-outlined">check_circle</i>
                            </div>
                            <div>
                                <p class="mb-0">Dapatkan notifikasi perubahan tahapan tender</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="me-3 text-success">
                                <i class="material-icons-outlined">check_circle</i>
                            </div>
                            <div>
                                <p class="mb-0">Lihat detail peserta dan pemenang tender</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm rounded-4">
                    <div class="card-body p-4">
                        <div class="text-center mb-3">
                            <div class="d-inline-flex align-items-center justify-content-center bg-info bg-opacity-10 rounded-circle p-3 mb-3">
                                <i class="material-icons-outlined text-info" style="font-size: 36px;">shopping_cart</i>
                            </div>
                            <h4 class="fw-bold">e-Katalog</h4>
                            <p class="text-muted">Transaksi Pengadaan</p>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3 text-success">
                                <i class="material-icons-outlined">check_circle</i>
                            </div>
                            <div>
                                <p class="mb-0">Pantau produk dan layanan di e-Katalog</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3 text-success">
                                <i class="material-icons-outlined">check_circle</i>
                            </div>
                            <div>
                                <p class="mb-0">Lacak transaksi e-Katalog dari berbagai instansi</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="me-3 text-success">
                                <i class="material-icons-outlined">check_circle</i>
                            </div>
                            <div>
                                <p class="mb-0">Analisis tren pembelian produk dan layanan</p>
                                <span class="badge bg-info">Fitur Baru</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5">
            <p class="lead">Rasakan pengalaman mencari informasi pengadaan dengan mudah dalam satu platform terintegrasi</p>
            <a href="{{ route('register') }}" class="btn btn-primary btn-lg px-4 mt-3">Mulai Sekarang</a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="display-6 fw-bold mb-4">Siap Memulai?</h2>
                <p class="lead mb-4">Daftar sekarang dan dapatkan akses ke semua fitur TenderID.</p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="{{ route('register') }}" class="btn btn-light btn-lg px-4">Daftar Sekarang</a>
                    <a href="{{ route('login') }}" class="btn btn-outline-light btn-lg px-4">Masuk</a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure all AJAX requests use HTTPS
        const ensureHttps = (url) => {
            return url.replace(/^http:\/\//i, 'https://');
        };

        // Load stats data
        fetch(ensureHttps('{{ route("landing.stats") }}'), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                const statsContainer = document.getElementById('stats-container');
                if (data.error) {
                    statsContainer.innerHTML = `
                        <div class="alert alert-info text-center">
                            <i class="material-icons-outlined fs-3 d-block mb-2">info</i>
                            <p class="mb-0">Sedang memperbarui data statistik. Silakan coba lagi nanti.</p>
                        </div>
                    `;
                } else {
                    statsContainer.innerHTML = `
                        <div class="row g-4">
                            <div class="col-md-3 col-sm-6">
                                <div class="stat-item">
                                    <div class="stat-number" data-count="${data.tender_count}">${new Intl.NumberFormat().format(data.tender_count)}</div>
                                    <div class="stat-label">Tender</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="stat-item">
                                    <div class="stat-number" data-count="${data.sirup_count}">${new Intl.NumberFormat().format(data.sirup_count)}</div>
                                    <div class="stat-label">SIRUP</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="stat-item">
                                    <div class="stat-number" data-count="${data.ekatalog_count}">${new Intl.NumberFormat().format(data.ekatalog_count)}</div>
                                    <div class="stat-label">e-Katalog</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="stat-item">
                                    <div class="stat-number" data-count="${data.lpse_count}">${new Intl.NumberFormat().format(data.lpse_count)}</div>
                                    <div class="stat-label">LPSE</div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Initialize counter animation after stats are loaded
                    initCounterAnimation();
                }
            })
            .catch(error => {
                console.error('Error loading stats:', error);
                document.getElementById('stats-container').innerHTML = `
                    <div class="alert alert-info text-center">
                        <i class="material-icons-outlined fs-3 d-block mb-2">info</i>
                        <p class="mb-0">Terjadi kesalahan saat memuat data statistik. Silakan coba lagi nanti.</p>
                    </div>
                `;
            });

        // Load latest tenders
        fetch(ensureHttps('{{ route("landing.latest-tenders") }}'), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('latest-tenders-container');
                if (data.error) {
                    container.innerHTML = `
                        <div class="alert alert-info text-center">
                            <i class="material-icons-outlined fs-3 d-block mb-2">info</i>
                            <p class="mb-0">Sedang memperbarui data tender. Silakan coba lagi nanti.</p>
                        </div>
                    `;
                } else if (data.length === 0) {
                    container.innerHTML = `
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="material-icons-outlined text-muted" style="font-size: 64px;">search_off</i>
                            </div>
                            <h5 class="text-muted">Belum ada data tender</h5>
                            <p class="text-muted">Data tender akan ditampilkan di sini setelah tersedia.</p>
                        </div>
                    `;
                } else {
                    let html = '<div class="row g-4">';
                    data.forEach(tender => {
                        const logoHtml = tender.lpse && tender.lpse.logo_path
                            ? `<img src="${tender.lpse.logo_path}" alt="${tender.lpse.nama_lpse}" class="rounded" width="50" height="50" style="object-fit: contain;">`
                            : `<div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;"><i class="material-icons-outlined text-secondary">business</i></div>`;

                        const statusClass = tender.tahap_tender === 'Pengumuman Pemenang'
                            ? 'success'
                            : (tender.tahap_tender === 'Evaluasi' ? 'warning' : 'primary');

                        html += `
                            <div class="col-lg-4 col-md-6">
                                <div class="tender-card card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="me-3">
                                                ${logoHtml}
                                            </div>
                                            <div>
                                                <h6 class="mb-0 text-truncate" style="max-width: 200px;">${tender.lpse ? tender.lpse.nama_lpse : 'LPSE'}</h6>
                                                <small class="text-muted">${tender.tahun_anggaran || new Date().getFullYear()}</small>
                                            </div>
                                        </div>
                                        <h5 class="card-title mb-3" style="height: 50px; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">${tender.nama_paket}</h5>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between mb-1">
                                                <span class="text-muted">Nilai Pagu:</span>
                                                <span class="fw-bold">Rp ${new Intl.NumberFormat().format(tender.pagu)}</span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span class="text-muted">Status:</span>
                                                <span class="badge bg-${statusClass}">${tender.tahap_tender}</span>
                                            </div>
                                        </div>
                                        <a href="/tender-public/${tender.kode_tender}" class="btn btn-sm btn-outline-primary w-100">Lihat Detail</a>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    container.innerHTML = html;
                }
            })
            .catch(error => {
                console.error('Error loading latest tenders:', error);
                document.getElementById('latest-tenders-container').innerHTML = `
                    <div class="alert alert-info text-center">
                        <i class="material-icons-outlined fs-3 d-block mb-2">info</i>
                        <p class="mb-0">Terjadi kesalahan saat memuat data tender. Silakan coba lagi nanti.</p>
                    </div>
                `;
            });

        // Load latest SIRUP
        fetch(ensureHttps('{{ route("landing.latest-sirup") }}'), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('latest-sirup-container');
                if (data.error) {
                    container.innerHTML = `
                        <div class="alert alert-info text-center">
                            <i class="material-icons-outlined fs-3 d-block mb-2">info</i>
                            <p class="mb-0">Sedang memperbarui data SIRUP. Silakan coba lagi nanti.</p>
                        </div>
                    `;
                } else if (data.length === 0) {
                    container.innerHTML = `
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="material-icons-outlined text-muted" style="font-size: 64px;">search_off</i>
                            </div>
                            <h5 class="text-muted">Belum ada data SIRUP</h5>
                            <p class="text-muted">Data SIRUP akan ditampilkan di sini setelah tersedia.</p>
                        </div>
                    `;
                } else {
                    let html = '<div class="row g-4">';
                    data.forEach(sirup => {
                        html += `
                            <div class="col-lg-4 col-md-6">
                                <div class="tender-card card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="me-3">
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                    <i class="material-icons-outlined text-secondary">description</i>
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 text-truncate" style="max-width: 200px;">${sirup.nama_klpd || 'KLPD'}</h6>
                                                <small class="text-muted">${sirup.tahun_anggaran}</small>
                                            </div>
                                        </div>
                                        <h5 class="card-title mb-3" style="height: 50px; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">${sirup.nama_paket}</h5>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between mb-1">
                                                <span class="text-muted">Nilai Pagu:</span>
                                                <span class="fw-bold">Rp ${new Intl.NumberFormat().format(sirup.total_pagu)}</span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span class="text-muted">Metode:</span>
                                                <span class="badge bg-info">${sirup.metode_pemilihan}</span>
                                            </div>
                                        </div>
                                        <a href="/sirup-public/${sirup.kode_rup}" class="btn btn-sm btn-outline-primary w-100">Lihat Detail</a>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    container.innerHTML = html;
                }
            })
            .catch(error => {
                console.error('Error loading latest SIRUP:', error);
                document.getElementById('latest-sirup-container').innerHTML = `
                    <div class="alert alert-info text-center">
                        <i class="material-icons-outlined fs-3 d-block mb-2">info</i>
                        <p class="mb-0">Terjadi kesalahan saat memuat data SIRUP. Silakan coba lagi nanti.</p>
                    </div>
                `;
            });
    });

    // Function to initialize counter animation
    function initCounterAnimation() {
        const statNumbers = document.querySelectorAll('.stat-number');

        const animateCounter = (el, target) => {
            let current = 0;
            const increment = target / 100;
            const duration = 2000; // 2 seconds
            const step = Math.ceil(duration / 100);

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    clearInterval(timer);
                    el.textContent = new Intl.NumberFormat().format(target);
                } else {
                    el.textContent = new Intl.NumberFormat().format(Math.floor(current));
                }
            }, step);
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const target = parseInt(entry.target.getAttribute('data-count'));
                    animateCounter(entry.target, target);
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        statNumbers.forEach(stat => {
            observer.observe(stat);
        });
    }
</script>
@endsection
