@extends('landing.layouts.app')

@section('title', "Detail SIRUP - {$sirup->nama_paket}")

@php
$klpdNama = $sirup->nama_klpd ?? 'KLPD';
$paguFormatted = number_format($sirup->total_pagu ?? 0, 0, ',', '.');
$tahunAnggaran = $sirup->tahun_anggaran ?? date('Y');
$metode = $sirup->metode_pemilihan ?? 'pengadaan';
@endphp

@section('meta_description', "Informasi detail SIRUP {$sirup->nama_paket} dari {$klpdNama} tahun anggaran {$tahunAnggaran} dengan nilai pagu Rp {$paguFormatted}. <PERSON><PERSON>, lokasi, dan informasi SIRUP lainnya.")

@section('meta_keywords', "sirup {$sirup->nama_paket}, {$klpdNama}, sirup lkpp, rencana umum pengadaan, {$metode}, pengadaan {$tahunAnggaran}")

@section('css')
<style>
    .sirup-header {
        background: linear-gradient(135deg, #0dcaf0 0%, #0aa2c0 100%);
        color: white;
        border-radius: 0.5rem 0.5rem 0 0;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .sirup-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 150px;
        height: 150px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50% 0 0 0;
        pointer-events: none;
    }

    .sirup-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        line-height: 1.3;
    }

    .sirup-meta {
        display: flex;
        align-items: center;
        margin-top: 1rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .sirup-meta-item {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.4rem 0.8rem;
        border-radius: 50px;
        font-size: 0.85rem;
    }

    .sirup-meta-item i {
        font-size: 1rem;
        margin-right: 0.4rem;
    }

    .info-card {
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        background: white;
        margin-bottom: 1.5rem;
        border: none;
        overflow: hidden;
    }

    .info-card-header {
        background: #f8f9fa;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .info-card-header i {
        margin-right: 0.5rem;
        color: #0dcaf0;
    }

    .info-card-body {
        padding: 1.5rem;
    }

    .info-item {
        margin-bottom: 1.25rem;
        display: flex;
        flex-direction: column;
    }

    .info-item:last-child {
        margin-bottom: 0;
    }

    .info-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-weight: 500;
    }

    .info-value.highlight {
        font-size: 1.25rem;
        font-weight: 700;
        color: #0dcaf0;
    }

    .cta-section {
        background: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        text-align: center;
        margin-top: 2rem;
    }

    .lpse-logo {
        max-height: 60px;
        max-width: 120px;
        object-fit: contain;
    }

    .badge-sirup {
        display: inline-block;
        padding: 0.35rem 0.65rem;
        font-size: 0.85rem;
        font-weight: 600;
        border-radius: 50px;
        background-color: rgba(13, 202, 240, 0.1);
        color: #0dcaf0;
    }

    .location-list {
        list-style-type: none;
        padding-left: 0;
        margin-bottom: 0;
    }

    .location-list li {
        position: relative;
        padding-left: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .location-list li:last-child {
        margin-bottom: 0;
    }

    .location-list li::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0.5rem;
        width: 0.5rem;
        height: 0.5rem;
        border-radius: 50%;
        background-color: #0dcaf0;
    }

    .uraian-pekerjaan {
        line-height: 1.6;
    }

    @media (max-width: 768px) {
        .sirup-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .sirup-meta-item {
            width: 100%;
        }
    }
</style>
@endsection

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <!-- Alert Info -->
            <div class="alert alert-info mb-4">
                <div class="d-flex align-items-center">
                    <i class="material-icons-outlined me-3">info</i>
                    <div>
                        <strong>Informasi Terbatas</strong>
                        <p class="mb-0">Anda sedang melihat informasi terbatas. <a href="{{ route('login') }}" class="alert-link">Login</a> atau <a href="{{ route('register') }}" class="alert-link">Daftar</a> untuk melihat detail lengkap dan fitur lainnya.</p>
                    </div>
                </div>
            </div>

            <!-- SIRUP Header -->
            <div class="sirup-header mb-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h1 class="sirup-title">{{ $sirup->nama_paket }}</h1>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-light text-dark me-2">{{ $sirup->kode_rup }}</span>
                            <span class="badge-sirup">{{ $sirup->metode_pemilihan ?? 'Metode tidak tersedia' }}</span>
                        </div>
                    </div>
                    @if(isset($sirup->lpse) && $sirup->lpse && $sirup->lpse->logo_path)
                    <div class="d-none d-md-block">
                        <img src="{{ asset(str_replace('http://proyeklpse.test', '', $sirup->lpse->logo_path)) }}"
                            alt="{{ $sirup->lpse->nama_lpse }}" class="lpse-logo">
                    </div>
                    @endif
                </div>

                <div class="sirup-meta">
                    <div class="sirup-meta-item">
                        <i class="material-icons-outlined">business</i>
                        <span>{{ $sirup->nama_klpd ?? 'KLPD Tidak tersedia' }}</span>
                    </div>
                    <div class="sirup-meta-item">
                        <i class="material-icons-outlined">payments</i>
                        <span>Rp {{ number_format($sirup->total_pagu ?? 0, 0, ',', '.') }}</span>
                    </div>
                    <div class="sirup-meta-item">
                        <i class="material-icons-outlined">calendar_today</i>
                        <span>{{ $sirup->tahun_anggaran ?? 'Tahun tidak tersedia' }}</span>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Kolom Kiri -->
                <div class="col-lg-4 mb-4 mb-lg-0">
                    <!-- Informasi SIRUP -->
                    <div class="info-card">
                        <div class="info-card-header">
                            <i class="material-icons-outlined">info</i>
                            Informasi SIRUP
                        </div>
                        <div class="info-card-body">
                            <div class="info-item">
                                <div class="info-label">Kode RUP</div>
                                <div class="info-value">{{ $sirup->kode_rup }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Satuan Kerja</div>
                                <div class="info-value">{{ $sirup->satuan_kerja ?? 'Tidak tersedia' }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Nama KLPD</div>
                                <div class="info-value">{{ $sirup->nama_klpd ?? 'Tidak tersedia' }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Tahun Anggaran</div>
                                <div class="info-value">{{ $sirup->tahun_anggaran ?? 'Tidak tersedia' }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Nilai Pagu</div>
                                <div class="info-value highlight">Rp {{ number_format($sirup->total_pagu ?? 0, 0, ',', '.') }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Metode Pemilihan</div>
                                <div class="info-value">{{ $sirup->metode_pemilihan ?? 'Tidak tersedia' }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Jenis Pengadaan</div>
                                <div class="info-value">
                                    @if($sirup->jenis_pengadaan === null)
                                        Tidak tersedia
                                    @elseif(is_string($sirup->jenis_pengadaan))
                                        {{ $sirup->jenis_pengadaan }}
                                    @elseif(is_array($sirup->jenis_pengadaan) || is_object($sirup->jenis_pengadaan))
                                        @php
                                            $jenisPengadaanArray = (array) $sirup->jenis_pengadaan;
                                            $jenisList = [];

                                            foreach ($jenisPengadaanArray as $jenis) {
                                                if (is_string($jenis)) {
                                                    $jenisList[] = $jenis;
                                                } elseif (is_array($jenis) || is_object($jenis)) {
                                                    $jenisObj = (object) $jenis;
                                                    if (isset($jenisObj->nama)) {
                                                        $jenisList[] = $jenisObj->nama;
                                                    }
                                                }
                                            }

                                            echo !empty($jenisList) ? implode(', ', $jenisList) : 'Tidak tersedia';
                                        @endphp
                                    @else
                                        Format tidak dikenali
                                    @endif
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Tanggal Diumumkan</div>
                                <div class="info-value">{{ $sirup->tanggal_paket_diumumkan ? date('d M Y', strtotime($sirup->tanggal_paket_diumumkan)) : 'Tidak tersedia' }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- CTA Section -->
                    <div class="cta-section">
                        <h5 class="mb-3">Ingin melihat detail lengkap?</h5>
                        <p class="text-muted mb-3">Login atau daftar untuk melihat informasi SIRUP lengkap, jadwal, dan dokumen terkait.</p>
                        <div class="d-grid gap-2">
                            <a href="{{ route('login') }}" class="btn btn-info text-white">Login</a>
                            <a href="{{ route('register') }}" class="btn btn-outline-info">Daftar Sekarang</a>
                        </div>
                    </div>
                </div>

                <!-- Kolom Kanan -->
                <div class="col-lg-8">
                    <!-- Uraian Pekerjaan -->
                    <div class="info-card mb-4">
                        <div class="info-card-header">
                            <i class="material-icons-outlined">description</i>
                            Uraian Pekerjaan
                        </div>
                        <div class="info-card-body">
                            @if($sirup->uraian_pekerjaan === null)
                                <div class="text-center py-4">
                                    <i class="material-icons-outlined" style="font-size: 48px; color: #dee2e6;">description</i>
                                    <p class="mt-3 text-muted">Uraian pekerjaan lengkap tersedia setelah login</p>
                                </div>
                            @elseif(is_string($sirup->uraian_pekerjaan))
                                <div class="uraian-pekerjaan">
                                    {!! $sirup->uraian_pekerjaan !!}
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="material-icons-outlined" style="font-size: 48px; color: #dee2e6;">description</i>
                                    <p class="mt-3 text-muted">Uraian pekerjaan lengkap tersedia setelah login</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Lokasi Pekerjaan -->
                    <div class="info-card mb-4">
                        <div class="info-card-header">
                            <i class="material-icons-outlined">place</i>
                            Lokasi Pekerjaan
                        </div>
                        <div class="info-card-body">
                            @if($sirup->lokasi_pekerjaan === null)
                                <div class="text-center py-4">
                                    <i class="material-icons-outlined" style="font-size: 48px; color: #dee2e6;">place</i>
                                    <p class="mt-3 text-muted">Lokasi pekerjaan lengkap tersedia setelah login</p>
                                </div>
                            @elseif(is_string($sirup->lokasi_pekerjaan))
                                <ul class="location-list">
                                    <li>{{ $sirup->lokasi_pekerjaan }}</li>
                                </ul>
                            @elseif(is_array($sirup->lokasi_pekerjaan) || is_object($sirup->lokasi_pekerjaan))
                                @php
                                    $lokasiArray = (array) $sirup->lokasi_pekerjaan;
                                    $hasValidLocations = false;
                                @endphp

                                <ul class="location-list">
                                    @foreach($lokasiArray as $lokasi)
                                        @if(is_array($lokasi) || is_object($lokasi))
                                            @php
                                                $lokasiObj = (object) $lokasi;
                                                $hasValidLocations = true;
                                                $lokasiText = [];

                                                if(isset($lokasiObj->provinsi) && !empty($lokasiObj->provinsi)) {
                                                    $lokasiText[] = $lokasiObj->provinsi;
                                                }

                                                if(isset($lokasiObj->kabupaten_kota) && !empty($lokasiObj->kabupaten_kota)) {
                                                    $lokasiText[] = $lokasiObj->kabupaten_kota;
                                                }

                                                if(isset($lokasiObj->detail_lokasi) && !empty($lokasiObj->detail_lokasi)) {
                                                    $lokasiText[] = $lokasiObj->detail_lokasi;
                                                }

                                                $lokasiDisplay = implode(', ', $lokasiText);
                                            @endphp

                                            @if(!empty($lokasiDisplay))
                                                <li>{{ $lokasiDisplay }}</li>
                                            @endif
                                        @elseif(is_string($lokasi))
                                            @php $hasValidLocations = true; @endphp
                                            <li>{{ $lokasi }}</li>
                                        @endif
                                    @endforeach
                                </ul>

                                @if(!$hasValidLocations)
                                    <div class="text-center py-4">
                                        <i class="material-icons-outlined" style="font-size: 48px; color: #dee2e6;">place</i>
                                        <p class="mt-3 text-muted">Lokasi pekerjaan lengkap tersedia setelah login</p>
                                    </div>
                                @endif
                            @else
                                <div class="text-center py-4">
                                    <i class="material-icons-outlined" style="font-size: 48px; color: #dee2e6;">place</i>
                                    <p class="mt-3 text-muted">Lokasi pekerjaan lengkap tersedia setelah login</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Status Realisasi -->
                    <div class="info-card mb-4">
                        <div class="info-card-header">
                            <i class="material-icons-outlined">assignment_turned_in</i>
                            Status Realisasi
                        </div>
                        <div class="info-card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <div class="rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; background-color: rgba(13, 202, 240, 0.1);">
                                        <i class="material-icons-outlined" style="font-size: 24px; color: #0dcaf0;">hourglass_empty</i>
                                    </div>
                                </div>
                                <div>
                                    <h6 class="mb-1">Status Paket</h6>
                                    <p class="mb-0 text-muted">Login untuk melihat status paket (Belum Tayang/Sudah Tayang/Klik)</p>
                                </div>
                            </div>

                            <div class="alert alert-light border">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <i class="material-icons-outlined" style="font-size: 24px; color: #0dcaf0;">info</i>
                                    </div>
                                    <div>
                                        <h6 class="mb-2">Informasi Realisasi</h6>
                                        <p class="mb-0">Untuk melihat informasi realisasi paket seperti:</p>
                                        <ul class="mt-2 mb-0">
                                            <li>Status paket (Belum Tayang/Sudah Tayang/Klik)</li>
                                            <li>Data tender terkait jika paket sudah tayang</li>
                                            <li>Data e-katalog terkait jika paket sudah klik</li>
                                            <li>Pemenang tender dan nilai kontrak</li>
                                        </ul>
                                        <div class="mt-3">
                                            <a href="{{ route('login') }}" class="btn btn-sm btn-info text-white me-2">Login</a>
                                            <a href="{{ route('register') }}" class="btn btn-sm btn-outline-info">Daftar</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-center mt-4">
                <a href="{{ route('landing.index') }}" class="btn btn-outline-secondary">
                    <i class="material-icons-outlined me-1" style="font-size: 1rem;">arrow_back</i>
                    Kembali ke Beranda
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
