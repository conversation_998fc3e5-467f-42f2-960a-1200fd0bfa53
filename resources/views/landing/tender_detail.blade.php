@extends('landing.layouts.app')

@section('title', "Detail Tender - {$tender->nama_paket}")

@php
$lpseNama = isset($tender->lpse) && isset($tender->lpse->nama_lpse) ? $tender->lpse->nama_lpse : 'LPSE';
$paguFormatted = number_format($tender->pagu ?? 0, 0, ',', '.');
$kategori = $tender->kategori_peker<PERSON>an ?? 'pengadaan barang dan jasa';
$metode = $tender->metode_pemilihan ?? 'tender';
@endphp

@section('meta_description', "Informasi detail tender {$tender->nama_paket} dari {$lpseNama} dengan nilai pagu Rp {$paguFormatted}. <PERSON><PERSON> jadwal, syarat kualifika<PERSON>, dan informasi tender lainnya.")

@section('meta_keywords', "tender {$tender->nama_paket}, {$lpseNama}, tender pemerintah, pengadaan, {$kategori}, {$metode}")

@section('css')
<style>
    .tender-header {
        background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
        color: white;
        border-radius: 0.5rem 0.5rem 0 0;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .tender-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 150px;
        height: 150px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50% 0 0 0;
        pointer-events: none;
    }

    .tender-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        line-height: 1.3;
    }

    .tender-meta {
        display: flex;
        align-items: center;
        margin-top: 1rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .tender-meta-item {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.4rem 0.8rem;
        border-radius: 50px;
        font-size: 0.85rem;
    }

    .tender-meta-item i {
        font-size: 1rem;
        margin-right: 0.4rem;
    }

    .info-card {
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        background: white;
        margin-bottom: 1.5rem;
        border: none;
        overflow: hidden;
    }

    .info-card-header {
        background: #f8f9fa;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .info-card-header i {
        margin-right: 0.5rem;
        color: #0d6efd;
    }

    .info-card-body {
        padding: 1.5rem;
    }

    .info-item {
        margin-bottom: 1.25rem;
        display: flex;
        flex-direction: column;
    }

    .info-item:last-child {
        margin-bottom: 0;
    }

    .info-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-weight: 500;
    }

    .info-value.highlight {
        font-size: 1.25rem;
        font-weight: 700;
        color: #0d6efd;
    }

    .timeline {
        position: relative;
        padding-left: 2rem;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 0.5rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }

    .timeline-item {
        position: relative;
        padding-bottom: 1.5rem;
    }

    .timeline-item:last-child {
        padding-bottom: 0;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: -2rem;
        top: 0.25rem;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        background: #0d6efd;
        border: 2px solid white;
    }

    .timeline-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .timeline-date {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }

    .cta-section {
        background: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        text-align: center;
        margin-top: 2rem;
    }

    .lpse-logo {
        max-height: 60px;
        max-width: 120px;
        object-fit: contain;
    }

    .status-badge {
        display: inline-block;
        padding: 0.35rem 0.65rem;
        font-size: 0.85rem;
        font-weight: 600;
        border-radius: 50px;
    }

    .status-badge.active {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }

    .status-badge.pending {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .status-badge.closed {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }

    .table-jadwal th {
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .table-jadwal td, .table-jadwal th {
        padding: 0.75rem 1rem;
        vertical-align: middle;
    }

    .table-jadwal tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    @media (max-width: 768px) {
        .tender-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .tender-meta-item {
            width: 100%;
        }
    }
</style>
@endsection

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <!-- Alert Info -->
            <div class="alert alert-info mb-4">
                <div class="d-flex align-items-center">
                    <i class="material-icons-outlined me-3">info</i>
                    <div>
                        <strong>Informasi Terbatas</strong>
                        <p class="mb-0">Anda sedang melihat informasi terbatas. <a href="{{ route('login') }}" class="alert-link">Login</a> atau <a href="{{ route('register') }}" class="alert-link">Daftar</a> untuk melihat detail lengkap dan fitur lainnya.</p>
                    </div>
                </div>
            </div>

            <!-- Tender Header -->
            <div class="tender-header mb-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h1 class="tender-title">{{ $tender->nama_paket }}</h1>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-light text-dark me-2">{{ $tender->kode_tender }}</span>
                            @php
                                $statusClass = 'pending';
                                if ($tender->status_tender == 'Aktif' && $tender->tahap_tender == 'Tender Sudah Selesai') {
                                    $statusClass = 'active';
                                } elseif ($tender->status_tender == 'Ditutup') {
                                    $statusClass = 'closed';
                                }
                            @endphp
                            <span class="status-badge {{ $statusClass }}">{{ $tender->tahap_tender }}</span>
                        </div>
                    </div>
                    @if($tender->lpse && $tender->lpse->logo_path)
                    <div class="d-none d-md-block">
                        <img src="{{ asset(str_replace('http://proyeklpse.test', '', $tender->lpse->logo_path)) }}"
                            alt="{{ $tender->lpse->nama_lpse }}" class="lpse-logo">
                    </div>
                    @endif
                </div>

                <div class="tender-meta">
                    <div class="tender-meta-item">
                        <i class="material-icons-outlined">business</i>
                        <span>{{ $tender->lpse->nama_lpse ?? 'LPSE Tidak tersedia' }}</span>
                    </div>
                    <div class="tender-meta-item">
                        <i class="material-icons-outlined">payments</i>
                        <span>Rp {{ number_format($tender->pagu ?? 0, 0, ',', '.') }}</span>
                    </div>
                    <div class="tender-meta-item">
                        <i class="material-icons-outlined">calendar_today</i>
                        <span>
                            @php
                                $jadwalAwal = $tender->jadwal->sortBy('step')->first();
                                $jadwalAkhir = $tender->jadwal->sortByDesc('step')->first();
                                $tanggalAwal = $jadwalAwal ? date('d M Y', strtotime($jadwalAwal->tanggal_mulai)) : 'Tidak tersedia';
                                $tanggalAkhir = $jadwalAkhir ? date('d M Y', strtotime($jadwalAkhir->tanggal_selesai ?? $jadwalAkhir->tanggal_mulai)) : '';
                            @endphp
                            {{ $tanggalAwal }}
                            @if($tanggalAkhir)
                                - {{ $tanggalAkhir }}
                            @endif
                        </span>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Kolom Kiri -->
                <div class="col-lg-4 mb-4 mb-lg-0">
                    <!-- Informasi Tender -->
                    <div class="info-card">
                        <div class="info-card-header">
                            <i class="material-icons-outlined">info</i>
                            Informasi Tender
                        </div>
                        <div class="info-card-body">
                            <div class="info-item">
                                <div class="info-label">Kode Tender</div>
                                <div class="info-value">{{ $tender->kode_tender }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Kategori</div>
                                <div class="info-value">{{ $tender->kategori_pekerjaan ?? 'Tidak tersedia' }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Metode Pemilihan</div>
                                <div class="info-value">{{ $tender->metode_pemilihan ?? 'Tidak tersedia' }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Nilai Pagu</div>
                                <div class="info-value highlight">Rp {{ number_format($tender->pagu ?? 0, 0, ',', '.') }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Nilai HPS</div>
                                <div class="info-value">Rp {{ number_format($tender->hps ?? 0, 0, ',', '.') }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Tanggal Tayang</div>
                                <div class="info-value">{{ $tender->tanggal_paket_tayang ? date('d M Y', strtotime($tender->tanggal_paket_tayang)) : 'Tidak tersedia' }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Status Tender</div>
                                <div class="info-value">
                                    <span class="status-badge {{ $statusClass }}">{{ $tender->tahap_tender }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CTA Section -->
                    <div class="cta-section">
                        <h5 class="mb-3">Ingin melihat detail lengkap?</h5>
                        <p class="text-muted mb-3">Login atau daftar untuk melihat informasi tender lengkap, peserta, dan dokumen tender.</p>
                        <div class="d-grid gap-2">
                            <a href="{{ route('login') }}" class="btn btn-primary">Login</a>
                            <a href="{{ route('register') }}" class="btn btn-outline-primary">Daftar Sekarang</a>
                        </div>
                    </div>
                </div>

                <!-- Kolom Kanan -->
                <div class="col-lg-8">

                    <!-- Syarat Kualifikasi -->
                    <div class="info-card">
                        <div class="info-card-header">
                            <i class="material-icons-outlined">assignment</i>
                            Syarat Kualifikasi
                        </div>
                        <div class="info-card-body">
                            @if($tender->syarat_kualifikasi)
                                <div class="syarat-kualifikasi">
                                    {!! $tender->syarat_kualifikasi !!}
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="material-icons-outlined" style="font-size: 48px; color: #dee2e6;">description</i>
                                    <p class="mt-3 text-muted">Syarat kualifikasi lengkap tersedia setelah login</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-center mt-4">
                <a href="{{ route('landing.index') }}" class="btn btn-outline-secondary">
                    <i class="material-icons-outlined me-1" style="font-size: 1rem;">arrow_back</i>
                    Kembali ke Beranda
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
