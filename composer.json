{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "akaunting/laravel-apexcharts": "^3.1", "amphp/amp": "^3.0", "amphp/parallel": "^2.3", "bacon/bacon-qr-code": "^2.0", "barryvdh/laravel-dompdf": "^3.1", "guzzlehttp/guzzle": "^7.9", "http-interop/http-factory-guzzle": "^1.2", "laravel-notification-channels/telegram": "^5.0", "laravel/fortify": "^1.24", "laravel/framework": "^11.9", "laravel/sanctum": "^4.0", "laravel/scout": "^10.11", "laravel/telescope": "^5.4", "laravel/tinker": "^2.9", "laravel/ui": "^4.3", "meilisearch/meilisearch-php": "^1.9", "spatie/async": "^1.6", "spatie/crawler": "^8.2", "symfony/dom-crawler": "^7.1"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/NumberHelper.php", "app/Helpers/NotificationHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}