# Konfigurasi Cloudflare untuk TenderID

Dokumen ini menjelaskan cara mengkonfigurasi aplikasi TenderID untuk bekerja dengan Cloudflare sebagai CDN dan proxy.

## Perubahan yang Telah Dilakukan

1. **Konfigurasi Trusted Proxies**
   - Semua proxy dipercaya (`'*'`) untuk mendukung Cloudflare
   - Header yang dipercaya termasuk semua header X-Forwarded-*

2. **Deteksi IP Asli Pengunjung**
   - Kelas `DeviceDetector` telah dimodifikasi untuk mendapatkan IP asli dari header Cloudflare
   - Prioritas header: CF-Connecting-IP > X-Forwarded-For > True-Client-IP > IP standar

3. **Pencatatan Header Cloudflare**
   - Header Cloudflare seperti CF-RAY, CF-Connecting-IP, CF-IPCountry, dll. dicatat dalam `additional_info`
   - Informasi ini berguna untuk debugging dan analisis keamanan

## Konfigurasi Cloudflare yang Direkomendasikan

### 1. Pengaturan SSL/TLS

- **Mode SSL/TLS**: Full (strict)
- **Minimum TLS Version**: TLS 1.2
- **Opportunistic Encryption**: On
- **TLS 1.3**: On
- **Automatic HTTPS Rewrites**: On

### 2. Pengaturan Firewall

- **Security Level**: Medium
- **Bot Fight Mode**: On
- **Challenge Passage**: 30 Minutes
- **Browser Integrity Check**: On

### 3. Pengaturan Cache

- **Cache Level**: Standard
- **Browser Cache TTL**: 4 hours
- **Always Online**: On
- **Development Mode**: Off (kecuali saat development)

### 4. Pengaturan Page Rules

Tambahkan page rule berikut:

1. **Bypass Cache untuk Admin Area**
   - URL Pattern: `*tenderid.com/admin*`
   - Setting: Cache Level: Bypass

2. **Bypass Cache untuk API**
   - URL Pattern: `*tenderid.com/api*`
   - Setting: Cache Level: Bypass

3. **Cache Statis untuk Assets**
   - URL Pattern: `*tenderid.com/assets*`
   - Setting: Cache Level: Cache Everything, Edge Cache TTL: 1 week

## Konfigurasi .env yang Direkomendasikan

Tambahkan konfigurasi berikut ke file `.env`:

```
# Cloudflare Configuration
CLOUDFLARE_ENABLED=true
TRUSTED_PROXIES=*
```

## Pengujian Konfigurasi

Untuk memastikan konfigurasi berfungsi dengan benar:

1. **Verifikasi IP Pengunjung**
   - Kunjungi halaman statistik akses
   - Pastikan IP yang tercatat adalah IP asli pengunjung, bukan IP Cloudflare

2. **Verifikasi Header Cloudflare**
   - Periksa log akses untuk memastikan header Cloudflare tercatat dengan benar
   - Header yang harus ada: CF-Connecting-IP, CF-RAY, CF-IPCountry

3. **Verifikasi Geolokasi**
   - Pastikan informasi negara dan kota yang tercatat akurat
   - Bandingkan dengan informasi CF-IPCountry dari Cloudflare

## Troubleshooting

### IP Pengunjung Tidak Terdeteksi dengan Benar

Jika IP pengunjung masih terdeteksi sebagai IP Cloudflare:

1. Pastikan konfigurasi trusted proxies sudah benar di `bootstrap/app.php`
2. Periksa apakah Cloudflare mengirimkan header CF-Connecting-IP
3. Restart aplikasi setelah mengubah konfigurasi

### Header Cloudflare Tidak Tercatat

Jika header Cloudflare tidak tercatat dalam log:

1. Pastikan Cloudflare benar-benar diaktifkan dan berfungsi sebagai proxy
2. Periksa apakah ada firewall atau middleware yang memblokir header
3. Verifikasi bahwa Cloudflare mengirimkan header yang diharapkan

### Geolokasi Tidak Akurat

Jika informasi geolokasi tidak akurat:

1. Gunakan nilai CF-IPCountry dari Cloudflare sebagai sumber utama
2. Pertimbangkan untuk menggunakan layanan geolokasi yang lebih akurat
3. Perhatikan bahwa pengguna mungkin menggunakan VPN yang menyembunyikan lokasi asli mereka

## Referensi

- [Dokumentasi Cloudflare tentang HTTP Headers](https://developers.cloudflare.com/fundamentals/get-started/reference/http-request-headers/)
- [Panduan Laravel untuk Trusted Proxies](https://laravel.com/docs/11.x/requests#configuring-trusted-proxies)
- [Cloudflare IP Ranges](https://www.cloudflare.com/ips/)
