<?php

namespace App\Console\Commands;

use App\Models\ListPerusahaanTender; // Pastikan ini diimpor
use App\Models\LPSE; // Pastikan ini diimpor
use App\Models\TenderUmumPublik; // Pastikan ini diimpor
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Symfony\Component\DomCrawler\Crawler;
use Illuminate\Support\Str;

class ScrapeTenderData extends Command
{
    // Nama dan deskripsi command
    protected $signature = 'scrape:tender {type} {year?}';
    protected $description = 'Scrape tender data based on type';


    public function handle()
    {
        $processId = (string) Str::uuid();
        $type = $this->argument('type');
        $year = $this->argument('year') ?? date('Y');
        Log::info("Starting scraping process for type: {$type} -- Process ID => {$processId}");

        switch ($type) {
            case 'tahap-tender-umum':
                $this->scrapeTahapTenderUmum();
                break;
            case 'update-tahap-tender-umum':
                $this->updateTahapTenderUmum();
                break;
            case 'kualifikasiusaha-tender-umum':
                $this->scrapeKualifikasiUsahaTenderUmum();
                break;
            case 'data-peserta-tender':
                $this->scrapeDataPesertaTender();
                break;
            case 'data-pemenang-tender':
                $this->scrapeDataPemenangTender();
                break;
            case 'fetch-all-tender-umum-publik':
                $this->fetchAllTenderUmumPublik($year);
                break;
            case 'inaproc-tahap-tender-umum':
                $this->scrapeInaprocTahapTenderUmum();
                break;
            case 'inaproc-update-tahap-tender-umum':
                $this->inaprocUpdateTahapTenderUmum();
                break;
            case 'nodejs-tahap-tender-umum':
                $this->scrapeTahapanTenderUmumNodeJs();
                break;
            default:
                Log::error("Invalid type specified: {$type}");
                $this->error('Invalid type specified.');
        }

        Log::info("Scraping process completed for type: {$type} -- Process ID => {$processId}");
        $this->info('Scraping process completed.');
    }

    public function scrapeTahapTenderUmum()
    {
        Log::channel('log_scrape_tahapan_tender')->info("Scraping tahap-tender-umum");

        $kd_lpse_groups = TenderUmumPublik::whereNull('tahap_tender')
            ->select('kd_lpse')
            ->groupBy('kd_lpse')
            ->pluck('kd_lpse')
            ->toArray();

        shuffle($kd_lpse_groups);
        $totalGroups = count($kd_lpse_groups);
        $progress = 0;

        foreach ($kd_lpse_groups as $kd_lpse) {
            $progress++;
            Log::channel('log_scrape_tahapan_tender')->info("Progress: {$progress}/{$totalGroups} (kd_lpse ke-{$progress} dari {$totalGroups})");

            $kode_tenders = TenderUmumPublik::where('kd_lpse', $kd_lpse)
                ->whereNull('tahap_tender')
                ->orderBy('tanggal_paket_tayang', 'desc')
                ->pluck('kode_tender')
                ->toArray();

            shuffle($kode_tenders);
            $totalTenders = count($kode_tenders);
            $tenderProgress = 0;
            $errorCount = 0;

            foreach ($kode_tenders as $kode_tender) {
                $tenderProgress++;
                Log::channel('log_scrape_tahapan_tender')->info("Processing kode_tender: {$tenderProgress}/{$totalTenders} for kd_lpse: {$kd_lpse} (kode_tender ke-{$tenderProgress} dari {$totalTenders})");

                $tender = TenderUmumPublik::find($kode_tender);
                if (!$tender) {
                    Log::channel('log_scrape_tahapan_tender')->info("Tender not found for kode_tender: {$kode_tender}");
                    continue;
                }

                $lpse = LPSE::where('kd_lpse', $kd_lpse)->first();
                if (!$lpse) {
                    Log::channel('log_scrape_tahapan_tender')->info("LPSE not found for kd_lpse: {$kd_lpse}");
                    continue;
                }

                $url = $lpse->url . '/lelang/' . $kode_tender . '/pengumumanlelang';
                Log::channel('log_scrape_tahapan_tender')->info("Data URL -> $url");

                $html = akses_http($url);
                if ($html === false) {
                    $errorCount++;
                    Log::channel('log_scrape_tahapan_tender')->error("Error fetching data from LPSE for kode_tender: {$kode_tender}");
                    if ($errorCount >= 2) {
                        Log::channel('log_scrape_tahapan_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                    continue;
                } else {
                    $errorCount = 0; // Reset error count if successful
                }

                try {
                    $crawler = new Crawler($html);
                    $kodeTender = $crawler->filter('th:contains("Kode Tender")')->siblings()->text();
                    $tahapTender = $crawler->filter('th:contains("Tahap Tender Saat Ini")')->siblings()->text();
                    $tahapTender = rapikan_str_tahap_tender($tahapTender);

                    if ($tender->tahap_tender != $tahapTender) {
                        Log::channel('log_scrape_tahapan_tender')->info("Updating data for kode_tender: {$kode_tender}");
                        Log::channel('log_scrape_tahapan_tender')->info("Old tahap_tender: {$tender->tahap_tender}, New tahap_tender: {$tahapTender}");
                        $tender->update([
                            'kode_tender' => $kodeTender,
                            'tahap_tender' => $tahapTender,
                        ]);
                        Log::channel('log_scrape_tahapan_tender')->info("Data {$kode_tender} BERHASIL dilakukan update");
                    } else {
                        Log::channel('log_scrape_tahapan_tender')->info("Data {$kode_tender} Sama, tidak perlu dilakukan update");
                    }

                    Log::channel('log_scrape_tahapan_tender')->info("Successfully scraped detail tender with kode_tender: {$kode_tender}");
                } catch (\Exception $e) {
                    if ($e->getMessage() === 'The current node list is empty.') {
                        Log::channel('log_scrape_tahapan_tender')->error("Error - The current node list is empty for {$kode_tender}. Skipping...");
                    } else {
                        Log::channel('log_scrape_tahapan_tender')->error("Error - {$e->getMessage()} occurred while scraping {$kode_tender}");
                    }
                    $errorCount++; // Increment error count on exception
                    if ($errorCount >= 2) {
                        Log::channel('log_scrape_tahapan_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                }
            }
        }
    }

    public function updateTahapTenderUmum()
    {
        Log::channel('update_tahapan_tender')->info("Scraping update-tahap-tender-umum");

        $kd_lpse_groups = TenderUmumPublik::whereNotNull('tahap_tender')
            ->where('tahap_tender', '!=', 'Tender Sudah Selesai')
            ->select('kd_lpse')
            ->groupBy('kd_lpse')
            ->pluck('kd_lpse')
            ->toArray();

        shuffle($kd_lpse_groups);
        $totalGroups = count($kd_lpse_groups);
        $groupProgress = 0;

        foreach ($kd_lpse_groups as $kd_lpse) {
            $groupProgress++;
            Log::channel('update_tahapan_tender')->info("Progress Group: {$groupProgress}/{$totalGroups} (kd_lpse ke-{$groupProgress} dari {$totalGroups})");

            $kode_tenders = TenderUmumPublik::where('kd_lpse', $kd_lpse)
                ->whereNotNull('tahap_tender')
                ->where('tahap_tender', '!=', 'Tender Sudah Selesai')
                ->orderBy('tanggal_paket_tayang', 'desc')
                ->pluck('kode_tender')
                ->toArray();

            shuffle($kode_tenders);
            $totalTenders = count($kode_tenders);
            $tenderProgress = 0;
            $errorCount = 0;

            foreach ($kode_tenders as $kode_tender) {
                $tenderProgress++;
                Log::channel('update_tahapan_tender')->info("Progress Tender: {$tenderProgress}/{$totalTenders} for kd_lpse: {$kd_lpse} (kode_tender ke-{$tenderProgress} dari {$totalTenders})");

                $tender = TenderUmumPublik::find($kode_tender);
                if (!$tender) {
                    Log::channel('update_tahapan_tender')->info("Tender not found for kode_tender: {$kode_tender}");
                    continue;
                }

                $lpse = LPSE::where('kd_lpse', $tender->kd_lpse)->first();
                if (!$lpse) {
                    Log::channel('update_tahapan_tender')->info("LPSE not found for kode_tender: {$kode_tender}");
                    continue;
                }

                //$url = 'https://tenderid.mcmtestapp.com/testlink?url='. $lpse->url . '/lelang/' . $kode_tender . '/pengumumanlelang';
                $url = $lpse->url . '/lelang/' . $kode_tender . '/pengumumanlelang';
                Log::channel('update_tahapan_tender')->info("Data URL -> $url");

                $html = akses_curl($url);
                if ($html === false) {
                    $errorCount++;
                    Log::channel('update_tahapan_tender')->error("Error fetching data from LPSE for kode_tender: {$kode_tender}");
                    if ($errorCount >= 2) {
                        Log::channel('update_tahapan_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                    continue;
                } else {
                    $errorCount = 0; // Reset error count if successful
                }

                try {
                    $crawler = new Crawler($html);
                    $kodeTender = $crawler->filter('th:contains("Kode Tender")')->siblings()->text();
                    $tahapTender = $crawler->filter('th:contains("Tahap Tender Saat Ini")')->siblings()->text();
                    $tahapTender = rapikan_str_tahap_tender($tahapTender);

                    if ($tender->tahap_tender != $tahapTender) {
                        Log::channel('update_tahapan_tender')->info("Updating data for kode_tender: {$kode_tender}");
                        Log::channel('update_tahapan_tender')->info("Old tahap_tender: {$tender->tahap_tender}, New tahap_tender: {$tahapTender}");
                        $tender->update([
                            'kode_tender' => $kodeTender,
                            'tahap_tender' => $tahapTender,
                        ]);
                        Log::channel('update_tahapan_tender')->info("Data {$kode_tender} BERHASIL dilakukan update");
                    } else {
                        Log::channel('update_tahapan_tender')->info("Data {$kode_tender} Sama, tidak perlu dilakukan update");
                    }

                    Log::channel('update_tahapan_tender')->info("Successfully scraped detail tender with kode_tender: {$kode_tender}");
                } catch (\Exception $e) {
                    if ($e->getMessage() === 'The current node list is empty.') {
                        Log::channel('update_tahapan_tender')->error("Error - The current node list is empty for {$kode_tender}. Skipping...");
                    } else {
                        Log::channel('update_tahapan_tender')->error("Error - {$e->getMessage()} occurred while scraping {$kode_tender}");
                    }
                    $errorCount++; // Increment error count on exception
                    if ($errorCount >= 2) {
                        Log::channel('update_tahapan_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                }
            }
        }
    }

    public function scrapeKualifikasiUsahaTenderUmum()
    {
        Log::channel('log_scrape_kualifikasi_usaha')->info("Scraping kualifikasiusaha-tender-umum");

        $kode_tenders = TenderUmumPublik::whereNull('kualifikasi_usaha')
            ->orderBy('created_at', 'desc')
            ->pluck('kode_tender')
            ->toArray();

        shuffle($kode_tenders);
        $totalTenders = count($kode_tenders);
        $progress = 0;
        $errorCount = 0;

        foreach ($kode_tenders as $kode_tender) {
            $progress++;
            Log::channel('log_scrape_kualifikasi_usaha')->info("Progress: {$progress}/{$totalTenders}");

            $tender = TenderUmumPublik::find($kode_tender);
            if (!$tender) {
                Log::channel('log_scrape_kualifikasi_usaha')->info("Tender not found for kode_tender: {$kode_tender}");
                continue;
            }

            $lpse = LPSE::where('kd_lpse', $tender->kd_lpse)->first();
            if (!$lpse) {
                Log::channel('log_scrape_kualifikasi_usaha')->info("LPSE not found for kode_tender: {$kode_tender}");
                continue;
            }

            $url = 'https://inaproc.id/tender/detail/' . $tender->kd_lpse . $kode_tender;
            Log::channel('log_scrape_kualifikasi_usaha')->info("Data URL -> $url");

            $html = akses_curl($url);
            if ($html === false) {
                $errorCount++;
                Log::channel('log_scrape_kualifikasi_usaha')->info("Error fetching data from LPSE for kode_tender: {$kode_tender}");
                if ($errorCount >= 2) {
                    Log::channel('log_scrape_kualifikasi_usaha')->info("Skipping kode_tender {$kode_tender} due to multiple errors");
                    break;
                }
                continue;
            } else {
                $errorCount = 0; // Reset error count if successful
            }

            try {
                $crawler = new Crawler($html);
                $kodeTender = $crawler->filter('td:contains("Kode Tender")')->siblings()->text();
                $kualifikasiUsaha = $crawler->filter('td:contains("Kualifikasi Usaha")')->siblings()->text();

                if ($tender->kualifikasi_usaha !== $kualifikasiUsaha) {
                    $tender->update([
                        'kode_tender' => $kodeTender,
                        'kualifikasi_usaha' => $kualifikasiUsaha,
                    ]);
                    Log::channel('log_scrape_kualifikasi_usaha')->info("Kualifikasi Usaha {$kode_tender} BERHASIL mendapatkan data kualifikasi");
                } else {
                    Log::channel('log_scrape_kualifikasi_usaha')->info("Kualifikasi Usaha {$kode_tender} Sama, tidak perlu dilakukan update");
                }

                Log::channel('log_scrape_kualifikasi_usaha')->info("Successfully scraped detail tender with kode_tender: {$kode_tender}");
            } catch (\Exception $e) {
                if ($e->getMessage() === 'The current node list is empty.') {
                    Log::channel('log_scrape_kualifikasi_usaha')->info("Error - The current node list is empty for {$kode_tender}. Skipping...");
                } else {
                    Log::channel('log_scrape_kualifikasi_usaha')->info("Error - {$e->getMessage()} occurred while scraping {$kode_tender}");
                }
                $errorCount++; // Increment error count on exception
                if ($errorCount >= 2) {
                    Log::channel('log_scrape_kualifikasi_usaha')->info("Skipping kode_tender {$kode_tender} due to multiple errors");
                    break;
                }
            }
        }
    }

    public function scrapeDataPesertaTender()
    {
        // Log::info("Scraping data-peserta-tender");

        $tahapTenders = [
            'Pembuktian Kualifikasi',
            'Pengumuman Prakualifikasi',
            'Pengumuman Pascakualifikasi',
            'Pembukaan dan Evaluasi Penawaran File II: Harga',
            'Tender Batal',
            'Seleksi Batal',
            'Tidak Ada Jadwal',
            'Kirim Persyaratan Kualifikasi',
            'Upload Dokumen Penawaran',
            'Download Dokumen Pengadaan',
            'Download Dokumen Pemilihan',
            'Download Dokumen Kualifikasi',
            'Pembukaan Dokumen Penawaran',
            'Klarifikasi dan Negosiasi Teknis dan Biaya',
            'Pembukaan dan Evaluasi Penawaran File I: Administrasi dan Teknis',
            'Evaluasi Dokumen Kualifikasi',
            'Masa Sanggah Prakualifikasi',
            'Penetapan Hasil Kualifikasi',
            'Pengumuman Hasil Prakualifikasi',
            'Penjelasan Dokumen Prakualifikasi',
            'Tender Belum Dilaksanakan',
        ];

        $kd_lpse_groups = TenderUmumPublik::whereNull('list_peserta')
            ->where('status_tender', 'Aktif')
            ->whereNotIn('tahap_tender', $tahapTenders)
            ->select('kd_lpse')
            ->groupBy('kd_lpse')
            ->pluck('kd_lpse')
            ->toArray();

        //shuffle($kd_lpse_groups);
        $totalGroups = count($kd_lpse_groups);
        $groupProgress = 0;

        foreach ($kd_lpse_groups as $kd_lpse) {
            $groupProgress++;
            Log::channel('log_scrape_peserta_tender')->info("Progress Group: {$groupProgress}/{$totalGroups} (kd_lpse ke-{$groupProgress} dari {$totalGroups})");

            $kode_tenders = TenderUmumPublik::where('kd_lpse', $kd_lpse)
                ->whereNull('list_peserta')
                ->where('status_tender', 'Aktif')
                ->whereNotIn('tahap_tender', $tahapTenders)
                ->orderBy('created_at', 'desc')
                ->pluck('kode_tender')
                ->toArray();

            //shuffle($kode_tenders);
            $totalTenders = count($kode_tenders);
            $tenderProgress = 0;
            $errorCount = 0;

            foreach ($kode_tenders as $kode_tender) {
                $tenderProgress++;
                Log::channel('log_scrape_peserta_tender')->info("Progress Tender: {$tenderProgress}/{$totalTenders} for kd_lpse: {$kd_lpse} (kode_tender ke-{$tenderProgress} dari {$totalTenders})");
                Log::channel('log_scrape_peserta_tender')->info("Starting to scrape peserta tender with kode_tender: {$kode_tender}");

                $tender = TenderUmumPublik::find($kode_tender);
                if (!$tender) {
                    Log::channel('log_scrape_peserta_tender')->warning("Tender not found for kode_tender: {$kode_tender}");
                    continue;
                }

                $lpse = LPSE::where('kd_lpse', $tender->kd_lpse)->first();
                if (!$lpse) {
                    Log::channel('log_scrape_peserta_tender')->warning("LPSE not found for kode_tender: {$kode_tender}");
                    continue;
                }

                $url = 'https://tenderid.duniapangangosyen.co.id/testlink?url=' . $lpse->url . '/lelang/' . $kode_tender . '/peserta';
                // $url = $lpse->url . '/lelang/' . $kode_tender . '/peserta';
                Log::channel('log_scrape_peserta_tender')->info("Data URL -> $url");

                $html = akses_curl($url);
                if ($html === false) {
                    $errorCount++;
                    Log::channel('log_scrape_peserta_tender')->error("Error fetching data from LPSE for kode_tender: {$kode_tender}");
                    if ($errorCount >= 2) {
                        Log::channel('log_scrape_peserta_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                    continue;
                } else {
                    $errorCount = 0; // Reset error count if successful
                }

                try {
                    $crawler = new Crawler($html);
                    $data = $crawler->filter('table.table tbody tr')->each(function ($node) {
                        return [
                            'nama_peserta' => trim($node->filter('td')->eq(1)->text()),
                            'npwp' => $node->filter('td')->eq(2)->text(),
                        ];
                    });

                    // if (empty($data)) {
                    //     // Try alternative URL
                    //     $url = $lpse->url . '/evaluasi/' . $kode_tender . '/hasil';
                    //     Log::channel('log_scrape_peserta_tender')->info("Retrying with alternative URL -> $url");

                    //     $html = akses_curl($url);
                    //     if ($html !== false) {
                    //         $crawler = new Crawler($html);
                    //         $data = $crawler->filter('table.table tbody tr')->each(function ($node) {
                    //             $text = trim($node->filter('td')->eq(1)->text());
                    //             $parts = explode('-', $text);

                    //             return [
                    //                 'nama_peserta' => trim($parts[0] ?? ''),
                    //                 'npwp' => trim($parts[1] ?? ''),
                    //             ];
                    //         });
                    //     }
                    // }

                    if (empty($data)) {
                        // Jika data kosong, set list_peserta menjadi NULL
                        $tender->list_peserta = null;
                        $tender->save();
                        Log::channel('log_scrape_peserta_tender')->info("No data found for kode_tender: {$kode_tender}. Set list_peserta to NULL.");
                    } else {
                        $existingNpwpList = json_decode($tender->list_peserta, true) ?? [];
                        $newNpwpList = [];
                        foreach ($data as $item) {
                            $existingEntry = ListPerusahaanTender::where('npwp', $item['npwp'])->first();
                            if (!$existingEntry) {
                                ListPerusahaanTender::create($item);
                                Log::channel('log_scrape_peserta_tender')->info("Saved participant with NPWP: {$item['npwp']}");
                            } else {
                                Log::channel('log_scrape_peserta_tender')->info("Participant with NPWP: {$item['npwp']} already exists. Skipping...");
                            }
                            if (!in_array($item['npwp'], $existingNpwpList)) {
                                $newNpwpList[] = $item['npwp'];
                            }
                        }
                        $updatedNpwpList = array_merge($existingNpwpList, $newNpwpList);
                        $tender->list_peserta = json_encode($updatedNpwpList);
                        $tender->save();
                    }

                    Log::channel('log_scrape_peserta_tender')->info("Successfully scraped detail tender with kode_tender: {$kode_tender}");
                } catch (\Exception $e) {
                    if ($e->getMessage() === 'The current node list is empty.') {
                        Log::channel('log_scrape_peserta_tender')->error("Error - The current node list is empty for {$kode_tender}. Skipping...");
                    } else {
                        Log::channel('log_scrape_peserta_tender')->error("Error - {$e->getMessage()} occurred while scraping {$kode_tender}");
                    }
                    $errorCount++; // Increment error count on exception
                    if ($errorCount >= 2) {
                        Log::channel('log_scrape_peserta_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                }
            }
        }
    }


    public function scrapeDataPemenangTender()
    {
        Log::channel('log_scrape_data_pemenang_tender')->info("Scraping data-pemenang-tender");

        $tahapTenders = [
            'Masa Sanggah atas Hasil Kelulusan Administrasi dan Teknis',
            'Pengumuman Prakualifikasi',
            'Pengumuman Pascakualifikasi',
            'Evaluasi Administrasi, Kualifikasi, Teknis, dan Harga',
            'Pembukaan dan Evaluasi Penawaran File II: Harga',
            'Tender Batal',
            'Seleksi Batal',
            'Upload Dokumen Penawaran',
            'Download Dokumen Pemilihan',
            'Pembukaan Dokumen Penawaran',
            'Klarifikasi dan Negosiasi Teknis dan Biaya',
            'Pembukaan dan Evaluasi Penawaran File I: Administrasi dan Teknis',
            'Evaluasi Dokumen Kualifikasi',
        ];

        $kd_lpse_groups = TenderUmumPublik::whereNull('pemenang')
            ->whereNotNull('list_peserta')
            ->where('status_tender', 'Aktif')
            ->whereNotIn('tahap_tender', $tahapTenders)
            ->select('kd_lpse')
            ->groupBy('kd_lpse')
            ->pluck('kd_lpse')
            ->toArray();

        shuffle($kd_lpse_groups);
        $totalGroups = count($kd_lpse_groups);
        $groupProgress = 0;

        foreach ($kd_lpse_groups as $kd_lpse) {
            $groupProgress++;
            Log::channel('log_scrape_data_pemenang_tender')->info("Progress Group: {$groupProgress}/{$totalGroups} (kd_lpse ke-{$groupProgress} dari {$totalGroups})");

            $kode_tenders = TenderUmumPublik::where('kd_lpse', $kd_lpse)
                ->whereNull('pemenang')
                ->whereNotNull('list_peserta')
                ->where('status_tender', 'Aktif')
                ->whereNotIn('tahap_tender', $tahapTenders)
                ->orderBy('created_at', 'desc')
                ->pluck('kode_tender')
                ->toArray();

            shuffle($kode_tenders);
            $totalTenders = count($kode_tenders);
            $tenderProgress = 0;
            $errorCount = 0;

            foreach ($kode_tenders as $kode_tender) {
                $tenderProgress++;
                Log::channel('log_scrape_data_pemenang_tender')->info("Progress Tender: {$tenderProgress}/{$totalTenders} for kd_lpse: {$kd_lpse} (kode_tender ke-{$tenderProgress} dari {$totalTenders})");

                $tender = TenderUmumPublik::find($kode_tender);
                if (!$tender) {
                    Log::channel('log_scrape_data_pemenang_tender')->info("Tender not found for kode_tender: {$kode_tender}");
                    continue;
                }

                $lpse = LPSE::where('kd_lpse', $tender->kd_lpse)->first();
                if (!$lpse) {
                    Log::channel('log_scrape_data_pemenang_tender')->info("LPSE not found for kd_lpse: {$tender->kd_lpse}");
                    continue;
                }

                $url = 'https://tenderid.mcmtestapp.com/testlink?url=' . $lpse->url . '/evaluasi/' . $kode_tender . '/pemenang';
                Log::channel('log_scrape_data_pemenang_tender')->info("Data URL: $url");

                $html = akses_curl($url);
                if ($html === false) {
                    $errorCount++;
                    Log::channel('log_scrape_data_pemenang_tender')->info("Error fetching data from LPSE for URL: $url");
                    if ($errorCount >= 2) {
                        Log::channel('log_scrape_data_pemenang_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                    continue;
                } else {
                    $errorCount = 0; // Reset error count if successful
                }

                try {
                    $crawler = new Crawler($html);
                    $pemenang = [];
                    $crawler->filter('table.table-sm')->eq(1)->filter('tr')->each(function ($node) use (&$pemenang) {
                        if ($node->filter('td')->count() > 5) {
                            $pemenang[] = [
                                'namaPemenang' => $node->filter('td')->eq(0)->text(),
                                'alamat' => $node->filter('td')->eq(1)->text(),
                                'npwp' => $node->filter('td')->eq(2)->text(),
                                'hargaPenawaran' => $node->filter('td')->eq(3)->text(),
                                'hargaTerkoreksi' => $node->filter('td')->eq(4)->text(),
                                'hargaNegosiasi' => $node->filter('td')->eq(5)->text(),
                            ];
                        }
                    });

                    $existingNpwpList = json_decode($tender->list_peserta, true) ?? [];
                    foreach ($pemenang as $item) {
                        $existingEntry = ListPerusahaanTender::where('npwp', $item['npwp'])->first();
                        if ($existingEntry) {
                            $existingEntry->update(['alamat' => $item['alamat']]);
                            Log::channel('log_scrape_data_pemenang_tender')->info("Updated address for participant with NPWP: {$item['npwp']}");
                        } else {
                            ListPerusahaanTender::create($item);
                            Log::channel('log_scrape_data_pemenang_tender')->info("Saved participant with NPWP: {$item['npwp']}");
                        }
                        $tender->pemenang = $item['npwp'];
                        $tender->harga_penawaran = str_rupiah_to_decimal($item['hargaPenawaran']);
                        $tender->harga_terkoreksi = str_rupiah_to_decimal($item['hargaTerkoreksi']);
                        $tender->save();
                        Log::channel('log_scrape_data_pemenang_tender')->info("Updated Pemenang for participant with nama: {$item['namaPemenang']} - Harga: {$item['hargaPenawaran']}");
                    }

                    Log::channel('log_scrape_data_pemenang_tender')->info("BERHASIL scraped Pemenang tender with kode_tender: {$kode_tender}");
                } catch (\Exception $e) {
                    if ($e->getMessage() === 'The current node list is empty.') {
                        Log::channel('log_scrape_data_pemenang_tender')->info("Error - The current node list is empty for {$kode_tender}. Skipping...");
                    } else {
                        Log::channel('log_scrape_data_pemenang_tender')->info("Error - {$e->getMessage()} occurred while scraping {$kode_tender}");
                    }
                    $errorCount++; // Increment error count on exception
                    if ($errorCount >= 2) {
                        Log::channel('log_scrape_data_pemenang_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                }
            }
        }
    }

    private function scrapeInaprocTahapTenderUmum()
    {
        Log::channel('log_inaproc_tahapan_tender')->info("Scraping inaproc-tahap-tender-umum");

        $kd_lpse_groups = TenderUmumPublik::whereNull('tahap_tender')
            ->select('kd_lpse')
            ->groupBy('kd_lpse')
            ->pluck('kd_lpse')
            ->toArray();

        shuffle($kd_lpse_groups);
        $totalGroups = count($kd_lpse_groups);
        $groupProgress = 0;

        foreach ($kd_lpse_groups as $kd_lpse) {
            $groupProgress++;
            Log::channel('log_inaproc_tahapan_tender')->info("Progress Group: {$groupProgress}/{$totalGroups} (kd_lpse ke-{$groupProgress} dari {$totalGroups})");

            $kode_tenders = TenderUmumPublik::where('kd_lpse', $kd_lpse)
                ->whereNull('tahap_tender')
                ->orderBy('tanggal_paket_tayang', 'desc')
                ->pluck('kode_tender')
                ->toArray();

            shuffle($kode_tenders);
            $totalTenders = count($kode_tenders);
            $tenderProgress = 0;
            $errorCount = 0;

            foreach ($kode_tenders as $kode_tender) {
                $tenderProgress++;
                Log::channel('log_inaproc_tahapan_tender')->info("Progress Tender: {$tenderProgress}/{$totalTenders} for kd_lpse: {$kd_lpse} (kode_tender ke-{$tenderProgress} dari {$totalTenders})");

                $tender = TenderUmumPublik::find($kode_tender);
                if (!$tender) {
                    Log::channel('log_inaproc_tahapan_tender')->info("Tender not found for kode_tender: {$kode_tender}");
                    continue;
                }

                $lpse = LPSE::where('kd_lpse', $kd_lpse)->first();
                if (!$lpse) {
                    Log::channel('log_inaproc_tahapan_tender')->info("LPSE not found for kd_lpse: {$kd_lpse}");
                    continue;
                }

                $url = "https://inaproc.id/tender?prov=&kab=&qualification=0&price=0&year=&kldi=0&repo=0&keyword={$kode_tender}";
                Log::channel('log_inaproc_tahapan_tender')->info("Data URL -> $url");
                $html = akses_http($url);

                if ($html === false) {
                    $errorCount++;
                    Log::channel('log_inaproc_tahapan_tender')->error("Error fetching data from LPSE for kode_tender: {$kode_tender}");
                    if ($errorCount >= 2) {
                        Log::channel('log_inaproc_tahapan_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                    continue;
                }

                $errorCount = 0;
                try {
                    $crawler = new Crawler($html);
                    $data = $crawler->filter('.ui.table.small.compact.table-list tbody tr')->each(function ($node) {
                        return [
                            'kualifikasiUsaha' => $node->filter('.label span.ui.label.default.small')->eq(1)->text(),
                            'tahapTender' => $node->filter('td')->eq(1)->filter('.ui.label.basic.small')->text(),
                        ];
                    });

                    foreach ($data as $item) {
                        $kodeTender = $kode_tender;
                        $tahapTender = $item['tahapTender'];
                        $tahapTender = rapikan_str_tahap_tender($tahapTender);
                        $tahapTender = samakan_sinonim_tahap($tahapTender);
                        $kualifikasiUsaha = $item['kualifikasiUsaha'];

                        Log::channel('log_inaproc_tahapan_tender')->info("Hasil Scrape: {$kode_tender} - Kualifikasi {$kualifikasiUsaha} - tahap {$tahapTender}");

                        if ($tender->tahap_tender != $tahapTender || $tender->kualifikasi_usaha != $kualifikasiUsaha) {
                            Log::channel('log_inaproc_tahapan_tender')->info("Updating data for kode_tender: {$kode_tender}");
                            Log::channel('log_inaproc_tahapan_tender')->info("Old tahap_tender: {$tender->tahap_tender}, New tahap_tender: {$tahapTender}");
                            Log::channel('log_inaproc_tahapan_tender')->info("Old Kualifikasi Usaha: {$tender->kualifikasi_usaha}, New Kualifikasi Usaha: {$kualifikasiUsaha}");
                            $tender->update([
                                'kode_tender' => $kodeTender,
                                'tahap_tender' => $tahapTender,
                                'kualifikasi_usaha' => $kualifikasiUsaha,
                            ]);
                            Log::channel('log_inaproc_tahapan_tender')->info("Data {$kode_tender} BERHASIL dilakukan update");
                        } else {
                            Log::channel('log_inaproc_tahapan_tender')->info("Data {$kode_tender} Sama, tidak perlu dilakukan update");
                        }
                    }
                    Log::channel('log_inaproc_tahapan_tender')->info("Successfully scraped detail tender with kode_tender: {$kode_tender}");
                } catch (\Exception $e) {
                    if ($e->getMessage() === 'The current node list is empty.') {
                        Log::channel('log_inaproc_tahapan_tender')->error("Error - The current node list is empty for {$kode_tender}. Skipping...");
                    } else {
                        Log::channel('log_inaproc_tahapan_tender')->error("Error - {$e->getMessage()} occurred while scraping {$kode_tender}");
                    }
                }
            }
        }
    }

    public function scrapeTahapanTenderUmumNodeJs()
    {
        Log::channel('tahapan_tender_umum_nodejs')->info("Scraping tahap-tender-umum-nodejs");

        $kd_lpse_groups = TenderUmumPublik::whereNull('tahap_tender')
            ->select('kd_lpse')
            ->groupBy('kd_lpse')
            ->pluck('kd_lpse')
            ->toArray();

        shuffle($kd_lpse_groups);
        $totalGroups = count($kd_lpse_groups);
        $progress = 0;

        foreach ($kd_lpse_groups as $kd_lpse) {
            $progress++;
            Log::channel('tahapan_tender_umum_nodejs')->info("Progress: {$progress}/{$totalGroups} (kd_lpse ke-{$progress} dari {$totalGroups})");

            $kode_tenders = TenderUmumPublik::where('kd_lpse', $kd_lpse)
                ->whereNull('tahap_tender')
                ->orderBy('tanggal_paket_tayang', 'desc')
                ->pluck('kode_tender')
                ->toArray();

            shuffle($kode_tenders);
            $totalTenders = count($kode_tenders);
            $tenderProgress = 0;
            $errorCount = 0;

            foreach ($kode_tenders as $kode_tender) {
                $tenderProgress++;
                Log::channel('tahapan_tender_umum_nodejs')->info("Processing kode_tender: {$tenderProgress}/{$totalTenders} for kd_lpse: {$kd_lpse} (kode_tender ke-{$tenderProgress} dari {$totalTenders})");

                $tender = TenderUmumPublik::find($kode_tender);
                if (!$tender) {
                    Log::channel('tahapan_tender_umum_nodejs')->info("Tender not found for kode_tender: {$kode_tender}");
                    continue;
                }

                $lpse = LPSE::where('kd_lpse', $kd_lpse)->first();
                if (!$lpse) {
                    Log::channel('tahapan_tender_umum_nodejs')->info("LPSE not found for kd_lpse: {$kd_lpse}");
                    continue;
                }

                $nodeJsUrl = 'http://tenderid.test/scrape-detail-tender?url=' . urlencode($lpse->url . '/lelang/' . $kode_tender . '/pengumumanlelang');
                Log::channel('tahapan_tender_umum_nodejs')->info("NodeJS Data URL -> $nodeJsUrl");

                $response = akses_curl($nodeJsUrl);
                if ($response === false) {
                    $errorCount++;
                    Log::channel('tahapan_tender_umum_nodejs')->error("Error fetching data from NodeJS for kode_tender: {$kode_tender}");
                    if ($errorCount >= 2) {
                        Log::channel('tahapan_tender_umum_nodejs')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                    continue;
                } else {
                    $errorCount = 0; // Reset error count if successful
                }

                $data = json_decode($response, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::channel('tahapan_tender_umum_nodejs')->error("JSON decode error for kode_tender: {$kode_tender}");
                    continue;
                }

                try {
                    // Check for null values and use default if necessary
                    $tahapTender = isset($data['tahap_tender']) ? rapikan_str_tahap_tender($data['tahap_tender']) : '';
                    $kualifikasiUsaha = $data['kualifikasi_usaha'] ?? '';

                    if ($tender->tahap_tender != $tahapTender || $tender->kualifikasi_usaha != $kualifikasiUsaha) {
                        Log::channel('tahapan_tender_umum_nodejs')->info("Updating data for kode_tender: {$kode_tender}");
                        Log::channel('tahapan_tender_umum_nodejs')->info("Old tahap_tender: {$tender->tahap_tender}, New tahap_tender: {$tahapTender}");
                        Log::channel('tahapan_tender_umum_nodejs')->info("Old kualifikasi_usaha: {$tender->kualifikasi_usaha}, New kualifikasi_usaha: {$kualifikasiUsaha}");
                        $tender->update([
                            'tahap_tender' => $tahapTender,
                            'kualifikasi_usaha' => $kualifikasiUsaha,
                        ]);
                        Log::channel('tahapan_tender_umum_nodejs')->info("Data {$kode_tender} BERHASIL dilakukan update");
                    } else {
                        Log::channel('tahapan_tender_umum_nodejs')->info("Data {$kode_tender} Sama, tidak perlu dilakukan update");
                    }

                    Log::channel('tahapan_tender_umum_nodejs')->info("Successfully scraped detail tender with kode_tender: {$kode_tender}");
                } catch (\Exception $e) {
                    Log::channel('tahapan_tender_umum_nodejs')->error("Error - {$e->getMessage()} occurred while updating data for {$kode_tender}");
                    $errorCount++; // Increment error count on exception
                    if ($errorCount >= 2) {
                        Log::channel('tahapan_tender_umum_nodejs')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                }
            }
        }
    }


    public function inaprocUpdateTahapTenderUmum()
    {
        Log::channel('update_inaproc_tahapan_tender')->info("Scraping inaproc-update-tahap-tender-umum");

        $kd_lpse_groups = TenderUmumPublik::whereNotNull('tahap_tender')
            ->where('tahap_tender', '!=', 'Tender Sudah Selesai')
            ->select('kd_lpse')
            ->groupBy('kd_lpse')
            ->pluck('kd_lpse')
            ->toArray();

        shuffle($kd_lpse_groups);
        $totalGroups = count($kd_lpse_groups);
        $groupProgress = 0;

        foreach ($kd_lpse_groups as $kd_lpse) {
            $groupProgress++;
            Log::channel('update_inaproc_tahapan_tender')->info("Progress Group: {$groupProgress}/{$totalGroups} (kd_lpse ke-{$groupProgress} dari {$totalGroups})");

            $kode_tenders = TenderUmumPublik::where('kd_lpse', $kd_lpse)
                ->whereNotNull('tahap_tender')
                ->where('tahap_tender', '!=', 'Tender Sudah Selesai')
                ->orderBy('tanggal_paket_tayang', 'desc')
                ->pluck('kode_tender')
                ->toArray();

            shuffle($kode_tenders);
            $totalTenders = count($kode_tenders);
            $tenderProgress = 0;
            $errorCount = 0;

            foreach ($kode_tenders as $kode_tender) {
                $tenderProgress++;

                $tender = TenderUmumPublik::find($kode_tender);
                if (!$tender) {
                    Log::channel('update_inaproc_tahapan_tender')->info("Tender not found for kode_tender: {$kode_tender}");
                    continue;
                }

                $lpse = LPSE::where('kd_lpse', $tender->kd_lpse)->first();
                if (!$lpse) {
                    Log::channel('update_inaproc_tahapan_tender')->info("LPSE not found for kode_tender: {$kode_tender}");
                    continue;
                }
                Log::channel('update_inaproc_tahapan_tender')->info("Progress Tender: {$tenderProgress}/{$totalTenders} for kd_lpse: {$kd_lpse} ({$lpse->nama_lpse}) (kode_tender ke-{$tenderProgress} dari {$totalTenders})");

                $url = "https://inaproc.id/tender?prov=&kab=&qualification=0&price=0&year=&kldi=0&repo=0&keyword={$kode_tender}";
                Log::channel('update_inaproc_tahapan_tender')->info("Data URL -> $url");

                $html = akses_curl($url);
                if ($html === false) {
                    $errorCount++;
                    Log::channel('update_inaproc_tahapan_tender')->error("Error fetching data from LPSE for kode_tender: {$kode_tender}");
                    if ($errorCount >= 2) {
                        Log::channel('update_inaproc_tahapan_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                    continue;
                } else {
                    $errorCount = 0; // Reset error count if successful
                }

                try {
                    $crawler = new Crawler($html);
                    $data = $crawler->filter('.ui.table.small.compact.table-list tbody tr')->each(function ($node) {
                        return [
                            'kualifikasiUsaha' => $node->filter('.label span.ui.label.default.small')->eq(1)->text(),
                            'tahapTender' => $node->filter('td')->eq(1)->filter('.ui.label.basic.small')->text(),
                        ];
                    });

                    if (empty($data) || empty($data[0]['tahapTender'])) {
                        Log::channel('update_inaproc_tahapan_tender')->info("No valid tahap_tender data found for kode_tender: {$kode_tender}, skipping update.");
                        continue; // Skip update if no valid tahap_tender data
                    }

                    $tahapTender = rapikan_str_tahap_tender($data[0]['tahapTender']);
                    $tahapTender = samakan_sinonim_tahap($tahapTender);
                    $kualifikasiUsaha = $data[0]['kualifikasiUsaha'] ?? null;

                    if ($tender->tahap_tender != $tahapTender || $tender->kualifikasi_usaha != $kualifikasiUsaha) {
                        Log::channel('update_inaproc_tahapan_tender')->info("Updating data for kode_tender: {$kode_tender}");
                        Log::channel('update_inaproc_tahapan_tender')->info("Old tahap_tender: {$tender->tahap_tender}, New tahap_tender: {$tahapTender}");
                        Log::channel('update_inaproc_tahapan_tender')->info("Old Kualifikasi Usaha: {$tender->kualifikasi_usaha}, New Kualifikasi Usaha: {$kualifikasiUsaha}");
                        $tender->update([
                            'tahap_tender' => $tahapTender,
                            'kualifikasi_usaha' => $kualifikasiUsaha,
                        ]);
                        Log::channel('update_inaproc_tahapan_tender')->info("Data {$kode_tender} BERHASIL dilakukan update");
                    } else {
                        Log::channel('update_inaproc_tahapan_tender')->info("Data {$kode_tender} Sama, tidak perlu dilakukan update");
                    }

                    Log::channel('update_inaproc_tahapan_tender')->info("Successfully scraped detail tender with kode_tender: {$kode_tender}");
                } catch (\Exception $e) {
                    if ($e->getMessage() === 'The current node list is empty.') {
                        Log::channel('update_inaproc_tahapan_tender')->error("Error - The current node list is empty for {$kode_tender}. Skipping...");
                    } else {
                        Log::channel('update_inaproc_tahapan_tender')->error("Error - {$e->getMessage()} occurred while scraping {$kode_tender}");
                    }
                    $errorCount++; // Increment error count on exception
                    if ($errorCount >= 2) {
                        Log::channel('update_inaproc_tahapan_tender')->info("Skipping kd_lpse {$kd_lpse} due to multiple errors");
                        break;
                    }
                }
            }
        }
    }


    //KHUSUS FETCH DATA

    private function fetchAllTenderUmumPublik($year)
    {
        $startTime = microtime(true);
        Log::channel('tender_fetch')->info("Memulai pengambilan data tender umum publik untuk tahun: {$year}");

        $lpseList = DB::table('lpse')->pluck('kd_lpse')->toArray();
        $totalLpse = count($lpseList);
        $processedLpse = 0;
        $totalTenders = 0;
        $updatedTenders = 0;
        $newTenders = 0;

        foreach ($lpseList as $kd_lpse) {
            $processedLpse++;
            $url = "https://isb.lkpp.go.id/isb-2/api/satudata/TenderUmumPublik/{$year}/{$kd_lpse}";
            Log::channel('tender_fetch')->info("Memproses LPSE {$processedLpse}/{$totalLpse}: {$kd_lpse}");

            try {
                $response = akses_curl($url);

                if ($response !== false) {
                    $tenderDataList = json_decode($response, true);

                    if (is_array($tenderDataList)) {
                        $tenderCount = count($tenderDataList);
                        $totalTenders += $tenderCount;

                        Log::channel('tender_fetch')->info("Berhasil mengambil {$tenderCount} tender dari LPSE {$kd_lpse}");

                        $chunks = array_chunk($tenderDataList, 100); // Memproses dalam batch 100

                        foreach ($chunks as $tenders) {
                            $this->processTenderBatch($tenders, $updatedTenders, $newTenders);
                        }

                        Log::channel('tender_fetch')->info("Selesai memproses LPSE {$kd_lpse}. Total tender: {$tenderCount}");
                    } else {
                        Log::channel('tender_fetch')->warning("Data yang diterima dari LPSE {$kd_lpse} bukan array yang valid. Response: " . substr($response, 0, 200));
                    }
                } else {
                    Log::channel('tender_fetch')->error("Gagal mengambil data dari URL: {$url}");
                }
            } catch (\Exception $e) {
                Log::channel('tender_fetch')->error("Pengecualian saat memproses LPSE {$kd_lpse}: " . $e->getMessage());
            }

            // Log progress setiap 10 LPSE
            if ($processedLpse % 10 == 0 || $processedLpse == $totalLpse) {
                $elapsedTime = round(microtime(true) - $startTime, 2);
                $progress = round(($processedLpse / $totalLpse) * 100, 2);
                Log::channel('tender_fetch')->info("Progress: {$progress}% ({$processedLpse}/{$totalLpse} LPSE). Waktu berlalu: {$elapsedTime} detik");
            }
        }

        $totalTime = round(microtime(true) - $startTime, 2);
        Log::channel('tender_fetch')->info("Selesai mengambil data tender untuk tahun {$year}. Total waktu: {$totalTime} detik");
        Log::channel('tender_fetch')->info("Ringkasan: Total tender: {$totalTenders}, Diperbarui: {$updatedTenders}, Baru: {$newTenders}");
    }

    private function processTenderBatch($tenders, &$updatedTenders, &$newTenders)
    {
        $existingTenders = DB::table('tender_umum_publik')
            ->whereIn('kode_tender', array_column($tenders, 'Kode Tender'))
            ->get()
            ->keyBy('kode_tender');

        $updatesData = [];
        $insertsData = [];

        foreach ($tenders as $tender) {
            $kodeTender = $tender['Kode Tender'];
            $tenderData = $this->prepareTenderData($tender);

            if (isset($existingTenders[$kodeTender])) {
                $existingTender = $existingTenders[$kodeTender];
                $changes = $this->getChanges($existingTender, $tenderData);
                if (!empty($changes)) {
                    $updatesData[] = array_merge(['kode_tender' => $kodeTender], $changes);
                    $updatedTenders++;

                    // Log data yang diperbarui
                    Log::channel('tender_fetch')->info("Tender diperbarui (Kode Tender: {$kodeTender}): ", $changes);
                }
            } else {
                $insertsData[] = $tenderData;
                $newTenders++;

                // Log data baru yang ditambahkan
                Log::channel('tender_fetch')->info("Tender baru ditambahkan (Kode Tender: {$kodeTender}): ", $tenderData);
            }
        }

        if (!empty($updatesData)) {
            $this->batchUpdate('tender_umum_publik', $updatesData, 'kode_tender');
        }

        if (!empty($insertsData)) {
            DB::table('tender_umum_publik')->insert($insertsData);
        }
    }

    private function prepareTenderData($tender)
    {
        return [
            'kode_tender' => $tender['Kode Tender'],
            'kd_lpse' => $tender['Repo id LPSE'],
            'lpse' => $tender['LPSE'],
            'status_tender' => $tender['Status_Tender'],
            'nama_paket' => $tender['Nama Paket'],
            'pagu' => $tender['Pagu'],
            'hps' => $tender['HPS'],
            'tanggal_paket_dibuat' => $tender['tanggal paket dibuat'],
            'tanggal_paket_tayang' => $tender['tanggal paket tayang'],
            'kategori_pekerjaan' => $tender['Kategori Pekerjaan'],
            'metode_pemilihan' => $tender['Metode Pemilihan'],
            'metode_pengadaan' => $tender['Metode Pengadaan'],
            'metode_evaluasi' => $tender['Metode Evaluasi'],
            'cara_pembayaran' => $tender['Cara Pembayaran'] ?? null,
            'jenis_penetapan_pemenang' => $tender['Jenis Penetapan Pemenang'],
            'instansi_dan_satker' => json_encode($tender['Instansi dan Satker']),
            'apakah_paket_konsolidasi' => $tender['Apakah paket konsolidasi'],
            'daftar_paket_konsolidasi' => json_encode($tender['daftar paket konsolidasi']),
            'anggaran' => json_encode($tender['anggaran']),
            'lokasi_paket' => json_encode($tender['lokasi_paket']),
            'jumlah_pendaftar' => $tender['Jumlah Pendaftar'],
            'jumlah_penawar' => $tender['Jumlah Penawar'],
            'jumlah_kirim_kualifikasi' => $tender['jumlah_kirim_kualifikasi'],
            'durasi_tender' => $tender['Durasi Tender'],
            'versi_paket_spse' => $tender['Versi_spse_paket'],
            'jadwal_pengumuman' => json_encode($tender['jadwal_pengumuman']),
            'jadwal_penawaran' => json_encode($tender['jadwal_penawaran']),
        ];
    }

    private function getChanges($existingTender, $newData)
    {
        $changes = [];
        foreach ($newData as $key => $value) {
            if ($existingTender->$key != $value) {
                $changes[$key] = $value ?? null;
            }
        }
        return $changes;
    }

    private function batchUpdate($table, $values, $index)
    {
        $caseStatements = [];
        $ids = [];

        foreach ($values as $data) {
            $id = $data[$index];
            $ids[] = $id;

            foreach ($data as $key => $value) {
                if ($key != $index) {
                    $caseStatements[$key][] = "WHEN '{$id}' THEN ?";
                }
            }
        }

        $cases = [];
        $bindings = [];

        foreach ($caseStatements as $key => $statements) {
            $cases[] = "{$key} = CASE {$index} " . implode(' ', $statements) . " ELSE {$key} END";
            foreach ($values as $data) {
                if (isset($data[$key])) {
                    $bindings[] = $data[$key] ?? null;
                }
            }
        }

        $ids = implode("','", $ids);
        $sql = "UPDATE {$table} SET " . implode(', ', $cases) . " WHERE {$index} IN ('{$ids}')";

        DB::update($sql, $bindings);
    }


}
