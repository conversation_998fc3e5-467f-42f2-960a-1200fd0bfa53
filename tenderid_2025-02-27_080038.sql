/*M!999999\- enable the sandbox mode */ 
-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.5.26-MariaDB, for debian-linux-gnu (x86_64)
--
-- Host: 127.0.0.1    Database: tenderid
-- ------------------------------------------------------
-- Server version	10.5.26-MariaDB-0+deb11u2

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `anggaran`
--

DROP TABLE IF EXISTS `anggaran`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `anggaran` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `kode_tender` varchar(255) NOT NULL,
  `rup_id` varchar(255) DEFAULT NULL,
  `sbd_id` varchar(255) DEFAULT NULL,
  `ang_nilai` bigint(20) DEFAULT NULL,
  `ang_tahun` int(11) DEFAULT NULL,
  `ang_koderekening` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `anggaran_kode_tender_foreign` (`kode_tender`),
  KEY `idx_anggaran_tender` (`kode_tender`,`rup_id`),
  KEY `idx_rup_id` (`rup_id`),
  KEY `idx_sbd_id` (`sbd_id`),
  KEY `idx_ang_tahun` (`ang_tahun`),
  KEY `idx_ang_koderekening` (`ang_koderekening`),
  KEY `idx_anggaran_kode_tender_rup_id` (`kode_tender`,`rup_id`),
  KEY `idx_anggaran_rup_id` (`rup_id`),
  KEY `idx_anggaran_kode_tender` (`kode_tender`),
  CONSTRAINT `anggaran_kode_tender_foreign` FOREIGN KEY (`kode_tender`) REFERENCES `tender_umum_publik` (`kode_tender`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6903138 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cache`
--

DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cache_locks`
--

DROP TABLE IF EXISTS `cache_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `commands_status`
--

DROP TABLE IF EXISTS `commands_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `commands_status` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `command_name` varchar(255) NOT NULL,
  `is_running` tinyint(1) NOT NULL DEFAULT 0,
  `started_at` timestamp NULL DEFAULT NULL,
  `finished_at` timestamp NULL DEFAULT NULL,
  `process_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `command_name` (`command_name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `data_sirup`
--

DROP TABLE IF EXISTS `data_sirup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `data_sirup` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `kode_rup` bigint(20) NOT NULL,
  `nama_paket` text NOT NULL,
  `nama_klpd` varchar(255) DEFAULT NULL,
  `satuan_kerja` varchar(255) DEFAULT NULL,
  `tahun_anggaran` int(11) DEFAULT NULL,
  `lokasi_pekerjaan` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`lokasi_pekerjaan`)),
  `volume_pekerjaan` text DEFAULT NULL,
  `uraian_pekerjaan` text DEFAULT NULL,
  `spesifikasi_pekerjaan` text DEFAULT NULL,
  `produk_dalam_negeri` tinyint(1) DEFAULT NULL,
  `umkm` tinyint(1) DEFAULT NULL,
  `pra_dipa_dpa` tinyint(1) DEFAULT NULL,
  `sumber_dana` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`sumber_dana`)),
  `jenis_pengadaan` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`jenis_pengadaan`)),
  `total_pagu` bigint(20) DEFAULT NULL,
  `metode_pemilihan` varchar(255) DEFAULT NULL,
  `pemanfaatan_barang_jasa` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`pemanfaatan_barang_jasa`)),
  `jadwal_pelaksanaan_kontrak` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`jadwal_pelaksanaan_kontrak`)),
  `jadwal_pemilihan_penyedia` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`jadwal_pemilihan_penyedia`)),
  `tanggal_paket_diumumkan` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_processing` tinyint(1) DEFAULT 0,
  `tanggal_paket_diumumkan_date` date GENERATED ALWAYS AS (cast(`tanggal_paket_diumumkan` as date)) STORED,
  PRIMARY KEY (`id`),
  KEY `idx_tahun_anggaran` (`tahun_anggaran`),
  KEY `idx_tanggal_paket_diumumkan` (`tanggal_paket_diumumkan`),
  KEY `idx_nama_paket` (`nama_paket`(768)),
  KEY `idx_nama_klpd` (`nama_klpd`),
  KEY `idx_kode_rup` (`kode_rup`),
  KEY `idx_metode_pemilihan` (`metode_pemilihan`),
  KEY `idx_satuan_kerja` (`satuan_kerja`),
  KEY `idx_fulltext_tanggal` (`nama_paket`(191),`nama_klpd`(191),`metode_pemilihan`(191),`satuan_kerja`(191),`tanggal_paket_diumumkan`),
  KEY `idx_total_pagu_nama_klpd_tanggal_paket` (`total_pagu`,`nama_klpd`,`tanggal_paket_diumumkan`),
  KEY `idx_tanggal_date` (`tanggal_paket_diumumkan_date`),
  KEY `idx_data_sirup_filter` (`nama_paket`(100),`total_pagu`,`tahun_anggaran`,`nama_klpd`(100),`metode_pemilihan`,`tanggal_paket_diumumkan`),
  KEY `idx_klpd_paket_date` (`nama_klpd`,`nama_paket`(100),`tanggal_paket_diumumkan_date`),
  KEY `idx_klpd_paket_` (`nama_klpd`,`nama_paket`(100),`tanggal_paket_diumumkan`),
  KEY `idx_is_processing` (`is_processing`),
  KEY `idx_tahun_anggaran_kode_rup` (`tahun_anggaran`,`kode_rup`),
  FULLTEXT KEY `idx_fulltext` (`nama_paket`,`nama_klpd`,`metode_pemilihan`,`satuan_kerja`)
) ENGINE=InnoDB AUTO_INCREMENT=3328979 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ekatalog_paket_pengadaan`
--

DROP TABLE IF EXISTS `ekatalog_paket_pengadaan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ekatalog_paket_pengadaan` (
  `id_paket` int(11) NOT NULL AUTO_INCREMENT,
  `nama_paket` text NOT NULL,
  `nomor_paket` varchar(100) DEFAULT NULL,
  `pengelola` varchar(255) DEFAULT NULL,
  `satuan_kerja` varchar(255) DEFAULT NULL,
  `instansi_pembeli` varchar(255) DEFAULT NULL,
  `jenis_katalog` varchar(100) DEFAULT NULL,
  `etalase` varchar(255) DEFAULT NULL,
  `tanggal_paket` datetime DEFAULT NULL,
  `status_paket` varchar(100) DEFAULT NULL,
  `rup_id` bigint(20) DEFAULT NULL,
  `status_umkm` varchar(50) DEFAULT NULL,
  `id_penyedia` bigint(20) unsigned DEFAULT NULL,
  `id_pelaksana` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `tanggal` date GENERATED ALWAYS AS (cast(`tanggal_paket` as date)) STORED,
  PRIMARY KEY (`id_paket`),
  KEY `id_penyedia` (`id_penyedia`),
  KEY `id_pelaksana` (`id_pelaksana`),
  KEY `idx_rup_id` (`rup_id`),
  KEY `idx_status_paket` (`status_paket`),
  KEY `idx_tanggal_paket` (`tanggal_paket`),
  KEY `idx_nomor_paket` (`nomor_paket`),
  KEY `idx_tanggal` (`tanggal`),
  KEY `idx_nama_paket` (`nama_paket`(768)),
  KEY `idx_pengelola` (`pengelola`),
  KEY `idx_satuan_kerja` (`satuan_kerja`),
  KEY `idx_instansi_pembeli` (`instansi_pembeli`),
  KEY `idx_rup_id_nomor_paket` (`rup_id`,`nomor_paket`),
  CONSTRAINT `ekatalog_paket_pengadaan_ibfk_1` FOREIGN KEY (`id_penyedia`) REFERENCES `list_perusahaan_tender` (`id`),
  CONSTRAINT `ekatalog_paket_pengadaan_ibfk_2` FOREIGN KEY (`id_pelaksana`) REFERENCES `list_perusahaan_tender` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2472283 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ekatalog_produk`
--

DROP TABLE IF EXISTS `ekatalog_produk`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ekatalog_produk` (
  `id_produk` int(11) NOT NULL AUTO_INCREMENT,
  `nama_produk` text DEFAULT NULL,
  `kategori_lv1` text DEFAULT NULL,
  `kategori_lv2` text DEFAULT NULL,
  `nama_manufaktur` varchar(255) DEFAULT NULL,
  `jenis_produk` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id_produk`),
  KEY `idx_nama_produk` (`nama_produk`(768))
) ENGINE=InnoDB AUTO_INCREMENT=2122294 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ekatalog_transaksi_paket`
--

DROP TABLE IF EXISTS `ekatalog_transaksi_paket`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ekatalog_transaksi_paket` (
  `id_transaksi` int(11) NOT NULL AUTO_INCREMENT,
  `id_paket` int(11) DEFAULT NULL,
  `id_produk` int(11) DEFAULT NULL,
  `kuantitas_produk` int(11) NOT NULL,
  `harga_satuan` decimal(15,2) NOT NULL,
  `harga_ongkos_kirim` decimal(15,2) DEFAULT NULL,
  `total_harga` decimal(15,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id_transaksi`),
  KEY `id_paket` (`id_paket`),
  KEY `id_produk` (`id_produk`),
  CONSTRAINT `ekatalog_transaksi_paket_ibfk_1` FOREIGN KEY (`id_paket`) REFERENCES `ekatalog_paket_pengadaan` (`id_paket`),
  CONSTRAINT `ekatalog_transaksi_paket_ibfk_2` FOREIGN KEY (`id_produk`) REFERENCES `ekatalog_produk` (`id_produk`),
  CONSTRAINT `ekatalog_transaksi_paket_ibfk_3` FOREIGN KEY (`id_paket`) REFERENCES `ekatalog_paket_pengadaan` (`id_paket`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=10488064 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `failed_jobs`
--

DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB AUTO_INCREMENT=16758 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `instansi_satker`
--

DROP TABLE IF EXISTS `instansi_satker`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `instansi_satker` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `kode_tender` varchar(255) NOT NULL,
  `rup_id` varchar(255) DEFAULT NULL,
  `nama_satker` varchar(255) NOT NULL,
  `nama_instansi` varchar(255) NOT NULL,
  `jenis_instansi` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `instansi_satker_kode_tender_foreign` (`kode_tender`),
  KEY `idx_instansi_satker_tender` (`kode_tender`,`rup_id`),
  KEY `idx_nama_satker` (`nama_satker`),
  KEY `idx_nama_instansi` (`nama_instansi`),
  KEY `idx_jenis_instansi` (`jenis_instansi`),
  KEY `idx_rup_id` (`rup_id`),
  KEY `idx_instansi_satker_kode_tender_rup_id` (`kode_tender`,`rup_id`),
  KEY `idx_instansi_satker_rup_id` (`rup_id`),
  KEY `idx_instansi_satker_kode_tender` (`kode_tender`),
  CONSTRAINT `instansi_satker_kode_tender_foreign` FOREIGN KEY (`kode_tender`) REFERENCES `tender_umum_publik` (`kode_tender`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6046010 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `jadwal`
--

DROP TABLE IF EXISTS `jadwal`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jadwal` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `kode_tender` varchar(50) NOT NULL,
  `tahap` varchar(255) NOT NULL,
  `tanggal_mulai` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `tanggal_selesai` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `step` int(10) unsigned DEFAULT NULL,
  `keterangan` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `jadwal_kode_tender_tahap_unique` (`kode_tender`,`tahap`),
  KEY `jadwal_kode_tender_index` (`kode_tender`),
  CONSTRAINT `jadwal_kode_tender_foreign` FOREIGN KEY (`kode_tender`) REFERENCES `tender_umum_publik` (`kode_tender`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=48489681 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `job_batches`
--

DROP TABLE IF EXISTS `job_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `jobs`
--

DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) unsigned NOT NULL,
  `reserved_at` int(10) unsigned DEFAULT NULL,
  `available_at` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB AUTO_INCREMENT=497669 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `klpd`
--

DROP TABLE IF EXISTS `klpd`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `klpd` (
  `kd_klpd` varchar(50) NOT NULL,
  `nama_klpd` varchar(255) NOT NULL,
  `jenis_klpd` varchar(255) DEFAULT NULL,
  `kd_provinsi` int(11) DEFAULT NULL,
  `kd_kabupaten` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`kd_klpd`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `list_perusahaan_tender`
--

DROP TABLE IF EXISTS `list_perusahaan_tender`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `list_perusahaan_tender` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `nama_peserta` varchar(255) NOT NULL,
  `normalized_name` varchar(255) NOT NULL,
  `npwp` varchar(255) DEFAULT NULL,
  `partial_npwp` varchar(255) DEFAULT NULL,
  `alamat` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `npwp_blur` varchar(255) DEFAULT NULL COMMENT '255',
  `pembeda` varchar(255) DEFAULT NULL COMMENT '255',
  PRIMARY KEY (`id`),
  KEY `list_perusahaan_tender_npwp_index` (`npwp`),
  KEY `idx_peserta_id` (`id`),
  KEY `idx_list_perusahaan_npwp` (`npwp`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_nama_peserta` (`nama_peserta`),
  KEY `idx_id` (`id`),
  KEY `idx_list_perusahaan_tender_id` (`id`),
  KEY `normalized_name` (`normalized_name`),
  KEY `partial_npwp` (`partial_npwp`),
  KEY `npwp_blur` (`npwp_blur`),
  KEY `pembeda` (`pembeda`)
) ENGINE=InnoDB AUTO_INCREMENT=345366 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `login_logs`
--

DROP TABLE IF EXISTS `login_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `login_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `login_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `logout_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `login_logs_user_id_foreign` (`user_id`),
  CONSTRAINT `login_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lokasi_paket`
--

DROP TABLE IF EXISTS `lokasi_paket`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lokasi_paket` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `kode_tender` varchar(255) NOT NULL,
  `kbp_nama` varchar(255) DEFAULT NULL,
  `prp_nama` varchar(255) DEFAULT NULL,
  `pkt_lokasi` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `lokasi_paket_kode_tender_foreign` (`kode_tender`),
  CONSTRAINT `lokasi_paket_kode_tender_foreign` FOREIGN KEY (`kode_tender`) REFERENCES `tender_umum_publik` (`kode_tender`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6232346 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lpse`
--

DROP TABLE IF EXISTS `lpse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lpse` (
  `kd_lpse` int(11) NOT NULL,
  `nama_lpse` varchar(255) NOT NULL,
  `provinsi` varchar(255) DEFAULT NULL,
  `alamat` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `helpdesk` varchar(255) DEFAULT NULL,
  `versi_lpse` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `logo_url` varchar(255) DEFAULT NULL,
  `logo_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`kd_lpse`),
  KEY `idx_provinsi` (`provinsi`),
  KEY `idx_email` (`email`),
  KEY `idx_url` (`url`),
  KEY `idx_nama_lpse` (`nama_lpse`),
  KEY `idx_logo_path` (`logo_path`),
  KEY `idx_kd_lpse_nama_lpse` (`kd_lpse`,`nama_lpse`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) unsigned NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `page_access_logs`
--

DROP TABLE IF EXISTS `page_access_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `page_access_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `url` varchar(1000) DEFAULT NULL,
  `method` varchar(255) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `page_access_logs_user_id_foreign` (`user_id`),
  KEY `idx_url` (`url`(255)),
  CONSTRAINT `page_access_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3566 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `password_reset_tokens`
--

DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pemenang_lpse`
--

DROP TABLE IF EXISTS `pemenang_lpse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pemenang_lpse` (
  `kode_tender` varchar(50) NOT NULL,
  `id_peserta` bigint(20) unsigned NOT NULL,
  `harga_penawaran` decimal(15,2) DEFAULT NULL,
  `harga_terkoreksi` decimal(15,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`kode_tender`),
  KEY `pemenang_lpse_id_peserta_foreign` (`id_peserta`),
  KEY `idx_pemenang_id_peserta` (`id_peserta`),
  CONSTRAINT `pemenang_lpse_id_peserta_foreign` FOREIGN KEY (`id_peserta`) REFERENCES `list_perusahaan_tender` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pemenang_lpse_kode_tender_foreign` FOREIGN KEY (`kode_tender`) REFERENCES `tender_umum_publik` (`kode_tender`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `perusahaan_statistik`
--

DROP TABLE IF EXISTS `perusahaan_statistik`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `perusahaan_statistik` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `id_perusahaan` bigint(20) unsigned NOT NULL,
  `jumlah_tender_diikuti` int(11) DEFAULT 0,
  `jumlah_tender_dimenangkan` int(11) DEFAULT 0,
  `nilai_tender_dimenangkan` decimal(20,2) DEFAULT 0.00,
  `jumlah_ekatalog_didapatkan` int(11) DEFAULT 0,
  `nilai_ekatalog_didapatkan` decimal(20,2) DEFAULT 0.00,
  `total_tender_ekatalog` decimal(20,2) DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_id_perusahaan` (`id_perusahaan`),
  KEY `idx_jumlah_tender_diikuti` (`jumlah_tender_diikuti`),
  KEY `idx_jumlah_tender_dimenangkan` (`jumlah_tender_dimenangkan`),
  KEY `idx_nilai_tender_dimenangkan` (`nilai_tender_dimenangkan`),
  KEY `idx_jumlah_ekatalog_didapatkan` (`jumlah_ekatalog_didapatkan`),
  KEY `idx_nilai_ekatalog_didapatkan` (`nilai_ekatalog_didapatkan`),
  KEY `idx_total_tender_ekatalog` (`total_tender_ekatalog`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`),
  CONSTRAINT `fk_id_perusahaan` FOREIGN KEY (`id_perusahaan`) REFERENCES `list_perusahaan_tender` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=157575 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `peserta_tender`
--

DROP TABLE IF EXISTS `peserta_tender`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `peserta_tender` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `kode_tender` varchar(50) NOT NULL,
  `id_peserta` bigint(20) unsigned NOT NULL,
  `status` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_kode_peserta` (`kode_tender`,`id_peserta`),
  KEY `peserta_tender_kode_tender_foreign` (`kode_tender`),
  KEY `peserta_tender_id_peserta_foreign` (`id_peserta`),
  KEY `idx_peserta_tender_id_peserta` (`id_peserta`),
  KEY `idx_kode_tender_id_peserta` (`kode_tender`,`id_peserta`),
  CONSTRAINT `peserta_tender_id_peserta_foreign` FOREIGN KEY (`id_peserta`) REFERENCES `list_perusahaan_tender` (`id`),
  CONSTRAINT `peserta_tender_kode_tender_foreign` FOREIGN KEY (`kode_tender`) REFERENCES `tender_umum_publik` (`kode_tender`)
) ENGINE=InnoDB AUTO_INCREMENT=12558303 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_foreign` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`),
  CONSTRAINT `sessions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sync_logs`
--

DROP TABLE IF EXISTS `sync_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sync_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `record_id` varchar(255) NOT NULL,
  `action` varchar(255) NOT NULL,
  `changes` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`changes`)),
  `synced_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `table` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25306 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sync_progress`
--

DROP TABLE IF EXISTS `sync_progress`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sync_progress` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `command_name` varchar(255) DEFAULT NULL,
  `last_kode_tender` varchar(50) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `command_name` (`command_name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `telescope_entries`
--

DROP TABLE IF EXISTS `telescope_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_entries` (
  `sequence` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `batch_id` char(36) NOT NULL,
  `family_hash` varchar(255) DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT 1,
  `type` varchar(20) NOT NULL,
  `content` longtext NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`sequence`),
  UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  KEY `telescope_entries_batch_id_index` (`batch_id`),
  KEY `telescope_entries_family_hash_index` (`family_hash`),
  KEY `telescope_entries_created_at_index` (`created_at`),
  KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`)
) ENGINE=InnoDB AUTO_INCREMENT=3653029 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `telescope_entries_tags`
--

DROP TABLE IF EXISTS `telescope_entries_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) NOT NULL,
  `tag` varchar(255) NOT NULL,
  PRIMARY KEY (`entry_uuid`,`tag`),
  KEY `telescope_entries_tags_tag_index` (`tag`),
  CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `telescope_monitoring`
--

DROP TABLE IF EXISTS `telescope_monitoring`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) NOT NULL,
  PRIMARY KEY (`tag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tender_umum_publik`
--

DROP TABLE IF EXISTS `tender_umum_publik`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tender_umum_publik` (
  `kode_tender` varchar(50) NOT NULL,
  `kd_lpse` int(11) NOT NULL,
  `status_tender` varchar(255) DEFAULT NULL,
  `nama_paket` varchar(2000) NOT NULL,
  `pagu` decimal(15,1) DEFAULT NULL,
  `hps` decimal(15,1) DEFAULT NULL,
  `tanggal_paket_dibuat` date DEFAULT NULL,
  `tanggal_paket_tayang` timestamp NULL DEFAULT NULL,
  `kategori_pekerjaan` varchar(255) DEFAULT NULL,
  `metode_pemilihan` varchar(255) DEFAULT NULL,
  `metode_pengadaan` varchar(255) DEFAULT NULL,
  `metode_evaluasi` varchar(255) DEFAULT NULL,
  `cara_pembayaran` varchar(255) DEFAULT NULL,
  `jenis_penetapan_pemenang` varchar(255) DEFAULT NULL,
  `apakah_paket_konsolidasi` varchar(255) DEFAULT NULL,
  `jumlah_pendaftar` int(11) DEFAULT NULL,
  `jumlah_penawar` int(11) DEFAULT NULL,
  `jumlah_kirim_kualifikasi` int(11) DEFAULT NULL,
  `durasi_tender` int(11) DEFAULT NULL,
  `versi_paket_spse` varchar(255) DEFAULT NULL,
  `tahap_tender` varchar(255) DEFAULT NULL,
  `kualifikasi_usaha` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `syarat_kualifikasi` text DEFAULT NULL,
  `tanggal_paket_tayang_date` date GENERATED ALWAYS AS (cast(`tanggal_paket_tayang` as date)) STORED,
  PRIMARY KEY (`kode_tender`),
  KEY `tender_umum_publik_kd_lpse_index` (`kd_lpse`),
  KEY `tender_umum_publik_tanggal_paket_tayang_index` (`tanggal_paket_tayang`),
  KEY `idx_status_tender` (`status_tender`),
  KEY `idx_nama_paket` (`nama_paket`(768)),
  KEY `idx_kategori_pekerjaan` (`kategori_pekerjaan`),
  KEY `idx_metode_pemilihan` (`metode_pemilihan`),
  KEY `idx_metode_pengadaan` (`metode_pengadaan`),
  KEY `idx_metode_evaluasi` (`metode_evaluasi`),
  KEY `idx_cara_pembayaran` (`cara_pembayaran`),
  KEY `idx_tahap_tender` (`tahap_tender`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`),
  KEY `idx_kode_tender` (`kode_tender`),
  KEY `idx_tender_umum_publik_kode_tender` (`kode_tender`),
  KEY `idx_tender_kode_tender` (`kode_tender`),
  KEY `idx_tanggal_paket_tayang_date` (`tanggal_paket_tayang_date`),
  CONSTRAINT `tender_umum_publik_kd_lpse_foreign` FOREIGN KEY (`kd_lpse`) REFERENCES `lpse` (`kd_lpse`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `two_factor_secret` text DEFAULT NULL,
  `two_factor_recovery_codes` text DEFAULT NULL,
  `two_factor_confirmed_at` timestamp NULL DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `account_type` enum('Super Admin','Individu','Perusahaan','Bank & Asuransi') NOT NULL,
  `npwp` varchar(255) DEFAULT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `last_seen` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `telegram_bot` varchar(255) DEFAULT NULL,
  `tender_followed` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tender_followed`)),
  `lpse_followed` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`lpse_followed`)),
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  UNIQUE KEY `users_npwp_unique` (`npwp`)
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'tenderid'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-02-27  8:00:42
