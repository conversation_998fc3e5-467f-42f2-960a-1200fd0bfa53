#!/bin/bash

# Setup automatic log monitoring and permission fixing for Docker environment
# This script sets up cron jobs and monitoring for Laravel log files

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the current directory (Laravel project root)
PROJECT_ROOT=$(pwd)
PHP_PATH=$(which php 2>/dev/null || echo "/usr/local/bin/php")

print_status "Setting up log monitoring for Laravel project at: $PROJECT_ROOT"

# Function to check if we're in a Laravel project
check_laravel_project() {
    if [[ ! -f "artisan" ]]; then
        print_error "This doesn't appear to be a Laravel project (artisan file not found)"
        exit 1
    fi
    print_success "Laravel project detected"
}

# Function to test PHP and artisan
test_php_artisan() {
    print_status "Testing PHP and artisan..."
    
    if ! command -v php &> /dev/null; then
        print_warning "PHP command not found in PATH"
        print_status "Trying alternative PHP paths..."
        
        # Try common PHP paths
        for php_path in "/usr/local/bin/php" "/usr/bin/php" "/opt/php/bin/php"; do
            if [[ -x "$php_path" ]]; then
                PHP_PATH="$php_path"
                print_success "Found PHP at: $PHP_PATH"
                break
            fi
        done
    else
        print_success "PHP found at: $(which php)"
    fi
    
    # Test artisan
    if $PHP_PATH artisan --version &>/dev/null; then
        print_success "Artisan is working"
    else
        print_error "Artisan is not working properly"
        exit 1
    fi
}

# Function to create monitoring scripts
create_monitoring_scripts() {
    print_status "Creating monitoring scripts..."
    
    # Create log permission monitor script
    cat > "monitor-logs.sh" << EOF
#!/bin/bash
# Auto-generated log monitoring script
cd "$PROJECT_ROOT"

# Fix log permissions
$PHP_PATH artisan logs:monitor-permissions --fix --quiet

# Create new log files if needed
./create-log-files.sh >/dev/null 2>&1

# Log the monitoring activity
echo "\$(date): Log permissions checked and fixed" >> storage/logs/monitoring.log
EOF

    chmod +x "monitor-logs.sh"
    print_success "Created monitor-logs.sh"
    
    # Create daily maintenance script
    cat > "daily-maintenance.sh" << EOF
#!/bin/bash
# Auto-generated daily maintenance script
cd "$PROJECT_ROOT"

# Analyze slow queries
$PHP_PATH artisan query:analyze-slow --days=1 --threshold=100 --limit=10 --quiet

# Check for critical issues
if [[ -f "storage/logs/query/critical_query-\$(date +%Y-%m-%d).log" ]]; then
    CRITICAL_COUNT=\$(wc -l < "storage/logs/query/critical_query-\$(date +%Y-%m-%d).log" 2>/dev/null || echo "0")
    if [[ \$CRITICAL_COUNT -gt 0 ]]; then
        echo "\$(date): WARNING - \$CRITICAL_COUNT critical slow queries detected" >> storage/logs/monitoring.log
    fi
fi

# Clean old monitoring logs (keep 30 days)
find storage/logs/ -name "monitoring.log.*" -mtime +30 -delete 2>/dev/null || true

echo "\$(date): Daily maintenance completed" >> storage/logs/monitoring.log
EOF

    chmod +x "daily-maintenance.sh"
    print_success "Created daily-maintenance.sh"
}

# Function to setup cron jobs
setup_cron_jobs() {
    print_status "Setting up cron jobs..."
    
    # Check if cron is available
    if ! command -v crontab &> /dev/null; then
        print_warning "Crontab not available. You'll need to setup monitoring manually."
        return
    fi
    
    # Backup existing crontab
    crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
    
    # Create new crontab entries
    TEMP_CRON=$(mktemp)
    
    # Keep existing crontab entries
    crontab -l 2>/dev/null | grep -v "# Laravel Log Monitoring" > "$TEMP_CRON" || true
    
    # Add our monitoring entries
    cat >> "$TEMP_CRON" << EOF

# Laravel Log Monitoring - Auto-generated
# Fix log permissions every 6 hours
0 */6 * * * cd $PROJECT_ROOT && ./monitor-logs.sh >/dev/null 2>&1

# Daily maintenance at 2 AM
0 2 * * * cd $PROJECT_ROOT && ./daily-maintenance.sh >/dev/null 2>&1

# Create log files daily at 1 AM
0 1 * * * cd $PROJECT_ROOT && ./create-log-files.sh >/dev/null 2>&1
EOF

    # Install new crontab
    if crontab "$TEMP_CRON"; then
        print_success "Cron jobs installed successfully"
        print_status "Monitoring will run every 6 hours"
        print_status "Daily maintenance will run at 2 AM"
    else
        print_error "Failed to install cron jobs"
    fi
    
    rm -f "$TEMP_CRON"
}

# Function to test the setup
test_setup() {
    print_status "Testing the setup..."
    
    # Test log monitoring
    if $PHP_PATH artisan logs:monitor-permissions --check-only &>/dev/null; then
        print_success "Log monitoring command works"
    else
        print_warning "Log monitoring command has issues"
    fi
    
    # Test slow query analysis
    if $PHP_PATH artisan query:analyze-slow --days=1 --limit=1 &>/dev/null; then
        print_success "Slow query analysis works"
    else
        print_warning "Slow query analysis has issues"
    fi
    
    # Test log file creation
    if ./create-log-files.sh &>/dev/null; then
        print_success "Log file creation works"
    else
        print_warning "Log file creation has issues"
    fi
}

# Function to show status and recommendations
show_status() {
    print_status "Setup completed! Here's what was configured:"
    echo
    print_success "✅ Monitoring scripts created:"
    print_status "   • monitor-logs.sh - Fixes permissions every 6 hours"
    print_status "   • daily-maintenance.sh - Daily analysis and cleanup"
    echo
    print_success "✅ Cron jobs scheduled:"
    print_status "   • Log permission monitoring: Every 6 hours"
    print_status "   • Daily maintenance: 2 AM daily"
    print_status "   • Log file creation: 1 AM daily"
    echo
    print_status "📊 Manual commands available:"
    print_status "   • $PHP_PATH artisan logs:monitor-permissions --fix"
    print_status "   • $PHP_PATH artisan query:analyze-slow --days=7"
    print_status "   • ./monitor-logs.sh"
    print_status "   • ./daily-maintenance.sh"
    echo
    print_status "📁 Log files to monitor:"
    print_status "   • storage/logs/monitoring.log - Monitoring activity"
    print_status "   • storage/logs/query/slow_query-*.log - Slow queries"
    print_status "   • storage/logs/query/critical_query-*.log - Critical issues"
    echo
    print_warning "🔔 Set up alerting for critical queries if needed!"
}

# Main execution
main() {
    print_status "🚀 Starting Laravel log monitoring setup..."
    echo
    
    check_laravel_project
    test_php_artisan
    create_monitoring_scripts
    setup_cron_jobs
    test_setup
    
    echo
    show_status
    
    print_success "🎉 Log monitoring setup completed successfully!"
}

# Run main function
main "$@"
