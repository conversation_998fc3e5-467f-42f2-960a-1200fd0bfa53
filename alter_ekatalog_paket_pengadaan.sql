-- <PERSON><PERSON><PERSON> kolom tahun sebagai GENERATED COLUMN
ALTER TABLE ekatalog_paket_pengadaan
ADD COLUMN tahun YEAR GENERATED ALWAYS AS (YEAR(tanggal_paket)) STORED AFTER tanggal_paket;

-- <PERSON><PERSON><PERSON> kolom bulan sebagai GENERATED COLUMN
ALTER TABLE ekatalog_paket_pengadaan
ADD COLUMN bulan TINYINT GENERATED ALWAYS AS (MONTH(tanggal_paket)) STORED AFTER tahun;

-- <PERSON><PERSON><PERSON> kolom tanggal sudah ada dan merupakan GENERATED COLUMN
-- <PERSON><PERSON> belum ada, tambahkan kolom tanggal
ALTER TABLE ekatalog_paket_pengadaan
ADD COLUMN tanggal DATE GENERATED ALWAYS AS (CAST(tanggal_paket AS DATE)) STORED AFTER bulan;

-- Tambahkan index untuk kolom tahun dan bulan
ALTER TABLE ekatalog_paket_pengadaan
ADD INDEX idx_tahun_bulan (tahun, bulan);

-- Tambahkan index untuk kolom tanggal jika belum ada
ALTER TABLE ekatalog_paket_pengadaan
ADD INDEX idx_tanggal (tanggal);
