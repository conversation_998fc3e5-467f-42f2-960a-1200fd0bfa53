<?php

namespace Tests\Unit;

use App\Models\ListPerusahaanTender;
use App\Models\PesertaTender;
use App\Models\PemenangLpse;
use App\Models\PaketPengadaan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Support\Facades\Artisan;

class DeduplikasiPerusahaanCommandTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test deduplikasi perusahaan dengan NPWP yang sama
     */
    public function testDeduplikasiPerusahaanDenganNpwpSama()
    {
        // Buat dua perusahaan dengan NPWP yang sama
        $perusahaan1 = ListPerusahaanTender::create([
            'nama_peserta' => 'PT ABC COMPANY',
            'normalized_name' => 'PT ABC COMPANY',
            'npwp' => '01.234.567.8-910.123',
            'partial_npwp' => '01234',
            'pembeda' => 'PTABCCOMPANY01234'
        ]);

        $perusahaan2 = ListPerusahaanTender::create([
            'nama_peserta' => 'PT ABC COMPANY (CABANG)',
            'normalized_name' => 'PT ABC COMPANY CABANG',
            'npwp' => '01.234.567.8-910.123', // NPWP sama
            'partial_npwp' => '01234',
            'pembeda' => 'PTABCCOMPANYCABANG01234'
        ]);

        // Buat peserta tender untuk kedua perusahaan
        PesertaTender::create([
            'kode_tender' => '12345',
            'id_peserta' => $perusahaan1->id,
            'status' => 'Peserta'
        ]);

        PesertaTender::create([
            'kode_tender' => '67890',
            'id_peserta' => $perusahaan2->id,
            'status' => 'Peserta'
        ]);

        // Jalankan command dengan mode dry-run
        Artisan::call('perusahaan:deduplikasi', [
            '--dry-run' => true,
            '--only-npwp' => true
        ]);

        // Pastikan kedua perusahaan masih ada (karena dry-run)
        $this->assertEquals(2, ListPerusahaanTender::count());

        // Jalankan command tanpa dry-run
        Artisan::call('perusahaan:deduplikasi', [
            '--only-npwp' => true
        ]);

        // Pastikan hanya satu perusahaan yang tersisa
        $this->assertEquals(1, ListPerusahaanTender::count());

        // Pastikan semua peserta tender terasosiasi dengan perusahaan yang tersisa
        $perusahaanTersisa = ListPerusahaanTender::first();
        $this->assertEquals(2, PesertaTender::where('id_peserta', $perusahaanTersisa->id)->count());
    }

    /**
     * Test deduplikasi perusahaan dengan nama yang mirip
     */
    public function testDeduplikasiPerusahaanDenganNamaMirip()
    {
        // Buat dua perusahaan dengan nama yang mirip
        $perusahaan1 = ListPerusahaanTender::create([
            'nama_peserta' => 'PT XYZ COMPANY',
            'normalized_name' => 'PT XYZ COMPANY',
            'pembeda' => 'PTXYZCOMPANY'
        ]);

        $perusahaan2 = ListPerusahaanTender::create([
            'nama_peserta' => 'PT. XYZ COMPANY',
            'normalized_name' => 'PT XYZ COMPANY',
            'pembeda' => 'PTXYZCOMPANY2'
        ]);

        // Buat peserta tender untuk kedua perusahaan
        PesertaTender::create([
            'kode_tender' => '12345',
            'id_peserta' => $perusahaan1->id,
            'status' => 'Peserta'
        ]);

        PesertaTender::create([
            'kode_tender' => '67890',
            'id_peserta' => $perusahaan2->id,
            'status' => 'Peserta'
        ]);

        // Jalankan command dengan mode dry-run
        Artisan::call('perusahaan:deduplikasi', [
            '--dry-run' => true,
            '--only-name' => true
        ]);

        // Pastikan kedua perusahaan masih ada (karena dry-run)
        $this->assertEquals(2, ListPerusahaanTender::count());

        // Jalankan command tanpa dry-run
        Artisan::call('perusahaan:deduplikasi', [
            '--only-name' => true
        ]);

        // Pastikan hanya satu perusahaan yang tersisa
        $this->assertEquals(1, ListPerusahaanTender::count());

        // Pastikan semua peserta tender terasosiasi dengan perusahaan yang tersisa
        $perusahaanTersisa = ListPerusahaanTender::first();
        $this->assertEquals(2, PesertaTender::where('id_peserta', $perusahaanTersisa->id)->count());
    }

    /**
     * Test deduplikasi perusahaan dengan konflik di tabel peserta_tender
     */
    public function testDeduplikasiPerusahaanDenganKonflikPesertaTender()
    {
        // Buat dua perusahaan dengan NPWP yang sama
        $perusahaan1 = ListPerusahaanTender::create([
            'nama_peserta' => 'PT DEF COMPANY',
            'normalized_name' => 'PT DEF COMPANY',
            'npwp' => '02.345.678.9-012.345',
            'partial_npwp' => '02345',
            'pembeda' => 'PTDEFCOMPANY02345'
        ]);

        $perusahaan2 = ListPerusahaanTender::create([
            'nama_peserta' => 'PT DEF COMPANY (PUSAT)',
            'normalized_name' => 'PT DEF COMPANY PUSAT',
            'npwp' => '02.345.678.9-012.345', // NPWP sama
            'partial_npwp' => '02345',
            'pembeda' => 'PTDEFCOMPANYPUSAT02345'
        ]);

        // Buat peserta tender untuk kedua perusahaan dengan kode_tender yang sama
        PesertaTender::create([
            'kode_tender' => '12345',
            'id_peserta' => $perusahaan1->id,
            'status' => 'Peserta'
        ]);

        PesertaTender::create([
            'kode_tender' => '12345', // Kode tender sama
            'id_peserta' => $perusahaan2->id,
            'status' => 'Peserta'
        ]);

        // Jalankan command
        Artisan::call('perusahaan:deduplikasi');

        // Pastikan hanya satu perusahaan yang tersisa
        $this->assertEquals(1, ListPerusahaanTender::count());

        // Pastikan hanya satu peserta tender yang tersisa untuk kode_tender tersebut
        $perusahaanTersisa = ListPerusahaanTender::first();
        $this->assertEquals(1, PesertaTender::where('kode_tender', '12345')->count());
    }
}
