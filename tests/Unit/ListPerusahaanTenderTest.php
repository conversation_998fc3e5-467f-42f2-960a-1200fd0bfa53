<?php

namespace Tests\Unit;

use App\Models\ListPerusahaanTender;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ListPerusahaanTenderTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test createOrUpdatePerusahaan dengan nama perusahaan saja (kasus Ekatalog)
     */
    public function testCreateOrUpdatePerusahaanWithNameOnly()
    {
        // Buat perusahaan baru dengan nama saja
        $perusahaan = ListPerusahaanTender::createOrUpdatePerusahaan('PT ABC COMPANY');
        
        $this->assertNotNull($perusahaan);
        $this->assertEquals('PT ABC COMPANY', $perusahaan->nama_peserta);
        $this->assertEquals('PT ABC COMPANY', $perusahaan->normalized_name);
        $this->assertNull($perusahaan->npwp);
        $this->assertNull($perusahaan->alamat);
        
        // Coba buat lagi dengan nama yang sama, seharus<PERSON> mengembalikan yang sudah ada
        $perusahaan2 = ListPerusahaanTender::createOrUpdatePerusahaan('PT ABC COMPANY');
        
        $this->assertEquals($perusahaan->id, $perusahaan2->id);
    }
    
    /**
     * Test createOrUpdatePerusahaan dengan nama dan NPWP (kasus Peserta Tender)
     */
    public function testCreateOrUpdatePerusahaanWithNameAndNpwp()
    {
        // Buat perusahaan baru dengan nama dan NPWP
        $perusahaan = ListPerusahaanTender::createOrUpdatePerusahaan(
            'PT XYZ COMPANY',
            '01.234.567.8-910.123'
        );
        
        $this->assertNotNull($perusahaan);
        $this->assertEquals('PT XYZ COMPANY', $perusahaan->nama_peserta);
        $this->assertEquals('01.234.567.8-910.123', $perusahaan->npwp);
        $this->assertNotNull($perusahaan->npwp_blur);
        $this->assertNotNull($perusahaan->partial_npwp);
        $this->assertNull($perusahaan->alamat);
        
        // Coba buat lagi dengan nama yang sama dan NPWP yang sama, seharusnya mengembalikan yang sudah ada
        $perusahaan2 = ListPerusahaanTender::createOrUpdatePerusahaan(
            'PT XYZ COMPANY',
            '01.234.567.8-910.123'
        );
        
        $this->assertEquals($perusahaan->id, $perusahaan2->id);
    }
    
    /**
     * Test createOrUpdatePerusahaan dengan nama, NPWP, dan alamat (kasus Pemenang Tender)
     */
    public function testCreateOrUpdatePerusahaanWithNameNpwpAndAddress()
    {
        // Buat perusahaan baru dengan nama, NPWP, dan alamat
        $perusahaan = ListPerusahaanTender::createOrUpdatePerusahaan(
            'PT DEF COMPANY',
            '02.345.678.9-012.345',
            'Jl. Contoh No. 123, Jakarta'
        );
        
        $this->assertNotNull($perusahaan);
        $this->assertEquals('PT DEF COMPANY', $perusahaan->nama_peserta);
        $this->assertEquals('02.345.678.9-012.345', $perusahaan->npwp);
        $this->assertEquals('Jl. Contoh No. 123, Jakarta', $perusahaan->alamat);
        
        // Coba buat lagi dengan nama yang sama, NPWP yang sama, tapi alamat berbeda
        // Seharusnya mengembalikan yang sudah ada dan mengupdate alamat
        $perusahaan2 = ListPerusahaanTender::createOrUpdatePerusahaan(
            'PT DEF COMPANY',
            '02.345.678.9-012.345',
            'Jl. Contoh Baru No. 456, Jakarta'
        );
        
        $this->assertEquals($perusahaan->id, $perusahaan2->id);
        $this->assertEquals('Jl. Contoh Baru No. 456, Jakarta', $perusahaan2->alamat);
    }
    
    /**
     * Test update data perusahaan yang sudah ada dengan data baru
     */
    public function testUpdateExistingPerusahaanWithNewData()
    {
        // Buat perusahaan baru dengan nama saja (kasus Ekatalog)
        $perusahaan = ListPerusahaanTender::createOrUpdatePerusahaan('PT GHI COMPANY');
        
        $this->assertNotNull($perusahaan);
        $this->assertNull($perusahaan->npwp);
        $this->assertNull($perusahaan->alamat);
        
        // Update dengan NPWP (kasus Peserta Tender)
        $perusahaan2 = ListPerusahaanTender::createOrUpdatePerusahaan(
            'PT GHI COMPANY',
            '03.456.789.0-123.456'
        );
        
        $this->assertEquals($perusahaan->id, $perusahaan2->id);
        $this->assertEquals('03.456.789.0-123.456', $perusahaan2->npwp);
        $this->assertNull($perusahaan2->alamat);
        
        // Update dengan alamat (kasus Pemenang Tender)
        $perusahaan3 = ListPerusahaanTender::createOrUpdatePerusahaan(
            'PT GHI COMPANY',
            '03.456.789.0-123.456',
            'Jl. Contoh No. 789, Surabaya'
        );
        
        $this->assertEquals($perusahaan->id, $perusahaan3->id);
        $this->assertEquals('03.456.789.0-123.456', $perusahaan3->npwp);
        $this->assertEquals('Jl. Contoh No. 789, Surabaya', $perusahaan3->alamat);
    }
    
    /**
     * Test normalisasi nama perusahaan
     */
    public function testNormalizeNameWithDifferentFormats()
    {
        // Format nama yang berbeda tapi seharusnya dianggap sama
        $perusahaan1 = ListPerusahaanTender::createOrUpdatePerusahaan('PT. ABC COMPANY');
        $perusahaan2 = ListPerusahaanTender::createOrUpdatePerusahaan('PT ABC COMPANY');
        $perusahaan3 = ListPerusahaanTender::createOrUpdatePerusahaan('ABC COMPANY, PT');
        
        $this->assertEquals($perusahaan1->id, $perusahaan2->id);
        $this->assertEquals($perusahaan1->id, $perusahaan3->id);
    }
    
    /**
     * Test penanganan NPWP blur
     */
    public function testHandleBlurredNpwp()
    {
        // Buat perusahaan dengan NPWP blur
        $perusahaan = ListPerusahaanTender::createOrUpdatePerusahaan(
            'PT JKL COMPANY',
            '0*.2**.3**.*-*45.**6'
        );
        
        $this->assertNotNull($perusahaan);
        $this->assertNull($perusahaan->npwp);
        $this->assertEquals('0*.2**.3**.*-*45.**6', $perusahaan->npwp_blur);
        $this->assertNotNull($perusahaan->partial_npwp);
        
        // Update dengan NPWP lengkap
        $perusahaan2 = ListPerusahaanTender::createOrUpdatePerusahaan(
            'PT JKL COMPANY',
            '04.234.345.6-456.789'
        );
        
        $this->assertEquals($perusahaan->id, $perusahaan2->id);
        $this->assertEquals('04.234.345.6-456.789', $perusahaan2->npwp);
    }
}
