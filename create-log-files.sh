#!/bin/bash

# Script to pre-create log files with proper permissions for Docker environment
# This prevents <PERSON><PERSON> from trying to set permissions when creating new daily log files

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Get current date and next few days
CURRENT_DATE=$(date +%Y-%m-%d)
TOMORROW=$(date -d "+1 day" +%Y-%m-%d)
DAY_AFTER=$(date -d "+2 days" +%Y-%m-%d)

# Log directories and files to create
LOG_DIRS=(
    "storage/logs/query"
    "storage/logs/performance"
)

LOG_FILES=(
    "storage/logs/query/slow_query-${CURRENT_DATE}.log"
    "storage/logs/query/slow_query-${TOMORROW}.log"
    "storage/logs/query/slow_query-${DAY_AFTER}.log"
    "storage/logs/query/critical_query-${CURRENT_DATE}.log"
    "storage/logs/query/critical_query-${TOMORROW}.log"
    "storage/logs/query/critical_query-${DAY_AFTER}.log"
    "storage/logs/performance/performance-${CURRENT_DATE}.log"
    "storage/logs/performance/performance-${TOMORROW}.log"
    "storage/logs/performance/performance-${DAY_AFTER}.log"
)

print_status "Creating log files for Docker environment..."

# Ensure directories exist
for dir in "${LOG_DIRS[@]}"; do
    if [[ ! -d "$dir" ]]; then
        mkdir -p "$dir"
        print_success "Created directory: $dir"
    fi
done

# Create log files with proper permissions
for file in "${LOG_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        touch "$file"
        chmod 666 "$file" 2>/dev/null || true
        # Try to change ownership to current user if possible
        chown $(whoami):$(id -gn) "$file" 2>/dev/null || true
        print_success "Created log file: $file"
    else
        # Fix ownership for existing files
        if [[ -O "$file" ]] || [[ $(stat -c %U "$file" 2>/dev/null) == "root" ]]; then
            chown $(whoami):$(id -gn) "$file" 2>/dev/null || true
        fi
        chmod 666 "$file" 2>/dev/null || true
        print_success "Updated permissions for: $file"
    fi
done

print_status "Log files created successfully!"
print_status "You can add this script to your container startup or cron job."
