# 📅 Scheduled Slow Query Monitoring Guide

## 🚀 Overview

Sistem monitoring slow query telah dikonfigurasi untuk ber<PERSON>lan otomatis setiap hari menggunakan Laravel 11 Task Scheduling. Se<PERSON><PERSON> tasks telah ditambahkan ke `routes/console.php` dan akan ber<PERSON>lan otomatis.

## ⏰ Scheduled Tasks

### 📊 **Daily Monitoring (Setiap Hari)**

#### **00:00 - Daily Slow Query Analysis**
```bash
php artisan query:analyze-slow --days=1 --threshold=100 --limit=50
```
- **Tujuan**: Analisis query lambat dari hari sebelumnya
- **Output**: `storage/logs/query/daily_analysis.log`
- **Threshold**: 100ms (queries lebih lambat dari 100ms)

#### **00:15 - Database Optimization Analysis**
```bash
php artisan query:optimize
```
- **Tujuan**: Analisis peluang optimasi database
- **Output**: `storage/logs/query/optimization_analysis.log`
- **Info**: Memberikan rekomendasi optimasi

#### **23:55 - Pre-create Tomorrow's Log Files**
```php
// Closure function untuk membuat file log hari berikutnya
```
- **Tujuan**: Mencegah permission errors untuk log files baru
- **Action**: Membuat file log dengan permission 666

### 📈 **Weekly Monitoring (Mingguan)**

#### **Sunday 00:30 - Weekly Comprehensive Analysis**
```bash
php artisan query:analyze-slow --days=7 --threshold=50 --limit=100
```
- **Tujuan**: Analisis mendalam query lambat seminggu terakhir
- **Output**: `storage/logs/query/weekly_analysis.log`
- **Threshold**: 50ms (lebih sensitif untuk analisis mingguan)

#### **Saturday 02:00 - Database Table Analysis**
```bash
php artisan query:apply-optimizations --analyze-tables
```
- **Tujuan**: Analisis statistik tabel dan rekomendasi maintenance
- **Output**: `storage/logs/query/table_analysis.log`

### 📅 **Monthly Monitoring (Bulanan)**

#### **1st of Month 01:00 - Monthly Deep Analysis**
```bash
php artisan query:analyze-slow --days=30 --threshold=25 --limit=200
```
- **Tujuan**: Analisis komprehensif performa bulanan
- **Output**: `storage/logs/query/monthly_analysis.log`
- **Threshold**: 25ms (sangat sensitif untuk trend analysis)

#### **1st of Month 03:00 - Log Cleanup**
```php
// Cleanup old log files (keep 30 days)
```
- **Tujuan**: Membersihkan log files lama (>30 hari)
- **Action**: Hapus file log lama untuk menghemat space

### 🔧 **Maintenance Tasks**

#### **Every 6 Hours - Permission Monitoring**
```bash
php artisan logs:monitor-permissions --fix
```
- **Schedule**: `0 */6 * * *` (00:00, 06:00, 12:00, 18:00)
- **Tujuan**: Monitor dan fix permission issues
- **Output**: `storage/logs/monitoring.log`

## 🛠️ Setup & Activation

### 1. **Pastikan Scheduler Berjalan**

Untuk Laravel 11, pastikan cron job berikut aktif di server:

```bash
# Tambahkan ke crontab
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
```

### 2. **Cek Scheduled Tasks**

```bash
# Lihat semua scheduled tasks
php artisan schedule:list

# Lihat hanya monitoring tasks
php artisan schedule:list | grep -E "(query|logs|monitoring)"
```

### 3. **Test Manual Execution**

```bash
# Test daily analysis
php artisan query:analyze-slow --days=1 --threshold=100 --limit=50

# Test optimization analysis
php artisan query:optimize --dry-run

# Test permission monitoring
php artisan logs:monitor-permissions --check-only
```

## 📊 Output Files & Locations

### **Daily Analysis Files:**
```
storage/logs/query/
├── daily_analysis.log          # Daily slow query analysis
├── weekly_analysis.log         # Weekly comprehensive analysis  
├── monthly_analysis.log        # Monthly deep analysis
├── optimization_analysis.log   # Daily optimization recommendations
└── table_analysis.log          # Weekly table statistics
```

### **Monitoring Files:**
```
storage/logs/
├── monitoring.log              # Permission monitoring activity
└── query/
    ├── slow_query-YYYY-MM-DD.log      # Raw slow query logs
    ├── critical_query-YYYY-MM-DD.log  # Critical queries (>2s)
    └── performance/
        └── performance-YYYY-MM-DD.log # Performance monitoring
```

## 📈 Expected Benefits

### **Daily Monitoring:**
- ✅ **Early Detection**: Identify performance issues within 24 hours
- ✅ **Trend Analysis**: Track daily performance patterns
- ✅ **Proactive Alerts**: Catch critical queries immediately

### **Weekly Analysis:**
- ✅ **Pattern Recognition**: Identify recurring performance issues
- ✅ **Optimization Planning**: Plan database improvements
- ✅ **Capacity Planning**: Monitor growth trends

### **Monthly Reviews:**
- ✅ **Strategic Planning**: Long-term performance strategy
- ✅ **Infrastructure Decisions**: Scale-up/scale-out planning
- ✅ **Historical Analysis**: Performance evolution tracking

## 🔔 Alerting & Notifications

### **Critical Query Alerts:**
Queries >2 seconds akan logged ke `critical_query.log`. Setup alerting:

```bash
# Example: Check for critical queries and send alert
if [[ -f "storage/logs/query/critical_query-$(date +%Y-%m-%d).log" ]]; then
    CRITICAL_COUNT=$(wc -l < "storage/logs/query/critical_query-$(date +%Y-%m-%d).log")
    if [[ $CRITICAL_COUNT -gt 0 ]]; then
        echo "WARNING: $CRITICAL_COUNT critical slow queries detected today!"
        # Send notification (email, Slack, etc.)
    fi
fi
```

### **Performance Degradation Alerts:**
Monitor trends dalam daily analysis untuk detect performance degradation.

## 🎯 Optimization Workflow

### **Daily Routine (Automated):**
1. **00:00** - Analyze yesterday's slow queries
2. **00:15** - Generate optimization recommendations
3. **23:55** - Prepare tomorrow's log files

### **Weekly Review (Manual):**
1. Review `weekly_analysis.log` untuk patterns
2. Implement recommended optimizations
3. Plan database maintenance

### **Monthly Planning (Strategic):**
1. Review `monthly_analysis.log` untuk trends
2. Plan infrastructure improvements
3. Update performance baselines

## 🚀 Advanced Configuration

### **Custom Thresholds:**
Edit `routes/console.php` untuk adjust thresholds:

```php
// More sensitive daily monitoring
Schedule::command('query:analyze-slow --days=1 --threshold=50 --limit=100')
    ->dailyAt('00:00');

// Less sensitive for high-traffic periods
Schedule::command('query:analyze-slow --days=1 --threshold=200 --limit=25')
    ->dailyAt('00:00');
```

### **Additional Monitoring:**
```php
// Monitor specific tables
Schedule::command('query:analyze-slow --days=1 --threshold=100 --table=peserta_tender')
    ->dailyAt('00:05');

// Critical hours monitoring
Schedule::command('query:analyze-slow --days=1 --threshold=50')
    ->cron('0 8,12,17 * * *'); // 8 AM, 12 PM, 5 PM
```

---

## ✅ Status

**ACTIVE**: ✅ Semua scheduled tasks telah dikonfigurasi dan siap berjalan otomatis!

**Next Steps:**
1. Pastikan cron job Laravel scheduler aktif
2. Monitor output files untuk memastikan tasks berjalan
3. Setup alerting untuk critical queries
4. Review weekly analysis untuk optimization opportunities

**Monitoring akan dimulai otomatis pada 00:00 hari berikutnya!** 🎉
