<?php

use Illuminate\Support\Facades\Schedule;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;


$tanggal_akhir = Carbon::now()->format('Y-m-d');
$tanggal_awal = Carbon::now()->subDays(10)->format('Y-m-d');
$tanggal_awal_direct = Carbon::now()->subDays(3)->format('Y-m-d');

// Schedule::command('sync:tender-recently')->hourlyAt(59);
// //Schedule::command('fetch:data-sirup')->hourlyAt(37);
// Schedule::command('fixkan:pagu-datasirup')->hourlyAt(29);
// Schedule::command('fixkan:all-pagu-datasirup')->hourlyAt(30);

// Jadwal untuk command yang sudah menggunakan CommandManagementTrait
// Tidak perlu pengecekan manual karena trait sudah menangani locking

// Command untuk scraping data SIRUP
Schedule::command('sirup:scrape --lock-name=sirup:scrape')->hourlyAt(12);

// Command untuk scraping detail tender
Schedule::command('scrape:tender-details --lock-name=scrape:tender-details')->hourlyAt(10);

// Update status tender setiap jam
Schedule::command('tender:update-status --lock-name=tender:update-status')->hourlyAt(25);

// Command untuk sinkronisasi peserta tender
Schedule::command('sync:peserta-tender --lock-name=sync:peserta-tender')->hourlyAt(40);

// Command untuk mengambil data tender via API
Schedule::command('fetch:tender-via-api --lock-name=fetch:tender-via-api')->twiceDailyAt(6, 9, 8);

// Command untuk scraping data ekatalog6
Schedule::command('scrape:ekatalog6 --lock-name=scrape:ekatalog6')->twiceDailyAt(7, 12, 19);

Schedule::command('subscriptions:update-expired')->dailyAt('00:05');
Schedule::command('sirup:fetch --lock-name=sirup:fetch')->twiceDaily(3, 13);

Schedule::command("update:perusahaan-statistik")->twiceDaily(11, 22);
Schedule::command("update:nilai --updateNULL=yes")->dailyAt('08:00');

Schedule::command("fetch:ekatalog-paralel {$tanggal_awal_direct} {$tanggal_akhir} --direct")->twiceDaily(6, 18);
Schedule::command("fetch:ekatalog {$tanggal_awal} {$tanggal_akhir}")->twiceDaily(5, 17);

// Kirim laporan metrik LPSE mingguan setiap hari Senin pukul 08:00
Schedule::command('lpse:metrics-report weekly --lock-name=lpse:metrics-report-weekly')->weeklyOn(1, '08:00');

// Kirim laporan metrik LPSE bulanan pada tanggal 1 setiap bulan pukul 09:00
Schedule::command('lpse:metrics-report monthly --lock-name=lpse:metrics-report-monthly')->monthlyOn(1, '09:00');

// WhatsApp monitoring schedule - check every 15 minutes and notify if there are issues
Schedule::command('whatsapp:monitor --notify --lock-name=whatsapp:monitor')
    ->everyFifteenMinutes()
    ->appendOutputTo(storage_path('logs/whatsapp/monitor.log'));

// Generate access statistics daily
Schedule::command('stats:generate --period=daily --days=30 --lock-name=stats:generate-daily')
    ->dailyAt('01:00')
    ->appendOutputTo(storage_path('logs/statistics/daily.log'));

// Generate access statistics weekly
Schedule::command('stats:generate --period=weekly --days=90 --lock-name=stats:generate-weekly')
    ->weeklyOn(1, '02:00') // Setiap hari Senin jam 2 pagi
    ->appendOutputTo(storage_path('logs/statistics/weekly.log'));

// Generate access statistics monthly
Schedule::command('stats:generate --period=monthly --days=365 --lock-name=stats:generate-monthly')
    ->monthlyOn(1, '03:00') // Setiap tanggal 1 jam 3 pagi
    ->appendOutputTo(storage_path('logs/statistics/monthly.log'));

// Recovery data DataSirup dari Meilisearch setiap 20 menit
Schedule::command('sirup:recover-from-meilisearch --force --chunk=500 --lock-name=sirup:recover-from-meilisearch')
    ->cron('*/20 * * * *')
    ->appendOutputTo(storage_path('logs/sirup/recovery.log'));

// Tidak perlu mendaftarkan command TestWhatsAppWelcomeMessage di sini
// karena sudah didefinisikan sebagai class Command di app/Console/Commands/TestWhatsAppWelcomeMessage.php
// dan Laravel 11 secara otomatis mendaftarkan semua command di direktori app/Console/Commands

// ============================================================================
// SLOW QUERY MONITORING & OPTIMIZATION SCHEDULES
// ============================================================================

// Daily slow query analysis at midnight (00:00)
Schedule::command('query:analyze-slow --days=1 --threshold=100 --limit=50 --lock-name=query:analyze-slow-daily')
    ->dailyAt('00:00')
    ->appendOutputTo(storage_path('logs/query/daily_analysis.log'));

// Weekly comprehensive analysis every Sunday at 00:30
Schedule::command('query:analyze-slow --days=7 --threshold=50 --limit=100 --lock-name=query:analyze-slow-weekly')
    ->weeklyOn(0, '00:30') // Sunday at 00:30
    ->appendOutputTo(storage_path('logs/query/weekly_analysis.log'));

// Monthly deep analysis on 1st of each month at 01:00
Schedule::command('query:analyze-slow --days=30 --threshold=25 --limit=200 --lock-name=query:analyze-slow-monthly')
    ->monthlyOn(1, '01:00')
    ->appendOutputTo(storage_path('logs/query/monthly_analysis.log'));

// Database optimization analysis every day at 00:15
Schedule::command('query:optimize --lock-name=query:optimize-daily')
    ->dailyAt('00:15')
    ->appendOutputTo(storage_path('logs/query/optimization_analysis.log'));

// Log permission monitoring every 6 hours
Schedule::command('logs:monitor-permissions --fix --lock-name=logs:monitor-permissions')
    ->cron('0 */6 * * *') // Every 6 hours
    ->appendOutputTo(storage_path('logs/monitoring.log'));

// Pre-create log files daily at 23:55 (before midnight analysis)
Schedule::call(function () {
    // Create log files for next day to prevent permission issues
    $tomorrow = now()->addDay()->format('Y-m-d');
    $logFiles = [
        storage_path("logs/query/slow_query-{$tomorrow}.log"),
        storage_path("logs/query/critical_query-{$tomorrow}.log"),
        storage_path("logs/performance/performance-{$tomorrow}.log"),
    ];

    foreach ($logFiles as $file) {
        if (!file_exists($file)) {
            @touch($file);
            @chmod($file, 0666);
        }
    }

    Log::info('Pre-created log files for tomorrow', ['date' => $tomorrow]);
})->dailyAt('23:55')->name('create-tomorrow-log-files');

// Database table analysis weekly on Saturday at 02:00
Schedule::command('query:apply-optimizations --analyze-tables --lock-name=query:apply-optimizations-weekly')
    ->weeklyOn(6, '02:00') // Saturday at 02:00
    ->appendOutputTo(storage_path('logs/query/table_analysis.log'));

// Clean old query logs monthly (keep 30 days)
Schedule::call(function () {
    $directories = [
        storage_path('logs/query'),
        storage_path('logs/performance'),
    ];

    foreach ($directories as $dir) {
        $files = glob($dir . '/*.log');
        foreach ($files as $file) {
            if (filemtime($file) < strtotime('-30 days')) {
                @unlink($file);
                Log::info('Cleaned old log file', ['file' => basename($file)]);
            }
        }
    }
})->monthlyOn(1, '03:00')->name('cleanup-old-query-logs');
