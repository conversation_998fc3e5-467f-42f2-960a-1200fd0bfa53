<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\MessageController;

// Admin Message Routes
Route::middleware(['auth', 'check.superadmin'])->group(function () {
    Route::prefix('admin/messages')->name('admin.messages.')->group(function () {
        Route::get('/', [MessageController::class, 'index'])->name('index');
        Route::post('/send', [MessageController::class, 'send'])->name('send');
        Route::get('/{id}/content', [MessageController::class, 'getMessageContent'])->name('content');
        Route::delete('/{id}', [MessageController::class, 'delete'])->name('delete');
        Route::delete('/delete-all/confirm', [MessageController::class, 'deleteAll'])->name('delete-all');
    });
});
