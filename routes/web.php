<?php

use Illuminate\Support\Facades\Route;

// Include admin message routes
require __DIR__.'/admin_messages.php';
use App\Http\Controllers\ManualTenderController;
use App\Http\Controllers\ManualScrapingController;
use App\Http\Controllers\WhatsAppIntegrationController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserSubscriptionController;
use App\Http\Controllers\UserManagementController;
use App\Http\Controllers\KlpdController;
use App\Http\Controllers\LpseController;
use App\Http\Controllers\TenderLpseController;
use App\Http\Controllers\ListPerusahaanController;
use App\Http\Controllers\CompanyAnalysisController;
use App\Http\Controllers\FollowController;
use Laravel\Fortify\Fortify;
use App\Http\Controllers\Auth\VerificationController;
use App\Http\Controllers\TelegramController;
use App\Http\Controllers\DataSirupController;
use App\Http\Controllers\EkatalogController;
use App\Http\Controllers\EkatalogAnalysisController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\WhatsAppAuthController;
use App\Http\Controllers\UrlHelperController;
use Illuminate\Support\Facades\Mail;
use App\Http\Controllers\SyncLogController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\SearchController;
use Illuminate\Http\Request;

Route::get('/tender/getUraianFile', [TenderLpseController::class, 'getUraianLink']);

// Landing Pages
Route::get('/', [App\Http\Controllers\LandingController::class, 'index'])->name('landing.index');
Route::get('/features', [App\Http\Controllers\LandingController::class, 'features'])->name('landing.features');
Route::get('/pricing', [App\Http\Controllers\LandingController::class, 'pricing'])->name('landing.pricing');
Route::get('/tender-public/{id}', [App\Http\Controllers\LandingController::class, 'tenderDetail'])->name('landing.tender.detail');
Route::get('/sirup-public/{id}', [App\Http\Controllers\LandingController::class, 'sirupDetail'])->name('landing.sirup.detail');

// Landing API endpoints for AJAX
Route::get('/api/landing/stats', [App\Http\Controllers\LandingController::class, 'getStats'])->name('landing.stats');
Route::get('/api/landing/latest-tenders', [App\Http\Controllers\LandingController::class, 'getLatestTenders'])->name('landing.latest-tenders');
Route::get('/api/landing/latest-sirup', [App\Http\Controllers\LandingController::class, 'getLatestSirup'])->name('landing.latest-sirup');

// Rute yang memerlukan autentikasi dan verifikasi
// check.subscription
Route::middleware(['auth', 'verified', 'log.page.access', 'update.last.seen'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/for-you', [App\Http\Controllers\ForYouController::class, 'index'])->name('for-you');
    Route::get('/home', [DashboardController::class, 'index'])->name('home');
    Route::put('/lpse/update', [LpseController::class, 'update'])->name('lpse.update');
    Route::get('/lpse/{kode_lpse}/dashboard', [LpseController::class, 'dashboard'])->name('lpse.dashboard');
    Route::get('/tender-umum', [TenderLpseController::class, 'index'])->name('tender_lpse.index');
    Route::get('/tender-hari-ini', [TenderLpseController::class, 'tenderHariIni'])->name('tender_lpse.hari_ini');
    Route::get('/tender-selesai', [TenderLpseController::class, 'TenderSelesai'])->name('tender_lpse.tenderSelesai');
    Route::get('/tender-gagal', [TenderLpseController::class, 'TenderGagal'])->name('tender_lpse.tenderGagal');
    Route::get('/tender-berjalan', [TenderLpseController::class, 'TenderOnProcess'])->name('tender_lpse.tenderOnProcess');
    Route::get('/tender/{kode_tender}', [TenderLpseController::class, 'show'])->name('tender_lpse.detail');
    Route::post('/tender/sync_peserta/{kode_tender}', [TenderLpseController::class, 'syncPeserta']);
    Route::post('/tender/sync_jadwal/{kode_tender}', [TenderLpseController::class, 'syncJadwal']);

    // Tender Analysis routes
    Route::get('/tender-analisis', [\App\Http\Controllers\TenderAnalysisController::class, 'index'])->name('tender.analysis');
    Route::get('/perusahaan', [ListPerusahaanController::class, 'index'])->name('perusahaan.index');
    Route::get('/perusahaan/{pembeda}', [ListPerusahaanController::class, 'show'])->name('perusahaan.show');
    Route::get('/perusahaan/{pembeda}/analisis', [CompanyAnalysisController::class, 'show'])->name('perusahaan.analysis');
    Route::get('/tender-count', [TenderLpseController::class, 'count'])->name('tender.lpse.count');
    Route::get('/search', [SearchController::class, 'search']);
    Route::get('/dashboard/data', [DashboardController::class, 'getData']);
    Route::get('/dashboard/chartTender', [DashboardController::class, 'getChartTenderData']);
    Route::get('/dashboard/chartEkatalog', [DashboardController::class, 'getChartEkatalogData']);
    Route::get('/dashboard/chartKategoriPekerjaan', [DashboardController::class, 'getChartKategoriPekerjaan']);
    Route::get('/dashboard/chartEkatStatusUMKM', [DashboardController::class, 'getChartStatusUMKMEkatalog']);
    Route::get('/dashboard/topTender', [DashboardController::class, 'getTopTenders']);
    Route::get('/dashboard/topPaketPengadaan', [DashboardController::class, 'topPaketPengadaan']);
    Route::get('/dashboard/topEkatalogByPeriod/{period}', [DashboardController::class, 'getTopEkatalogByPeriod']);
    Route::get('/dashboard/topTendersByPeriod/{period}', [DashboardController::class, 'getTopTendersByPeriod']);
    Route::get('/dashboard/topEkatalog6ByPeriod/{period}', [DashboardController::class, 'getTopEkatalog6ByPeriod']);
    Route::get('/test-npwp', [DashboardController::class, 'testNpwp']);

    // Rute untuk daftar SIRUP
    Route::get('/sirup', [DataSirupController::class, 'index'])->name('sirup.index');
    // Rute untuk detail SIRUP
    Route::get('/sirup/{id}', [DataSirupController::class, 'show'])->name('sirup.show');

    // Routes for logo upload
    Route::post('/lpse/{kd_lpse}/update-logo', [App\Http\Controllers\LogoController::class, 'uploadLogoLpse'])->name('lpse.updateLogo');
    Route::post('/klpd/{kd_klpd}/update-logo', [App\Http\Controllers\LogoController::class, 'uploadLogoKlpd'])->name('klpd.updateLogo');

    // Route for uploading logo by image URL from Bing search
    Route::post('/api/upload-logo', [App\Http\Controllers\LogoController::class, 'upload']);

    // Telegram routes
    Route::get('/link-telegram', [TelegramController::class, 'requestTelegramLink'])->middleware('auth')->name('telegram.link');
    Route::get('/disconnect-telegram', [TelegramController::class, 'disconnectTelegram'])->middleware('auth')->name('telegram.disconnect');

    // QR Code routes
    Route::get('/qrcode', [App\Http\Controllers\QrCodeController::class, 'generate'])->name('qrcode.generate');

    // Route for logo search API
    Route::get('/api/logo-search', [App\Http\Controllers\LogoController::class, 'search']);

    // Ekatalog routes
    Route::get('ekatalog', [EkatalogController::class, 'index'])->name('ekatalog.index');
    Route::get('ekatalog/{id}', [EkatalogController::class, 'show'])->name('ekatalog.show');
    Route::get('ekatalog6', [\App\Http\Controllers\Ekatalog6Controller::class, 'index'])->name('ekatalog6.index');
    Route::get('ekatalog-kalender', function () {
        return view('tenderid.ekatalog.kalender'); // resources/views/ekatalog.blade.php
    })->name('ekatalog.kalender');

    // Ekatalog Analysis routes
    Route::get('ekatalog-analisis', [EkatalogAnalysisController::class, 'index'])->name('ekatalog.analysis');

    // Ekatalog Analysis API routes
    Route::prefix('api/ekatalog-analisis')->group(function () {
        Route::get('statistics', [EkatalogAnalysisController::class, 'getStatistics'])->name('api.ekatalog.statistics');
        Route::get('monthly-trend', [EkatalogAnalysisController::class, 'getMonthlyTrend'])->name('api.ekatalog.monthly-trend');
        Route::get('top-products', [EkatalogAnalysisController::class, 'getTopProducts'])->name('api.ekatalog.top-products');
        Route::get('top-vendors', [EkatalogAnalysisController::class, 'getTopVendors'])->name('api.ekatalog.top-vendors');
        Route::get('top-executors', [EkatalogAnalysisController::class, 'getTopExecutors'])->name('api.ekatalog.top-executors');
        Route::get('top-brands', [EkatalogAnalysisController::class, 'getTopBrands'])->name('api.ekatalog.top-brands');
        Route::get('top-categories', [EkatalogAnalysisController::class, 'getTopCategories'])->name('api.ekatalog.top-categories');
        Route::get('top-buyers', [EkatalogAnalysisController::class, 'getTopBuyers'])->name('api.ekatalog.top-buyers');
    });

    // Helper API routes
    Route::prefix('api/helper')->group(function () {
        Route::get('company-urls', [UrlHelperController::class, 'getCompanyUrls'])->name('api.helper.company-urls');
    });

    // Ekatalog Analytics routes
    Route::get('ekatalog-analytics', [\App\Http\Controllers\EkatalogAnalyticsController::class, 'index'])->name('ekatalog.analytics');
    Route::get('ekatalog-analytics/kategori/{kategori}', [\App\Http\Controllers\EkatalogAnalyticsController::class, 'kategoriDetail'])->name('ekatalog.analytics.kategori');

    // Ekatalog Analytics AJAX routes
    Route::get('ekatalog-analytics-ajax', [\App\Http\Controllers\EkatalogAnalyticsAjaxController::class, 'index'])->name('ekatalog.analytics.ajax');

    // Ekatalog Prinsipal Analysis routes
    Route::get('ekatalog-prinsipal', [\App\Http\Controllers\PrinsipalAnalysisController::class, 'index'])->name('ekatalog.prinsipal');

    // API endpoints for AJAX - moved outside middleware to ensure accessibility
});

// API endpoints for AJAX - outside middleware to ensure accessibility
Route::prefix('api')->group(function () {
    // Tender Analysis AJAX API
    Route::get('tender-analysis/stats', [\App\Http\Controllers\TenderAnalysisController::class, 'getStats'])->name('tender.analysis.ajax.stats');
    Route::get('tender-analysis/trends', [\App\Http\Controllers\TenderAnalysisController::class, 'getTenderTrends'])->name('tender.analysis.ajax.trends');
    Route::get('tender-analysis/category', [\App\Http\Controllers\TenderAnalysisController::class, 'getTenderByCategory'])->name('tender.analysis.ajax.category');
    Route::get('tender-analysis/method', [\App\Http\Controllers\TenderAnalysisController::class, 'getTenderByMethod'])->name('tender.analysis.ajax.method');
    Route::get('tender-analysis/top-lpse', [\App\Http\Controllers\TenderAnalysisController::class, 'getTopLpse'])->name('tender.analysis.ajax.top-lpse');
    Route::get('tender-analysis/active-companies', [\App\Http\Controllers\TenderAnalysisController::class, 'getMostActiveCompanies'])->name('tender.analysis.ajax.active-companies');
    Route::get('tender-analysis/top-winners', [\App\Http\Controllers\TenderAnalysisController::class, 'getTopWinners'])->name('tender.analysis.ajax.top-winners');
    Route::get('tender-analysis/fraud-analysis', [\App\Http\Controllers\TenderAnalysisController::class, 'getFraudAnalysis'])->name('tender.analysis.ajax.fraud-analysis');
    Route::get('tender-analysis/duration', [\App\Http\Controllers\TenderAnalysisController::class, 'getTenderDurationAnalysis'])->name('tender.analysis.ajax.duration');
    Route::get('tender-analysis/value-distribution', [\App\Http\Controllers\TenderAnalysisController::class, 'getTenderValueDistribution'])->name('tender.analysis.ajax.value-distribution');
    Route::get('tender-analysis/schedule', [\App\Http\Controllers\TenderAnalysisController::class, 'getTenderScheduleAnalysis'])->name('tender.analysis.ajax.schedule');
    Route::get('tender-analysis/regional', [\App\Http\Controllers\TenderAnalysisController::class, 'getRegionalAnalysis'])->name('tender.analysis.ajax.regional');

    // Ekatalog Analytics AJAX API
    Route::get('ekatalog-analytics-ajax/stats', [\App\Http\Controllers\EkatalogAnalyticsAjaxController::class, 'getStats'])->name('ekatalog.analytics.ajax.stats');
    Route::get('ekatalog-analytics-ajax/tren-bulanan', [\App\Http\Controllers\EkatalogAnalyticsAjaxController::class, 'getTrenBulanan'])->name('ekatalog.analytics.ajax.tren-bulanan');
    Route::get('ekatalog-analytics-ajax/top-produk-kategori', [\App\Http\Controllers\EkatalogAnalyticsAjaxController::class, 'getTopProdukKategori'])->name('ekatalog.analytics.ajax.top-produk-kategori');
    Route::get('ekatalog-analytics-ajax/top-instansi-penyedia', [\App\Http\Controllers\EkatalogAnalyticsAjaxController::class, 'getTopInstansiPenyedia'])->name('ekatalog.analytics.ajax.top-instansi-penyedia');
    Route::get('ekatalog-analytics-ajax/distribusi', [\App\Http\Controllers\EkatalogAnalyticsAjaxController::class, 'getDistribusi'])->name('ekatalog.analytics.ajax.distribusi');
    Route::get('ekatalog-analytics-ajax/kategori-detail', [\App\Http\Controllers\EkatalogAnalyticsAjaxController::class, 'getKategoriDetail'])->name('ekatalog.analytics.ajax.kategori.detail');

    // Ekatalog Prinsipal Analysis API
    Route::get('ekatalog-prinsipal/stats', [\App\Http\Controllers\PrinsipalAnalysisController::class, 'getStats'])->name('ekatalog.prinsipal.stats');
    Route::get('ekatalog-prinsipal/top-products', [\App\Http\Controllers\PrinsipalAnalysisController::class, 'getTopProducts'])->name('ekatalog.prinsipal.top-products');
    Route::get('ekatalog-prinsipal/top-buyers', [\App\Http\Controllers\PrinsipalAnalysisController::class, 'getTopBuyers'])->name('ekatalog.prinsipal.top-buyers');
    Route::get('ekatalog-prinsipal/top-distributors', [\App\Http\Controllers\PrinsipalAnalysisController::class, 'getTopDistributors'])->name('ekatalog.prinsipal.top-distributors');
    Route::get('ekatalog-prinsipal/top-brands', [\App\Http\Controllers\PrinsipalAnalysisController::class, 'getTopBrands'])->name('ekatalog.prinsipal.top-brands');
    Route::get('ekatalog-prinsipal/prinsipal-list', [\App\Http\Controllers\PrinsipalAnalysisController::class, 'getPrinsipalList'])->name('ekatalog.prinsipal.prinsipal-list');
});

// SIRUP AJAX endpoints - outside middleware to ensure accessibility
Route::get('/sirup/status/{kode_rup}', [DataSirupController::class, 'getStatusPaket'])->name('sirup.status');
Route::get('/sirup/pagu/{rup_id}', [DataSirupController::class, 'fixkanPagu'])->name('sirup.pagu');

// Route for kategori detail page - needs to be outside middleware
Route::get('ekatalog-analytics-ajax/kategori/{kategori}', [\App\Http\Controllers\EkatalogAnalyticsAjaxController::class, 'kategoriDetail'])->name('ekatalog.analytics.ajax.kategori');

Route::middleware(['auth', 'check.superadmin'])->group(function () {
        // Financial Journal
        Route::get('/admin/financial-journal', [App\Http\Controllers\Admin\FinancialJournalController::class, 'index'])
            ->name('admin.financial_journal');

        // Expense Management
        Route::prefix('admin/expenses')->name('admin.expenses.')->group(function () {
            Route::get('/create', [App\Http\Controllers\Admin\FinancialJournalController::class, 'createExpense'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\FinancialJournalController::class, 'storeExpense'])->name('store');
            Route::get('/{expense}/edit', [App\Http\Controllers\Admin\FinancialJournalController::class, 'editExpense'])->name('edit');
            Route::put('/{expense}', [App\Http\Controllers\Admin\FinancialJournalController::class, 'updateExpense'])->name('update');
            Route::delete('/{expense}', [App\Http\Controllers\Admin\FinancialJournalController::class, 'destroyExpense'])->name('destroy');
        });

        // Admin Withdrawal Management
        Route::prefix('admin/withdrawals')->name('admin.withdrawals.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\WithdrawalController::class, 'index'])->name('index');
            Route::post('/{withdrawal}/approve', [App\Http\Controllers\Admin\WithdrawalController::class, 'approve'])->name('approve');
            Route::post('/{withdrawal}/reject', [App\Http\Controllers\Admin\WithdrawalController::class, 'reject'])->name('reject');
        });

        // Email Logs Management
        Route::prefix('admin/email-logs')->name('admin.email_logs.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\EmailLogController::class, 'index'])->name('index');
            Route::get('/{id}', [App\Http\Controllers\Admin\EmailLogController::class, 'show'])->name('show');
            Route::post('/{id}/resend', [App\Http\Controllers\Admin\EmailLogController::class, 'resend'])->name('resend');
            Route::delete('/{id}', [App\Http\Controllers\Admin\EmailLogController::class, 'destroy'])->name('destroy');
        });

        Route::get('/sync_log', [SyncLogController::class, 'index'])->name('sync_log');
        Route::get('/ekatalog_nol', [EkatalogController::class, 'paketTanpaNilai'])->name('ekatalog.nol');
        Route::get('/ekatalog_nol2', [EkatalogController::class, 'paketTanpaNilai2'])->name('ekatalog.nol');
        Route::get('/user/activity-logs', [UserController::class, 'activityLogs'])->name('user.activity-logs');
        Route::get('/user/online', [UserController::class, 'onlineUsers'])->name('user.online');
        Route::get('/user/login-durations', [UserController::class, 'loginDurations'])->name('user.login-durations');
        // KLPD Management
        Route::prefix('klpd')->name('klpd.')->group(function () {
            Route::get('/', [KlpdController::class, 'index'])->name('index');
            Route::get('/create', [KlpdController::class, 'create'])->name('create');
            Route::post('/', [KlpdController::class, 'store'])->name('store');
            Route::get('/{klpd}/edit', [KlpdController::class, 'edit'])->name('edit');
            Route::put('/{klpd}', [KlpdController::class, 'update'])->name('update');
            Route::delete('/{klpd}', [KlpdController::class, 'destroy'])->name('destroy');
            Route::get('/fix-data', [KlpdController::class, 'fixKlpdData'])->name('fix-data');
        });
        // LPSE Management
        Route::prefix('lpse')->name('lpse.')->group(function () {
            Route::get('/', [LpseController::class, 'index'])->name('index');
            Route::get('/create', [LpseController::class, 'create'])->name('create');
            Route::post('/', [LpseController::class, 'store'])->name('store');
            Route::get('/{lpse}/edit', [LpseController::class, 'edit'])->name('edit');
            Route::put('/{lpse}', [LpseController::class, 'update'])->name('update');
            Route::delete('/{lpse}', [LpseController::class, 'destroy'])->name('destroy');
            Route::post('/{lpse}/update-logo', [LpseController::class, 'updateLogo'])->name('update-logo');
        });

        // User management routes
        Route::prefix('admin/users')->name('admin.users.')->group(function () {
            Route::get('/', [UserManagementController::class, 'index'])->name('index');
            Route::get('/{id}', [UserManagementController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [UserManagementController::class, 'edit'])->name('edit');
            Route::put('/{id}', [UserManagementController::class, 'update'])->name('update');
            Route::delete('/{id}', [UserManagementController::class, 'destroy'])->name('destroy');
        });

        // Subscription package management routes
        Route::prefix('admin/subscriptions')->name('admin.subscriptions.')->group(function () {
            Route::get('/', [\App\Http\Controllers\SubscriptionPackageController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\SubscriptionPackageController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\SubscriptionPackageController::class, 'store'])->name('store');
            Route::get('/{subscription}/edit', [\App\Http\Controllers\SubscriptionPackageController::class, 'edit'])->name('edit');
            Route::put('/{subscription}', [\App\Http\Controllers\SubscriptionPackageController::class, 'update'])->name('update');
            Route::delete('/{subscription}', [\App\Http\Controllers\SubscriptionPackageController::class, 'destroy'])->name('destroy');
            Route::post('/{subscription}/toggle-active', [\App\Http\Controllers\SubscriptionPackageController::class, 'toggleActive'])->name('toggle-active');
        });

        // Admin user subscriptions management
        Route::prefix('admin/user-subscriptions')->name('admin.user_subscriptions.')->group(function () {
            Route::get('/', [\App\Http\Controllers\UserSubscriptionController::class, 'adminIndex'])->name('index');
            Route::post('/{userSubscription}/approve', [\App\Http\Controllers\UserSubscriptionController::class, 'approvePayment'])->name('approve');
            Route::post('/{userSubscription}/reject', [\App\Http\Controllers\UserSubscriptionController::class, 'rejectPayment'])->name('reject');
            Route::delete('/{userSubscription}/delete', [\App\Http\Controllers\UserSubscriptionController::class, 'deleteSubscription'])->name('delete');
            Route::delete('/bulk-delete', [\App\Http\Controllers\UserSubscriptionController::class, 'bulkDeleteSubscriptions'])->name('bulk-delete');
        });
    });

Route::middleware(['auth'])->group(function () {
    Route::get('/subscriptions', [UserSubscriptionController::class, 'index'])->name('subscriptions.index');

    // Profile routes
    Route::get('/profile', [ProfileController::class, 'index'])->name('tenderid.profile.index');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('tenderid.profile.edit');
    Route::post('/profile/update', [ProfileController::class, 'update'])->name('tenderid.profile.update');
    Route::get('/profile/change-password', [ProfileController::class, 'showChangePasswordForm'])->name('profile.change-password');
    Route::put('/profile/update-password', [ProfileController::class, 'updatePassword'])->name('profile.update-password');

    // Notification Routes
    Route::get('/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::get('/notifications/mark-as-read/{id}', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('notifications.markAsRead');
    Route::get('/notifications/mark-all-as-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.markAllAsRead');
    Route::get('/notifications/delete/{id}', [App\Http\Controllers\NotificationController::class, 'destroy'])->name('notifications.destroy');
    Route::get('/notifications/delete-all', [App\Http\Controllers\NotificationController::class, 'destroyAll'])->name('notifications.destroyAll');
    Route::get('/my-subscriptions', [UserSubscriptionController::class, 'mySubscriptions'])->name('subscriptions.my');
    Route::get('/subscriptions/select', [UserSubscriptionController::class, 'selectSubscription'])->name('subscriptions.select');
    Route::post('/subscriptions/invoice', [UserSubscriptionController::class, 'generateInvoice'])->name('subscriptions.invoice');
    Route::get('/subscriptions/invoice/{userSubscription}', [UserSubscriptionController::class, 'showInvoice'])->name('subscriptions.invoice.show');
    Route::get('/subscriptions/invoice/{userSubscription}/pdf', [UserSubscriptionController::class, 'exportInvoicePdf'])->name('subscriptions.invoice.pdf');
    Route::get('/subscriptions/invoice/{userSubscription}/print', [UserSubscriptionController::class, 'showPrintInvoice'])->name('subscriptions.invoice.print');
    Route::get('/subscriptions/{userSubscription}/confirm-payment', [UserSubscriptionController::class, 'confirmPaymentForm'])->name('payments.confirm.form');
    Route::post('/subscriptions/{userSubscription}/confirm-payment', [UserSubscriptionController::class, 'confirmPayment'])->name('payments.confirm.submit');
    Route::get('/withdrawals/request', [UserSubscriptionController::class, 'withdrawalForm'])->name('withdrawals.request');
    Route::post('/withdrawals/request', [UserSubscriptionController::class, 'requestWithdrawal'])->name('withdrawals.submit');
    Route::get('/withdrawals/history', [UserSubscriptionController::class, 'withdrawalHistory'])->name('withdrawals.history');
    Route::get('/commissions/history', [UserSubscriptionController::class, 'commissionHistory'])->name('commissions.history');

    Route::post('/follow/tender/{kodeTender}', [FollowController::class, 'followTender'])->name('follow.tender');
    Route::post('/unfollow/tender/{kodeTender}', [FollowController::class, 'unfollowTender'])->name('unfollow.tender');
    Route::post('/follow/lpse/{kdLpse}', [FollowController::class, 'followLpse'])->name('follow.lpse');
    Route::post('/unfollow/lpse/{kdLpse}', [FollowController::class, 'unfollowLpse'])->name('unfollow.lpse');
    Route::post('/follow/sirup/{kodeRup}', [FollowController::class, 'followSirup'])->name('follow.sirup');
    Route::post('/unfollow/sirup/{kodeRup}', [FollowController::class, 'unfollowSirup'])->name('unfollow.sirup');

    Route::get('/monitor/queue/status', function () {
        $status = shell_exec('ps aux | grep "artisan queue:work" | grep -v "grep"');
        return response()->json(['running' => !empty($status)]);
    });

    Route::post('/monitor/queue/start', function (Request $request) {
        try {
            // Run the queue worker as a background process
            // Adjust the command as needed for your environment
            $command = 'php ' . base_path('artisan') . ' queue:work --queue=default,notifications > /dev/null 2>&1 &';
            exec($command);

            return response()->json(['status' => 'success', 'message' => 'Queue worker started in background']);
        } catch (Exception $e) {
            Log::error('Queue worker failed to start.', ['error' => $e->getMessage()]);
            return response()->json(['status' => 'error', 'message' => 'Failed to start queue worker'], 500);
        }
    });

    Route::get('/monitor/queue/jobs', function () {
        // Batasi jumlah queue yang ditampilkan untuk mengurangi beban server
        return response()->json(DB::table('jobs')->orderBy('id', 'desc')->limit(50)->get());
    });

    Route::post('/monitor/queue/retry-failed', function () {
        Artisan::call('queue:retry', ['id' => 'all']); // Retry semua job yang gagal
        return response()->json(['message' => 'Semua job yang gagal telah dicoba kembali.']);
    });

    Route::get('/monitor/queue/failed-jobs', function () {
        // Batasi jumlah failed jobs yang ditampilkan untuk mengurangi beban server
        $failedJobs = DB::table('failed_jobs')
            ->select('id', 'queue', 'payload', 'exception', 'failed_at')
            ->orderBy('failed_at', 'desc')
            ->limit(50)
            ->get();
        return response()->json($failedJobs);
    });

    Route::get('/monitor/queue', function () {
        return view('tenderid.monitoring.queue');
    })->name('monitoring.queue');

    // Manual Tender Data Input Routes
    Route::get('/manual-tender', [ManualTenderController::class, 'showForm'])->name('manual.tender.form');
    Route::post('/manual-tender', [ManualTenderController::class, 'processData'])->name('manual.tender.process');
    Route::get('/manual-tender-auto', [ManualTenderController::class, 'showAutoFetchForm'])->name('manual.tender.auto.form');
});

//Route::post('/api/telegram/webhook', [TelegramController::class, 'handleWebhook']);
Route::get('/test-telegram/{userId}', function ($userId) {
    $user = \App\Models\User::find($userId);
    if (!$user) {
        return "User not found";
    }
    if (!$user->telegram_bot) {
        return "User doesn't have a Telegram chat ID";
    }
    \Illuminate\Support\Facades\Notification::send($user, new \App\Notifications\TenderLpseStatusNotification(new \App\Models\TenderLpse()));
    return "Test notification sent";
});

Route::get('/login', function () {
    return view('tenderid.auth.login');
})->middleware('guest')->name('login');

Route::get('/register', function () {
    return view('tenderid.auth.register');
})->middleware('guest')->name('register');

// Route untuk logout
Route::post('/logout', function () {
    // Simpan user_id di session sebelum logout untuk digunakan oleh LogPageAccess middleware
    if (auth()->check()) {
        session(['logged_user_id' => auth()->id()]);
    }
    auth()->logout();
    return redirect('/login'); // Halaman login setelah logout
})->name('logout');

// Rute untuk verifikasi email (tanpa middleware auth)
Route::get('email/verify/{id}/{hash}', [VerificationController::class, 'verify'])
    ->middleware(['signed'])
    ->name('verification.verify');

// Rute untuk mengirim ulang tautan verifikasi (tanpa middleware auth)
Route::match(['get', 'post'], '/email/resend', [VerificationController::class, 'resend'])
    ->middleware('throttle:6,1') // Throttle untuk membatasi permintaan
    ->name('verification.resend');

// Rute untuk tampilan pemberitahuan verifikasi
Route::get('/email/verify', function () {
    return view('tenderid.auth.notice');
})->name('verification.notice');

// Rute untuk halaman sukses verifikasi
Route::get('email/verify/success', function () {
    return view('tenderid.auth.verification-success');
})->name('verification.success');

// Route untuk menampilkan form "Forgot Password"
Route::get('/forgot-password', function () {
    return view('tenderid.auth.forgot-password');
})->middleware('guest')->name('password.request');

// Route untuk menampilkan form "Reset Password"
Route::get('/reset-password/{token}', function ($token) {
    return view('tenderid.auth.reset-password', ['token' => $token]);
})->middleware('guest')->name('password.reset');

Route::prefix('whatsapp')->group(function () {
    Route::get('/qr-api', [WhatsAppIntegrationController::class, 'getQR'])->name('whatsapp.qr.api');
    Route::get('/status', [WhatsAppIntegrationController::class, 'getStatus'])->name('whatsapp.status');
    Route::post('/message/send', [WhatsAppIntegrationController::class, 'sendMessage'])->name('whatsapp.message.send');
    Route::post('/message/welcome', [WhatsAppIntegrationController::class, 'sendWelcomeMessage'])->name('whatsapp.message.welcome');
    // Route::get('/test-send', [WhatsAppIntegrationController::class, 'sendTestMessage'])->name('whatsapp.test.send'); // Dinonaktifkan untuk menghindari pengiriman ganda

    // WhatsApp Monitoring Routes - Hanya untuk Super Admin
    Route::middleware(['auth', 'check.superadmin'])->group(function () {
        Route::get('/monitoring', [App\Http\Controllers\WhatsAppMonitoringController::class, 'dashboard'])->name('whatsapp.monitoring');
        Route::get('/logs', [App\Http\Controllers\WhatsAppMonitoringController::class, 'viewLogs'])->name('whatsapp.logs');
        Route::post('/test-notification', [App\Http\Controllers\WhatsAppMonitoringController::class, 'sendTestNotification'])->name('whatsapp.test.notification');
    });
});



// Environment Configuration Routes
Route::middleware(['auth', 'check.superadmin'])->group(function () {
    Route::get('/admin/env-config', [App\Http\Controllers\EnvConfigController::class, 'index'])->name('env.config.index');
    Route::put('/admin/env-config', [App\Http\Controllers\EnvConfigController::class, 'update'])->name('env.config.update');

    // Command Monitoring Routes
    Route::get('/admin/commands', [App\Http\Controllers\CommandMonitoringController::class, 'index'])->name('admin.commands.index');
    Route::get('/admin/commands/progress', [App\Http\Controllers\CommandMonitoringController::class, 'getProgress'])->name('admin.commands.progress');
    Route::get('/admin/commands/all-progress', [App\Http\Controllers\CommandMonitoringController::class, 'getAllProgress'])->name('admin.commands.all-progress');
    Route::get('/admin/commands/running', [App\Http\Controllers\CommandMonitoringController::class, 'getRunningCommands'])->name('admin.commands.running');

    // System Error Monitoring Routes
    Route::prefix('admin/system-errors')->name('admin.system-errors.')->group(function () {
        Route::get('/', [App\Http\Controllers\SystemErrorController::class, 'index'])->name('index');
        Route::get('/{systemError}', [App\Http\Controllers\SystemErrorController::class, 'show'])->name('show');
        Route::post('/{systemError}/resolve', [App\Http\Controllers\SystemErrorController::class, 'resolve'])->name('resolve');
        Route::post('/bulk-resolve', [App\Http\Controllers\SystemErrorController::class, 'bulkResolve'])->name('bulk-resolve');
        Route::delete('/{systemError}', [App\Http\Controllers\SystemErrorController::class, 'destroy'])->name('destroy');
        Route::delete('/resolved/destroy', [App\Http\Controllers\SystemErrorController::class, 'destroyResolved'])->name('destroy-resolved');
    });

    // Manual Scraping Routes
    Route::prefix('admin/manual-scraping')->name('admin.manual-scraping.')->group(function () {
        Route::get('/', [App\Http\Controllers\ManualScrapingController::class, 'index'])->name('index');
        Route::post('/tender-details', [App\Http\Controllers\ManualScrapingController::class, 'processTenderDetails'])->name('process-tender-details');
        Route::post('/jadwal-tender', [App\Http\Controllers\ManualScrapingController::class, 'processJadwalTender'])->name('process-jadwal-tender');
        Route::post('/peserta-tender', [App\Http\Controllers\ManualScrapingController::class, 'processPesertaTender'])->name('process-peserta-tender');
        Route::post('/pemenang-tender', [App\Http\Controllers\ManualScrapingController::class, 'processPemenangTender'])->name('process-pemenang-tender');
    });

    // User Access Statistics Routes
    Route::prefix('admin/statistics')->name('admin.statistics.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\StatisticsController::class, 'index'])->name('index');
        Route::get('/export-csv', [App\Http\Controllers\Admin\StatisticsController::class, 'exportCsv'])->name('export-csv');
    });

    // Slow Query Analysis Routes
    Route::prefix('admin/slow-query')->name('admin.slow-query.')->group(function () {
        Route::get('/', [App\Http\Controllers\SlowQueryAnalysisController::class, 'index'])->name('index');
        Route::get('/analysis', [App\Http\Controllers\SlowQueryAnalysisController::class, 'getAnalysisData'])->name('analysis');
    });
});

// Fallback route for 404 errors
Route::fallback(function () {
    return response()->view('errors.404', [], 404);
});