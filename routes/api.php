<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\TelegramController;
use App\Http\Controllers\EkatalogController;
use App\Http\Controllers\TenderLpseController;
use App\Http\Controllers\WhatsAppAuthController;

// Route untuk webhook Telegram tanpa middleware apapun
Route::post('/telegram/webhook', [TelegramController::class, 'handleWebhook'])
    ->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class]); // Explicitly remove CSRF

Route::get('/paket-pengadaan', [EkatalogController::class, 'getPerubahanData']);

// API untuk WhatsApp Bot
Route::prefix('whatsapp')->group(function () {
    Route::get('/tenders/search', [TenderLpseController::class, 'searchForWhatsApp']);
    Route::get('/tenders/{id}', [TenderLpseController::class, 'showForWhatsApp']);
});

Route::post('/save-qr', [WhatsAppAuthController::class, 'saveQrCode']);



// Hapus route webhook dari web.php jika masih ada