#!/bin/bash

# Setup Laravel 11 Task Scheduler for Slow Query Monitoring
# This script configures cron job to run Laravel scheduler

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the current directory (Laravel project root)
PROJECT_ROOT=$(pwd)
PHP_PATH=$(which php 2>/dev/null || echo "/usr/local/bin/php")

print_status "🚀 Setting up Laravel 11 Task Scheduler..."
print_status "Project: $PROJECT_ROOT"
echo

# Function to check if we're in a Laravel project
check_laravel_project() {
    if [[ ! -f "artisan" ]]; then
        print_error "This doesn't appear to be a Laravel project (artisan file not found)"
        exit 1
    fi
    
    if [[ ! -f "routes/console.php" ]]; then
        print_error "routes/console.php not found. This might not be Laravel 11."
        exit 1
    fi
    
    print_success "Laravel 11 project detected"
}

# Function to test PHP and artisan
test_php_artisan() {
    print_status "Testing PHP and artisan..."
    
    if ! command -v php &> /dev/null; then
        print_warning "PHP command not found in PATH"
        print_status "Trying alternative PHP paths..."
        
        # Try common PHP paths
        for php_path in "/usr/local/bin/php" "/usr/bin/php" "/opt/php/bin/php"; do
            if [[ -x "$php_path" ]]; then
                PHP_PATH="$php_path"
                print_success "Found PHP at: $PHP_PATH"
                break
            fi
        done
    else
        print_success "PHP found at: $(which php)"
    fi
    
    # Test artisan
    if $PHP_PATH artisan --version &>/dev/null; then
        local version=$($PHP_PATH artisan --version)
        print_success "Artisan is working: $version"
    else
        print_error "Artisan is not working properly"
        exit 1
    fi
}

# Function to check scheduled tasks
check_scheduled_tasks() {
    print_status "Checking scheduled tasks..."
    
    # Test schedule:list command
    if $PHP_PATH artisan schedule:list &>/dev/null; then
        print_success "Schedule system is working"
        
        # Count monitoring tasks
        local monitoring_tasks=$($PHP_PATH artisan schedule:list 2>/dev/null | grep -E "(query|logs|monitoring)" | wc -l || echo "0")
        print_status "Found $monitoring_tasks monitoring-related scheduled tasks"
        
        if [[ $monitoring_tasks -gt 0 ]]; then
            print_success "Slow query monitoring tasks are configured"
        else
            print_warning "No monitoring tasks found. Check routes/console.php"
        fi
    else
        print_error "Schedule system is not working properly"
        exit 1
    fi
}

# Function to setup cron job
setup_cron_job() {
    print_status "Setting up cron job for Laravel scheduler..."
    
    # Check if cron is available
    if ! command -v crontab &> /dev/null; then
        print_warning "Crontab not available. Manual setup required."
        show_manual_setup
        return
    fi
    
    # Create cron entry
    local cron_entry="* * * * * cd $PROJECT_ROOT && $PHP_PATH artisan schedule:run >> /dev/null 2>&1"
    
    # Check if entry already exists
    if crontab -l 2>/dev/null | grep -q "artisan schedule:run"; then
        print_warning "Laravel scheduler cron job already exists"
        print_status "Current cron entries for Laravel:"
        crontab -l 2>/dev/null | grep "artisan schedule:run" || true
        return
    fi
    
    # Backup existing crontab
    local backup_file="/tmp/crontab_backup_$(date +%Y%m%d_%H%M%S)"
    crontab -l > "$backup_file" 2>/dev/null || true
    print_status "Backed up existing crontab to: $backup_file"
    
    # Create new crontab
    local temp_cron=$(mktemp)
    
    # Keep existing entries
    crontab -l 2>/dev/null > "$temp_cron" || true
    
    # Add Laravel scheduler entry
    echo "" >> "$temp_cron"
    echo "# Laravel 11 Task Scheduler - Auto-generated" >> "$temp_cron"
    echo "$cron_entry" >> "$temp_cron"
    
    # Install new crontab
    if crontab "$temp_cron"; then
        print_success "✅ Cron job installed successfully!"
        print_status "Laravel scheduler will run every minute"
    else
        print_error "❌ Failed to install cron job"
        print_status "Restoring backup..."
        crontab "$backup_file" 2>/dev/null || true
    fi
    
    rm -f "$temp_cron"
}

# Function to show manual setup instructions
show_manual_setup() {
    print_status "📋 MANUAL SETUP INSTRUCTIONS:"
    echo
    print_status "Add this line to your crontab:"
    echo "* * * * * cd $PROJECT_ROOT && $PHP_PATH artisan schedule:run >> /dev/null 2>&1"
    echo
    print_status "To edit crontab manually:"
    print_status "1. Run: crontab -e"
    print_status "2. Add the line above"
    print_status "3. Save and exit"
}

# Function to test the scheduler
test_scheduler() {
    print_status "Testing the scheduler..."
    
    # Test schedule:run command
    if $PHP_PATH artisan schedule:run --verbose &>/dev/null; then
        print_success "✅ Scheduler test successful"
    else
        print_warning "⚠️ Scheduler test had issues (this might be normal if no tasks are due)"
    fi
    
    # Test specific monitoring commands
    print_status "Testing monitoring commands..."
    
    local test_commands=(
        "query:analyze-slow --days=1 --limit=1"
        "query:optimize --dry-run"
        "logs:monitor-permissions --check-only"
    )
    
    for cmd in "${test_commands[@]}"; do
        if $PHP_PATH artisan $cmd &>/dev/null; then
            print_success "✅ $cmd - Working"
        else
            print_warning "⚠️ $cmd - Issues detected"
        fi
    done
}

# Function to create monitoring directories
create_monitoring_directories() {
    print_status "Creating monitoring directories..."
    
    local directories=(
        "storage/logs/query"
        "storage/logs/performance"
        "storage/logs/statistics"
    )
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            chmod 755 "$dir"
            print_success "Created: $dir"
        else
            print_status "Exists: $dir"
        fi
    done
}

# Function to show status and next steps
show_status() {
    echo
    print_success "🎉 Laravel 11 Task Scheduler Setup Complete!"
    echo
    print_status "📊 SCHEDULED MONITORING TASKS:"
    print_status "• Daily Analysis: 00:00 - Analyze yesterday's slow queries"
    print_status "• Optimization: 00:15 - Generate optimization recommendations"
    print_status "• Weekly Review: Sunday 00:30 - Comprehensive analysis"
    print_status "• Monthly Deep Dive: 1st of month 01:00 - Long-term trends"
    print_status "• Permission Check: Every 6 hours - Fix log permissions"
    print_status "• Log Preparation: 23:55 - Pre-create tomorrow's files"
    echo
    print_status "📁 OUTPUT LOCATIONS:"
    print_status "• Daily: storage/logs/query/daily_analysis.log"
    print_status "• Weekly: storage/logs/query/weekly_analysis.log"
    print_status "• Monthly: storage/logs/query/monthly_analysis.log"
    print_status "• Optimization: storage/logs/query/optimization_analysis.log"
    print_status "• Monitoring: storage/logs/monitoring.log"
    echo
    print_status "🔍 MONITORING COMMANDS:"
    print_status "• Check schedule: php artisan schedule:list"
    print_status "• Run manually: php artisan schedule:run"
    print_status "• Test analysis: php artisan query:analyze-slow --days=1"
    print_status "• View cron jobs: crontab -l"
    echo
    print_warning "⏰ IMPORTANT: Monitoring will start automatically at midnight!"
    print_status "First analysis will run at 00:00 tomorrow and analyze today's queries."
    echo
    print_status "📖 For detailed information, see: SCHEDULED_MONITORING_GUIDE.md"
}

# Main execution
main() {
    check_laravel_project
    test_php_artisan
    check_scheduled_tasks
    create_monitoring_directories
    setup_cron_job
    test_scheduler
    show_status
}

# Run main function
main "$@"
