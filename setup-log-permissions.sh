#!/bin/bash

# Laravel Log Permissions Setup Script
# This script sets up proper permissions for Laravel log files and directories

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOG_BASE_DIR="storage/logs"
WEB_USER="root"  # PHP-FPM running as root in container
WEB_GROUP="root"
DIR_PERMISSIONS="755"
FILE_PERMISSIONS="666"  # More permissive for container environment

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running as root or with sudo
check_permissions() {
    # In container environment, we might not need root for file operations
    if [[ $EUID -ne 0 ]] && ! groups | grep -q docker; then
        print_warning "Not running as root. Some operations might fail."
        print_status "Continuing anyway for Docker environment..."
    fi
}

# Function to detect web server user
detect_web_user() {
    # Check if we're in a container environment by looking at processes
    if ps aux | grep -q "php-fpm.*master.*root" && ps aux | grep -q "nginx.*master.*root"; then
        WEB_USER="root"
        WEB_GROUP="root"
        print_status "Detected containerized environment with root processes"
    elif id "www-data" &>/dev/null; then
        WEB_USER="www-data"
        WEB_GROUP="www-data"
    elif id "nginx" &>/dev/null; then
        WEB_USER="nginx"
        WEB_GROUP="nginx"
    elif id "apache" &>/dev/null; then
        WEB_USER="apache"
        WEB_GROUP="apache"
    else
        # Default to current user in container environment
        WEB_USER=$(whoami)
        WEB_GROUP=$(id -gn)
        print_warning "Using current user: ${WEB_USER}:${WEB_GROUP}"
    fi

    print_status "Using web server user: ${WEB_USER}:${WEB_GROUP}"
}

# Function to create log directories
create_log_directories() {
    print_status "Creating log directories..."

    local directories=(
        "${LOG_BASE_DIR}"
        "${LOG_BASE_DIR}/query"
        "${LOG_BASE_DIR}/telegram"
        "${LOG_BASE_DIR}/whatsapp"
        "${LOG_BASE_DIR}/sirup"
        "${LOG_BASE_DIR}/performance"
    )

    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_success "Created directory: $dir"
        else
            print_status "Directory already exists: $dir"
        fi

        # Set directory permissions
        chmod "$DIR_PERMISSIONS" "$dir"
        chown "${WEB_USER}:${WEB_GROUP}" "$dir"
        print_success "Set permissions for directory: $dir"
    done
}

# Function to fix existing log file permissions
fix_log_file_permissions() {
    print_status "Fixing log file permissions..."

    # Find all .log files in the logs directory
    find "$LOG_BASE_DIR" -name "*.log" -type f | while read -r file; do
        chmod "$FILE_PERMISSIONS" "$file"
        chown "${WEB_USER}:${WEB_GROUP}" "$file"
        print_success "Fixed permissions for: $(basename "$file")"
    done
}

# Function to create logrotate configuration
create_logrotate_config() {
    print_status "Creating logrotate configuration..."

    local config_file="${LOG_BASE_DIR}/logrotate.conf"
    local state_file="${LOG_BASE_DIR}/logrotate.state"

    cat > "$config_file" << EOF
# Laravel Application Log Rotation Configuration
# Generated by setup-log-permissions.sh

$(pwd)/${LOG_BASE_DIR}/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create ${FILE_PERMISSIONS} ${WEB_USER} ${WEB_GROUP}
    postrotate
        # Reload PHP-FPM if needed
        # systemctl reload php8.2-fpm
    endscript
}

$(pwd)/${LOG_BASE_DIR}/query/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create ${FILE_PERMISSIONS} ${WEB_USER} ${WEB_GROUP}
    size 100M
}

$(pwd)/${LOG_BASE_DIR}/telegram/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create ${FILE_PERMISSIONS} ${WEB_USER} ${WEB_GROUP}
}

$(pwd)/${LOG_BASE_DIR}/whatsapp/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create ${FILE_PERMISSIONS} ${WEB_USER} ${WEB_GROUP}
}

$(pwd)/${LOG_BASE_DIR}/sirup/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create ${FILE_PERMISSIONS} ${WEB_USER} ${WEB_GROUP}
    size 500M
}

$(pwd)/${LOG_BASE_DIR}/performance/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create ${FILE_PERMISSIONS} ${WEB_USER} ${WEB_GROUP}
}
EOF

    chmod 644 "$config_file"
    chown "${WEB_USER}:${WEB_GROUP}" "$config_file"

    print_success "Created logrotate configuration: $config_file"
    print_status "To enable automatic log rotation, add this to your crontab:"
    print_status "0 2 * * * /usr/sbin/logrotate $(pwd)/$config_file --state $(pwd)/$state_file"
}

# Function to create cron job for log rotation
setup_cron_job() {
    print_status "Setting up cron job for log rotation..."

    local cron_command="0 2 * * * /usr/sbin/logrotate $(pwd)/${LOG_BASE_DIR}/logrotate.conf --state $(pwd)/${LOG_BASE_DIR}/logrotate.state"

    # Check if cron job already exists
    if crontab -u "$WEB_USER" -l 2>/dev/null | grep -q "logrotate.*$(pwd)"; then
        print_warning "Cron job for log rotation already exists"
    else
        # Add cron job
        (crontab -u "$WEB_USER" -l 2>/dev/null; echo "$cron_command") | crontab -u "$WEB_USER" -
        print_success "Added cron job for automatic log rotation"
    fi
}

# Function to create .gitignore for logs
create_gitignore() {
    print_status "Creating .gitignore for log directory..."

    local gitignore_file="${LOG_BASE_DIR}/.gitignore"

    cat > "$gitignore_file" << EOF
# Ignore all log files
*.log
*.log.*

# Ignore logrotate state
logrotate.state

# But keep the directory structure
!.gitignore
EOF

    chmod 644 "$gitignore_file"
    print_success "Created .gitignore for log directory"
}

# Function to test log writing
test_log_writing() {
    print_status "Testing log file writing..."

    local test_file="${LOG_BASE_DIR}/permission_test.log"

    # Test as web user
    if sudo -u "$WEB_USER" touch "$test_file" 2>/dev/null; then
        sudo -u "$WEB_USER" echo "Test log entry - $(date)" >> "$test_file"
        print_success "Log writing test passed"
        rm -f "$test_file"
    else
        print_error "Log writing test failed - check permissions"
        return 1
    fi
}

# Main execution
main() {
    print_status "Starting Laravel log permissions setup..."

    # Check if we're in a Laravel project
    if [[ ! -f "artisan" ]]; then
        print_error "This doesn't appear to be a Laravel project (artisan file not found)"
        exit 1
    fi

    check_permissions
    detect_web_user
    create_log_directories
    fix_log_file_permissions
    create_logrotate_config
    create_gitignore

    # Ask user if they want to setup cron job
    read -p "Do you want to setup automatic log rotation cron job? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_cron_job
    fi

    test_log_writing

    print_success "Log permissions setup completed successfully!"
    print_status "Summary:"
    print_status "- Log directories created with ${DIR_PERMISSIONS} permissions"
    print_status "- Log files set to ${FILE_PERMISSIONS} permissions"
    print_status "- Owner set to ${WEB_USER}:${WEB_GROUP}"
    print_status "- Logrotate configuration created"
    print_status ""
    print_status "You can now run 'php artisan logs:fix-permissions' to maintain permissions"
}

# Run main function
main "$@"
