<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Optimize list_perusahaan_tender table
        Schema::table('list_perusahaan_tender', function (Blueprint $table) {
            // Add composite index for normalized_name LIKE queries
            if (!$this->indexExists('list_perusahaan_tender', 'idx_normalized_name_fulltext')) {
                // Add fulltext index for better LIKE performance
                DB::statement('ALTER TABLE list_perusahaan_tender ADD FULLTEXT idx_normalized_name_fulltext (normalized_name)');
            }

            // Add index for common WHERE conditions
            if (!$this->indexExists('list_perusahaan_tender', 'idx_npwp_normalized')) {
                $table->index(['npwp', 'normalized_name'], 'idx_npwp_normalized');
            }

            // Add index for pembeda with normalized_name
            if (!$this->indexExists('list_perusahaan_tender', 'idx_pembeda_normalized')) {
                $table->index(['pembeda', 'normalized_name'], 'idx_pembeda_normalized');
            }
        });

        // Optimize command_progress table for frequent updates
        Schema::table('command_progress', function (Blueprint $table) {
            if (!$this->indexExists('command_progress', 'idx_command_status')) {
                $table->index(['command_name', 'status'], 'idx_command_status');
            }

            if (!$this->indexExists('command_progress', 'idx_updated_at')) {
                $table->index('updated_at', 'idx_updated_at');
            }
        });

        // Optimize data_sirup table
        Schema::table('data_sirup', function (Blueprint $table) {
            if (!$this->indexExists('data_sirup', 'idx_kode_rup_updated')) {
                $table->index(['kode_rup', 'updated_at'], 'idx_kode_rup_updated');
            }

            // tahun_anggaran index already exists, skip
            if (!$this->indexExists('data_sirup', 'idx_updated_at')) {
                $table->index('updated_at', 'idx_updated_at');
            }
        });

        // Optimize peserta_tender table
        Schema::table('peserta_tender', function (Blueprint $table) {
            if (!$this->indexExists('peserta_tender', 'idx_kode_tender_status')) {
                $table->index(['kode_tender', 'status'], 'idx_kode_tender_status');
            }

            if (!$this->indexExists('peserta_tender', 'idx_id_peserta_status')) {
                $table->index(['id_peserta', 'status'], 'idx_id_peserta_status');
            }
        });

        // Optimize commands_status table
        Schema::table('commands_status', function (Blueprint $table) {
            if (!$this->indexExists('commands_status', 'idx_command_name_unique')) {
                $table->unique('command_name', 'idx_command_name_unique');
            }
        });

        // Add indexes for ekatalog tables if they exist
        if (Schema::hasTable('ekatalog_paket_pengadaan')) {
            Schema::table('ekatalog_paket_pengadaan', function (Blueprint $table) {
                if (!$this->indexExists('ekatalog_paket_pengadaan', 'idx_tanggal_paket')) {
                    $table->index('tanggal_paket', 'idx_tanggal_paket');
                }

                if (!$this->indexExists('ekatalog_paket_pengadaan', 'idx_id_penyedia_tanggal')) {
                    $table->index(['id_penyedia', 'tanggal_paket'], 'idx_id_penyedia_tanggal');
                }
            });
        }

        if (Schema::hasTable('ekatalog_produk')) {
            Schema::table('ekatalog_produk', function (Blueprint $table) {
                // Add fulltext index for product search
                if (!$this->indexExists('ekatalog_produk', 'idx_nama_produk_fulltext')) {
                    DB::statement('ALTER TABLE ekatalog_produk ADD FULLTEXT idx_nama_produk_fulltext (nama_produk)');
                }

                // Add index with limited length to avoid key too long error
                if (!$this->indexExists('ekatalog_produk', 'idx_manufaktur_produk')) {
                    DB::statement('ALTER TABLE ekatalog_produk ADD INDEX idx_manufaktur_produk (nama_manufaktur(191), nama_produk(191))');
                }
            });
        }

        // Create performance monitoring table
        if (!Schema::hasTable('query_performance_log')) {
            Schema::create('query_performance_log', function (Blueprint $table) {
                $table->id();
                $table->text('sql_pattern');
                $table->string('sql_pattern_hash', 64)->nullable(); // Hash for indexing
                $table->decimal('execution_time', 8, 2);
                $table->integer('query_count')->default(1);
                $table->decimal('avg_execution_time', 8, 2);
                $table->decimal('max_execution_time', 8, 2);
                $table->string('request_uri')->nullable();
                $table->string('user_agent')->nullable();
                $table->timestamp('first_seen');
                $table->timestamp('last_seen');
                $table->timestamps();

                // Use hash for indexing instead of full text
                $table->index(['sql_pattern_hash', 'last_seen'], 'idx_pattern_hash_last_seen');
                $table->index('execution_time', 'idx_execution_time');
                $table->index('last_seen', 'idx_last_seen');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop performance monitoring table
        Schema::dropIfExists('query_performance_log');

        // Remove indexes from list_perusahaan_tender
        Schema::table('list_perusahaan_tender', function (Blueprint $table) {
            if ($this->indexExists('list_perusahaan_tender', 'idx_normalized_name_fulltext')) {
                DB::statement('ALTER TABLE list_perusahaan_tender DROP INDEX idx_normalized_name_fulltext');
            }

            $this->dropIndexIfExists($table, 'list_perusahaan_tender', 'idx_npwp_normalized');
            $this->dropIndexIfExists($table, 'list_perusahaan_tender', 'idx_pembeda_normalized');
        });

        // Remove indexes from command_progress
        Schema::table('command_progress', function (Blueprint $table) {
            $this->dropIndexIfExists($table, 'command_progress', 'idx_command_status');
            $this->dropIndexIfExists($table, 'command_progress', 'idx_updated_at');
        });

        // Remove indexes from data_sirup
        Schema::table('data_sirup', function (Blueprint $table) {
            $this->dropIndexIfExists($table, 'data_sirup', 'idx_kode_rup_updated');
            $this->dropIndexIfExists($table, 'data_sirup', 'idx_updated_at');
        });

        // Remove indexes from peserta_tender
        Schema::table('peserta_tender', function (Blueprint $table) {
            $this->dropIndexIfExists($table, 'peserta_tender', 'idx_kode_tender_status');
            $this->dropIndexIfExists($table, 'peserta_tender', 'idx_id_peserta_status');
        });

        // Remove indexes from commands_status
        Schema::table('commands_status', function (Blueprint $table) {
            $this->dropIndexIfExists($table, 'commands_status', 'idx_command_name_unique');
        });

        // Remove indexes from ekatalog tables
        if (Schema::hasTable('ekatalog_paket_pengadaan')) {
            Schema::table('ekatalog_paket_pengadaan', function (Blueprint $table) {
                $this->dropIndexIfExists($table, 'ekatalog_paket_pengadaan', 'idx_tanggal_paket');
                $this->dropIndexIfExists($table, 'ekatalog_paket_pengadaan', 'idx_id_penyedia_tanggal');
            });
        }

        if (Schema::hasTable('ekatalog_produk')) {
            Schema::table('ekatalog_produk', function (Blueprint $table) {
                if ($this->indexExists('ekatalog_produk', 'idx_nama_produk_fulltext')) {
                    DB::statement('ALTER TABLE ekatalog_produk DROP INDEX idx_nama_produk_fulltext');
                }

                $this->dropIndexIfExists($table, 'ekatalog_produk', 'idx_manufaktur_produk');
            });
        }
    }

    /**
     * Check if index exists
     */
    private function indexExists(string $table, string $index): bool
    {
        $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$index]);
        return !empty($indexes);
    }

    /**
     * Drop index if it exists
     */
    private function dropIndexIfExists(Blueprint $table, string $tableName, string $indexName): void
    {
        if ($this->indexExists($tableName, $indexName)) {
            $table->dropIndex($indexName);
        }
    }
};
