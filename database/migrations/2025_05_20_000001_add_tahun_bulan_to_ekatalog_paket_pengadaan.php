<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tambahkan kolom tahun dan bulan sebagai GENERATED COLUMN
        Schema::table('ekatalog_paket_pengadaan', function (Blueprint $table) {
            // Cek apakah kolom tahun sudah ada
            if (!Schema::hasColumn('ekatalog_paket_pengadaan', 'tahun')) {
                $table->year('tahun')->nullable()->after('tanggal_paket')
                    ->storedAs('YEAR(tanggal_paket)');
            }
            
            // Cek apakah kolom bulan sudah ada
            if (!Schema::hasColumn('ekatalog_paket_pengadaan', 'bulan')) {
                $table->tinyInteger('bulan')->nullable()->after('tahun')
                    ->storedAs('MONTH(tanggal_paket)');
            }
            
            // Tambahkan index untuk kolom tahun dan bulan
            if (!Schema::hasIndex('ekatalog_paket_pengadaan', 'idx_tahun_bulan')) {
                $table->index(['tahun', 'bulan'], 'idx_tahun_bulan');
            }
        });
        
        // Pastikan kolom tanggal sudah ada dan merupakan GENERATED COLUMN
        if (!Schema::hasColumn('ekatalog_paket_pengadaan', 'tanggal')) {
            Schema::table('ekatalog_paket_pengadaan', function (Blueprint $table) {
                $table->date('tanggal')->nullable()->after('bulan')
                    ->storedAs('CAST(tanggal_paket AS DATE)');
                
                // Tambahkan index untuk kolom tanggal
                $table->index('tanggal', 'idx_tanggal');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ekatalog_paket_pengadaan', function (Blueprint $table) {
            // Hapus index
            if (Schema::hasIndex('ekatalog_paket_pengadaan', 'idx_tahun_bulan')) {
                $table->dropIndex('idx_tahun_bulan');
            }
            
            // Hapus kolom
            if (Schema::hasColumn('ekatalog_paket_pengadaan', 'bulan')) {
                $table->dropColumn('bulan');
            }
            
            if (Schema::hasColumn('ekatalog_paket_pengadaan', 'tahun')) {
                $table->dropColumn('tahun');
            }
        });
    }
};
