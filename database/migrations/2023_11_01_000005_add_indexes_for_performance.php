<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tambahkan index untuk tabel list_perusahaan_tender
        Schema::table('list_perusahaan_tender', function (Blueprint $table) {
            // Cek apakah index sudah ada sebelum menambahkannya
            if (!Schema::hasIndex('list_perusahaan_tender', 'list_perusahaan_tender_pembeda_index')) {
                $table->index('pembeda', 'list_perusahaan_tender_pembeda_index');
            }
            
            if (!Schema::hasIndex('list_perusahaan_tender', 'list_perusahaan_tender_npwp_index')) {
                $table->index('npwp', 'list_perusahaan_tender_npwp_index');
            }
            
            if (!Schema::hasIndex('list_perusahaan_tender', 'list_perusahaan_tender_normalized_name_index')) {
                $table->index('normalized_name', 'list_perusahaan_tender_normalized_name_index');
            }
        });
        
        // Tambahkan index untuk tabel ekatalog_paket_pengadaan
        Schema::table('ekatalog_paket_pengadaan', function (Blueprint $table) {
            if (!Schema::hasIndex('ekatalog_paket_pengadaan', 'ekatalog_paket_pengadaan_nomor_paket_index')) {
                $table->index('nomor_paket', 'ekatalog_paket_pengadaan_nomor_paket_index');
            }
            
            if (!Schema::hasIndex('ekatalog_paket_pengadaan', 'ekatalog_paket_pengadaan_id_penyedia_index')) {
                $table->index('id_penyedia', 'ekatalog_paket_pengadaan_id_penyedia_index');
            }
            
            if (!Schema::hasIndex('ekatalog_paket_pengadaan', 'ekatalog_paket_pengadaan_id_pelaksana_index')) {
                $table->index('id_pelaksana', 'ekatalog_paket_pengadaan_id_pelaksana_index');
            }
        });
        
        // Tambahkan index untuk tabel produk
        Schema::table('ekatalog_produk', function (Blueprint $table) {
            if (!Schema::hasIndex('ekatalog_produk', 'produk_nama_produk_index')) {
                $table->index('nama_produk', 'produk_nama_produk_index');
            }
            
            if (!Schema::hasIndex('ekatalog_produk', 'produk_nama_manufaktur_index')) {
                $table->index('nama_manufaktur', 'produk_nama_manufaktur_index');
            }
        });
        
        // Tambahkan index untuk tabel transaksi_paket
        Schema::table('ekatalog_transaksi_paket', function (Blueprint $table) {
            if (!Schema::hasIndex('ekatalog_transaksi_paket', 'transaksi_paket_id_paket_index')) {
                $table->index('id_paket', 'transaksi_paket_id_paket_index');
            }
            
            if (!Schema::hasIndex('ekatalog_transaksi_paket', 'transaksi_paket_id_produk_index')) {
                $table->index('id_produk', 'transaksi_paket_id_produk_index');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Hapus index dari tabel list_perusahaan_tender
        Schema::table('list_perusahaan_tender', function (Blueprint $table) {
            if (Schema::hasIndex('list_perusahaan_tender', 'list_perusahaan_tender_pembeda_index')) {
                $table->dropIndex('list_perusahaan_tender_pembeda_index');
            }
            
            if (Schema::hasIndex('list_perusahaan_tender', 'list_perusahaan_tender_npwp_index')) {
                $table->dropIndex('list_perusahaan_tender_npwp_index');
            }
            
            if (Schema::hasIndex('list_perusahaan_tender', 'list_perusahaan_tender_normalized_name_index')) {
                $table->dropIndex('list_perusahaan_tender_normalized_name_index');
            }
        });
        
        // Hapus index dari tabel ekatalog_paket_pengadaan
        Schema::table('ekatalog_paket_pengadaan', function (Blueprint $table) {
            if (Schema::hasIndex('ekatalog_paket_pengadaan', 'ekatalog_paket_pengadaan_nomor_paket_index')) {
                $table->dropIndex('ekatalog_paket_pengadaan_nomor_paket_index');
            }
            
            if (Schema::hasIndex('ekatalog_paket_pengadaan', 'ekatalog_paket_pengadaan_id_penyedia_index')) {
                $table->dropIndex('ekatalog_paket_pengadaan_id_penyedia_index');
            }
            
            if (Schema::hasIndex('ekatalog_paket_pengadaan', 'ekatalog_paket_pengadaan_id_pelaksana_index')) {
                $table->dropIndex('ekatalog_paket_pengadaan_id_pelaksana_index');
            }
        });
        
        // Hapus index dari tabel produk
        Schema::table('ekatalog_produk', function (Blueprint $table) {
            if (Schema::hasIndex('ekatalog_produk', 'produk_nama_produk_index')) {
                $table->dropIndex('produk_nama_produk_index');
            }
            
            if (Schema::hasIndex('ekatalog_produk', 'produk_nama_manufaktur_index')) {
                $table->dropIndex('ekatalog_produk_nama_manufaktur_index');
            }
        });
        
        // Hapus index dari tabel transaksi_paket
        Schema::table('ekatalog_transaksi_paket', function (Blueprint $table) {
            if (Schema::hasIndex('ekatalog_transaksi_paket', 'transaksi_paket_id_paket_index')) {
                $table->dropIndex('transaksi_paket_id_paket_index');
            }
            
            if (Schema::hasIndex('ekatalog_transaksi_paket', 'transaksi_paket_id_produk_index')) {
                $table->dropIndex('transaksi_paket_id_produk_index');
            }
        });
    }
};
