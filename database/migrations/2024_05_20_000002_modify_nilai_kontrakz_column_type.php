<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ubah tipe data kolom nilai_kontrakz menjadi bigint unsigned
        Schema::table('ekatalog_paket_pengadaan', function (Blueprint $table) {
            // Cek apakah kolom nilai_kontrakz sudah ada
            if (Schema::hasColumn('ekatalog_paket_pengadaan', 'nilai_kontrakz')) {
                // Ubah tipe data kolom nilai_kontrakz menjadi bigint unsigned
                DB::statement('ALTER TABLE ekatalog_paket_pengadaan MODIFY nilai_kontrakz BIGINT UNSIGNED DEFAULT 0');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Kembalikan tipe data kolom nilai_kontrakz menjadi int unsigned
        Schema::table('ekatalog_paket_pengadaan', function (Blueprint $table) {
            if (Schema::hasColumn('ekatalog_paket_pengadaan', 'nilai_kontrakz')) {
                // Peringatan: Ini bisa menyebabkan data hilang jika nilai melebihi batas int unsigned
                DB::statement('ALTER TABLE ekatalog_paket_pengadaan MODIFY nilai_kontrakz INT UNSIGNED DEFAULT 0');
            }
        });
    }
};
