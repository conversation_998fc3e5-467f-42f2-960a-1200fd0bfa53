<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('list_perusahaan_tender', function (Blueprint $table) {
            $table->id()->primary();
            $table->string('nama_peserta', 255);
            $table->string('npwp', 255)->unique();
            $table->text('alamat')->nullable();
            $table->string('pembeda', 255)->unique();
            $table->string('npwp_blur', 255)->nullable();
            $table->string('partial_npwp', 255)->nullable();
            $table->string('normalized_name', 255)->nullable();
            
            $table->timestamps();

            $table->index('npwp');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('list_perusahaan_tender');
    }
};
