<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('jadwal', function (Blueprint $table) {
            $table->id();
            $table->string('kode_tender', 50);
            $table->string('tahap', 255);
            $table->timestamp('tanggal_mulai');
            $table->timestamp('tanggal_selesai')->nullable();
            $table->timestamps();

            $table->unique(['kode_tender', 'tahap']);

            // Menambahkan foreign key dengan cascade delete
            $table->foreign('kode_tender')->references('kode_tender')->on('tender_umum_publik')->onDelete('cascade');
            $table->index('kode_tender');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('jadwal');
    }
};
