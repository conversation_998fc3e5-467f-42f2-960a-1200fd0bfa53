<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('lpse', function (Blueprint $table) {
            $table->integer('kd_lpse')->primary();
            $table->string('nama_lpse', 255);
            $table->string('provinsi', 255)->nullable();
            $table->string('alamat', 255)->nullable();
            $table->string('email', 255)->nullable();
            $table->string('helpdesk', 255)->nullable();
            $table->string('versi_lpse', 255)->nullable();
            $table->string('url', 255)->nullable();
            $table->string('logo_url', 255)->nullable();
            $table->string('logo_path', 255)->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('lpse');
    }
};
