<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('login_logs', function (Blueprint $table) {
            // Información del dispositivo
            $table->string('device_type')->nullable()->after('os');
            $table->string('device_brand')->nullable()->after('device_type');
            $table->string('device_model')->nullable()->after('device_brand');
            
            // Información de ubicación
            $table->string('country')->nullable()->after('device_model');
            $table->string('region')->nullable()->after('country');
            $table->string('city')->nullable()->after('region');
            $table->string('latitude')->nullable()->after('city');
            $table->string('longitude')->nullable()->after('latitude');
            
            // Información adicional
            $table->json('additional_info')->nullable()->after('longitude');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('login_logs', function (Blueprint $table) {
            $table->dropColumn([
                'device_type',
                'device_brand',
                'device_model',
                'country',
                'region',
                'city',
                'latitude',
                'longitude',
                'additional_info'
            ]);
        });
    }
};
