<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToUserSubscriptionsTable extends Migration
{
    public function up()
    {
        Schema::table('user_subscriptions', function (Blueprint $table) {
            $table->string('status')->default('pending')->after('end_date');
            $table->integer('unique_code')->nullable()->after('status');
            $table->decimal('total_amount', 15, 2)->nullable()->after('unique_code');
        });
    }

    public function down()
    {
        Schema::table('user_subscriptions', function (Blueprint $table) {
            $table->dropColumn(['status', 'unique_code', 'total_amount']);
        });
    }
}
