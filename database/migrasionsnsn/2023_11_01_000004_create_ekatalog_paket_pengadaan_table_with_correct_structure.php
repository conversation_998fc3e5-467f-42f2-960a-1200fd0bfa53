<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ekatalog_paket_pengadaan', function (Blueprint $table) {
            $table->integer('id_paket')->autoIncrement();
            $table->text('nama_paket');
            $table->string('nomor_paket', 100)->nullable()->index();
            $table->string('pengelola', 255)->nullable()->index();
            $table->string('satuan_kerja', 255)->nullable()->index();
            $table->string('instansi_pembeli', 255)->nullable()->index();
            $table->string('jenis_katalog', 100)->nullable();
            $table->string('etalase', 255)->nullable();
            $table->dateTime('tanggal_paket')->nullable()->index();
            $table->string('status_paket', 100)->nullable()->index();
            $table->bigInteger('rup_id')->nullable()->index();
            $table->string('status_umkm', 50)->nullable();
            $table->unsignedBigInteger('id_penyedia')->nullable()->index();
            $table->unsignedBigInteger('id_pelaksana')->nullable()->index();
            $table->timestamps();
            
            // Generated column for date part of tanggal_paket
            $table->date('tanggal')->virtualAs('cast(`tanggal_paket` as date)')->index();
            
            // Add indexes
            $table->index('nama_paket', 'idx_nama_paket', 'FULLTEXT');
            $table->index(['rup_id', 'nomor_paket'], 'idx_rup_id_nomor_paket');
            
            // Add foreign keys
            $table->foreign('id_penyedia')->references('id')->on('list_perusahaan_tender');
            $table->foreign('id_pelaksana')->references('id')->on('list_perusahaan_tender');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ekatalog_paket_pengadaan');
    }
};
