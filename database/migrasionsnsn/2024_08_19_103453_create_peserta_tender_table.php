<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('peserta_tender', function (Blueprint $table) {
            $table->id();
            $table->string('kode_tender', 50);
            $table->unsignedBigInteger('id_peserta');
            $table->string('status', 255)->nullable();
            $table->timestamps();

            $table->foreign('kode_tender')->references('kode_tender')->on('tender_umum_publik');
            $table->foreign('id_peserta')->references('id')->on('list_perusahaan_tender');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('peserta_tender');
    }
};
