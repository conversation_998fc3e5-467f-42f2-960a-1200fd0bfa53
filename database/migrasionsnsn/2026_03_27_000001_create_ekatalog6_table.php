<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('ekatalog6', function (Blueprint $table) {
            $table->string('kode')->primary();
            $table->string('kode_rup');
            $table->string('kode_klpd');
            $table->integer('tahun_anggaran');
            $table->string('nama_satker');
            $table->string('sumberdata');
            $table->unsignedBigInteger('id_penyedia');
            $table->foreign('id_penyedia')->references('id')->on('list_perusahaan_tender');
            $table->text('nama_paket');
            $table->decimal('total_pelaksanaan', 20, 0);
            $table->decimal('pdn', 20, 0);
            $table->timestamps();

            $table->index('kode_klpd');
            $table->index('tahun_anggaran');
        });
    }

    public function down()
    {
        Schema::dropIfExists('ekatalog6');
    }
};
