<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_errors', function (Blueprint $table) {
            $table->id();
            $table->string('type')->index(); // Exception class name
            $table->string('code')->nullable(); // Error code if available
            $table->text('message'); // Error message
            $table->string('file'); // File where the error occurred
            $table->integer('line'); // Line number where the error occurred
            $table->longText('trace')->nullable(); // Stack trace (JSON)
            $table->string('request_url')->nullable(); // URL that caused the error
            $table->string('request_method')->nullable(); // HTTP method
            $table->longText('request_input')->nullable(); // Request input data (JSON)
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete(); // User who triggered the error
            $table->string('user_email')->nullable(); // User email (for quick reference)
            $table->string('ip_address')->nullable(); // IP address
            $table->text('user_agent')->nullable(); // User agent
            $table->boolean('resolved')->default(false); // Whether the error has been resolved
            $table->timestamp('resolved_at')->nullable(); // When the error was resolved
            $table->foreignId('resolved_by')->nullable()->constrained('users')->nullOnDelete(); // Who resolved the error
            $table->text('notes')->nullable(); // Notes about the resolution
            $table->timestamps();
            
            // Indexes for faster queries
            $table->index('created_at');
            $table->index('resolved');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_errors');
    }
};
