<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ekatalog_paket_pengadaan', function (Blueprint $table) {
            $table->id();
            $table->string('id_paket')->nullable()->index();
            $table->string('nomor_paket')->nullable()->index();
            $table->string('nama_paket')->nullable();
            $table->string('nama_komoditas')->nullable();
            $table->string('nama_manufaktur')->nullable();
            $table->string('nama_produk')->nullable();
            $table->string('nama_penyedia')->nullable();
            $table->unsignedBigInteger('id_penyedia')->nullable()->index();
            $table->string('nama_pelaksana')->nullable();
            $table->unsignedBigInteger('id_pelaksana')->nullable()->index();
            $table->string('nama_satker')->nullable();
            $table->string('nama_instansi')->nullable();
            $table->string('nama_provinsi')->nullable();
            $table->string('nama_kabupaten')->nullable();
            $table->decimal('harga_satuan', 20, 2)->nullable();
            $table->decimal('kuantitas', 20, 2)->nullable();
            $table->decimal('total_harga', 20, 2)->nullable();
            $table->date('tanggal_buat_paket')->nullable();
            $table->date('tanggal_ubah_paket')->nullable();
            $table->string('status_paket')->nullable();
            $table->string('tahun_anggaran')->nullable();
            $table->string('jenis_katalog')->nullable();
            $table->string('kode_produk')->nullable();
            $table->string('kode_komoditas')->nullable();
            $table->string('kode_manufaktur')->nullable();
            $table->string('kode_penyedia')->nullable();
            $table->string('kode_pelaksana')->nullable();
            $table->string('kode_satker')->nullable();
            $table->string('kode_instansi')->nullable();
            $table->string('kode_provinsi')->nullable();
            $table->string('kode_kabupaten')->nullable();
            $table->string('kode_katalog')->nullable();
            $table->string('url_paket')->nullable();
            $table->string('url_produk')->nullable();
            $table->string('url_penyedia')->nullable();
            $table->string('url_pelaksana')->nullable();
            $table->string('url_satker')->nullable();
            $table->string('url_instansi')->nullable();
            $table->string('url_provinsi')->nullable();
            $table->string('url_kabupaten')->nullable();
            $table->string('url_katalog')->nullable();
            $table->string('url_komoditas')->nullable();
            $table->string('url_manufaktur')->nullable();
            $table->json('raw_data')->nullable();
            $table->timestamp('fetched_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ekatalog_paket_pengadaan');
    }
};
