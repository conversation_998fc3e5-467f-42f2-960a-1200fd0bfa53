<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('klpd', function (Blueprint $table) {
            $table->string('kd_klpd', 50)->primary();
            $table->string('nama_klpd', 255);
            $table->string('jenis_klpd', 255)->nullable();
            $table->integer('kd_provinsi')->nullable();
            $table->integer('kd_kabupaten')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('klpd');
    }
};
