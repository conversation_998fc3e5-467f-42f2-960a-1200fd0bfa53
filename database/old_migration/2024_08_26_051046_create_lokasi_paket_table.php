<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLokasiPaketTable extends Migration
{
    public function up()
    {
        Schema::create('lokasi_paket', function (Blueprint $table) {
            $table->id();
            $table->string('kode_tender');
            $table->string('kbp_nama')->nullable();
            $table->string('prp_nama')->nullable();
            $table->text('pkt_lokasi')->nullable();
            $table->foreign('kode_tender')->references('kode_tender')->on('tender_umum_publik')->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('lokasi_paket');
    }
}
