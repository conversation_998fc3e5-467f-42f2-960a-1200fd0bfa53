<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdatePemenangLpsePrimaryKey extends Migration
{
    public function up()
    {
        Schema::table('pemenang_lpse', function (Blueprint $table) {
            // Drop foreign keys first
            $table->dropForeign(['kode_tender']);
            $table->dropForeign(['id_peserta']);

            // Drop existing primary key
            $table->dropPrimary(['kode_tender']);

            // Add composite primary key
            $table->primary(['kode_tender', 'id_peserta']);

            // Re-add foreign keys
            $table->foreign('kode_tender')->references('kode_tender')->on('tender_umum_publik')->onDelete('cascade');
            $table->foreign('id_peserta')->references('id')->on('list_perusahaan_tender')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::table('pemenang_lpse', function (Blueprint $table) {
            // Drop foreign keys first
            $table->dropForeign(['kode_tender']);
            $table->dropForeign(['id_peserta']);

            // Drop composite primary key
            $table->dropPrimary(['kode_tender', 'id_peserta']);

            // Add primary key back to kode_tender only
            $table->primary('kode_tender');

            // Re-add foreign keys
            $table->foreign('kode_tender')->references('kode_tender')->on('tender_umum_publik')->onDelete('cascade');
            $table->foreign('id_peserta')->references('id')->on('list_perusahaan_tender')->onDelete('cascade');
        });
    }
}
