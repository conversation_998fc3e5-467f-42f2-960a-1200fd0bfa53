<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAnggaranTable extends Migration
{
    public function up()
    {
        Schema::create('anggaran', function (Blueprint $table) {
            $table->id();
            $table->string('kode_tender');
            $table->string('rup_id')->nullable();
            $table->string('sbd_id')->nullable();
            $table->bigInteger('ang_nilai')->nullable();
            $table->integer('ang_tahun')->nullable();
            $table->string('ang_koderekening')->nullable();
            $table->foreign('kode_tender')->references('kode_tender')->on('tender_umum_publik')->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('anggaran');
    }
}
