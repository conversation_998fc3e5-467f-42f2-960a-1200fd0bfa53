<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('tender_umum_publik', function (Blueprint $table) {
            $table->string('kode_tender', 50)->primary();
            $table->integer('kd_lpse');
            $table->string('status_tender', 255)->nullable();
            $table->string('nama_paket', 2000);
            $table->decimal('pagu', 15, 1)->nullable();
            $table->decimal('hps', 15, 1)->nullable();
            $table->date('tanggal_paket_dibuat')->nullable();
            $table->timestamp('tanggal_paket_tayang')->nullable();
            $table->string('kategori_pekerjaan', 255)->nullable();
            $table->string('metode_pemilihan', 255)->nullable();
            $table->string('metode_pengadaan', 255)->nullable();
            $table->string('metode_evaluasi', 255)->nullable();
            $table->string('cara_pembayaran', 255)->nullable();
            $table->string('jenis_penetapan_pemenang', 255)->nullable();
            $table->string('apakah_paket_konsolidasi',255)->nullable();
            $table->integer('jumlah_pendaftar')->nullable();
            $table->integer('jumlah_penawar')->nullable();
            $table->integer('jumlah_kirim_kualifikasi')->nullable();
            $table->integer('durasi_tender')->nullable();
            $table->string('versi_paket_spse', 255)->nullable();
            $table->string('tahap_tender', 255)->nullable();
            $table->string('kualifikasi_usaha', 255)->nullable();
            $table->timestamps();

            $table->foreign('kd_lpse')->references('kd_lpse')->on('lpse');
            $table->index('kode_tender');
            $table->index('kd_lpse');
            $table->index('tanggal_paket_tayang');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tender_umum_publik');
    }
};
