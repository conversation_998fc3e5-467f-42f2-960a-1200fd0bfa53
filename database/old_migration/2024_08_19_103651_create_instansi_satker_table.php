<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInstansiSatkerTable extends Migration
{
    public function up()
    {
        Schema::create('instansi_satker', function (Blueprint $table) {
            $table->id();
            $table->string('kode_tender');
            $table->string('rup_id')->nullable();
            $table->string('nama_satker');
            $table->string('nama_instansi');
            $table->string('jenis_instansi');
            $table->foreign('kode_tender')->references('kode_tender')->on('tender_umum_publik')->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('instansi_satker');
    }
}
