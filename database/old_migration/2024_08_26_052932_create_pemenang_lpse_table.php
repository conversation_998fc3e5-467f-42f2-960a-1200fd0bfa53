<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePemenangLpseTable extends Migration
{
    public function up()
    {
Schema::create('pemenang_lpse', function (Blueprint $table) {
    $table->string('kode_tender', 50);
    $table->unsignedBigInteger('id_peserta');
    $table->decimal('harga_penawaran', 15, 2)->nullable();
    $table->decimal('harga_terkoreksi', 15, 2)->nullable();
    $table->timestamps();

    $table->primary(['kode_tender', 'id_peserta']);

    // Foreign key constraints
    $table->foreign('kode_tender')->references('kode_tender')->on('tender_umum_publik')->onDelete('cascade');
    $table->foreign('id_peserta')->references('id')->on('list_perusahaan_tender')->onDelete('cascade');
});
    }

    public function down()
    {
        Schema::dropIfExists('pemenang_lpse');
    }
}
