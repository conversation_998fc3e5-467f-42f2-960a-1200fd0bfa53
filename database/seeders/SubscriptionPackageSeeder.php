<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Subscription;

class SubscriptionPackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define subscription packages
        $subscriptionPackages = [
            [
                'name' => 'Bulanan',
                'price' => 250000.00,
                'duration_days' => 30,
            ],
            [
                'name' => 'Kuartalan',
                'price' => 650000.00,
                'duration_days' => 90,
            ],
            [
                'name' => 'Semesteran',
                'price' => 1200000.00,
                'duration_days' => 180,
            ],
            [
                'name' => 'Tahunan',
                'price' => 2000000.00,
                'duration_days' => 360,
            ],
        ];
        
        // Create or update subscription packages
        foreach ($subscriptionPackages as $package) {
            Subscription::updateOrCreate(
                ['name' => $package['name']],
                [
                    'price' => $package['price'],
                    'duration_days' => $package['duration_days'],
                ]
            );
        }
        
        $this->command->info('Subscription packages seeded successfully.');
    }
}
