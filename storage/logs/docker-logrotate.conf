# Docker-optimized Laravel Log Rotation Configuration
# For use inside containers

/opt/1panel/apps/openresty/openresty/www/sites/dev.tenderid.com/index/storage/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 666
    maxsize 100M
    postrotate
        # Signal Laravel to reopen log files if needed
        # kill -USR1 $(cat /var/run/php-fpm.pid) 2>/dev/null || true
    endscript
}

/opt/1panel/apps/openresty/openresty/www/sites/dev.tenderid.com/index/storage/logs/query/*.log {
    daily
    missingok
    rotate 5
    compress
    delaycompress
    notifempty
    create 666
    maxsize 50M
}

/opt/1panel/apps/openresty/openresty/www/sites/dev.tenderid.com/index/storage/logs/performance/*.log {
    daily
    missingok
    rotate 3
    compress
    delaycompress
    notifempty
    create 666
    maxsize 20M
}
