# Laravel Application Log Rotation Configuration
# Generated by FixLogPermissions command

/www/sites/dev.tenderid.com/index/storage/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    postrotate
        # Reload application if needed
        # systemctl reload php8.2-fpm
    endscript
}

/www/sites/dev.tenderid.com/index/storage/logs/query/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    size 100M
}

/www/sites/dev.tenderid.com/index/storage/logs/telegram/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
}

/www/sites/dev.tenderid.com/index/storage/logs/whatsapp/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
}

/www/sites/dev.tenderid.com/index/storage/logs/sirup/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    size 500M
}

/www/sites/dev.tenderid.com/index/storage/logs/performance/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
}